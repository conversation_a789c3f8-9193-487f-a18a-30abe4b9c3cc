<template>
	<view style="padding: 20rpx;">
		<web-view src="https://static.lluuxiu.com/userAgreement.html"></web-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				content: ""
			}
		},
		onLoad() {
			// this.$http.get('/userAgreement').then(res => {
			// 	this.content = res
			// })
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
		}
	}
</script>

<style>

</style>