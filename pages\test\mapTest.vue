<template>
  <view class="container">
    <view class="header">
      <text class="title">地图组件测试</text>
    </view>

    <view class="map-section">
      <text class="section-title">高德地图组件 (3D + 可缩放 + 可旋转 + 用户头像)</text>
      <view class="map-container">
        <FixedMap :width="'100%'" :height="'400rpx'" :latitude="currentLocation.latitude"
          :longitude="currentLocation.longitude" :markers="testMarkers" :userAvatar="testAvatar"
          :bgImage="testBgImage" />
      </view>
    </view>

    <view class="info-section">
      <text class="info-title">地图信息</text>
      <view class="info-item">
        <text>位置: {{ currentLocation.name }}</text>
      </view>
      <view class="info-item">
        <text>经度: {{ currentLocation.longitude }}</text>
      </view>
      <view class="info-item">
        <text>纬度: {{ currentLocation.latitude }}</text>
      </view>
      <view class="info-item">
        <text>地图版本: 高德地图1.4.15</text>
      </view>
      <view class="info-item">
        <text>地图样式: 自定义样式</text>
      </view>
      <view class="info-item">
        <text>标记类型: {{ testAvatar ? '用户头像' : '默认图标' }}</text>
      </view>
      <view class="info-item">
        <text>特性: 3D视图、可缩放、可旋转、可倾斜</text>
      </view>
    </view>

    <view class="button-section">
      <button class="test-btn" @click="changeLocation">切换到上海</button>
      <button class="test-btn" @click="changeToGuangzhou">切换到广州</button>
      <button class="test-btn" @click="resetLocation">重置到北京</button>
    </view>

    <view class="button-section" style="margin-top: 20rpx;">
      <button class="test-btn avatar-btn" @click="toggleAvatar">
        {{ testAvatar ? '使用默认图标' : '使用头像标记' }}
      </button>
      <button class="test-btn bg-btn" @click="toggleBgImage">
        {{ testBgImage ? '隐藏背景图' : '显示背景图' }}
      </button>
    </view>
  </view>
</template>

<script>
import FixedMap from "@/components/fixedMap/fixedMap.vue";

export default {
  name: 'MapTest',
  components: {
    FixedMap
  },
  data() {
    return {
      currentLocation: {
        name: '北京天安门',
        latitude: 39.9042,
        longitude: 116.4074
      },
      testAvatar: 'https://img2.baidu.com/it/u=2778471297,524433918&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', // 测试头像
      testBgImage: 'https://static-1317942045.cos.ap-shanghai.myqcloud.com/black.png', // 测试背景图片
      testMarkers: [{
        id: 1,
        latitude: 39.9042,
        longitude: 116.4074,
        iconPath: '/static/images/localtions.png',
        avatar: 'https://img2.baidu.com/it/u=2778471297,524433918&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
        width: 40,
        height: 40,
        title: '北京天安门',
        isAvatar: true,
        type: 'avatar'
      }]
    }
  },
  methods: {
    changeLocation() {
      // 切换到上海
      this.currentLocation = {
        name: '上海外滩',
        latitude: 31.2304,
        longitude: 121.4737
      };
      this.testMarkers = [{
        id: 1,
        latitude: 31.2304,
        longitude: 121.4737,
        iconPath: this.testAvatar ? this.testAvatar : '/static/images/localtions.png',
        avatar: this.testAvatar,
        width: 40,
        height: 40,
        title: '上海外滩',
        isAvatar: !!this.testAvatar,
        type: 'avatar'
      }];
    },
    changeToGuangzhou() {
      // 切换到广州
      this.currentLocation = {
        name: '广州塔',
        latitude: 23.1291,
        longitude: 113.2644
      };
      this.testMarkers = [{
        id: 1,
        latitude: 23.1291,
        longitude: 113.2644,
        iconPath: this.testAvatar ? this.testAvatar : '/static/images/localtions.png',
        avatar: this.testAvatar,
        width: 40,
        height: 40,
        title: '广州塔',
        isAvatar: !!this.testAvatar,
        type: 'avatar'
      }];
    },
    resetLocation() {
      // 重置到北京
      this.currentLocation = {
        name: '北京天安门',
        latitude: 39.9042,
        longitude: 116.4074
      };
      this.testMarkers = [{
        id: 1,
        latitude: 39.9042,
        longitude: 116.4074,
        iconPath: this.testAvatar ? this.testAvatar : '/static/images/localtions.png',
        avatar: this.testAvatar,
        width: 40,
        height: 40,
        title: '北京天安门',
        isAvatar: !!this.testAvatar,
        type: 'avatar'
      }];
    },
    toggleAvatar() {
      // 切换头像显示
      if (this.testAvatar) {
        this.testAvatar = '';
      } else {
        this.testAvatar = 'https://img2.baidu.com/it/u=2778471297,524433918&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500';
      }

      // 更新当前标记
      this.testMarkers = [{
        id: 1,
        latitude: this.currentLocation.latitude,
        longitude: this.currentLocation.longitude,
        iconPath: this.testAvatar ? this.testAvatar : '/static/images/localtions.png',
        avatar: this.testAvatar,
        width: 40,
        height: 40,
        title: this.currentLocation.name,
        isAvatar: !!this.testAvatar,
        type: 'avatar'
      }];
    },
    toggleBgImage() {
      // 切换背景图片显示
      if (this.testBgImage) {
        this.testBgImage = '';
      } else {
        this.testBgImage = 'https://static-1317942045.cos.ap-shanghai.myqcloud.com/black.png';
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.map-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.map-container {
  width: 100%;
  height: 400rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-section {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item text {
  font-size: 28rpx;
  color: #666;
}

.button-section {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
  flex-wrap: wrap;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #4bc6ed, #bc93f2);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.test-btn:active {
  opacity: 0.8;
}

.avatar-btn {
  background: linear-gradient(45deg, #ff6b6b, #ffa726) !important;
}

.bg-btn {
  background: linear-gradient(45deg, #9c27b0, #673ab7) !important;
}
</style>
