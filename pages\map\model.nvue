<template>
	<view class="map-page">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode="widthFix"></image>
			<view class="nav-title">模型展示地点</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			goBack() {
				uni.navigateTo({
					url: '/pages/shop/shop'
				});
				// if (this.isShowMap) {
				// 	this.isShowMap = false;
				// } else {
				// 	uni.navigateTo({
				// 		url: '/pages/shop/shop'
				// 	});
				// }
			
			},
		}
	}
</script>

<style>
	.map-page {
	  width: 375px; /* rpx 转换为 px */
	  height: 900px; /* 假设屏幕高度为750px，vh 转换为 px */
	  padding-top: 100px;
	  box-sizing: border-box;
	  /* background-color: #F5F7FB; */ /* 可以根据需要启用 */
	  position: relative;
	}
	
	.navigation-bar {
	  width: 375px; /* 100% 转换为具体的 px 值 */
	  display: flex;
	  align-items: center;
	  height: 140px;
	  /* // background-image: 'https://yourdomain.com/static/images/vip/newBackground.png'; /* 使用完整 URL */ */
	  background-image: url('../../static/images/vip/newBackground.png');
	  background-size: cover;
	  position: absolute;
	  z-index: 0;
	  top: 0;
	  left: 0;
	}
	
	.navigation-bar .back {
	  width: 31px;
	  height: 31px;
	  margin-left: 15px; /* 2% 转换为 px，750px * 2% = 15px */
	  position: relative;
	  z-index: 2;
	}
	
	.navigation-bar .nav-title {
	  width: 280px; /* 82% 转换为 px，750px * 82% = 615px */
	  height: 30px;
	  color: #000000;
	  font-family: 'pingfang sc'; /* 替换为系统支持的字体 */
	  font-size: 18px;
	  font-weight: 500;
	  line-height: 30px;
	  letter-spacing: 0;
	  text-align: center;
	  z-index: 1;
	  overflow: hidden;
	  white-space: nowrap;
	  text-overflow: ellipsis;
	}
	
	.navigation-zhezhao {
	  width: 375px; /* 100% 转换为具体的 px 值 */
	  height: 140px;
	  /* // background-image: 'https://yourdomain.com/static/images/vip/nav-zhezhao.png'; /* 使用完整 URL */ */
	  background-image: url('../../static/images/vip/nav-zhezhao.png');
	  background-size: 375px 140px; /* Weex 需要具体的尺寸 */
	  background-repeat: no-repeat;
	  background-position: bottom;
	  position: absolute;
	  z-index: 10;
	  top: 0;
	  left: 0;
	  display: flex;
	  align-items: center;
	  height: 140px;
	}
	
	.navigation-zhezhao .back {
	  width: 31px;
	  height: 31px;
	  margin-left: 15px; /* 2% 转换为 px */
	  position: relative;
	  z-index: 2;
	}
	
	.navigation-zhezhao .nav-title {
	  width: 280px; /* 82% 转换为 px */
	  height: 30px;
	  color: #000000;
	  font-family: 'pingfang sc'; /* 替换为系统支持的字体 */
	  font-size: 18px;
	  font-weight: 500;
	  line-height: 30px;
	  letter-spacing: 0;
	  text-align: center;
	  z-index: 1;
	  overflow: hidden;
	  white-space: nowrap;
	  text-overflow: ellipsis;
	}

</style>
