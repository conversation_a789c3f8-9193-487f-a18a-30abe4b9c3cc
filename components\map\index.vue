<template>
	<view class="container">
		<view :center="center" :change:center="center" id="AmapRender" class="map" :event="event"
			:change:event="AmapRender.receiveEvent" :clearMap="clearMap" :change:clearMap="AmapRender.receiveClear"
			:style="{width:width}"></view>
	</view>
</template>
<script module="AmapRender" lang="renderjs">
	import renderJs from './renderJs.js'
	export default renderJs
</script>
<script>
	export default {
		props: {
			width: {
				type: String,
				default: '710rpx'
			},
			location: {
				type: Array,
				default: []
			}
		},
		data() {
			return {
				zoom: 13,
				event: {
					key: '',
					params: {}
				},
				clearMap: false,
				center: null

			}
		},
		watch: {
			location() {
				this.event = {
					key: 'location',
					params: this.location
				}
			}
		},
		mounted() {
			console.log(uni.getStorageSync('userLoglat'),
				` uni.getStorageSync('userLoglat') uni.getStorageSync('userLoglat') uni.getStorageSync('userLoglat')`);
			this.center = uni.getStorageSync('userLoglat')
		},
		methods: {
			clear() {
				this.clearMap = !this.clearMap
			},
			async callApp({
				act,
				option,
				cb,
				interval
			} = {}) {

				switch (act) {
					case 'mapLocation':
						this.$emit('mapLocation', option)
						break;
					case 'mapLocationMoveend':
						this.$emit('mapLocationMoveend', option)
						break;

					default:
						break;
				}


			},



		}
	}
</script>

<style scoped lang="scss">
	.container {
		position: relative;
		height: 669rpx;

		#AmapRender {
			width: 710rpx;
			height: 669rpx;
			background-color: transparent;
		}


	}
</style>