<template>
	<!-- 地址搜索 -->
	<view class="sscontent">
		<view class="search">
			<uni-icons type="search" size="24"></uni-icons>
			<input class="uni-input" @input="serchAddress" focus placeholder="请输入地址" />
		</view>
		<scroll-view scroll-y="true" :style="'height:'+scr_height+'px;'" lower-threshold="300"
			@scrolltolower="load_more">
			<block v-for="(item,index) in address_data_lists" :key="index">
				<view class="item" @tap="goReapAddress(item)">
					<view>{{item.name}}</view>
					<view>{{item.pname+item.cityname+item.adname+item.address}}</view>
				</view>
			</block>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: '',
					city: '',
					region: '',
					output: 'JSON',
					city_limit: true,
					sortrule: 'distance',
				},
				address_data_lists: [],
				scr_height: 0,
				total: 0,
			}
		},
		onLoad(options) {
			console.log("============onLoad===========");
			this.MapSearchForm.city = options.adcode
			this.MapSearchForm.location = uni.getStorageSync('location')
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					this.scr_height = res.windowHeight - uni.upx2px(80 + 20 + 20)
				}
			})
		},
		methods: {
			goReapAddress(address) {
				uni.$emit('address', address)
				uni.navigateBack()
			},
			/// 获取地址根据关键字
			async getAdress() {
				await uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v5/place/text',
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: (res) => {
						if (res.statusCode == 200) {
							console.log(res);
							this.total = res.data.count
							if (this.MapSearchForm.page_num == 1) {
								return this.address_data_lists = res.data.pois;
							}
							return this.address_data_lists = [...this.address_data_lists, ...res.data.pois]
						}
						this.toast('搜索地址信息出错');
					},
					fail(err) {
						this.toast(err.errMsg);
					}
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			serchAddress(e) {
				console.log("=sadsadsa");
				this.MapSearchForm.keywords = e.detail.value
				this.MapSearchForm.page_num = 1;
				this.address_data_lists = [];
				this.getAdress()
			},
			//下滑加载更多
			load_more() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
				}
				this.getAdress()
			}
		}
	}
</script>

<style lang="less" scoped>
	.sscontent {
		position: absolute;
		width: 100%;
		height: 100%;
		background: #191C26;

		.search {
			margin: 20rpx;
			padding-left: 10rpx;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			background-color: #22252F;
			overflow: hidden;
			height: 80rpx;

			.uni-input {
				padding-left: 10rpx;
			}
		}

		.item {
			padding: 20rpx 30rpx 10px 30rpx;
			border-bottom: 1px solid #D8D8D8;

			view:first-child {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			view:last-child {
				font-size: 24rpx;
				color: #999;
				line-height: 2em;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
</style>