 <template>
 	<view class="container">
 		<u-navbar :customBack="goBack" back-text="" title="发布" :background="{backgroundColor: '#191C26'}"
 			:border-bottom="false" title-color="#fff" back-icon-color="#fff">
 			<view slot="content" style="width: 240rpx;">
 				<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" bg-color="#191C26"
 					active-color="#fff" inactive-color="rgba(255,255,255,0.72)"></u-tabs>
 			</view>
 			<view class="navbar-right" slot="right">
 				<uni-icons type="search" color="#fff" size="24"></uni-icons>
 				<uni-icons type="plus" color="#fff" size="24" style="margin:0 20rpx;"></uni-icons>
 			</view>
 		</u-navbar>
 		<swiper class="swiperC" circular :current="current" @change="setCurrent">
 			<swiper-item>
 				<tianxie-chat-list :pageUrl="pageUrl" :userList="userList" @scrollBnt="scrollBnt"></tianxie-chat-list>
 			</swiper-item>
 			<swiper-item>
 				<view class="swiper-item uni-bg-green">B</view>
 			</swiper-item>
 		</swiper>
 	</view>
 </template>

 <script>
 	export default {
 		data() {
 			return {
 				list: [{
 					name: '消息'
 				}, {
 					name: '好友'
 				}],
 				itemArr: [{
 						title: "新朋友通知",
 						avatar: "@/static/images/newFirend.png",
 						note: "收到1个新的好友申请",
 						path: ""
 					},
 					{
 						title: "新朋友通知",
 						avatar: "@/static/images/newFirend.png",
 						note: "收到1个新的好友申请",
 						path: ""
 					},
 					{
 						title: "新朋友通知",
 						avatar: "@/static/images/newFirend.png",
 						note: "收到1个新的好友申请",
 						path: ""
 					},
 					{
 						title: "新朋友通知",
 						avatar: "@/static/images/newFirend.png",
 						note: "收到1个新的好友申请",
 						path: ""
 					},
 				],
 				current: 0,
 				//pageUrl跳转到聊天室路径
 				pageUrl: "/chat/vue/chat-room",
 				//新增加的聊天人员列表，请求完数据后需清空，等待下次数据请求在赋值
 				userList: [{
 					uid: 1,
 					chatName: "蝎子",
 					unreadNum: "103",
 					newMsg: "",
 					msgType: "img",
 					newMsgTime: "2023-9-20 20:38:35",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_02.png",
 					setTopPerson: "0",
 				}, {
 					uid: 2,
 					chatName: "幺妹",
 					unreadNum: "3",
 					newMsg: "",
 					msgType: "voice",
 					newMsgTime: "2023-9-20 20:40:02",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_03.png",
 					setTopPerson: "0",
 				}, {
 					uid: 3,
 					chatName: "二弟",
 					unreadNum: "4",
 					newMsg: "",
 					msgType: "voido",
 					newMsgTime: "2023-9-20 20:44:20",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_04.png",
 					setTopPerson: "0",
 				}, {
 					uid: 4,
 					chatName: "肖肖",
 					unreadNum: "1",
 					newMsg: "",
 					msgType: "meme",
 					newMsgTime: "2023-9-20 20:16:33",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_01.png",
 					setTopPerson: "1",
 				}, {
 					uid: 5,
 					chatName: "老表",
 					unreadNum: "0",
 					newMsg: "在js中将时间戳转换为常用的时间格式,有三种主要的方式1、使用JS中已有的函数,例如getFullYear(),getMonth()等,将时间戳直接转换成对应的年月",
 					msgType: "text",
 					newMsgTime: "2023-9-20 20:55:10",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_05.png",
 					setTopPerson: "1",
 				}, {
 					uid: 1,
 					chatName: "蝎子",
 					unreadNum: "103",
 					newMsg: "",
 					msgType: "img",
 					newMsgTime: "2023-9-20 20:38:35",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_02.png",
 					setTopPerson: "0",
 				}, {
 					uid: 2,
 					chatName: "幺妹",
 					unreadNum: "3",
 					newMsg: "",
 					msgType: "voice",
 					newMsgTime: "2023-9-20 20:40:02",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_03.png",
 					setTopPerson: "0",
 				}, {
 					uid: 3,
 					chatName: "二弟",
 					unreadNum: "4",
 					newMsg: "",
 					msgType: "voido",
 					newMsgTime: "2023-9-20 20:44:20",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_04.png",
 					setTopPerson: "0",
 				}, {
 					uid: 4,
 					chatName: "肖肖",
 					unreadNum: "1",
 					newMsg: "",
 					msgType: "meme",
 					newMsgTime: "2023-9-20 20:16:33",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_01.png",
 					setTopPerson: "1",
 				}, {
 					uid: 5,
 					chatName: "老表",
 					unreadNum: "0",
 					newMsg: "在js中将时间戳转换为常用的时间格式,有三种主要的方式1、使用JS中已有的函数,例如getFullYear(),getMonth()等,将时间戳直接转换成对应的年月",
 					msgType: "text",
 					newMsgTime: "2023-9-20 20:55:10",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_05.png",
 					setTopPerson: "1",
 				}, {
 					uid: 1,
 					chatName: "蝎子",
 					unreadNum: "103",
 					newMsg: "",
 					msgType: "img",
 					newMsgTime: "2023-9-20 20:38:35",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_02.png",
 					setTopPerson: "0",
 				}, {
 					uid: 2,
 					chatName: "幺妹",
 					unreadNum: "3",
 					newMsg: "",
 					msgType: "voice",
 					newMsgTime: "2023-9-20 20:40:02",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_03.png",
 					setTopPerson: "0",
 				}, {
 					uid: 3,
 					chatName: "二弟",
 					unreadNum: "4",
 					newMsg: "",
 					msgType: "voido",
 					newMsgTime: "2023-9-20 20:44:20",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_04.png",
 					setTopPerson: "0",
 				}, {
 					uid: 4,
 					chatName: "肖肖",
 					unreadNum: "1",
 					newMsg: "",
 					msgType: "meme",
 					newMsgTime: "2023-9-20 20:16:33",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_01.png",
 					setTopPerson: "1",
 				}, {
 					uid: 5,
 					chatName: "老表",
 					unreadNum: "0",
 					newMsg: "在js中将时间戳转换为常用的时间格式,有三种主要的方式1、使用JS中已有的函数,例如getFullYear(),getMonth()等,将时间戳直接转换成对应的年月",
 					msgType: "text",
 					newMsgTime: "2023-9-20 20:55:10",
 					chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_05.png",
 					setTopPerson: "1",
 				}],
 			}
 		},
 		methods: {
 			setCurrent(ids) {
 				this.current = ids.detail.current
 			},
 			goBack() {
 				uni.navigateBack()
 			},
 			change(index) {
 				this.current = index;
 			},
 			//列表滑动到底部
 			scrollBnt(val) {
 				if (val) {
 					this.$common.toast("到底部了!")
 					console.log("到底部了!");
 				}
 			},
 		}
 	}
 </script>
 <style lang="scss" scoped>
 	.container {
 		.swiperC {
 			height: 100vh;
 		}
 	}
 </style>