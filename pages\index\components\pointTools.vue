<template>
	<uni-popup class="infoDialog" :safe-area="false" ref="popup" type="bottom" @maskClick="close" :mask-click="true">
		<view class="pointTypes" @click="close">
			<view class="title">创建你的常用地点</view>
			<view class="types">
				<view class="type-li " :class="{active:active=='1'}" @click.stop="changeType('1')">
					<!-- <image class="icon" v-if="active=='1'" src="../../../static/map/j.png"></image> -->
					<image class="iconBg" src="../../../static/map/xz.png" mode="" v-if="active=='1'"></image>
					<image class="iconBg" src="../../../static/map/wxz.png" mode="" v-else></image>
					<image class="icon" src="../../../static/map/j0.png"></image>
					<text class="type-li-txt">家庭</text>
				</view>
				<view class="type-li" :class="{active:active=='2'}" @click.stop="changeType('2')">
					<!-- <image class="icon" v-if="active=='2'" src="../../../static/map/xx.png"></image> -->
					<image class="iconBg" src="../../../static/map/xz.png" mode="" v-if="active=='2'"></image>
					<image class="iconBg" src="../../../static/map/wxz.png" mode="" v-else></image>
					<image class="icon" src="../../../static/map/xx0.png"></image>
					<text class="type-li-txt">学校</text>
				</view>
				<view class="type-li" :class="{active:active=='3'}" @click.stop="changeType('3')">
					<image class="iconBg" src="../../../static/map/xz.png" mode="" v-if="active=='3'"></image>
					<image class="iconBg" src="../../../static/map/wxz.png" mode="" v-else></image>
					<!-- <image class="icon" v-if="active=='3'" src="../../../static/map/gz.png"></image> -->
					<image class="icon" src="../../../static/map/gz0.png"></image>
					<text class="type-li-txt">工作</text>
				</view>
				<view class="type-li" :class="{active:active=='4'}" @click.stop="changeType('4')">
					<image class="iconBg" src="../../../static/map/xz.png" mode="" v-if="active=='4'"></image>
					<image class="iconBg" src="../../../static/map/wxz.png" mode="" v-else></image>
					<!-- <image class="icon" v-if="active=='4'" src="../../../static/map/e.png"></image> -->
					<image class="icon" src="../../../static/map/e0.png"></image>
					<text class="type-li-txt">自定义</text>
				</view>

			</view>
			<view class="btn" @click.stop="submit">
				创建
			</view>
			<u-toast ref='notify' />
			<!-- <view class="type-addbtn" @click.stop="submit">添加</view> -->
			<!-- <view class="img140"></view> -->
		</view>
	</uni-popup>

</template>

<script>
	export default {
		data() {
			return {
				active: '1',
				option: {}
			}
		},
		mounted() {
			// this.open()
		},
		methods: {
			open(option) {
				this.active = '1'
				this.option = option
				this.$refs.popup.open('bottom')
			},
			submit() {
				this.$refs.popup.close('bottom')
				this.$emit('PointTypeSubmit', {
					type: this.active,
					...this.option
				})
			},
			close() {
				this.$refs.popup.close()
			},
			changeType(str) {
				this.active = str
			}
		}
	}
</script>

<style scoped lang="scss">
	.btn {
		width: 686rpx;
		height: 92rpx;
		margin-top: 92rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		text-align: center;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 92rpx;
	}

	.pointTypes {
		width: 100vw;
		height: 553rpx;
		background: #191C26;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		// background-color: rgba(0, 0, 0, .1);
		box-sizing: border-box;
		// // position: fixed;
		// // left: 0;
		// // top: 0;
		// z-index: 100;
		display: flex;
		flex-direction: column;
		// align-items: center;
		// justify-content: flex-end;
		padding: 38rpx 32rpx;

		.title {
			width: 304rpx;
			height: 55rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			font-size: 38rpx;
			color: #FFFFFF;
			line-height: 55rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-bottom: 50rpx;
		}

		.types {
			width: 100%;
			// position: absolute;
			// left: 50%;
			// bottom: 400rpx;
			// transform: translateX(-50%);
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-sizing: border-box;

			.confrimBtn {
				width: 62rpx;
				height: 118rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 14rpx 14rpx 14rpx 14rpx;
				opacity: 1;
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					display: inline-block;
					width: 24rpx;
					height: 70rpx;
					font-size: 24rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 35rpx;
				}
			}

			.type-li {
				width: 104rpx;
				border-radius: 14rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				// padding: 16rpx 0;
				box-sizing: border-box;
				position: relative;

				.iconBg {
					width: 104rpx;
					height: 104rpx;
					position: absolute;
					top: -20rpx;
				}

				.icon {
					width: 58rpx;
					height: 58rpx;
				}

				.type-li-txt {
					font-weight: bold;
					color: rgba(255, 255, 255, 0.72);
					margin-top: 39rpx;
					font-weight: 500;
					font-size: 32rpx;
				}
			}
		}

		.active {
			position: relative;
			background-color: #191C26 !important;

			.type-li-txt {
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;

				margin-top: 16rpx;
				background: #fff;
				// background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			&::after {
				// width: 100%;
				// height: 100%;
				// position: absolute;
				// left: -4rpx;
				// top: -4rpx;
				// content: '';
				// z-index: -1;
				// padding: 4rpx;
				// background: linear-gradient(145deg, #4BEAE8 0%, #C095F7 100%);
				// border-radius: 36rpx;
			}

		}

		.type-addbtn {
			width: 200rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: linear-gradient(145deg, #4BEAE8 0%, #C095F7 100%);
			color: #333;
			font-size: 32rpx;
			letter-spacing: 5rpx;
			font-weight: bold;
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;

		}
	}
</style>