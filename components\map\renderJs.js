let myMap;
import {
	config
} from '@/config.js'
import {
	newMarker,
	getGeocoder
} from '@/utils/MapTools.js'

const {
	avatarW,
	avatarH,
	avatarHoverW,
	avatarHoverH,
	packetW,
	packetH
} = config
export default {
	data() {
		return {

		}
	},
	mounted() {

		if (typeof window.AMap === 'function') {
			this.initMap()

		} else {
			window._AMapSecurityConfig = {
				securityJsCode: '********************************',
			}
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
			script.onload = this.initMap.bind(this)
			document.head.appendChild(script)
		}

	},
	onUnload() {},
	methods: {
		initMap() {
			myMap = new AMap.Map('AmapRender', {
				center: this.center,
				zoom: 15,
				zooms: [5, 20],
				viewMode: '3D',
				resizeEnable: false,
				draggable: false
			});
			// 地图加载完成！
			myMap.on("complete", async () => {
				myMap.setCenter(this.center)
			});
			myMap.on('click', async (ev) => {
				myMap && myMap.setCenter([ev.lnglat.lng, ev.lnglat.lat])
				const res = await getGeocoder(AMap, {
					lnglat: [ev.lnglat.lng, ev.lnglat.lat]
				})

				this.callMethod('mapLocation', {
					city: res.regeocode.addressComponent.city || res.regeocode.addressComponent
						.province,
					address: res.regeocode.formattedAddress,
				})
			}, this);
			myMap.on('touchend', async (ev) => {
				console.log(123, '123');
				setTimeout(async () => {
					myMap && myMap.clearMap()
					const center = myMap.getCenter()
					const onlineIcon = new AMap.Icon({
						image: "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/location.png",
						size: new AMap.Size(packetW, packetH),
						imageSize: new AMap.Size(packetW, packetH),
					})
					const marker = new AMap.Marker({
						position: center,
						icon: onlineIcon
					});
					marker.setMap(myMap);
					const res = await getGeocoder(AMap, {
						lnglat: [center.lng, center.lat]
					})
					// console.log(res,'ressssss');
					const addressComponent = res.regeocode.addressComponent
					this.callMethod('mapLocationMoveend', {
						city: res.regeocode.addressComponent.city || res.regeocode
							.addressComponent
							.province,
						address: res.regeocode.formattedAddress,
						name: res.regeocode.formattedAddress,
						location: `${center.lng}, ${center.lat}`,
						pname: addressComponent.province,
						"cityname": addressComponent.city,
						"adname": addressComponent.district,
						"address": addressComponent.street + addressComponent
							.streetNumber,
						"distance": "",
						"parent": "",
						"locationName": res.regeocode.formattedAddress
					})
				}, 1000)
			}, this);
		},
		receiveClear(newValue, oldValue, ownerVm, vm) {
			myMap && myMap.clearMap()
		},
		async receiveEvent(newValue, oldValue, ownerVm, vm) {
			console.log(newValue, 'receiveEvent===事件透传');
			const {
				key,
				params,
				interval
			} = newValue
			const icon_red = new AMap.Icon({
				image: "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/location.png",
				size: new AMap.Size(packetW, packetH),
				imageSize: new AMap.Size(packetW, packetH),
			})
			switch (key) {
				case "location":
					const marker = new AMap.Marker({
						position: params,
						icon: icon_red
					});
					marker.setMap(myMap);
					marker.setIcon(icon_red);
					myMap.setCenter(params)
					break;
				default:
					break;
			}
		},

		// 调用 view 层的方法
		callMethod(act = '', params, cb) {
			this.$ownerInstance.callMethod('callApp', {
				act,
				option: params,
				cb: cb
			})
		},

	}
}