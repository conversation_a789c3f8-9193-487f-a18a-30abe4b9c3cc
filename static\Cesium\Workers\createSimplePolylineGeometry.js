/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.115
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as r}from"./chunk-GUUUD723.js";import{a as L}from"./chunk-AJBZ3ZYP.js";import{a as E}from"./chunk-GNAFTO6Q.js";import"./chunk-JXMT2ZQS.js";import"./chunk-KZVVSHMS.js";import"./chunk-EQAHH27B.js";import"./chunk-LWO5EUNN.js";import{a as X}from"./chunk-SPMRTIBU.js";import{a as Q}from"./chunk-U6MIZ4IC.js";import{b as j,c as K,d as Y}from"./chunk-3G4OCZJT.js";import{d as W}from"./chunk-LYPPBP4Q.js";import"./chunk-PCFGFNNQ.js";import"./chunk-V2SDNSQR.js";import"./chunk-S7TTFAYA.js";import{a as H}from"./chunk-TLYHKSDJ.js";import{a as k,d as b}from"./chunk-PYVDHCDQ.js";import{a as q}from"./chunk-JMWWNZHX.js";import"./chunk-DNO4OWAM.js";import"./chunk-XDVDNOI4.js";import{a as w}from"./chunk-Z3SYNMQT.js";import{a as D}from"./chunk-4KGDZUZQ.js";import{e as u}from"./chunk-F3TINEFX.js";function Z(t,e,o,i,s,c,p){let g=E.numberOfPoints(t,e,s),f,n=o.red,m=o.green,d=o.blue,T=o.alpha,l=i.red,h=i.green,y=i.blue,V=i.alpha;if(r.equals(o,i)){for(f=0;f<g;f++)c[p++]=r.floatToByte(n),c[p++]=r.floatToByte(m),c[p++]=r.floatToByte(d),c[p++]=r.floatToByte(T);return p}let M=(l-n)/g,C=(h-m)/g,G=(y-d)/g,_=(V-T)/g,a=p;for(f=0;f<g;f++)c[a++]=r.floatToByte(n+f*M),c[a++]=r.floatToByte(m+f*C),c[a++]=r.floatToByte(d+f*G),c[a++]=r.floatToByte(T+f*_);return a}function R(t){t=w(t,w.EMPTY_OBJECT);let e=t.positions,o=t.colors,i=w(t.colorsPerVertex,!1);if(!u(e)||e.length<2)throw new D("At least two positions are required.");if(u(o)&&(i&&o.length<e.length||!i&&o.length<e.length-1))throw new D("colors has an invalid length.");this._positions=e,this._colors=o,this._colorsPerVertex=i,this._arcType=w(t.arcType,L.GEODESIC),this._granularity=w(t.granularity,q.RADIANS_PER_DEGREE),this._ellipsoid=w(t.ellipsoid,b.WGS84),this._workerName="createSimplePolylineGeometry";let s=1+e.length*k.packedLength;s+=u(o)?1+o.length*r.packedLength:1,this.packedLength=s+b.packedLength+3}R.pack=function(t,e,o){if(!u(t))throw new D("value is required");if(!u(e))throw new D("array is required");o=w(o,0);let i,s=t._positions,c=s.length;for(e[o++]=c,i=0;i<c;++i,o+=k.packedLength)k.pack(s[i],e,o);let p=t._colors;for(c=u(p)?p.length:0,e[o++]=c,i=0;i<c;++i,o+=r.packedLength)r.pack(p[i],e,o);return b.pack(t._ellipsoid,e,o),o+=b.packedLength,e[o++]=t._colorsPerVertex?1:0,e[o++]=t._arcType,e[o]=t._granularity,e};R.unpack=function(t,e,o){if(!u(t))throw new D("array is required");e=w(e,0);let i,s=t[e++],c=new Array(s);for(i=0;i<s;++i,e+=k.packedLength)c[i]=k.unpack(t,e);s=t[e++];let p=s>0?new Array(s):void 0;for(i=0;i<s;++i,e+=r.packedLength)p[i]=r.unpack(t,e);let g=b.unpack(t,e);e+=b.packedLength;let f=t[e++]===1,n=t[e++],m=t[e];return u(o)?(o._positions=c,o._colors=p,o._ellipsoid=g,o._colorsPerVertex=f,o._arcType=n,o._granularity=m,o):new R({positions:c,colors:p,ellipsoid:g,colorsPerVertex:f,arcType:n,granularity:m})};var F=new Array(2),N=new Array(2),$={positions:F,height:N,ellipsoid:void 0,minDistance:void 0,granularity:void 0};R.createGeometry=function(t){let e=t._positions,o=t._colors,i=t._colorsPerVertex,s=t._arcType,c=t._granularity,p=t._ellipsoid,g=q.chordLength(c,p.maximumRadius),f=u(o)&&!i,n,m=e.length,d,T,l,h,y=0;if(s===L.GEODESIC||s===L.RHUMB){let _,a,P;s===L.GEODESIC?(_=q.chordLength(c,p.maximumRadius),a=E.numberOfPoints,P=E.generateArc):(_=c,a=E.numberOfPointsRhumbLine,P=E.generateRhumbArc);let v=E.extractHeights(e,p),B=$;if(s===L.GEODESIC?B.minDistance=g:B.granularity=c,B.ellipsoid=p,f){let A=0;for(n=0;n<m-1;n++)A+=a(e[n],e[n+1],_)+1;d=new Float64Array(A*3),l=new Uint8Array(A*4),B.positions=F,B.height=N;let S=0;for(n=0;n<m-1;++n){F[0]=e[n],F[1]=e[n+1],N[0]=v[n],N[1]=v[n+1];let O=P(B);if(u(o)){let z=O.length/3;h=o[n];for(let U=0;U<z;++U)l[S++]=r.floatToByte(h.red),l[S++]=r.floatToByte(h.green),l[S++]=r.floatToByte(h.blue),l[S++]=r.floatToByte(h.alpha)}d.set(O,y),y+=O.length}}else if(B.positions=e,B.height=v,d=new Float64Array(P(B)),u(o)){for(l=new Uint8Array(d.length/3*4),n=0;n<m-1;++n){let S=e[n],O=e[n+1],z=o[n],U=o[n+1];y=Z(S,O,z,U,g,l,y)}let A=o[m-1];l[y++]=r.floatToByte(A.red),l[y++]=r.floatToByte(A.green),l[y++]=r.floatToByte(A.blue),l[y++]=r.floatToByte(A.alpha)}}else{T=f?m*2-2:m,d=new Float64Array(T*3),l=u(o)?new Uint8Array(T*4):void 0;let _=0,a=0;for(n=0;n<m;++n){let P=e[n];if(f&&n>0&&(k.pack(P,d,_),_+=3,h=o[n-1],l[a++]=r.floatToByte(h.red),l[a++]=r.floatToByte(h.green),l[a++]=r.floatToByte(h.blue),l[a++]=r.floatToByte(h.alpha)),f&&n===m-1)break;k.pack(P,d,_),_+=3,u(o)&&(h=o[n],l[a++]=r.floatToByte(h.red),l[a++]=r.floatToByte(h.green),l[a++]=r.floatToByte(h.blue),l[a++]=r.floatToByte(h.alpha))}}let V=new Q;V.position=new Y({componentDatatype:H.DOUBLE,componentsPerAttribute:3,values:d}),u(o)&&(V.color=new Y({componentDatatype:H.UNSIGNED_BYTE,componentsPerAttribute:4,values:l,normalize:!0})),T=d.length/3;let M=(T-1)*2,C=X.createTypedArray(T,M),G=0;for(n=0;n<T-1;++n)C[G++]=n,C[G++]=n+1;return new K({attributes:V,indices:C,primitiveType:j.LINES,boundingSphere:W.fromPoints(e)})};var J=R;function x(t,e){return u(e)&&(t=J.unpack(t,e)),t._ellipsoid=b.clone(t._ellipsoid),J.createGeometry(t)}var _e=x;export{_e as default};
