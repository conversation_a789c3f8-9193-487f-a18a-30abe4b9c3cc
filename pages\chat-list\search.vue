<template>
	<view class="searchPage">
		<u-search placeholder="搜索" v-model="keyword" :showAction="false" bgColor="#272727"
			style="width: 710rpx;margin: auto;" @search="getData"></u-search>
		<scroll-view class="scroll-view" scroll-y="true">
			<uni-list :border="false">
				<view class="p2p" v-if="keyword && pers.length > 0">
					<view class="title">联系人</view>
					<view class="list">
						<uni-list-chat v-for="(item, index) in pers.slice(0, 5)" :key="index"
							:title="item.userInfo.nick" :clickable="true" :avatar="item.userInfo.avatar"
							:note="getNote(item)" :avatar-circle="true" :time="item.time ? getTimeFmt(item.time) : ' '"
							@click="goP2p(item)"></uni-list-chat>
					</view>
					<view class="more" v-if="pers.length > 5" @click="goMorePer">更多联系人</view>
				</view>
				<view class="team" v-if="keyword && groups.length > 0">
					<view class="title">群聊</view>
					<view class="list">
						<uni-list-chat v-for="(item, index) in groups.slice(0, 5)" :key="index"
							:title="item.teamInfo.name" :clickable="true" :avatarList="item.avatarList"
							:note="getNote(item)" :time="item.time ? getTimeFmt(item.time) : ' '"
							@click="goTeam(item)"></uni-list-chat>
					</view>
					<view class="more" v-if="groups.length > 5" @click="goMoreGroup">更多群聊</view>
				</view>
			</uni-list>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		timeFmt
	} from '@/utils/common.js';
	export default {
		data() {
			return {
				keyword: '',
				pers: [],
				groups: []
			};
		},
		methods: {
			goMoreGroup() {
				uni.navigateTo({
					url: '/pages/chat-list/searchAllGroup?keyword=' + this.keyword
				});
			},
			goMorePer() {
				uni.navigateTo({
					url: '/pages/chat-list/searchAllPer?keyword=' + this.keyword
				});
			},
			goTeam(item) {
				uni.navigateTo({
					url: '/pages/HM-chat/HM-chat?userItem=' +
						encodeURIComponent(
							JSON.stringify({
								chatName: item.teamInfo.name,
								uid: item.sessionId,
								type: 'team',
								teamId: item.teamInfo.teamId,
								nuck: item.teamInfo.name
							})
						)
				});
			},
			goP2p(item) {
				console.log(123);
				uni.navigateTo({
					url: '/pages/HM-chat/HM-chat?userItem=' +
						encodeURIComponent(
							JSON.stringify({
								chatName: item.sessionId,
								idServer: item.idServer,
								uid: item.sessionId,
								type: 'p2p',
								to: item.to,
								nuck: item.userInfo.nick,
								account: item.target
							})
						)
				});
			},
			getNote(item) {
				let context =
					item.type == 'text' || item.type == 'meme' ?
					item.newMsg :
					item.type == 'image' ?
					'[图片]' :
					item.type == 'audio' ?
					'[语音]' :
					item.type == 'video' ?
					'[视频]' :
					item.type == 'custom' ?
					'自定义消息' :
					'';
				return item.fromNick + '：' + item.body;
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			getTimeFmt(str) {
				// console.log(str,'sss');
				if (str) {
					try {
						return timeFmt(str, 'yyyy-MM-DD hh:mm:ss');
					} catch (err) {
						console.log(err);
					}
				} else {
					return '';
				}
			},
			async getData() {
				const Yxim = this.$store.state._Yxim;
				const msgs = await this.$store.state._Yxim.msgLog.ftsCloudMsgLogs({
					keyword: this.keyword
				})
				console.log("========msgs=========", msgs);
				(this.pers = []), (this.groups = []);
				if (msgs && msgs.length > 0) {
					msgs.forEach(async item => {
						if (item.scene == 'team') {
							const users = await this.$store.state._Yxim.team.getTeamMembers({
								teamId: item.target
							});
							const teamInfo = await this.$store.state._Yxim.team.getTeamInfo({
								teamId: item.target
							});
							const userRes = await this.$store.state._Yxim.user.getUsersNameCardFromServer({
								accounts: users.map(item1 => item1.account)
							});
							this.groups.push({
								...item,
								teamInfo,
								avatarList: userRes.map(per => {
									return {
										url: per.avatar ||
											'https://img2.baidu.com/it/u=**********,*********&fm=253&fmt=auto?w=800&h=1067'
									};
								})
							});
							console.log(this.groups, 'team============userRes1');
						} else if (item.scene == 'p2p') {
							const userRes1 = await this.$store.state._Yxim.user
								.getUsersNameCardFromServer({
									accounts: [item.target]
								});
							this.pers.push({
								...item,
								userInfo: userRes1[0]
							});
							console.log(this.pers, 'p2p============userRes1');
						}
					});
				}
			}
		}
	};
</script>
<style>
	::v-deep .uni-list--border:after {
		background-color: transparent;
	}
</style>
<style scoped lang="scss">
	.searchPage {
		.scroll-view {

			.p2p,
			.team {
				color: #fff;
				font-size: 28rpx;
				background-color: #22252f;
				margin-top: 40rpx;

				.title {
					width: 100%;
					padding: 20rpx;
					box-sizing: border-box;
					border-bottom: 1rpx solid rgba(255, 255, 255, 0.4);
				}

				.list {}

				.more {
					padding: 20rpx;
					box-sizing: border-box;
					border-top: 1rpx solid rgba(255, 255, 255, 0.4);
				}
			}
		}
	}
</style>