<template>
	<view style="height: 100%; overflow-x: hidden;">
		<view class="info-wrapper">
			<view class="title-label">活动名称</view>
			<view class="title">{{ orderInfo.title || ''}}</view>
			<view class="info" style="margin-top: 56rpx;">
				<u-icon name="clock"></u-icon>
				时间：{{ orderInfo.timeline || '' }}
			</view>
			<view class="info" style="margin-top: 16rpx; margin-bottom: 24rpx;">
				<u-icon name="map"></u-icon>
				地址：{{ orderInfo.address || '' }}
			</view>
			<image class="face" src="@/static/images/order/face.png"></image>
		</view>
		<view class="bottom-wrapper">
			<view class="code-wrapper">
				验证码<text class="code">{{ orderInfo.code || '' }}</text>
			</view>
			<view class="QR-wrapper">
				<view class="order-number">订单号：{{ orderInfo.order_no || '' }}</view>
				<view class="divider" />
				<view class="image-container">
					<!-- 		<Uqrcode :auto="false" ref="uqrcode" canvas-id="qrcode" size="320" sizeUnit="rpx" :value="orderInfo.code" :options="{ margin: 10 }">
					</Uqrcode> -->
					<view style="background-color: white; padding: 16rpx; border-radius: 8rpx;">
						<image class="qr" :src="'data:image/png;base64,' + orderInfo.qrcode"></image>
					</view>
					<view v-if="orderInfo.order_status === 2 || orderInfo.order_status === 3" style="background-color: #606169; opacity: 0.8; padding: 16rpx; border-radius: 8rpx; position: absolute;">
						<image class="qr" />
					</view>
					<image v-if="orderInfo.order_status === 3" class="refund" src="@/static/images/order/refund.png">
					</image>
					<image v-if="orderInfo.order_status === 2" class="refund" src="@/static/images/order/checked.png">
					</image>
				</view>
				<view v-if="orderInfo.order_status === 1" class="btn-wrapper">
					<view class="self-verify" @click="selfVerify">自助验票</view>
					<view class="exit" @click="exit">退款</view>
				</view>
				<view v-if="orderInfo.order_status === 1 || orderInfo.order_status === 2" class="see-activity"
					@click="seeActivity">
					查看活动
				</view>
				<view v-else class="back" @click="goBack">
					返回
				</view>
			</view>
		</view>
		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showExit">
			<view>
				<view class="pop-content-exit">
					<view class="pop-title">确认退款</view>
				</view>
				<view class="pop-note">注：退款后钱款会<text
						style="color: rgb(190, 147, 250)">原额返回</text>钱包内</view>
				<view class="pop-button">
					<view class="fail" @click="showExit = false">取消</view>
					<view class="success" @click="exitConfirm">确认</view>
				</view>
			</view>
		</u-popup>
		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showVerify">
			<view>
				<view class="pop-content">
					<image class="pop-img" src="@/static/images/order/self_verify.png"></image>
					<view class="pop-title">是否自助验票？</view>
				</view>
				<view class="pop-note">备注：自助验票后系统判定该门票状态为<text
						style="color: rgb(190, 147, 250)">“已核验”</text>，与活动发起人验票后状态一致，验票后不可退款。</view>
				<view class="pop-button">
					<view class="fail" @click="showVerify = false">取消</view>
					<view class="verify" @click="verifyConfirm">确认</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import Uqrcode from "@/components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue"
	export default {
		components: {
			Uqrcode
		},
		onLoad(option) {
			const that = this
			// 获取订单详情信息
			// const eventChannel = this.getOpenerEventChannel();
			// eventChannel.on('getOrderInfo', function(order) {
			// 	that.orderInfo = order
			// })
			if (option.orderNo) {
				this.orderNo = option.orderNo
				this.getOrderInfo()
			} else {
				uni.showToast({
					title: "订单编号为空",
					icon: "none"
				})
			}
		},
		data() {
			return {
				orderNo: '',
				orderInfo: {
					title: '',
					address: '',
					order_status: 1,
					code: '',
					qrcode: '',
					order_no: '',
					price: 0,
					timeline: '',
				},
				showExit: false,
				showVerify: false,
			}
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			seeActivity() {
				uni.navigateTo({
					url: `/pages/activity/details?id=${this.orderInfo.activity_uuid}`
				})
			},
			selfVerify() {
				this.showVerify = true
			},
			exit() {
				this.showExit = true
			},
			getOrderInfo() {
				this.$http.get('/api/pay/app-order-detail', { order_no: this.orderNo }).then(res => {
					if (res.code === 200) {
						this.orderInfo = res.message
						console.log(this.orderInfo)
					}
				})
			},
			exitConfirm() {
				this.showExit = false
				uni.showLoading({
					title: '退出中...'
				});
				this.$http.post("/activity/refund-ticket", {
					uuid: this.orderInfo.activity_uuid
				}).then((res) => {
					uni.hideLoading()
					console.log(res);
					if (res.code === 200) {
						uni.showToast({
							title: "已退出该活动！"
						})
						this.getOrderInfo()
					} else {
						uni.showToast({
							title: res.message,
							icon: "none"
						})
					}
				});
			},
			verifyConfirm() {
				this.$http.post("/api/pay/check-ticket-self", { code: this.orderInfo.code }).then(res => {
					console.log(res)
					if (res.code === 200) {
						this.showVerify = false
						uni.showToast({
							title: "验票成功",
							icon: 'none'
						})
						this.getOrderInfo()
					} else {
						uni.showToast({
							title: res,
							icon: "none"
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.info-wrapper {
		padding: 24rpx 60rpx;
		position: relative;
	}

	.face {
		position: absolute;
		right: -8rpx;
		top: 36rpx;
		width: 140rpx;
		height: 140rpx;
	}

	.title-label {
		font-size: 28rpx;
		font-weight: 500;
	}

	.title {
		font-size: 40rpx;
		font-weight: 500;
		margin-top: 20rpx;
	}

	.info {
		color: rgb(188, 188, 188);
		font-size: 28rpx;
		font-weight: 500;
	}

	.bottom-wrapper {
		border-radius: 60rpx 0px 0px 0px;
		background: linear-gradient(90.00deg, rgb(83, 195, 238), rgb(189, 148, 250) 100%);
		min-height: 480rpx;
		/* height: 80vh; */
	}

	.code-wrapper {
		display: flex;
		padding: 40rpx 0;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		color: white;
	}

	.code {
		font-size: 44rpx;
		margin-left: 20rpx;
	}

	.QR-wrapper {
		border-radius: 60rpx 60rpx 0px 0px;
		background: rgb(56, 58, 68);
		padding: 80rpx 40rpx;
		position: relative;
		height: 100%;
	}

	/* .QR-wrapper::before {
	  content: '';
	  position: absolute;
	  top: -70rpx;
	  left: calc(50% - 50rpx);
	  width: 100rpx;
	  height: 100rpx;
	  background: white;
	  border-radius: 50%;
} */
	.order-number {
		font-size: 32rpx;
		font-weight: 400;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.divider {
		border-bottom: 2rpx dashed #ccc;
		margin-top: 48rpx;
	}

	.image-container {
		padding: 64rpx 0 100rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.qr {
		width: 320rpx;
		height: 320rpx;
	}

	.mask {
		height: 320rpx;
		width: 320rpx;
	}

	.refund {
		position: absolute;
		width: 180rpx;
		height: 180rpx;
		bottom: 40rpx;
		right: 100rpx;
	}

	.btn-wrapper {
		display: flex;
		align-items: center;
		row-gap: 16rpx;
	}

	.self-verify {
		color: rgb(80, 197, 238);
		font-family: HarmonyOS Sans;
		font-size: 32rpx;
		font-weight: 500;
		flex: 1;
		border-radius: 18rpx;
		border: rgb(80, 197, 238) 2rpx solid;
		text-align: center;
		padding: 32rpx 0;

	}

	.exit {
		color: rgb(255, 75, 58);
		font-family: HarmonyOS Sans;
		font-size: 32rpx;
		font-weight: 500;
		flex: 1;
		border-radius: 18rpx;
		border: rgb(255, 75, 58) 2rpx solid;
		text-align: center;
		padding: 32rpx 0;
		margin-left: 32rpx;

	}

	.see-activity {
		margin-top: 32rpx;
		border-radius: 18rpx;
		background: linear-gradient(90.00deg, rgb(82, 195, 238), rgb(191, 147, 250) 100%);
		font-size: 32rpx;
		font-weight: 500;
		text-align: center;
		padding: 32rpx 0;
	}

	.back {
		margin-top: 32rpx;
		border-radius: 18rpx;
		background: rgb(255, 76, 76);
		font-size: 32rpx;
		font-weight: 400;
		text-align: center;
		padding: 32rpx 0;
	}
	
	.pop-content-exit {
		border-radius: 40rpx 40rpx 0px 0px;
		background-image: url(@/static/images/order/bg_exit.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding-top: 80rpx;
		padding-bottom: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.pop-content {
		border-radius: 40rpx 40rpx 0px 0px;
		background-image: url(@/static/images/order/bg_self_verify.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding-top: 80rpx;
		padding-bottom: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.pop-img {
		width: 160rpx;
		height: 160rpx
	}

	.pop-title {
		color: rgb(33, 33, 33);
		font-family: HarmonyOS Sans;
		font-size: 40rpx;
		font-weight: 700;
		letter-spacing: 0px;
		text-align: center;
	}

	.pop-desc {
		color: rgb(69, 69, 69);
		font-family: HarmonyOS Sans;
		font-size: 32rpx;
		font-weight: 500;
		letter-spacing: 0px;
		text-align: center;
		margin-top: 64rpx;
	}

	.pop-button {
		display: flex;
		align-items: center;
		padding: 40rpx 40rpx 64rpx 40rpx;
	}

	.fail {
		color: rgb(120, 120, 120);
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		border: rgb(120, 120, 120) solid 2rpx;
		flex: 1;
		padding: 24rpx 0;
	}

	.success {
		color: rgb(255, 255, 255);
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		background: rgb(255, 75, 58);
		flex: 1;
		padding: 24rpx 0;
		margin-left: 32rpx;
	}

	.verify {
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		background: rgb(255, 75, 58);
		flex: 1;
		padding: 24rpx 0;
		margin-left: 32rpx;
		background: rgb(39, 203, 255);
	}

	.pop-note {
		margin: 0 40rpx;
		margin-top: 16rpx;
		border-radius: 20rpx;
		background: linear-gradient(90.00deg, rgb(238, 247, 251), rgb(248, 249, 253) 100%);
		padding: 24rpx 32rpx;
		color: rgb(102, 102, 102);
		font-size: 28rpx;
	}
</style>