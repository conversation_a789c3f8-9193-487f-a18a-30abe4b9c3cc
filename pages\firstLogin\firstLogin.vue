<template>
	<view class="appPage">
		<scroll-view scroll-y="true">
			<view :style="{height:statusBarHeight+ 'rpx'}" />
			<view class="title">请填写您的资料</view>
			<view class="content">
				完善资料，个性化你的内容
			</view>
			<view class="ava">
				<view class="avatar" @click="updateImage" v-if="!form.avatar">
					<text>点击添加头像</text>
				</view>
				<image class="avatarImg" :src="form.avatar" mode="aspectFill" v-else></image>
				<image class="camera" @click="updateImage" src="../../static/images/camera.png" mode=""></image>
			</view>
			<view class="inputBg" style="margin-top: 54rpx;">
				<u-input v-model="form.nickname" type="text" placeholder="昵称" :border="false" clearable />
			</view>
			<view class="intro">简介</view>
			<view class="inputTextareaBg">
				<textarea v-model="form.introduction" placeholder-style="color: rgba(255,255,255,0.52)"
					placeholder="请输入简介" />
			</view>
			<view class="intro">性别</view>
			<view class="sex">
				<view class="left" @click="setSexcurrent(2)"
					:style="{'background':form.sex==2 ? 'linear-gradient(93deg, #F96DD3 0%, #FFB52E 100%)':'#1D1C21'}">
					<image style="width: 42rpx;height: 42rpx;margin-right: 10rpx;" src="../../static/images/wuman.png"
						mode=""></image>
					女生
				</view>
				<view class="right" @click="setSexcurrent(1)"
					:style="{'background':form.sex==1 ? 'linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)':'#1D1C21'}">
					<image style="width: 42rpx;height: 42rpx;margin-right: 10rpx;" src="../../static/images/man.png"
						mode=""></image>
					男生
				</view>
			</view>
			<view class="sex" style="margin-top: 111rpx;">
				<view class="left" @click="submit()">
					跳过
				</view>
				<view class="right" @click="submit()"
					style="background:linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)">
					完成
				</view>
			</view>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 80,
				username: '',
				intro: '',
				sexCurrent: 0,
				avaImg: "",
				form: {
					"nickname": "",
					"avatar": "http://img.lluuxiu.com/default_avatar.jpg",
					"sex": 0,
					"introduction": "",
					// "birthday": 700577191,
					// "hometown": "",
					// "height": 180,
					// "degree": 2,
					// "cover": ""
				}
			}
		},
		onLoad() {
			const system = uni.getStorageSync('system')
			// this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			submit() {
				this.$http.post('/api/user/update', this.form).then(res => {
					uni.redirectTo({
						url: '/pages/index/index'
					})
				})

			},
			goNav(url) {
				uni.redirectTo({
					url
				})
			},
			updateImage() {
				uni.chooseImage({
					count: 1,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						this.$common.uploads(tempFilePaths[0], {
							type: 1
						}).then(imgUrl => {
							this.form.avatar = imgUrl
						}).catch(err => {
							console.log('err:', err);
						})
					}
				});
			},
			setSexcurrent(index) {
				this.form.sex = index
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 32rpx 56rpx;

		.sex {
			display: flex;
			text-align: center;
			line-height: 90rpx;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 313rpx;
				height: 90rpx;
				background: #1D1C21;
				border-radius: 14rpx;
			}

			.right {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 313rpx;
				height: 90rpx;
				background: #1D1C21;
				// background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 14rpx;
			}
		}

		.intro {
			padding: 32rpx 24rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 41rpx;
		}

		.inputBg {
			color: rgba(255, 255, 255, 0.52);
			background: #201F1F;
			height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
		}

		.inputTextareaBg {
			color: #fff;
			background: #201F1F;
			height: 332rpx;
			border-radius: 16rpx;
			padding: 32rpx 24rpx;
		}

		.ava {
			position: relative;
		}

		.avatar {
			width: 284rpx;
			height: 284rpx;
			line-height: 284rpx;
			background: #1D1C27;
			font-size: 28rpx;
			border-radius: 50%;
			border: 1px solid rgba(255, 255, 255, 0.08);
			text-align: center;
			margin: auto;
			margin-top: 56rpx;
		}

		.avatarImg {
			width: 284rpx;
			height: 284rpx;
			margin: auto;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			margin-top: 56rpx;
		}

		.camera {
			width: 86rpx;
			height: 86rpx;
			position: absolute;
			bottom: 0;
			right: 27%;
		}

		.content {
			margin-top: 32rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 41rpx;
		}

		.title {
			font-size: 36rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
</style>