<template>
	<view class="appPage">
		<u-navbar :customBack="goBack" back-text="" title="发布" :background="{backgroundColor: '#191C26'}"
			:border-bottom="false" height="60" title-color="#fff" back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">可见范围</view>
				</view>
			</view>
			<view class="navbar-right" @click="submit" slot="right">
				完成
			</view>
		</u-navbar>
		<u-radio-group v-model="radio" @change="checkboxGroupChange">
			<view class="item_group" style="justify-content: space-between;">
				<view class="t_display">
					<u-radio name="1" shape="circle" active-color=""></u-radio>
					<view class="info">
						<view class="name">
							所有人可见
						</view>
					</view>
				</view>
			</view>
			<view class="item_group" style="justify-content: space-between;">
				<view class="t_display">
					<u-radio name="2" shape="circle" active-color=""></u-radio>
					<view class="info">
						<view class="name">
							好友可见
						</view>
					</view>
				</view>
			</view>

			<!-- <view class="item_group" style="justify-content: space-between;"> 
			<view class="t_display">
				<u-radio shape="circle" name="3" style="margin-right: 30rpx;"></u-radio>
				<u-collapse arrow-color="#fff" :head-style="{color:'#fff'}" hover-class="hoverClass"
					style="width: 600rpx;">
					<u-collapse-item ref="collapseView" title="指定人可见">
						<view class="item">
							<view class="titleItem" @click="choose(3)">
								选择成员
							</view>
							<view class="note">
								点击前往选择成员
							</view>
							<scroll-view :scroll-y="true" style="max-height: 266rpx;width: 500rpx;">
								<view class="gridBox">
									<image class="img80 ava" mode="aspectFill" v-for="(img,ids) in userList" :key="ids"
										:src="img.avatar">
									</image>
								</view>
							</scroll-view>
						</view>
					</u-collapse-item>
				</u-collapse>
			</view>
	</view>
			<view class="item_group" style="justify-content: space-between;">
				<view class="t_display">
					<u-radio shape="circle" name="4" style="margin-right: 30rpx;"></u-radio>
					<u-collapse arrow-color="#fff" :head-style="{color:'#fff'}" hover-class="hoverClass"
						style="width: 600rpx;">
						<u-collapse-item ref="collapseView2" title="指定人不可见">
							<view class="item">
								<view class="titleItem" @click="choose(4)">
									选择成员
								</view>
								<view class="note">
									点击前往选择成员
								</view>
								<scroll-view :scroll-y="true" style="max-height: 266rpx;width: 500rpx;">
									<view class="gridBox">
										<image class="img80 ava" mode="aspectFill" v-for="(img,ids) in userList2"
										:key="ids" 	:src="img.avatar">
										</image>
									</view>
								</scroll-view>
							</view>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>-->
		</u-radio-group>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				flag: [false, false, false, false],
				hei: 0,
				statusBarHeight: 0,
				current: 0,
				radio: "",
				userList: [],
				userList2: [],
				itemList: [{
						head: "指定人可见",
						body: "",
						name: "3",
						open: true,
					},
					{
						head: "指定人不可见",
						body: "",
						name: "4",
						open: true,
					}
				],
			}
		},

		onLoad() {
			this.getData()
		},
		onShow() {
			const that = this
			uni.$on('infoArr', (data) => {
				this.$nextTick(() => {
					setTimeout(() => {
						if (parseInt(that.radio) == 3) {
							that.userList = data
						} else if (parseInt(that.radio) == 4) {
							that.userList2 = data
						}
						that.$refs.collapseView.init()
						that.$refs.collapseView2.init()
					}, 500)
				})
			})
		},

		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			choose(idx) {
				let uids = {
					'3': this.userList.map(item => item.uid),
					'4': this.userList2.map(item => item.uid)
				} [idx].join(',')
				this.goNav('/pages/checkMember/checkMember?mode=3&uids=' + uids)
			},
			submit() {
				if (!!!this.radio) return this.toast('请选择可见范围')
				let uids = []
				if (parseInt(this.radio) === 3) {
					uids = this.userList.map(item => {
						return item.uid
					})
				} else if (parseInt(this.radio) === 4) {
					uids = this.userList2.map(item => {
						return item.uid
					})
				}
				if (uids.length == 0 && ![1, 2].includes(parseInt(this.radio))) return this.toast('请选择成员')
				this.$http.post('/location/visible', {
					"role": parseInt(this.radio),
					uids
				}).then(res => {
					this.radio = res.message.role
					this.userList = res.message.user_list
					uni.navigateBack()
				})
			},
			getData() {
				this.$http.get('/location/visible/get').then(res => {
					this.radio = res.message.role.role
					if (this.radio === 3) {
						this.userList = res.message.user_list
					} else if (this.radio === 4) {
						this.userList2 = res.message.user_list
					}

					this.$nextTick(() => {
						this.$refs.collapseView.init()
						this.$refs.collapseView2.init()
					})
				})
			},
			// 选中任一checkbox时，由checkbox-group触发
			checkboxGroupChange(e) {
				this.flag = [false, false, false, false]
				if (e.length > 0) {
					const ids = e[e.length - 1]
					this.flag[ids] = true
				}
			},
			goBack() {
				uni.navigateBack()
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-collapse-item_content {
		height: auto;
	}

	/deep/ .u-collapse-head {
		padding: 0;
	}

	.navbar-right {
		font-size: 28rpx;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		color: #000000;
		width: 112rpx;
		height: 64rpx;
		margin-right: 34rpx;
		line-height: 64rpx;
		background: linear-gradient(145deg, #49C8F0 0%, #C095F7 100%);
		border-radius: 22rpx;
		text-align: center;
	}

	.appPage {
		padding: 0 30rpx;

		.gridBox {
			display: flex;
			flex-wrap: wrap;

			.ava {
				border-radius: 50%;
				margin: 12rpx;
			}
		}

		.item_group {
			margin-top: 56rpx;
			display: flex;
			align-items: center;

			.declined {
				width: 126rpx;
				height: 56rpx;
				border-radius: 16rpx;
				opacity: 1;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #767676;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #767676;
			}

			.yes {
				width: 126rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #FFFFFF;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				margin-left: 16rpx;
			}

			.no {
				font-size: 22rpx;
				font-weight: 400;
				text-align: center;
				width: 126rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid transparent;
				border-radius: 16rpx;
				background-clip: padding-box, border-box;
				background-origin: padding-box, border-box;
				background-image: linear-gradient(to right, #333, #333), linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
			}

			.outer_circle {
				position: relative;
				margin: 50px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: #ffffff;
			}

			.inner_circle {
				background-image: linear-gradient(to bottom, rgb(123, 93, 255) 0%,
						rgb(56, 225, 255) 100%);
				content: '';
				position: absolute;
				top: -20px;
				bottom: -20px;
				right: -20px;
				left: -20px;
				z-index: -1;
				border-radius: inherit;
			}

			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
				}

				.name {
					font-size: 26rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #FFFFFF;
				}
			}


			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.item {
			margin-top: 32rpx;

			.titleItem {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #49C8F0;
			}

			.note {
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #A2A2A4;
			}
		}

		.title {
			font-size: 36rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}

		.txt {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			margin-left: 28rpx;
		}

		.search {
			margin-left: 24rpx;
			font-size: 26rpx;
			font-family: Source Han Sans-Medium, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
		}

		.Cinpout {
			flex: 1;
			height: 80rpx;
			background: #272727;
			border-radius: 32rpx;
			padding: 0 52rpx;
		}
	}
</style>
