<template>
	<view class="">
		<!-- 主渲染内容 -->
		<view id="render-dom" class="render-content">
			<view class="activity-card" :style="{ backgroundImage: `url(${cardBgImage})` }">
				<view class="card-content">
					<view class="card-title">
						{{ cardData.title }}
					</view>
					<view class="card-info">
						<view class="info-item">
							<text style="font-weight: bold;">活动ID: {{ cardData.cardId }}</text>
							<text class="member-count">已有<span style="margin: 0 8rpx;">
									{{ cardData.memberCount }}</span> 人加入</text>
						</view>
						<view class="info-form">
							<text>Form</text>
							<view class="form-line"></view>
						</view>
					</view>
					<view class="card-footer">
						<view class="logo-container">
							<view class="logo-image">
								<image :src="logo" alt="" />
							</view>
							<view class="logo-text">
								<text class="logo-title">LightingBall</text>
								<text class="logo-subtitle">扫一扫，发现更多精彩活动</text>
							</view>
						</view>
						<view class="qrcode-image">
							<image class="qrImg" :src="cardData.qrCodeImage" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 隐藏的canvas元素 -->
		<canvas canvas-id="shareCanvas" class="hidden-canvas"></canvas>
	</view>
</template>

<script>
	import {
		base64ToPath
	} from "@/uni_modules/sp-html2canvas-render/utils/index.js";
	import bgImages from "./enum.js";

	export default {
		name: 'ActivityCard',
		props: {
			cardData: {
				type: Object,
				default: () => ({
					cardId: '',
					title: '',
					memberCount: '0',
					qrCodeImage: ''
				})
			},
		},
		created() {
			const bgIndex = Math.floor(Math.random() * 3) + 1;
			this.cardBgImage = bgImages[`cardBgImage${bgIndex}`]

			// 确保二维码图片已加载
			if (this.cardData.qrCodeImage) {
				console.log('二维码图片路径:', this.cardData.qrCodeImage);
				// 预加载二维码图片
				uni.getImageInfo({
					src: this.cardData.qrCodeImage,
					success: (res) => {
						console.log('二维码图片加载成功:', res.path);
						this.qrCodeImagePath = res.path;
					},
					fail: (err) => {
						console.error('二维码图片加载失败:', err);
					}
				});
			}
		},
		data() {
			return {
				cardBgImage: "",
				logo: bgImages.logo,
				bgImageIndex: "",
				canvasWidth: 600,
				canvasHeight: 900,
				qrCodeImagePath: '' // 存储二维码图片路径
			}
		},
		methods: {
			cusRenderDom() {
				uni.showLoading({
					title: '生成图片中...'
				});

				// 获取元素信息
				const query = uni.createSelectorQuery().in(this);
				query.select('#render-dom').boundingClientRect(data => {
					if (!data) {
						uni.hideLoading();
						console.error('无法获取渲染节点信息');
						return;
					}

					// 设置canvas大小
					this.canvasWidth = data.width;
					this.canvasHeight = data.height;

					// 开始绘制
					this.drawShareImage();
				}).exec();
			},

			// 绘制分享图片
			drawShareImage() {
				const ctx = uni.createCanvasContext('shareCanvas', this);

				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 设置白色背景
				ctx.setFillStyle('#FFFFFF');
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制背景图
				if (this.cardBgImage) {

					// 直接绘制背景图
					ctx.drawImage(this.cardBgImage, 0, 0, this.canvasWidth, this.canvasHeight);

					// 继续绘制其他内容
					this.drawCardContent(ctx);
				} else {
					console.error('背景图路径为空');
					this.drawCardContent(ctx);
				}
			},

			// 绘制卡片内容
			drawCardContent(ctx) {
				const leftMargin = 20; // 统一左边距为20
				const rightMargin = 20; // 统一右边距为20
				// 绘制标题
				ctx.setFillStyle('#000000');
				ctx.setTextAlign('center');
				ctx.setFontSize(40); // 对应80rpx
				ctx.font = "bold 40px 'TitleFont'"; // 使用自定义字体

				// 实现标题自动换行
				const title = this.cardData.title || '活动标题';
				const maxWidth = this.canvasWidth - leftMargin * 2; // 左右各留20的边距
				const lineHeight = 50; // 行高

				// 分割文本为多行
				const lines = this.wrapText(ctx, title, maxWidth);

				// 计算垂直居中的起始Y坐标
				const totalTextHeight = lines.length * lineHeight;
				let startY = 120; // 起始Y坐标

				// 绘制每一行文本
				lines.forEach((line, index) => {
					ctx.fillText(line, this.canvasWidth / 2, startY + index * lineHeight);
				});

				// 重置字体为正常样式
				ctx.font = 'normal 11px sans-serif';

				// 绘制活动ID和成员数量
				const infoY = this.canvasHeight - 140;

				ctx.setTextAlign('left');
				ctx.setFillStyle('#000000');
				ctx.setFontSize(11); // 对应22rpx
				ctx.fillText(`活动ID: ${this.cardData.cardId || '123456'}`, leftMargin, infoY);

				// 绘制成员数量
				ctx.setTextAlign('right');
				ctx.setFillStyle('#00A080');
				ctx.setFontSize(11); // 对应22rpx

				// 计算文本宽度以便更好地排列
				const prefix = '已有';
				const suffix = '人加入';
				const number = this.cardData.memberCount || '0';

				// 计算右侧文本的位置
				ctx.setFontSize(11);
				const suffixWidth = ctx.measureText(suffix).width;
				ctx.setFontSize(16);
				const numberWidth = ctx.measureText(number).width;
				ctx.setFontSize(11);
				const prefixWidth = ctx.measureText(prefix).width;

				// 从右向左计算位置
				const rightEdge = this.canvasWidth - rightMargin;

				// 绘制"已有"
				ctx.setFontSize(12); // 对应22rpx
				ctx.fillText(prefix, rightEdge - suffixWidth - numberWidth - prefixWidth + 10, infoY);

				// 绘制高亮数字
				ctx.setFontSize(16); // 对应32rpx
				ctx.font = "16px 'NumFont'"; // 使用数字专用字体
				ctx.fillText(number, rightEdge - suffixWidth - numberWidth + 5 + ((number.toString().length - 1) * 10),
					infoY);

				// 重置字体
				ctx.font = 'normal 11px sans-serif';

				// 绘制后续文字
				ctx.setFontSize(11); // 对应22rpx
				ctx.fillText(suffix, rightEdge - suffixWidth + 35, infoY);

				// 绘制Form文字和分隔线
				const formY = infoY + 30;
				ctx.setTextAlign('left');
				ctx.setFillStyle('#666666');
				ctx.setFontSize(14); // 对应28rpx
				ctx.fillText('Form', leftMargin, formY);

				// 绘制分隔线
				ctx.beginPath();
				ctx.setLineDash([5, 5]);
				ctx.moveTo(leftMargin + 50, formY - 5);
				ctx.lineTo(this.canvasWidth - rightMargin, formY - 5);
				ctx.setStrokeStyle('#CCCCCC');
				ctx.stroke();

				// 绘制底部区域
				const footerY = this.canvasHeight - 80;

				// 绘制Logo和二维码
				if (this.logo) {
					// 使用统一的左右边距
					const logoSize = 40;
					const qrSize = 60;

					// 绘制Logo
					ctx.drawImage(this.logo, leftMargin, footerY + 15, logoSize, logoSize);

					// 绘制Logo文字
					ctx.setTextAlign('left');
					ctx.setFillStyle('#000000');
					ctx.setFontSize(16); // 对应32rpx
					ctx.fillText('LightingBall', leftMargin + logoSize + 10, footerY + 30);
					ctx.setFontSize(11); // 对应22rpx
					ctx.setFillStyle('#666666');
					ctx.fillText('打一打，发现更多精彩活动', leftMargin + logoSize + 10, footerY + 50);

					// 绘制二维码
					if (this.cardData.qrCodeImage) {
						// 使用预加载的二维码图片路径或原始路径
						const qrImagePath = this.qrCodeImagePath || this.cardData.qrCodeImage;
						console.log('绘制二维码，使用路径:', qrImagePath);

						// 调整二维码位置
						const qrX = this.canvasWidth - qrSize - rightMargin;
						const qrY = footerY;

						// 绘制二维码
						try {
							ctx.drawImage(qrImagePath, qrX, qrY, qrSize, qrSize);
							console.log('二维码绘制成功');
						} catch (error) {
							console.error('二维码绘制失败:', error);
						}
					}
				}

				// 延迟完成绘制，确保所有内容绘制完成
				setTimeout(() => {
					this.finishDrawing(ctx);
				}, 300);
			},

			// 自动换行函数
			wrapText(ctx, text, maxWidth) {
				// 中文文本处理
				const chars = text.split('');
				const lines = [];
				let currentLine = '';

				for (let i = 0; i < chars.length; i++) {
					const char = chars[i];
					const testLine = currentLine + char;
					const testWidth = ctx.measureText(testLine).width;

					if (testWidth > maxWidth && currentLine !== '') {
						lines.push(currentLine);
						currentLine = char;
					} else {
						currentLine = testLine;
					}
				}

				if (currentLine) {
					lines.push(currentLine);
				}

				return lines;
			},

			// 完成绘制并导出图片
			finishDrawing(ctx) {
				ctx.draw(true, () => {
					// 延迟一下确保绘制完成
					setTimeout(() => {
						// 将Canvas导出为图片
						uni.canvasToTempFilePath({
							canvasId: 'shareCanvas',
							success: (res) => {
								uni.hideLoading();
								// 发送生成的图片路径
								this.$emit("renderOver", res.tempFilePath);
							},
							fail: (err) => {
								uni.hideLoading();
								console.error('导出图片失败', err);
								// 通知失败
								this.$emit("renderOver", "");
							}
						}, this);
					}, 300);
				});
			},

			renderOver(e) {
				// e为导出截图的base64格式字符串
				this.shareHB = e
				base64ToPath(e).then((res) => {
					// console.log("==== path :", res);
					this.$emit("renderOver", res)
					// uni.saveImageToPhotosAlbum({
					// 	filePath: res,
					// 	success: function() {
					// 		console.log('save success');
					// 	}
					// });
				});
			},
		}
	}
</script>

<style lang="scss">
	@font-face {
		font-family: 'TitleFont';
		src: url('/static/font/title.ttf');
		font-weight: normal;
		font-style: normal;
	}

	@font-face {
		font-family: 'NumFont';
		src: url('/static/font/num.otf');
		font-weight: normal;
		font-style: normal;
	}

	.render-content {
		width: 600rpx;
		height: 900rpx;
		margin: 30rpx auto;
	}

	.hidden-canvas {
		position: fixed;
		left: -9999rpx;
		width: 600rpx;
		height: 900rpx;
	}

	.activity-card {
		width: 600rpx;
		height: 900rpx;
		margin: 30rpx auto;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		border-radius: 30rpx;
		padding: 50rpx;
		position: relative;
		overflow: hidden;

		.card-content {
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		.card-title {
			font-family: 'TitleFont', sans-serif;
			font-size: 80rpx;
			font-weight: bold;
			color: #000;
			line-height: 1.3;
			margin-bottom: 20rpx;
			display: flex;
			flex: 1;
			justify-content: center;
			align-items: center;
			text-align: center;
		}

		.card-info {
			margin-top: auto;

			.info-item {
				display: flex;
				justify-content: space-between;
				margin-bottom: 20rpx;

				text {
					font-size: 22rpx;
					color: #000;
				}

				.member-count {
					color: #00A080;
					font-weight: bold;

					span {
						font-family: 'NumFont', sans-serif;
						font-size: 32rpx;
						color: #00A080;
					}
				}
			}

			.info-form {
				margin-top: 10rpx;
				display: flex;

				text {
					font-size: 28rpx;
					color: #666;
					margin-right: 20rpx;
				}

				.form-line {
					margin-top: 20rpx;
					width: 100%;
					border-top: 1px dashed #CCCCCC;
				}
			}
		}

		.card-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 40rpx;

			.logo-container {
				display: flex;
				align-items: center;

				.logo-image {
					width: 80rpx;
					height: 80rpx;
					border-radius: 26rpx;
					background-color: #000;
					margin-right: 20rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 100%;
						height: 100%;
					}

					text {
						color: #fff;
						font-size: 24rpx;
					}
				}

				.logo-text {
					display: flex;
					flex-direction: column;

					.logo-title {
						font-size: 32rpx;
						font-weight: bold;
						color: #000;
					}

					.logo-subtitle {
						font-size: 22rpx;
						color: #666;
					}
				}
			}

			.qrcode-image {
				width: 120rpx;
				height: 120rpx;

				.qrImg {
					width: 116rpx;
					height: 116rpx;
					box-sizing: border-box;
				}
			}
		}
	}
</style>