<!--
 截屏
 @Author: mosowe
 @Date:2024-02-26 14:32:15
-->

<template>
  <view class="mosowe-clip-screen">
    <view @click="start(selector)">
      <slot name="default"></slot>
    </view>
    <view class="canvas-wrap">
      <canvas
        v-if="showCanvas"
        :style="{
          width: canvasWidth * 2 + 'px',
          height: canvasHeight * 2 + 'px'
        }"
        :width="canvasWidth + 'px'"
        :height="canvasHeight + 'px'"
        canvas-id="canvasMain"
        id="canvasMain"
        class="canvas-main"></canvas>
    </view>
  </view>
</template>

<script>
export default {
  name: 'mosowe-clip-screen',
  props: {
    selector: {
      type: String,
      default: ''
    },
    base64: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: '生成图片中...'
    }
  },
  data() {
    return {
      // 获取系统信息
      systemInfo: uni.getSystemInfoSync(),
      // 判断是否为iOS系统
      isIOS: uni.getSystemInfoSync().platform === 'ios',
      canvas: null,
      
      clipTop: '0px', // 截屏开始顶部位置
      clipLeft: '0px', // 截屏开始左侧位置，根据selector计算
      clipWidth: '0px', // 截屏宽度，不能大于屏幕宽度，根据selector计算
      clipHeight: '0px', // 截屏高度，等于安全高度的80%
      
      safeHeight: 0, // 页面安全区域高度
      scrollLen: 0, // 每次滚动安全高度
      statusBarHeight: 0, // 状态栏高度
      screenTop: 0, // 系统自带顶部栏高度，若为0，表示该页面"navigationStyle": "custom"了
      
      elHeight: 0, // 页面高度
      selectorOffsetTop: 0, // 元素距离顶部距离
      
      canvasHeight: 300,
      canvasWidth: 300,
      
      times: 0, // 滚动次数
      lastHeight: '0px', // 最后一屏有效高度
      images: [], // 截屏图片数组
      
      currentWebview: null,
      bitmap: null,
      
      drawHeight: 0,
      showCanvas: true,
      pageScrollLen: 0
    }
  },
  onShow() {
    // 获取系统信息
    this.statusBarHeight = this.systemInfo.statusBarHeight;
  },
  onReady() {
    this.canvas = uni.createCanvasContext('canvasMain', this);
  },
  onPageScroll(e) {
    this.pageScrollLen = e.scrollTop;
  },
  methods: {
    // 获取需要滚动次数，向上取整
    scrollTimes(scrollLen, elHeight) {
      return Math.ceil(elHeight / scrollLen);
    },
    
    // 最后一次滚动距离，向上取整
    lastScrollLen(scrollLen, elHeight) {
      return this.scrollTimes(scrollLen, elHeight) > 0 ? Math.ceil(elHeight % scrollLen) : 0;
    },
    
    // 截屏，核心代码
    clipScreen(top, left, width, height) {
      const that = this;
      return new Promise((resolve, reject) => {
        // 将webview内容绘制到Bitmap对象中
        that.currentWebview.draw(
          that.bitmap,
          function () {
            that.bitmap.save(
              '_doc/' + that.generateRandomLetterNumber(10) + '.jpg',
              {},
              function (i) {
                resolve(i.target);
              },
              function (e) {
                console.log('截屏绘制图片失败：' + JSON.stringify(e));
              }
            );
          },
          function () {
            resolve('');
          },
          {
            clip: { top, left, height, width } // 设置截屏区域
          }
        );
      });
    },
    
    // 准备裁剪，递归
    deepReadyClip(i) {
      const that = this;
      if (i < that.times - 1) {
        that.clipScreen(that.clipTop, that.clipLeft, that.clipWidth, parseInt(that.clipHeight) - (that.isIOS ? parseInt(that.clipTop) : 0) + 'px').then(
          (res) => {
            that.images.push(res);
            const index = i + 1;
            uni.pageScrollTo({
              scrollTop: index * that.scrollLen + that.selectorOffsetTop,
              duration: 0,
              success: async () => {
                await that.timeSleep(1000);
                that.deepReadyClip(index);
              }
            });
          }
        );
      } else {
        that.clipScreen(that.clipTop, that.clipLeft, that.clipWidth, that.lastHeight).then(async (res) => {
          that.images.push(res);
          if (that.times > 1) {
            // 需要拼合的
            await that.timeSleep(1000);
            that.canvasDrawImages(0);
          } else {
            // 不需要拼合
            that.success(res);
          }
        });
      }
    },
    
    // 合并图片
    canvasDrawImages(i) {
      const that = this;
      plus.io.getImageInfo({
        src: that.images[i],
        success: async (res) => {
          that.canvas.drawImage(that.images[i], 0, that.drawHeight, parseInt(res.width), parseInt(res.height));
          that.drawHeight += res.height;
          await that.timeSleep(1000);
          if (i < that.images.length - 1) {
            that.canvasDrawImages(++i);
          } else {
            that.canvasImage();
          }
        }
      });
    },
    
    // 延时
    timeSleep(time = 100) {
      return new Promise((resolve) => {
        let t = setTimeout(() => {
          clearTimeout(t);
          t = null;
          resolve(true);
        }, time);
      });
    },
    
    // 绘制总图片
    canvasImage() {
      const that = this;
      that.canvas.draw(true, () => {
        uni.canvasToTempFilePath(
          {
            x: 0,
            y: 0,
            width: that.canvasWidth * 2,
            height: that.canvasHeight * 2,
            fileType: 'png',
            canvasId: 'canvasMain',
            success: (res) => {
              that.success(res.tempFilePath);
            },
            fail: (res) => {
              console.log(res);
            }
          },
          that
        );
      });
    },
    
    // 开始
    start(selector) {
      const that = this;
      if (!selector) {
        selector = that.selector;
      }
      if (!selector) {
        uni.showToast({
          title: '请设置selector',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: that.loadingText
      });
      
      const windowInfo = uni.getWindowInfo();
      that.safeHeight = windowInfo.screenHeight; // 页面安全区域高度
      that.scrollLen = that.safeHeight * 0.8; // 每次滚动安全高度的80%
      that.statusBarHeight = windowInfo.statusBarHeight; // 状态栏高度
      that.screenTop = windowInfo.screenTop; // 系统自带顶部栏高度，若为0，表示该页面"navigationStyle": "custom"了
      
      that.clipTop = (that.screenTop ? that.screenTop : that.statusBarHeight) + 'px'; // 截屏开始顶部位置
      that.clipHeight = that.safeHeight * 0.8 + 'px'; // 截屏高度
      
      const query = uni.createSelectorQuery().in(that);
      query.select(selector).boundingClientRect((data) => {
        if (!data) {
          uni.hideLoading();
          uni.showToast({
            title: 'selector不存在',
            icon: 'none'
          });
          return;
        }
        
        that.elHeight = Math.floor(data.height);
        that.selectorOffsetTop = Math.floor(data.top);
        
        that.canvasWidth = Math.floor(data.width);
        that.canvasHeight = that.elHeight;
        
        if (that.elHeight < that.safeHeight * 0.8) {
          that.clipHeight = that.elHeight + 'px';
        }
        
        that.clipLeft = Math.floor(data.left) + 'px';
        that.clipWidth = Math.floor(data.width) + 'px';
        
        that.times = that.scrollTimes(that.scrollLen, that.elHeight);
        that.lastHeight = that.lastScrollLen(that.scrollLen, that.elHeight) + 'px';
        
        // 初始化参数
        that.images = [];
        that.drawHeight = 0;
        
        let pages = getCurrentPages();
        let page = pages[pages.length - 1];
        that.currentWebview = page.$getAppWebview();
        
        // #ifdef APP-PLUS
        that.bitmap = new plus.nativeObj.Bitmap('screenPage');
        // #endif
        
        uni.pageScrollTo({
          scrollTop: that.selectorOffsetTop,
          duration: 0,
          success: async () => {
            await that.timeSleep(1000);
            that.deepReadyClip(0);
          }
        });
      }).exec();
    },
    
    // 生成随机字符串
    generateRandomLetterNumber(length) {
      let result = '';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const charactersLength = characters.length;
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return result;
    },
    
    // 操作成功
    success(res) {
      const that = this;
      uni.hideLoading();
      
      if (that.base64) {
        // 转换为base64
        plus.io.getImageInfo({
          src: res,
          success: (imageInfo) => {
            const base64 = imageInfo.base64Data;
            that.$emit('success', base64);
          },
          fail: (err) => {
            console.log('获取图片信息失败', err);
            that.$emit('success', res);
          }
        });
      } else {
        that.$emit('success', res);
      }
    }
  }
}
</script>

<style>
.mosowe-clip-screen {
  width: 100%;
}
.canvas-wrap {
  position: fixed;
  left: -2000px;
  top: 0;
  z-index: -1;
}
.canvas-main {
  transform: scale(0.5);
  transform-origin: 0 0;
}
</style>
