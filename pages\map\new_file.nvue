<template>
    <view class="page-main" :style="{height:pageHeight + 'px'}">
		<view class="page-header-container" :style="{width: '375px',height:headerHeight + 'px'}">
			<image class="page-header-bg" :style="{width: '375px',height:headerHeight + 'px'}" src="../../static/images/vip/newBackground.png" mode="widthFix"></image>
			<view class="page-header" :style="{paddingTop:statusBarHeight+'px'}">
				<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode="widthFix"></image>
				<view class="nav-title">模型展示地点</view>
				<view class="page-header-right"></view>
			</view>
		</view>
        <ModelMap style="width: 375px;" :style="{height:mapHeight+'px'}" ref="liuEasyMap" :centerLat="'36.05709'" :centerLng="'103.82538'" :scale="14" :businessId="business_id"
            :markerData="markerData" :polygons="polygons" @clickMarker="markerClick"></ModelMap>
    </view>
</template>

<script>
	import ModelMap from './components/model-map.vue';
    export default {
		components: {
			ModelMap
		},
        data() {
            return {
				pageHeight:uni.getSystemInfoSync().screenHeight,
				headerHeight: uni.getSystemInfoSync().statusBarHeight + 44,
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight,
				mapHeight: uni.getSystemInfoSync().screenHeight - uni.getSystemInfoSync().statusBarHeight - 44,
				markerData:[],
                // markerData: [{
                //     id: 1,
                //     name: '兰州市政府', //标记点展示名字
                //     address: '甘肃省兰州市城关区人民政府',
                //     latitude: '36.05989', //标记点纬度
                //     longitude: '103.83502', //标记点经度
                //     markerUrl: 'https://img0.baidu.com/it/u=550544800,2229099292&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', //标记点图标地址
                //     iconWidth: 32, //标记点图标宽度
                //     iconHeight: 32, //标记点图标高度
                //     calloutColor: '#ffffff', //气泡窗口 文本颜色
                //     calloutFontSize: 14, //气泡窗口 文本大小
                //     calloutBorderRadius: 6, //气泡窗口 边框圆角
                //     calloutPadding: 8, //气泡窗口 文本边缘留白
                //     calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
                //     calloutDisplay: 'ALWAYS', //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
                // }, {
                //     id: 2,
                //     name: '测试地址', //标记点展示名字
                //     address: '测试地址详细地址测试地址详细地址测试地址详细地址测试地址详细地址',
                //     latitude: "36.05064",
                //     longitude: "103.82538"
                // }],
                //展示区域点位信息
				polygons:[],
                // polygons: [{
                //     points: [{
                //         latitude: "36.06637",
                //         longitude: "103.82471"
                //     }, {
                //         latitude: "36.06255",
                //         longitude: "103.82321"
                //     }, {
                //         latitude: "36.06234",
                //         longitude: "103.81928"
                //     }, {
                //         latitude: "36.06036",
                //         longitude: "103.82175"
                //     }, {
                //         latitude: "36.05653",
                //         longitude: "103.82152"
                //     }, {
                //         latitude: "36.05953",
                //         longitude: "103.82476"
                //     }, {
                //         latitude: "36.05690",
                //         longitude: "103.82785"
                //     }, {
                //         latitude: "36.06023",
                //         longitude: "103.82747"
                //     }, {
                //         latitude: "36.06243",
                //         longitude: "103.83014"
                //     }, {
                //         latitude: "36.06245",
                //         longitude: "103.82616"
                //     }], //经纬度数组
                //     strokeWidth: 2, //描边的宽度
                //     strokeColor: "#FF000060", //描边的颜色
                //     fillColor: "#FF000090" //填充颜色
                // }]
				business_id:""
            }
        },
		onLoad(options) {
			console.log("地图高度",this.pageHeight)
			console.log(this.mapHeight);
			console.log(options.shopId)
			if (options.shopId) {
				this.business_id = options.shopId;
			}
		},
        methods: {
            //点击标记点
            markerClick(e) {
                console.log('点击标记点信息：', e)
            },
			goBack() {
				uni.navigateBack()
				// uni.navigateTo({
				// 	url: '/pages/shop/shop'
				// });
			},
        }
    }
</script>

<style lang="scss" scoped>
    .page-main {
        width: 375px;
		// height:800px !important;
		.page-header-container{
			position: relative;
			.page-header-bg{
				position: absolute;
				left:0;
				right:0;
				top:0;
			}
			.page-header{
				position: absolute;
				left:0;
				right:0;
				top:0;
				display:flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: row;
				padding:0 15px;
				box-sizing: border-box;
				.back {
					width: 30px;
					height: 30px;
				}
				.page-header-right{
					width: 30px;
					height: 30px;
				}
			}
			
		}
		
		
    }
</style>
<style>
	/* #easy-map .amap-container /deep/.amap-logo{
		display: none !important;
		z-index: -100 !important;
	} */
</style>