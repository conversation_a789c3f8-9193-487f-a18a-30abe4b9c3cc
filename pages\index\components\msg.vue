<template>
	<uni-popup ref="popup" type="bottom" @maskClick="close" :safe-area="false">
		<view class="hb">
			<view class="title">位置留言</view>
			<view class="anonymous t_betweent">
				<view class="border" @click="formData.anonymous = 1">
					<view class="anonymousBtn" :style="[{'background':anonymousBtn}]">
						<image class="img42" style="margin-right: 18rpx;"
							src="../../../static/images/message/nimingliuyan.png" mode=""></image>
						<view class="anonymousBtnText">匿名留言</view>
					</view>
				</view>
				<view class="border" @click="formData.anonymous = 2">
					<view class="anonymousBtn" :style="[{'background':realNameBtn}]">
						<image class="img42" style="margin-right: 18rpx;"
							src="../../../static/images/message/shimingliuyan.png" mode=""></image>
						<view class="anonymousBtnText">实名留言</view>
					</view>
				</view>
			</view>
			<view class="addFirend">
				<view class="">选择好友</view>
				<view class="avatarArr">
					<image class="img88 t_radius" style="margin: 24rpx 34rpx  0 0;" :src="item.avatar" mode=""
						v-for="(item,index) in formData.msgFirendArr" :key="index" />
					<image class="img88" style="margin:24rpx 34rpx 0 0;" src="/static/images/message/add.png" mode=""
						@click="goNav('/pages/checkMember/checkMember?mode=4')" />
				</view>
			</view>
			<view class="address t_betweent">
				<view style="font-weight: bold;">位置</view>
				<view class="t_display" @click="goNav('/pages/index/hbPosition')">
					<view class="addressInfo">{{formData.location}}</view>
					<view style="margin: 0 24rpx;">|</view>
					<image class="img24" src="../../../static/images/message/address.png" mode=""></image>
				</view>
			</view>
			<view class="address t_betweent">
				<view style="font-weight: bold;">位置标记</view>
				<u-switch v-model="formData.checked" inactive-color="#eee" size="45"
					@input="invisibleAccess"></u-switch>
			</view>
			<view class="address t_betweent" v-if="!formData.checked">
				<view style="font-weight: bold;">位置提示</view>
				<input v-model="formData.propmpt" type="text" style="width: 400rpx;" placeholder="默契大挑战，按提示让好友猜位置" />
			</view>
			<view class="btn" @click="submit">去写信</view>
		</view>
		<Postion ref="position" />
		<u-toast ref='notify' />
		<u-toast ref='notify' />
	</uni-popup>
</template>

<script>
	import Postion from './hbPosition.vue'
	import {
		apiWalletRedpacketGrant
	} from '@/api/common.js'
	export default {
		components: {
			Postion
		},
		props: {},
		data() {
			return {
				formData: {
					anonymous: 1,
					location: "",
					checked: false,
					propmpt: "",
					msgFirendArr: [],
				},
			}
		},
		watch: {
			'$store.state.hbLocation'(nVal) {
				this.formData.location = nVal ? nVal.locationName : ''
				this.formData.address = nVal ? nVal.address : ''
				this.formData.coordinate = nVal ? nVal.location.replace(/\s+/g, '') : ''
			}
		},
		computed: {
			anonymousBtn() {
				return this.formData.anonymous == 2 ? '#000' : 'linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)'
			},
			realNameBtn() {
				return this.formData.anonymous == 1 ? '#000' : 'linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)'
			},
		},
		mounted() {
			uni.$on('infoArr', (info) => {
				this.formData.msgFirendArr = info
			})
		},
		methods: {
			submit() {
				if (this.formData.msgFirendArr.length == 0) {
					return this.toast('请选择好友')
				} else if (!this.formData.location) {
					return this.toast('请选择位置')
				} else if (!this.formData.checked && this.formData.propmpt.length < 5) {
					return this.toast('位置提示最少五个字')
				}
				this.formData.friends = this.formData.msgFirendArr.map(item => {
					return item.uid
				})
				if (!this.formData.checked) {
					this.formData.propmpt = ""
				}

				delete this.formData.msgFirendArr
				this.$emit('msg', this.formData)
				this.close()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			invisibleAccess(flag) {
				this.formData.checked = flag
			},
			goNav(url) {
				this.$common.navigateTo({
					url
				})
			},
			open(isStore) {
				setTimeout(() => {
					this.$refs.popup.open('bottom')
				}, 500)
			},
			close() {
				this.formData = {
					anonymous: 1,
					location: "",
					checked: false,
					propmpt: "",
					msgFirendArr: [],
				}
				this.$refs.popup.close()
			},
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 750rpx;
		// height: 70vh;
		background: #191C26;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		opacity: 1;
		border-image: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
		padding: 63rpx 32rpx;
		position: relative;

		.address {
			width: 686rpx;
			height: 105rpx;
			background: #2F3341;
			border-radius: 16rpx;
			border: 1rpx solid #fff;
			margin-top: 32rpx;
			padding: 32rpx;

			.addressInfo {
				width: 400rpx;
				text-overflow: ellipsis;
				overflow: hidden;
				color: #999;
				white-space: nowrap;
				text-align: right;
			}
		}

		.avatarArr {
			display: flex;
			flex-wrap: wrap;
		}

		.addFirend {
			margin-top: 32rpx;
			width: 686rpx;
			height: 203rpx;
			background: #2F3341;
			border-radius: 16rpx;
			border: 1rpx solid #fff;
			padding: 24rpx 32rpx;
		}

		.border {
			width: 328rpx;
			height: 93rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 16rpx;
			padding: 2rpx;

			.anonymousBtn {
				width: 100%;
				height: 100%;

				border-radius: 16rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.anonymousBtnText {
					font-size: 28rpx;
				}
			}
		}

		.btn {
			width: 686rpx;
			height: 94rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			opacity: 1;
			margin: auto;
			text-align: center;
			line-height: 94rpx;
			margin-top: 32rpx;

		}

		.btn.disabled {
			background-color: #999;
			// color: #E93E3E;
			color: #fff;
		}

		.title {
			height: 75rpx;
			font-size: 52rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			color: #FFFFFF;
			line-height: 75rpx;
			margin-bottom: 32rpx;
		}
	}
</style>