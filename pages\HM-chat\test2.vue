<template>
	<view>lwy
		<button @click="sendText">sendText(to lwy)</button>

	</view>
</template>

<script>
	import _Yxim from '@/utils/Yxim.js'
	export default {
		data() {
			return {}
		},
		onLoad() {
			this.init()

		},
		methods: {
			init() {
				this.Yxim = _Yxim.Yxim_init({
					account: 'thl',
					token: 'admin123',
					onMsg: this.onMsg,
					onconnect: (res) => {
						console.log(123);
						this.getLocalMsgs()
					},
					onsessions: (sessions) => {
						console.log('收到会话列表', sessions);
						const new_Sessions =this.Yxim.mergeSessions([], sessions);
						console.log('====new_Sessions====',new_Sessions);
					}
				})
			},
			onMsg(msg) {
				console.log('收到消息', msg.scene, msg.type, msg);
				switch (msg.type) {
					case 'custom':
						/**
						 * 收到自定义消息，用户d根据消息内容处理
						 */
						break;
					case 'notification':
						/**
						 * 收到群通知消息，用户根据群通知的消息进行进一步处理
						 */
						break;
						// 其它case
					default:
						break;
				}
			},
			sendText() {
				this.Yxim.sendText({
					scene: 'p2p', //消息的场景  "p2p" | "team" | "superTeam"
					to: 'lwy', //接收人
					text: 'hello' + new Date().getDay(), //发送得文本消息
					done: (error, msg) => {

						console.log('发送' + msg.scene + ' ' + msg.type + '消息' + (!error ? '成功' : '失败') +
							', id=' + msg
							.idClient);
						// pushMsg(msg);
					}
				});

			},
			getLocalMsgs() {
				this.Yxim.getLocalMsgs({
					limit: 100,
					done: (error, obj) => {
						console.log('获取本地消息' + (!error ? '成功' : '失败'), error, obj)
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
</style>