<template>
	<view class="appPage">
		<u-toast ref='notify' />
		<view class="head">
			<view class="inputBg">
				<uni-icons type="search" color="#fff" style="margin-right: 8rpx;"></uni-icons>
				<u-input v-model="username" type="text" placeholder="请输入名称" :border="false" clearable />
			</view>
			<span class="search" @click="getData">
				搜索
			</span>
		</view>
		<view class="item" v-for="(item,index) in info" :key="index">
			<view class="t_display">
				<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
				<view class="name">{{item.nickname}}</view>
			</view>
			<image @click="showPopup(item)" style="width: 13rpx;height: 38rpx;" src="../../static/images/moreIcon.png"
				mode=""></image>
		</view>
		<uni-popup ref="popup" type="bottom" background-color="#fff" :safe-area="false">
			<view class="cPopup">
				<view class="t_display">
					<image class="avatar" :src="popupInfo.avatar" mode="aspectFill"></image>
					<view class="name">{{popupInfo.nickname}}</view>
				</view>
				<view class="item t-display" @click="cancelBlack(popupInfo.uid)">
					<image class="disable" src="../../static/images/disable.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">取消黑名单</view>
						<view style="font-size: 24rpx;">你们可以互发消息和申请加好友</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="cancel">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupInfo: "",
				username: "",
				info: []
			}
		},
		watch: {
			username: {
				handler(oldVal, newVal) {
					if (!this.username) this.getData()
				}
			}
		},
		onLoad() {
			this.getData()
		},
		methods: {
			cancelBlack(uid) {
				this.$http.post('/api/user/black-set', {
					uid,
					"opt": 2
				}).then(res => {
					this.getData()
					this.$refs.popup.close()
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			getData() {
				this.$http.get('/api/user/black-list', {
					keywords: this.username
				}).then(res => {
					this.info = res.message
				})
			},
			cancel() {
				this.$refs.popup.close()
			},
			showPopup(item) {
				this.popupInfo = item
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.head {
		display: flex;
		align-items: center;

		.search {
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			width: 600rpx;
			margin: 32rpx;
			color: #fff;
			background: #22252F;
			height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
		}
	}


	.item {
		padding: 24rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;



		.fous {
			width: 148rpx;
			height: 58rpx;
			text-align: center;
			line-height: 58rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
		}

		.avatar {
			width: 108rpx;
			height: 108rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 42rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>