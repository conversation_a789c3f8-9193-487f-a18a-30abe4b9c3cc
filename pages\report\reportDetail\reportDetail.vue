<template>
	<view class="report-detail-container">
		<scroll-view scroll-y class="scroll-content">
			<view class="content-wrapper">
				<view class="section-title">{{ reportType.text }}</view>
				<!-- 顶部提示信息 -->
				<view class="tip-box">
					<text class="tip-text">{{ reportType.tip }}</text>
				</view>

				<!-- 选择具体问题 -->
				<view class="section-title">请选择具体问题</view>

				<!-- 问题按钮列表 -->
				<view class="btn-list">
					<view v-for="(item, index) in reportType.btnArr" :key="index" class="problem-btn"
						:class="{ 'selected': selectedProblem === index }" @click="selectProblem(index)">
						{{ item }}
					</view>
				</view>

				<!-- 举报描述 -->
				<view class="section-title">举报描述 <text class="required">*</text></view>
				<view class="tip-box">
					<text class="tip-text">{{ reportType.desc }}</text>
				</view>
				<view class="description-box">
					<textarea class="description-input" v-model="description" placeholder="请详细填写，以提高举报成功率"
						maxlength="200" placeholder-style="color: #999; font-size: 26rpx;"></textarea>
					<view class="word-count">{{ description.length }}/200</view>
				</view>

				<!-- 图片材料提交 -->
				<view class="section-title">图片材料提交</view>
				<view class="upload-box">
					<view class="upload-item" @click="chooseImage" v-if="images.length < 4">
						<view class="upload-icon">
							<image src="../../../static/images/report/paizhao.png" mode=""
								style="width: 20px;height: 20px;">
							</image>
							<text class="iconfont icon-camera"></text>
						</view>
						<view class="upload-text">{{ images.length }}/4</view>
					</view>
					<view class="image-item" v-for="(item, index) in images" :key="index">
						<image :src="item" mode="aspectFill" @click="previewImage(index)"></image>
						<view class="delete-icon" @click="deleteImage(index)">×</view>
					</view>
				</view>

				<!-- 底部占位，确保内容不被底部按钮遮挡 -->
				<view class="bottom-placeholder"></view>
			</view>
		</scroll-view>

		<!-- 提交按钮 -->
		<view class="submit-btn" :class="{ 'btn-active': description.trim().length > 0 }" @click="submitReport">
			提交
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				reportType: null,
				selectedProblem: null,
				description: '',
				images: [],
				uuid: '',
				type: ''
			}
		},
		onLoad(option) {
			// 获取上一页传递的举报类型
			if (option.current) {
				try {
					// 尝试解析JSON字符串
					this.reportType = JSON.parse(decodeURIComponent(option.current));

				} catch (e) {
					console.error('解析举报类型失败', e);
					uni.showToast({
						title: '参数错误',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			}

			// 获取活动ID
			if (option.uuid) {
				this.uuid = option.uuid;
			}
			if (option.type) {
				this.type = option.type;
			}

			// 设置页面背景色和导航栏样式
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#ffffff'
			});

			// 设置页面背景色
			uni.setBackgroundColor({
				backgroundColor: '#ffffff'
			});
		},
		methods: {
			// 选择具体问题
			selectProblem(index) {
				this.selectedProblem = index;
			},

			// 选择图片
			chooseImage() {
				let count = 4 - this.images.length;
				uni.chooseImage({
					count,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let arr = [];
						uni.showLoading({
							title: '上传中...'
						});

						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								});
								arr.push(img);
							})
						);

						this.images.push(...arr);
						uni.hideLoading();
					},
					fail: (err) => {
						console.error('选择图片失败', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			// 删除图片
			deleteImage(index) {
				this.images.splice(index, 1);
			},

			// 预览图片
			previewImage(index) {
				uni.previewImage({
					current: index,
					urls: this.images
				});
			},

			// 提交举报
			submitReport() {
				if (this.description.trim().length === 0) {
					uni.showToast({
						title: '请填写举报描述',
						icon: 'none'
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});

				// 构建提交数据
				const formData = {
					"title": this.reportType.text,
					"subType": this.selectedProblem !== null ? this.reportType.btnArr[this.selectedProblem] : '',
					"desc": this.description,
					"imgs": this.images,
					"reportType": this.type,
					"reportId": this.uuid,
					// uuid: this.uuid,
					// type: this.reportType.value,
					// problem: this.selectedProblem !== null ? this.reportType.btnArr[this.selectedProblem] : '',
					// description: this.description,
					// images: this.images // 直接使用已上传的图片URL
				};

				// 发送举报数据
				this.sendReportData(formData);
			},

			// 发送举报数据
			sendReportData(formData) {
				console.log(formData);
				this.$http.post('/api/report/v2', formData)
					.then(res => {
						console.log("res", res);
						if (res.code == 200) {
							uni.hideLoading();
							uni.showToast({
								title: '举报成功',
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateBack({
									delta: 2 // 返回上上页
								});
							}, 1500);
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '举报失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						uni.showToast({
							title: '网络异常，请稍后再试',
							icon: 'none'
						});
					});
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 设置页面根元素背景色 */
	page {
		background-color: #ffffff !important;
		overflow: hidden;
	}

	/* 设置页面容器背景色 */
	.report-detail-container {
		height: 100vh;
		background-color: #ffffff !important;
		display: flex;
		flex-direction: column;
		position: relative;
	}

	.content-wrapper {
		padding: 0 30rpx;
	}

	.tip-box {
		padding-bottom: 20rpx;
		// background-color: #f8f8f8;
		border-radius: 8rpx;
		// margin: 30rpx 0;

		.tip-text {
			font-size: 26rpx;
			color: #999;
			line-height: 1.5;
		}
	}

	.section-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin: 30rpx 0 20rpx;
	}

	.required {
		color: #ff6b6b;
		font-size: 32rpx;
		margin-left: 10rpx;
	}

	.btn-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -8rpx;

		.problem-btn {
			width: calc(33.33% - 16rpx);
			height: 70rpx;
			line-height: 70rpx;
			text-align: center;
			background-color: #f5f5f5;
			color: #666;
			font-size: 24rpx;
			border-radius: 8rpx;
			margin: 8rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			&.selected {
				background-color: #ffebeb;
				color: #ff6b6b;
				border: 1px solid #ff6b6b;
			}
		}
	}

	.description-box {
		background-color: #f5f5f5;
		border-radius: 8rpx;
		padding: 20rpx;
		position: relative;

		.description-input {
			width: 100%;
			height: 200rpx;
			font-size: 28rpx;
			color: #333;
			line-height: 1.5;
		}

		.word-count {
			position: absolute;
			bottom: 20rpx;
			right: 20rpx;
			font-size: 24rpx;
			color: #999;
		}
	}

	.upload-box {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;

		.upload-item {
			width: 260rpx;
			height: 260rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-right: 30rpx;
			margin-bottom: 30rpx;

			.upload-icon {
				font-size: 50rpx;
				color: #ccc;
			}

			.upload-text {
				font-size: 24rpx;
				color: #999;
				margin-top: 10rpx;
			}
		}

		.image-item {
			width: 260rpx;
			height: 260rpx;
			border-radius: 8rpx;
			margin-right: 30rpx;
			margin-bottom: 30rpx;
			position: relative;
			overflow: hidden;

			&:nth-child(even) {
				margin-right: 0;
			}

			image {
				width: 100%;
				height: 100%;
			}

			.delete-icon {
				position: absolute;
				top: 0;
				right: 0;
				width: 40rpx;
				height: 40rpx;
				background-color: rgba(0, 0, 0, 0.5);
				color: #fff;
				font-size: 30rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 1;
			}
		}
	}

	.scroll-content {
		flex: 1;
		height: calc(100vh - 140rpx);
	}

	.bottom-placeholder {
		height: 140rpx;
	}

	.submit-btn {
		position: fixed;
		bottom: 50rpx;
		left: 30rpx;
		right: 30rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: #ccc;
		color: #fff;
		font-size: 32rpx;
		border-radius: 45rpx;
		z-index: 100;

		&.btn-active {
			background-color: #ff6b6b;
		}
	}
</style>