<template>
	<view class="container">
		<view ref="AmapRender" id="AmapRender" class="map" :center="center" :change:center="AmapRender.receive_Center"
			:person="person" :change:person="AmapRender.receive_Person" :house="house" :merchantStore="merchantStore"
			:change:merchantStore="AmapRender.receive_MerchantStore" :change:house="AmapRender.receive_House"
			:moon="moon" :change:moon="AmapRender.receive_Moon" :me="me" :msgArr="msgArr" :redPacket="redPacket"
			:change:redPacket="AmapRender.receive_redPacket" :change:msgArr="AmapRender.receive_msg"
			:change:me="AmapRender.receive_Me" :toolEvent="toolEvent" :change:toolEvent="AmapRender.receive_toolEvent">
		</view>
		<!-- 创建你的常用地点 -->
		<PointType ref="pointType" @PointTypeSubmit="PointTypeSubmit" />
		<PersonDialog ref="personDialog" @vipOpen="vipOpen" @followAddSuccess="followAddSuccess"
			@GhostSwitch="GhostSwitch" @clearSelection="clearPersonSelection" />
		<HouseDialog ref="houseDialog" @delAddress="houseDelSuccess" @closeAddress="closeAddress" />
		<StorePopup ref="storePopup" @popupClose="storePopupClose" @share-house-position="shareHousePosition" />
		<InputDialog ref="inputDialog" @InputDialogSubmit="InputDialogSubmit" />
		<MoonDialog ref="MoonPopup" @editMoon2Home="editMoon2Home" @close="moonPopupClose" />
		<!-- 信封悬浮按钮 -->
		<lc-fab-touch ref="fab" v-bind="$attrs" @trigger="trigger" @fabClick="dragClick" :popMenu="false"
			:style="{ visibility: showXf ? '' : 'hidden' }">
		</lc-fab-touch>

		<!-- 搜索按钮 -->
		<view class="searchTool">
			<image src="../../static/map/search.png" @click="handelSearch"></image>
		</view>
		<!-- 搜索按钮 -->
		<!-- 	<view class="searchTool">
			<image src="../../static/map/search.png" @click="openShareSh({uuid: '22:3',type:'sh' })"></image>
		</view> -->

		<view class="newFriend-list">
			<view class="new-friend" v-for="(item, index) in newFriendList" :key="item.request_id">
				<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
				<view class="info">
					<view class="name">{{ item.nickname }}</view>
					<view class="desc">
						<view class="age">
							<text class="iconfont icon-man" v-if="item.ext.sex == 1"></text>
							<text class="iconfont icon-xingbienv" v-else></text>
							{{ item.ext.age }}岁
						</view>
						<view class="addr" v-if="item.hometown">{{ item.hometown }}</view>
					</view>
				</view>
				<view class="btns">
					<icon class="dui" style="margin-right: 20rpx" type="success" color="#000" size="26"
						@click="handleFriendAsk('1', item, index)" />
					<icon class="cuo" type="cancel" size="26" color="#fff" @click="handleFriendAsk('3', item, index)" />
				</view>
			</view>
		</view>
		<view class="shentuhao"> 审图号 GS(2023)4047 </view>
		<!-- 设置按钮 -->
		<view class="settingTool" @click="handelSet('/pages/mapSetting/mapSetting')">
			<image src="../../static/map/set.png"></image>
		</view>
		<!-- 添加好友按钮 -->
		<view class="firendTool" @click="handelSet('/pages/addFirends/addFirends')">
			<image src="../../static/map/firend.png" @click="handelfriend"></image>
		</view>
		<!-- 回到自己定位按钮 -->
		<view class="centerTool">
			<image src="../../static/map/center.png" @click="() => handelCenter('handle')"></image>
		</view>
		<!-- 红包按钮 -->
		<view class="hbTool" @click="handleHb">
			<image src="../../static/map/hbsend.png"></image>
		</view>
		<!-- 留言 -->
		<!-- @click="handleMsg" -->
		<!-- <view class="msgTool" @click="$refs.temp.open()">
			<image src="../../static/images/message/msgLogo.png"></image>
		</view> -->
		<!-- 火苗 -->
		<view class="huobaTool">
			<image src="../../static/images/huoba1.png" v-if="fireStatus" class="fire" @click="handleFires">
			</image>
			<image src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/closeFollow.png"
				v-if="!fireStatus" class="fire" @click="handleFires"></image>
		</view>
		<Share ref="share" />
		<YdPopup ref="ydpopup" v-if="fireShow" @close="handleKnow" />
		<!-- 长按地图信息弹窗 -->
		<DianPopup ref="dianPopup" @mapLongClick="mapLongClick" @share-house-position="shareHousePosition" />
		<uni-popup ref="locationPopup">
			<view class="location-content">
				<view class="loca-name">欢迎登陆</view>
				<view class="loca-tip">请允许lightingball使用定位权限</view>
				<view class="loca-txt">为了提供区更贴近您兴趣的本地资讯及您所在位区域的信息服务，我们需要获取您设备的所在区域信息。不授权不影响您使用APP</view>
				<view class="loca-ty" @click="locaTy">继续</view>
				<view class="loca-quit" @click="locaQuit">不同意并退出APP</view>
			</view>
		</uni-popup>
		<!-- <HbPopup :center="[userInfo.longitude,userInfo.latitude]" :hbChooseLocation="hbChooseLocation" ref="hbPopup"
			@refresh_hb="refresh_hb" /> -->
		<HbPopup :hbChooseLocation="hbChooseLocation" ref="hbPopup" @refresh_hb="refresh_hb" />
		<MsgPopup ref="msgPopup" @msg="getMsg" />
		<HbRecord :redPacketInfo="redPacketInfo" ref="hbRecord" />
		<HbGet :redPacketInfo="redPacketInfo" ref="hbGet" @viewHbRecord="handleViewHbRecord" />
		<HbSend ref="hbSend" @sendHb="sendHb" />
		<SendMsg ref="SendMsgPopup" :type="type" @send="send" @getMapMsg="getMapMsg" />
		<u-toast ref="notify" />
		<uni-popup ref="temp">
			<view style="width: 750rpx; display: flex; justify-content: space-around">
				<view class="img323" @click="handleMsg" style="
            background-color: aquamarine;
            color: #000;
            line-height: 323rpx;
            text-align: center;
          ">发留言</view>
				<view class="img323" @click="goNav('/pages/leaveAMessage/leaveAMessage')" style="
            background-color: antiquewhite;
            color: #000;
            line-height: 323rpx;
            text-align: center;
          ">列表</view>
			</view>
		</uni-popup>
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirm" />
		<HmPopup ref="HmPopupRef" :status="!this.fireStatus" @confirm="handleFire" />
		<view class="tianqi">
			<Weather :temperature="temperature" :district="district" :weather="weather" />
		</view>
		<!-- 天气
		<!-- <view class="tianqi">
			<view class="temperature" v-if="temperature && district" style="">
				<image mode="widthFix" v-if="weather=='qing'" src="../../static/images/index/qing.png" class="weather">
				</image>
				<image mode="widthFix" v-else-if="weather=='yu'" src="../../static/images/index/yu.png" class="weather">
				</image>
				<image mode="widthFix" v-else-if="weather=='xue'" src="../../static/images/index/yin.png"
					class="weather">
				</image>
				<text style="margin-top: 2px;">{{ temperature }}</text>
			</view>
			<view class="district" v-if="district">
				{{ district&&district.length>5?district.slice(0,5):district }}
			</view>
		</view>
		<!-- 商家列表 -->
		<MerchantStore ref="merchantStoreRef" />
	</view>
</template>
<script module="AmapRender" lang="renderjs">
	import renderJs from './renderJs.js'
	export default renderJs
</script>
<script>
	var aliveModule = uni.requireNativePlugin(
		"LY-DCLocationAlive-LocationAliveModule"
	);

	/**
	 * 初始化配置，可以不配
	 * locationTimeout：指定单次定位超时时间, 默认5s
	 * reGeocodeTimeout：单次定位逆地理超时时间, 默认5s
	 * locationInterval：连续定位时间间隔, 默认10s
	 */
	aliveModule.initLocationConfig({
		locationInterval: 1,
	});
	import {
		isNotificationsEnabled,
		permissions,
	} from "@/js_sdk/AS-NotifyTheAuthority/index.js";
	const timer = null;
	import PointType from "./components/pointTools.vue";
	import Weather from "./components/weather.vue";
	import HmPopup from "./components/hmPopup.vue";
	import StorePopup from "./components/storeDialog.vue";
	import InputDialog from "./components/inputDialog.vue";
	import PersonDialog from "./components/personDialog.vue";
	import HouseDialog from "./components/houseDialog.vue";
	import DianPopup from "./components/dianPopup.vue";
	import MoonDialog from "./components/moonDialog.vue";
	import dragButton from "@/components/drag-button/drag-button.vue";
	import Share from "./components/share.vue";
	import YdPopup from "./components/ydPopup.vue";
	import MsgPopup from "./components/msg.vue";
	import HbPopup from "./components/hb.vue";
	import HbRecord from "./components/hbRecord.vue";
	import HbGet from "./components/hbGet.vue";
	import HbSend from "./components/hbSend.vue";
	import SendMsg from "./components/sendMsg.vue";
	import MerchantStore from "./components/merchantStore.vue";
	import {
		wgs84_to_gcj02
	} from "./wgs84_to_gcj02.js";
	import {
		apiLocationReport,
		apiLocationVisibleGet,
		apiLocationMe,
		apiAddressSet,
		apiLocationSearchPeople,
		apiNotifyCnt,
		apiNewFriend,
		apiAddressGet,
		apiFriendAsk,
		apiGetNight,
		apiToken,
		apiGetRedpacket,
		apiGetRedpacketDetail,
		apiMapMsg,
		apiMsgDetail,
		apiBusinessList,
	} from "@/api/common.js";
	export default {
		components: {
			Weather,
			HmPopup,
			MsgPopup,
			PointType,
			StorePopup,
			InputDialog,
			PersonDialog,
			HouseDialog,
			dragButton,
			MoonDialog,
			Share,
			YdPopup,
			DianPopup,
			HbPopup,
			HbRecord,
			HbGet,
			HbSend,
			SendMsg,
			MerchantStore,
		},
		// /inject: ['unReadCountPrv'],
		data() {
			return {
				// 通知权限是否已开启
				isNotifications: false,
				showXf: false,
				imgCurrent: 0,
				type: "",
				mapLoad: false,
				userInfo: {},
				msgInfo: {},
				visibleRole: 0,
				// center: uni.getStorageSync('location') ? uni.getStorageSync('location').split(',') : null,
				center: uni.getStorageSync("location") ?
					uni.getStorageSync("location").split(",") :
					null,
				person: null,
				redPacket: null,
				msgArr: null,
				// me: uni.getStorageSync('setStorageSync') || null,
				me: uni.getStorageSync("userInfo") || null,
				// me: null,
				moon: null,
				house: null,
				timer: null,
				fireShow: false,
				fireStatus: false,
				district: "",
				toolEvent: null,
				notify: {
					at: "",
					comment: "0",
					friendApply: "2",
					like: "8",
				},
				sendDialogFlag: true,
				gocenter: false,
				temperature: "℃",
				weather: "qing",
				searchPeople_option: {},
				mapComplete: false,
				mapStyle: "red",
				locaErr: "success",
				outInPage: false,
				count: 1,
				hbChooseLocation: {},
				redPacketInfo: {},
				leaveAMessage: {}, //留言信息
				merchantStore: null,
			};
		},
		// props: {
		// 	unReadCount: {
		// 		type: Number,
		// 		default: 0
		// 	},
		// },
		computed: {
			newFriendList(newVal, oldVal) {
				return this.$store.state.Yxim_info.friendApply;
			},
		},
		async mounted() {
			const appAuthorizeSetting = uni.getAppAuthorizeSetting();
			// if (appAuthorizeSetting.locationAuthorized !== 'authorized') {
			// 	this.$refs.locationPopup.open('center')
			// 	return false
			// }
			this.pageInit();
			uni.$on("sendMsg", (flag) => {
				if (flag) {
					this.handleMsg();
				}
			});

			setTimeout(() => {
				this.showXf = true;
			}, 500);
			uni.$on("seeDetail", (item) => {
				const coordinate = item.detail.coordinate.split(",");
				this.type = item.receive ? "3" : "4";

				this.outInPage = true;
				this.toolEvent = {
					act: "visualAngle",
					params: {
						lng: coordinate[0],
						lat: coordinate[1],
					},
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
				setTimeout(() => {
					this.outInPage = false;
				}, 2000);
				this.msgDetail(item.id);
			});
		},
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
			this.timer = null;
		},
		methods: {
			clearPersonSelection() {
				// 通知 renderJs 清除用户选择状态
				this.toolEvent = {
					act: "clearPersonSelection",
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},
			openShareSh(scanDetail) {
				//外部分享扫码获取到的数据
				console.log("--------------scanDetail", scanDetail);
				if (scanDetail && scanDetail.type == "sh") {
					const {
						uuid,
						lat,
						lng
					} = scanDetail;
					uni.setStorageSync("scanDetail", {});
					this.toMerchantStore({
						Name: uuid,
					});
					if (scanDetail.coordinate) {
						setTimeout(() => {
							this.center = scanDetail.coordinate.split(",");
						}, 300);
					}
				}
			},
			confirm() {
				uni.navigateTo({
					url: "/pages/vipCenter/vipCenter",
				});
			},
			vipOpen() {
				this.imgCurrent = 2;
				this.$refs.vipTips.open("center");
			},
			goNav(url) {
				this.$refs.temp.close();
				this.$common.navigateTo({
					url,
				});
			},
			send(txt) {
				this.$http
					.post("/api/location-msg/add", {
						...this.leaveAMessage,
						content: txt,
					})
					.then((res) => {
						// this.httpMsg(this.searchPeople_option)
						this.$refs.SendMsgPopup.close();
						this.toast("留言成功");
					});
			},
			getMsg(info) {
				this.type = "1";
				this.leaveAMessage = info;
				this.$refs.msgPopup.close();
				this.$refs.SendMsgPopup.openSend(info);
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},

			handleViewHbRecord(info) {
				this.redPacketInfo = info;
				this.$refs.hbRecord.open(info.uuid);
			},
			locaTy() {
				this.$refs.locationPopup.close("center");
				this.pageInit();
			},
			locaQuit() {
				this.$refs.locationPopup.close("center");
				plus.runtime.quit();
				return true;
			},
			refresh_hb(coordinate) {
				// 聚焦红包
				this.center = null;
				setTimeout(() => {
					this.center = coordinate.split(",");
				}, 1000);
			},
			async pageInit() {
				this.fireShow = !uni.getStorageSync("firstRegister") ? false : true; //获取是否显示指引
				await this.httpLocationVisibleGet();

				//是否获取通知权限
				this.isNotifications = isNotificationsEnabled();
				if (!this.isNotifications && this.sendDialogFlag) {
					this.sendDialogFlag = false;
					this.checkVersion();
				}
				// const pemission = uni.getStorageSync('pemission')
				// // 本机轮询
				// if (pemission != 1) {
				// 	this.openTimeInterval()
				// }

				// await this.httpLocationReport(async () => {
				// 	// //本机用户 位置 电量 上报
				// 	await this.httpLocationMe(false);
				// });
			},
			// 前往系统设置的功能
			checkVersion() {
				permissions(!this.isNotifications);
			},
			sendHb(type) {
				if (type == "store") {} else if (type == "user") {}
				this.$refs.hbPopup.open(type);
			},
			cleatTimeout() {
				clearTimeout(this.timer);
				this.timer = null;
			},
			async openTimeInterval(mode) {
				console.log("开始循环获取定位");

				aliveModule.startUpdatingLocation(true, true, async (res) => {
					let time =
						new Date().getHours() +
						":" +
						new Date().getMinutes() +
						":" +
						new Date().getSeconds();
					// console.log("持续定位" + time, res);
					// 轮询本机位置上报
					await this.httpLocationReport({
							longitude: res.longitude,
							latitude: res.latitude,
						},
						() => {
							this.httpLocationMe(() => {
								if (mode == "go") {
									this.handelCenter();
								}
							});
						}
					);
				});

				// if (this.timer) {
				// 	clearTimeout(this.timer)
				// 	this.timer = null;
				// }
				// this.timer = setTimeout(() => {
				// 	this.openTimeInterval()
				// }, 2000);
			},
			handleHb() {
				this.$refs.hbSend.open();
			},
			handleMsg() {
				console.log(this.$refs.msgPopup, "liuyan");
				this.$refs.temp.close();
				this.$refs.msgPopup.open();
			},
			styleChange() {
				this.mapStyle = this.mapStyle == "red" ? "blue" : "red";
				this.toolEvent = {
					act: "mapStyleChange",
					params: this.mapStyle == "red" ?
						"amap://styles/461d0df8becbaaefd1e2dcbd7c8971df" :
						"amap://styles/e0973364545af4fb92420a3bdf9409b5",
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},
			dragClick() {
				uni.navigateTo({
					url: "/pages/chat-list/chat-list",
				});
			},
			trigger(e) {
				console.log(e);
				this.content[e.index].active = !e.item.active;
				uni.showModal({
					title: "提示",
					content: `您${this.content[e.index].active ? "选中了" : "取消了"}${
          e.item.text
        }`,
					success: function(res) {
						if (res.confirm) {
							console.log("用户点击确定");
						} else if (res.cancel) {
							console.log("用户点击取消");
						}
					},
				});
			},

			GhostSwitch(data) {
				this.toolEvent = {
					act: "person_GhostSwitch",
					params: data,
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
				// this.httpLocationSearchPeople()
			},
			shareHousePosition(option) {
				this.$refs.share.open(option);
			},
			handelSet(url) {
				uni.navigateTo({
					url,
				});
			},

			/**
			 * 	地图层 回调
			 */
			async callApp({
				act,
				option,
				interval
			} = {}) {
				if(act=='open_personPopup'){
					console.log('==========open_personPopup==================')
				}
				switch (act) {
					case "map:complete": //地图加载完成
						const locationFlag = uni.getStorageSync("locationFlag");
						console.log("地图加载完成");
						this.mapComplete = true;
						//locationPermissions：用来判断是否获取过权限，如果没获取定位权限，延时获取，目的为了开屏动画加载完在获取
						const locationPermissions = uni.getStorageSync("locationPermissions");
						const time = locationPermissions ? 0 : 5000;
						/**
						 *单次定位
						 * aliveModule.requestLocationWithReGeocode(withReGeocode, callback);
						 * withReGeocode：是否需要地址信息
						 * callback：结果回调
						 */
						// aliveModule.requestLocationWithReGeocode(true, async (params) => {})
						setTimeout(() => {
							uni.getLocation({
								type: "wgs84",
								isHighAccuracy: true,
								success: async (res) => {
									uni.setStorageSync("locationPermissions", true);
									console.log("uniapp首次获取定位", res);
									const gcj02 = wgs84_to_gcj02(res.longitude, res.latitude);
									const locationParams = {
										longitude: gcj02[0],
										latitude: gcj02[1],
									};
									const flag = await this.httpLocationReport({
											longitude: locationParams.longitude,
											latitude: locationParams.latitude,
										},
										async () => {
											//本机用户 位置 电量 上报
											this.httpLocationMe((me) => {
												uni.setStorageSync("location",
													`${me[0]},${me[1]}`);
												this.center = [me[0], me[1]];
												if (!this.gocenter) {
													this.gocenter = true;
													this.toolEvent = {
														act: "move-view-center",
														params: {
															lng: me[0],
															lat: me[1],
														},
													};

													setTimeout(() => {
														this.toolEvent =
															null;
													}, 1400);
												}
											});
											const pemissions = uni.getStorageSync(
												"pemission");
											// 本机轮询
											if (!pemissions) {
												this.openTimeInterval();
												this.handelCenter("handle");
											}
											return true;
										}
									);
								},
							});
						}, time);
						this.searchPeople_option = option;
						this.httpLocationSearchPeople(option);
						this.httpRedpacket(option);
						this.httpMsg(option);
						this.httpAddressGet();
						this.httpGetNight();
						this.getMerchantList(option);
						break;
					case "close_house_popup":
						this.$refs.storePopup.close();
					case "close_dian_popup":
						this.$refs.dianPopup.close();
						break;
					case "change:maxDist": //重置可视半径
						this.searchPeople_option = option;
						this.httpLocationSearchPeople(option);
						this.httpRedpacket(option);
						this.httpMsg(option);
						// this.getMerchantList(option)
						break;

					case "view_redPacket_detail":
						apiGetRedpacketDetail({
							envelope_uuid: option.Name,
						}).then((res) => {
							if (res.code == 200) {
								this.redPacketInfo = res.message;
								if (this.redPacketInfo.can_take.can_take == 3) {
									this.$refs.hbRecord.open(option.Name);
								} else if (this.redPacketInfo.can_take.can_take == 5) {
									this.toast("红包已过期");
								} else {
									this.$refs.hbGet.open(option);
								}
							}
						});
						break;
					case "view_msg_detail":
						this.msgDetail(option.Name);
						break;
					case "temperature:change":
						this.temperature = option.temperature;
						this.weather = option.weather;
						break;
					case "open_storePopup":
						this.$refs.storePopup.open(option);
						break;
					case "open_moonPopup":
						this.$refs.MoonPopup.open(option);
						break;
						// case 'delMoon':
						// 	break;
					case "closeStoreDialog":
						this.$refs.storePopup.guanbi(option);
						break;
					case "SET_EVENT_NULL":
						this.event = null;
						break;
					case "changeCountry":
						this.district = option;
						break;
					case "close_storePopup":
						this.$refs.storePopup.close();
						break;
					case "open_personPopup":
						this.$refs.personDialog.open(option);
						break;
					case "open_housePopup":
						this.$refs.houseDialog.open(option);
						break;
					case "mapLongClick":
						this.$refs.dianPopup.open(option);

						// this.mapLongClick(option);
						break;
					case "update:data":
						const {
							key, value
						} = option;
						this[key] = value;
						break;
					case "to_merchant_store":
						this.toMerchantStore(option);
						break;
					default:
						break;
				}
			},
			PointTypeSubmit(option) {
				this.$refs.inputDialog.open({
					...option,
				});
			},
			poiMapAddMarker(option) {
				this.outInPage = true;
				this.$refs.dianPopup.open(option);
				this.toolEvent = {
					act: "poiMapAddMarker",
					params: option,
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
				setTimeout(() => {
					this.outInPage = false;
				}, 2000);
			},
			closeAddress() {
				this.toolEvent = {
					act: "houseSetPopupClose",
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},

			followAddSuccess(data) {
				this.toolEvent = {
					act: "person_guanzhu",
					params: data,
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
				// this.httpLocationSearchPeople(data);
			},
			houseDelSuccess() {
				this.$refs.storePopup.close();
				this.httpAddressGet();
				// this.toolEvent = {
				// 	act: 'houseDataDel'
				// }
				// setTimeout(() => {
				// 	this.toolEvent = null
				// }, 100);
			},
			storePopupClose() {
				this.toolEvent = {
					act: "housePopupClose",
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},
			async InputDialogSubmit(option) {
				const {
					location,
					text,
					lnglat,
					type
				} = option;

				const applyData = {
					location,
					remark: text,
					coordinate: `${lnglat.lng},${lnglat.lat}`,
					type,
				};
				apiAddressSet({
					...applyData,
				}).then((res) => {
					if (res.code == 200) {
						this.$refs.inputDialog.close();
						// 重置房子接口数据
						this.httpAddressGet();
					}
				});
			},
			//留言详情
			msgDetail(id) {
				apiMsgDetail({
					source: 1,
					id,
				}).then((res) => {
					if (res.code == 200) {
						console.log("this.type", this.type);
						this.type = res.message.detail.reply_at > 0 ? "3" : "2";
						console.log("this.type222", this.type);
						// this.type = res.message.detail.reply_at > 0 ? "3" : "2"
						this.msgInfo = res.message;
						this.$refs.SendMsgPopup.open(res.message);
					}
				});
			},
			// 地图点击事件！
			mapLongClick(ev) {
				this.$refs.dianPopup.close();
				this.$refs.pointType.open({
					...ev,
				});
			},
			/**
			 * 点亮/熄灭火把
			 */
			handleFires() {
				const flag = uni.getStorageSync("hmTips");
				if (flag) {
					this.handleFire();
				} else {
					this.$refs.HmPopupRef.open();
				}
			},
			handleFire() {
				this.fireStatus = !this.fireStatus;
				// 重新获取可见用户
				this.event = {
					key: "handleFire",
					params: this.fireStatus ? 1 : 2,
				};
				this.$http.post("/location/visible", {
					role: this.fireStatus ? 1 : 2,
					uids: [],
				});
				this.httpLocationSearchPeople(this.searchPeople_option);
			},
			async editMoon2Home(key) {
				// this.toolEvent = {
				// 	act: 'moonDataChange'
				// }
				// setTimeout(() => {
				// 	this.toolEvent = null
				// }, 100);
				setTimeout(() => {
					this.httpGetNight(1);
					this.httpAddressGet();
				}, 200);
			},
			moonPopupClose() {
				this.toolEvent = {
					act: "moonPopupClose",
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},

			handelfriend() {},
			handelSearch() {
				uni.navigateTo({
					url: "/pages/search/search",
				});
			},
			/**
			 * 重置中心点
			 */
			handelCenter(option) {
				const locationFlag = uni.getStorageSync("locationFlag");
				if (option && option == "handle") {
					// console.log(option, "======重置中心点=======", this.locaErr);
					if (this.locaErr == "fail" || locationFlag) {
						uni.getLocation({
							type: "wgs84",
							isHighAccuracy: true,
							success: async (res) => {
								console.log("========重置中心点=========", res.longitude);
								uni.setStorageSync(
									"location",
									`${res.longitude},${res.latitude}`
								);
								uni.setStorageSync("locationFlag", false);
								this.locaErr = "success";
								// uni.showToast({
								// 	icon: "none",
								// 	title: "授权成功后请重启应用",
								// });
							},
							fail: (err) => {
								console.log("定位错误");
								this.locaErr = "fail";
								uni.setStorageSync("locationFlag", true);

								if (err.errMsg && this.locaErr !== "fail") {
									// 1. 获取当前页面栈实例（此时最后一个元素为当前页）
									this.timer = null;
									clearInterval(this.timer);
								}
							},
						});

						// uni.navigateTo({
						// 	url: '/pages/permissions/permissions',
						// 	success: res => {},
						// 	fail: () => {},
						// 	complete: () => {}
						// });
						return;
					}
				}
				if (!this.mapComplete || this.outInPage) return false;
				if (option && option !== "handle") {
					this.center = [option.longitude, option.latitude];
					setTimeout(() => {
						this.center = null;
					}, 100);
				} else {
					this.center = [this.userInfo.longitude, this.userInfo.latitude];
					// 重置自己位置的水波纹动画
					setTimeout(() => {
						if (this.$refs.mapRender) {
							this.$refs.mapRender.resetMyRippleAnimation();
						}
					}, 500);
					setTimeout(() => {
						this.center = null;
					}, 1000);
				}
			},
			async handleFriendAsk(type, row, index) {
				let applyData = {
					requestId: row.request_id,
					type,
					msg: "",
				};
				const res = await apiFriendAsk({
					...applyData,
				});
				if (res.code == 200) {
					this.$store.commit("SET_FRIEND_REFUSE", row);
				}
			},
			handleKnow() {
				uni.setStorageSync("firstRegister", false);
				this.fireShow = false;
			},
			// 获取地图红包点
			httpRedpacket(option) {
				apiGetRedpacket(option).then((res) => {
					if (res.code == 200) {
						const data = [];
						this.redPacket = res.message;
						setTimeout(() => {
							this.redPacket = null;
						}, 100);
					}
				});
			},
			// 获取地图留言
			httpMsg(option) {
				apiMapMsg(option).then((res) => {
					if (res.code == 200) {
						const data = [];
						this.msgArr = res.message;
						setTimeout(() => {
							this.msgArr = null;
						}, 100);
					}
				});
			},
			getMapMsg() {
				this.httpMsg(this.searchPeople_option);
			},
			/**
			 * 	获取可见月亮
			 */
			httpGetNight(aa) {
				apiGetNight().then((res) => {
					if (res.code == 200) {
						this.moon = res.message;
						setTimeout(() => {
							this.moon = null;
						}, 400);
						// this.event = {
						// 	key: 'getNight',
						// 	params: res.message
						// }
					}
				});
			},
			/**
			 * 	获取地图中的用户
			 */
			httpLocationSearchPeople(option) {
				apiLocationSearchPeople({
					...option,
					type: this.fireStatus ? 1 : 2,
				}).then((res) => {
					if (res.code == 200) {
						const data = [];
						// res.message.forEach(item => {
						// 	data.push({...item})
						// 	data.push({
						// 		...item,
						// 		longitude: item.longitude + .2,
						// 		latitude: item.latitude + .2
						// 	})
						// })
						this.person = res.message;
						// console.log('获取地图中的用户', res.message);
						setTimeout(() => {
							this.person = null;
						}, 100);
						// this.event = {
						// 	key: 'getLocationSearchPeople',
						// 	params: res.message
						// }
					}
				});
			},

			/**
			 * 	获取地图中的房子
			 */
			httpAddressGet() {
				apiAddressGet()
					.then((res) => {
						if (res.code == 200) {
							this.house = res.message;
							setTimeout(() => {
								this.house = null;
							}, 100);
							// this.event = {
							// 	key: 'getAddressGet',
							// 	params: res.message
							// }
						}
					})
					.catch((err) => {});
			},
			/**
			 * 	获取可见范围
			 */
			httpLocationVisibleGet() {
				apiLocationVisibleGet().then((res) => {
					if (res.code == 200) {
						this.visibleRole = res.message.role.role;
						// this.event = {
						// 	key: 'getLocationVisibleGet',
						// 	params: res.message
						// }
					}
				});
			},
			/**
			 * 	获取用户位置
			 */
			httpLocationMe(fn) {
				// console.log("httpLocationMe开始执行: " + new Date().getTime());
				const fnc = fn;
				apiLocationMe().then((res) => {
					// console.log("apiLocationMe响应时间: " + new Date().getTime());
					if (res.code == 200) {
						this.userInfo = res.message;
						uni.setStorageSync("userLoglat", [
							this.userInfo.longitude,
							this.userInfo.latitude,
						]);
						if (!this.mapComplete) return false;
						fnc && fnc(res.message);
						this.me = null;
						this.count = 0;
						// console.log('this.userInfo', this.userInfo);
						setTimeout(() => {
							// console.log("设置this.me开始: " + new Date().getTime());
							this.me = {
								...this.userInfo,
								longitude: this.userInfo.longitude + this.count * 1 * 0.0002,
								latitude: this.userInfo.latitude + this.count * 1 * 0.0002,
							};
							// console.log("设置this.me完成: " + new Date().getTime());
						}, 200);
						// this.event = {
						// 	key: 'getLocationMe',
						// 	params: res.message,
						// 	interval
						// }
					}
					// console.log("httpLocationMe执行结束: " + new Date().getTime());
				});
			},
			/**
			 * 	本机用户 位置 电量 上报
			 */
			async httpLocationReport(param, fn) {
				const locationFlag = uni.getStorageSync("locationFlag", false);
				if (locationFlag) return;
				this.$common.getBattery(async (level) => {
					uni.getLocation({
						type: "wgs84",
						isHighAccuracy: true,
						success: async (res) => {
							try {
								uni.setStorageSync("locationFlag", false);
								this.locaErr = "success";
								uni.setStorageSync(
									"location",
									`${param.longitude},${param.latitude}`
								);
								// console.log('00000', param.longitude, param.latitude);
								const gcj02 = wgs84_to_gcj02(param.longitude, param.latitude);
								const result = await apiLocationReport({
									longitude: gcj02[0], //经度
									latitude: gcj02[1], //维度
									electricity: level, //电量 0-100
								}).then((res1) => {
									fn && fn(gcj02);
									return true;
								});
							} catch (err) {
								console.log("err", err);
							}
						},
						fail: (err) => {
							console.log("定位错误");
							this.locaErr = "fail";
							uni.setStorageSync("locationFlag", true);

							if (err.errMsg && this.locaErr !== "fail") {
								// 1. 获取当前页面栈实例（此时最后一个元素为当前页）

								this.timer = null;
								clearInterval(this.timer);

								// uni.navigateTo({
								// 	url: '/pages/permissions/permissions',
								// 	success: res => {},
								// 	fail: () => {},
								// 	complete: () => {}
								// });
							}
						},
					});
				});
			},
			// 获取商家列表
			getMerchantList(option) {
				var params = {
					longitude: option.longitude,
					latitude: option.latitude,
					// distance:option.maxDist,
					distance: 1000000000,
					size: 20,
				};

				apiBusinessList(params).then((res) => {
					if (res.code == 200) {
						this.merchantStore = res.message;
						setTimeout(() => {
							this.merchantStore = null;
						}, 100);
					}
				});
			},

			// 去商家列表
			toMerchantStore(option) {
				console.log("点击去商家列表");
				this.$refs.merchantStoreRef.open(option);
				// uni.navigateTo({
				// 	url: '/pages/merchantStore/merchantStore?business_id='+option.Name+"&business_lat="+option.Latitude+"&business_lng="+option.Longitude
				// });
			},
		},
	};
</script>
<style>
.amap-marker-label {
	border: 0;
	background-color: transparent;
}
</style>
<style scoped lang="scss">
.shentuhao {
	position: fixed;
	bottom: 145rpx;
	right: 10rpx;
	color: #000;
}

.houseinfo {
	background-color: #000 !important;
}

.location-content {
	width: 540rpx;
	margin: auto;
	background-color: #1a1a1a;
	border-radius: 20rpx;
	color: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 40rpx;

	.loca-name {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.loca-tip {
		width: 100%;
		text-align: left;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.loca-txt {
		font-size: 24rpx;
	}

	.loca-ty {
		padding: 20rpx 150rpx;
		color: #333;
		margin-top: 100rpx;
		background-color: #fff;
		border-radius: 60rpx;
	}

	.loca-quit {
		margin-top: 40rpx;
		font-weight: bold;
		font-size: 28rpx;
	}
}

.changeStyle {
	display: flex;
	position: fixed;
	right: 200rpx;
	bottom: 500rpx;

	.red,
	.blue {
		width: 100rpx;
		height: 100rpx;
		background: red;
	}

	.blue {
		background: blue;
	}
}

.container {
	position: relative;

	#AmapRender {
		width: 100%;
		height: 100vh;
		background-color: transparent;
	}

	.centerTool {
		width: 82rpx;
		height: 82rpx;
		background: #ffffff;
		box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(0, 0, 0, 0.62);
		border-radius: 50%;
		position: fixed;
		bottom: 210rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;

		image {
			width: 102rpx;
			height: 102rpx;
			margin-left: -8rpx;
		}
	}

	.searchTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		top: 95rpx;
		left: 32rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.settingTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		top: 95rpx;
		right: 32rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.hbTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		top: 327rpx;
		right: 32rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.tempTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		bottom: 500rpx;
		left: 132rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.msgTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		top: 450rpx;
		right: 32rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.firendTool {
		width: 74rpx;
		height: 74rpx;
		position: fixed;
		top: 211rpx;
		right: 32rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.tianqi {
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		left: 50%;
		top: 100rpx;
		transform: translateX(-50%);

		// .temperature {
		// 	font-family: Source Han Sans, Source Han Sans;
		// 	font-weight: 600;
		// 	font-size: 20rpx;
		// 	color: #FFFFFF;
		// 	text-align: left;
		// 	font-style: normal;
		// 	text-transform: none;
		// 	margin-right: 20rpx;
		// 	position: relative;
		// 	top: -41rpx;

		// 	text {
		// 		width: 74rpx;
		// 		height: 74rpx;
		// 		display: flex;
		// 		align-items: center;
		// 		justify-content: center;
		// 		// margin-top: 5rpx;
		// 		position: absolute;
		// 		z-index: 2;
		// 	}

		// 	.weather {
		// 		width: 74rpx;
		// 		position: absolute;
		// 		left: 0;
		// 		top: 4rpx;
		// 	}
		// }

		// .district {
		// 	display: flex;
		// 	align-items: center;
		// 	justify-content: center;
		// 	margin-left: 80rpx;
		// 	width: 208rpx;
		// 	height: 74rpx;
		// 	background: rgba(63, 63, 63, 0.38);
		// 	border-radius: 14rpx 14rpx 14rpx 14rpx;
		// 	opacity: 1;
		// 	font-size: 32rpx;
		// 	font-family: PingFang SC-Medium, PingFang SC;
		// 	color: #ffffff;
		// 	line-height: 45rpx;
		// }
	}

	.huobaMask {
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.4);
		position: fixed;
		left: 0;
		top: 0;
		z-index: 20;

		.know {
			position: absolute;
			left: 50%;
			bottom: 750rpx;
			transform: translateX(-50%);

			.knowImg {
				width: 168rpx;
				height: 79rpx;
			}
		}

		.box {
			width: 655rpx;
			height: 241.66rpx;
			position: absolute;
			left: 30rpx;
			bottom: 450rpx;
			padding: 42rpx 52rpx;
			box-sizing: border-box;

			&::after {
				width: 5rpx;
				height: 182rpx;
				background: linear-gradient(217deg,
						#54c2ee 0%,
						rgba(84, 194, 238, 0) 100%);
				border-radius: 0rpx 0rpx 0rpx 0rpx;
				content: "";
				position: absolute;
				left: 70rpx;
				top: 250rpx;
			}

			.boxTitle {
				width: 96rpx;
				height: 70rpx;
				font-size: 48rpx;
				font-family: Source Han Sans-Medium, Source Han Sans;
				font-weight: 500;
				color: #ffffff;
				line-height: 70rpx;
				position: relative;
				z-index: 2;
			}

			.boxTxt {
				width: 476rpx;
				height: 49rpx;
				font-size: 34rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #ffffff;
				line-height: 49rpx;
				margin-top: 12rpx;
				position: relative;
				z-index: 2;
			}

			image {
				width: 100%;
				height: 100%;
				position: absolute;
				left: 0;
				top: 0;
			}
		}
	}

	.huobaTool {
		width: 102rpx;
		height: 102rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(0, 0, 0, 0.62);
		border-radius: 50%;
		position: fixed;
		bottom: 200rpx;
		left: 50rpx;
		z-index: 30;

		.fire {
			width: 100%;
			height: 100%;
		}

		.shou {
			width: 88rpx;
			height: 100rpx;
			margin-left: 14rpx;
			z-index: 29;
		}

		.dian {
			width: 54rpx;
			height: 54rpx;
			position: absolute;
			bottom: -30rpx;
			left: 24rpx;
			z-index: 27;
		}
	}

	.newFriend-list {
		position: fixed;
		left: 50%;
		top: 50rpx;
		transform: translateX(-50%);
		z-index: 10;

		.new-friend {
			width: 90vw;
			background-color: rgba(0, 0, 0, 0.7);
			border-radius: 30rpx;
			box-shadow: 0 0 3rpx 6rpx rgba(0, 0, 0, 0.3);
			font-size: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx;
			box-sizing: border-box;
			margin-bottom: 20rpx;

			.avatar {
				width: 100rpx;
				height: 100rpx;
				border-radius: 20rpx;
				margin-right: 20rpx;
			}

			.info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				box-sizing: border-box;

				.name {
					font-size: 32rpx;
					color: #fff;
					margin-bottom: 10rpx;
				}

				.desc {
					font-size: 20rpx;

					.age,
					.addr {
						display: inline-block;
						padding: 2rpx 10rpx;
						background-color: rgba(0, 0, 0, 0.9);
						color: #fff;
						opacity: 0.7;
						border-radius: 5rpx;

						.iconfont {
							font-size: 20rpx;
							color: #1296db;
							margin-right: 10rpx;
						}

						.icon-xingbienv {
							color: #ff3ec9;
						}
					}
				}
			}

			.btns {
				.cuo {
					width: 50rpx;
					height: 50rpx;
				}

				.dui {
					width: 50rpx;
					height: 50rpx;
				}
			}
		}
	}

	.notifyMsg {
		width: 100%;
		height: 100%;

		.point {
			width: 10rpx;
			height: 10rpx;
			border-radius: 50%;
			background-color: red;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
			z-index: 2;
		}

		.notifyMsg-icon {
			width: 120rpx;
			height: 120rpx;
			// width: 100%;
			// height: 100%;
		}
	}

	button {
		position: fixed;
		left: 0;
		top: 0;
	}

	::v-deep .amap-logo {
		z-index: 1;
		transform: translateY(-140rpx);
		// display: none !important;
	}

	::v-deep .amap-copyright {
		display: none !important;
	}
}

.getToken {
	position: fixed;
	left: 100rpx;
	top: 600rpx;
	margin: 300rpx;
}
</style>