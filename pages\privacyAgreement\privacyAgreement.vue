<template>
	<view style="padding: 20rpx;">
		<web-view src="https://static.lluuxiu.com/privacy.html"></web-view>
		<!-- <rich-text :nodes="content"></rich-text> -->
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		config
	} from "@/config.js"
	export default {
		data() {
			return {
				config,
				content: ""
			}
		},
		onLoad() {
			this.$http.get('/agreement').then(res => {
				this.content = res
			})
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
		}
	}
</script>

<style>

</style>