<template>
	<uni-popup ref="popup" type="bottom" :safe-area="false">
		<view class="hb" v-if="redPacketInfo">

			<view class="dhb" v-if="redPacketInfo.can_take&&redPacketInfo.can_take.can_take == 6">
				<view v-if="redPacketInfo.can_take.inside">
					<view class=" gq">
						<view>
							<image class="gq-avatar" :src="userInfo.avatar"></image>
							{{redPacketInfo.title}}的红包
						</view>
						<view>未在{{redPacketInfo.location}}停留{{redPacketInfo.duration}}分钟</view>
					</view>
				</view>
				<view v-if="redPacketInfo.can_take&&!redPacketInfo.can_take.inside">
					<view class=" gq">
						<view style="display: flex;font-size: 20rpx;align-items: center;margin-bottom: 10rpx;">
							<image class="gq-avatar" :src="userInfo.avatar"></image>
							{{redPacketInfo.title}}的红包
						</view>
						<view>未在红包规定位置</view>
					</view>
				</view>
				<view class="userName">{{redPacketInfo.title}}</view>
				<image class="hbbg" src="@/static/map/dhb.png"></image>
			</view>
			<view class="dhb" v-if="redPacketInfo.can_take&&redPacketInfo.can_take.can_take == 4">
				<view class="gq">
					<view>
						<image class="gq-avatar" :src="userInfo.avatar"></image>
						{{redPacketInfo.title}}的红包
					</view>
					<view>手慢了，红包派完了</view>
				</view>
				<image src="@/static/map/dhb.png"></image>
			</view>
			<view class="dhb" v-if="redPacketInfo.can_take&&redPacketInfo.can_take.can_take == 1">
				<view class="userName2" style="text-align: center;">{{redPacketInfo.title}}</view>
				<image class="hbbg" src="@/static/map/bgb.png"></image>
				<view class="ppbox">
					<view class="ppView" v-if="redPacketInfo.receive_total!=redPacketInfo.quantity"
						:style="{left:`${(redPacketInfo.quantity-redPacketInfo.receive_total)/redPacketInfo.quantity*100+'%'}`}">
						还剩{{redPacketInfo.quantity-redPacketInfo.receive_total}}个
					</view>
				</view>
				<view class="line-box" v-if="redPacketInfo.receive_total!=redPacketInfo.quantity">
					<view class="lineview"
						:style="{width:(redPacketInfo.quantity-redPacketInfo.receive_total)/redPacketInfo.quantity*100+'%'}"
						style="background-color: #fdce8e;border-radius: 15rpx;">

					</view>
				</view>
			</view>
			<view class="getBox">
				<view class="topline"></view>
				<image class="close-icon" @click="close" src="@/static/map/close.png" mode=""></image>
				<view class="hb-desc">
					<image class="avatar" :src="userInfo.avatar">
					</image>
					<view class="info">
						<view class="item text-overflow" style="width: 100%;">
							<image class="icon" src="@/static/map/pos.png" mode=""></image>
							{{redPacketInfo.location}}
						</view>
						<view class="item">
							<image class="icon" src="@/static/map/qianbao.png" mode=""></image>
							￥{{redPacketInfo.total_amount}}
						</view>
						<view class="item" style="width: 60%;">
							<image class="icon" src="@/static/map/clock.png" mode=""></image>
							停留时间：<text
								:class="{canTake:redPacketInfo.can_take == 1}">{{redPacketInfo.duration||'0'}}</text>
						</view>
						<view class="item">
							<image class="icon" src="@/static/map/fanwei.png" mode=""></image>
							范围内：<text
								:class="{canTake:redPacketInfo.can_take == 1}">{{redPacketInfo.radius||'0'}}米</text>
						</view>
						<view class="item" style="width: 60%;">
							<image class="icon" src="@/static/map/clock.png" mode=""></image>
							已停留：<text
								:class="{canTake:redPacketInfo.can_take&&redPacketInfo.can_take == 1}">{{(redPacketInfo.can_take&&redPacketInfo.can_take.stay_format)||'0分钟'}}</text>
						</view>
					</view>
				</view>
				<view class="btn" @click="handleGet" :class="{canTake:redPacketInfo.can_take.can_take == 1}">
					领取
				</view>
			</view>
			<view class="img24" />
			<u-toast ref='notify' />
		</view>
	</uni-popup>
</template>

<script>
	import {
		apiGetRedpacketDetail,
		apiRedpacketTake
	} from '@/api/common.js'
	export default {
		data() {
			return {
				userInfo: uni.getStorageSync('userInfo')
			}
		},
		props: {
			redPacketInfo: {
				type: Object,
				default: () => null
			}
		},
		mounted() {
			console.log('userInfo', this.userInfo);
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			handleGet() {
				if (this.redPacketInfo.can_take.can_take == 1) {
					apiRedpacketTake({
						envelope_uuid: this.redPacketInfo.uuid
					}).then(res => {
						if (res.code == 200) {
							this.close()
							this.$emit('viewHbRecord', this.redPacketInfo)
						} else {
							this.toast(res.message);
						}
					})
				}
			},
			open(option) {
				setTimeout(() => {
					this.$refs.popup.open('bottom')
					console.log(this.redPacketInfo);
				}, 1000)
			},
			close() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 750rpx;
		height: 100vh;
		position: relative;
		overflow-y: scroll;

		.dhb {
			width: 573rpx;
			height: 697rpx;
			opacity: 1;
			position: absolute;
			left: 50%;
			top: 40%;
			transform: translate(-50%, -50%);

			.gq {
				font-size: 24rpx;
				position: absolute;
				left: 50%;
				top: 40rpx;
				transform: translateX(-50%);
				z-index: 2;
				color: #F14234;

				.gq-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
				}

			}

			.hbbg {
				width: 100%;
				height: 100%;

			}

			.userName {
				width: 168rpx;
				height: 41rpx;
				font-size: 28rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #FED1A6;
				line-height: 41rpx;
				position: absolute;
				left: 50%;
				bottom: 10%;
				transform: translateX(-50%);
				z-index: 2;
			}

			.ppbox {
				width: 457rpx;
				position: absolute;
				bottom: 155rpx;
				left: 50%;
				transform: translateX(-50%);

				.ppView {
					width: 111rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 700;
					height: 35rpx;
					font-size: 18rpx;
					color: #FFE9B4;
					line-height: 26rpx;
					text-align: center;
					position: absolute;
					bottom: 0;
					transform: translateX(-55rpx);
					background-image: url(@/static/map/paopao.png);
					background-size: contain;
					background-repeat: no-repeat;

				}
			}

			.userName2 {
				width: 168rpx;
				height: 41rpx;
				font-size: 28rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #FED1A6;
				line-height: 41rpx;
				position: absolute;
				left: 50%;
				top: 65rpx;
				transform: translateX(-50%);
				z-index: 2;
			}

			.line-box {
				width: 457rpx;
				height: 30rpx;
				background: #A51E07;
				box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(255, 223, 136, 0.28);
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				position: absolute;
				left: 50%;
				bottom: 112rpx;
				transform: translateX(-50%);

				.lineview {
					width: 50%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					background-image: url(@/static/map/lineline.png);
					background-size: 100% 100%;
					background-repeat: no-repeat;
				}
			}
		}

		.getBox {
			width: 750rpx;
			height: 402rpx;
			background: #22252F;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			opacity: 1;
			position: absolute;
			left: 0;
			bottom: 0;
			padding: 68rpx 0 78rpx 32rpx;
			box-sizing: border-box;

			.topline {
				width: 72rpx;
				height: 12rpx;
				background: #FFFFFF;
				border-radius: 14rpx 14rpx 14rpx 14rpx;
				position: absolute;
				left: 50%;
				top: 20rpx;
				transform: translateX(-50%);

			}

			.close-icon {
				width: 52rpx;
				height: 52rpx;
				position: absolute;
				right: 32rpx;
				top: 24rpx;
			}

			.hb-desc {
				display: flex;

				.avatar {
					width: 130rpx;
					height: 130rpx;
					border-radius: 50%;
					border: 5rpx solid #fff;
					margin-right: 24rpx;
				}

				.info {
					flex: 1;
					display: flex;
					flex-wrap: wrap;

					.item {
						width: 40%;
						height: 38rpx;
						font-size: 26rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 38rpx;

						.icon {
							width: 24rpx;
							height: 24rpx;
							margin-right: 8rpx;
						}

						text {
							height: 38rpx;
							font-size: 26rpx;
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 700;
							line-height: 38rpx;
							color: #fff;
						}

						.canTake {
							background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}
			}

			.btn {
				width: 686rpx;
				height: 74rpx;
				background: #767676;
				border-radius: 14rpx 14rpx 14rpx 14rpx;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 700;
				color: #FFFFFF;
				line-height: 46rpx;
				text-align: center;
				line-height: 74rpx;
				margin-top: 56rpx;
			}

			.btn.canTake {
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			}
		}

	}
</style>