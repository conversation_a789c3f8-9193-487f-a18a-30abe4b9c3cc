<template>
	<view class="appPage">
		<view class="card" v-for="(item,idx) in list" :key="idx">
			<view class="t_betweent">
				<view class="name">{{item.describe}}</view>
				<view class="price">{{item.amount}}</view>
			</view>
			<view class="time t_betweent">
				{{item.date_format}}
				<view class="t_betweent">
					<view class="detailTxt ">
						详情
					</view>
					<image @click="goNav(item)" v-if="item.envelop_uuid"
						style="height: 36rpx;width: 20rpx;" src="../../static/images/colorRight.png" mode=""></image>
				</view>
			</view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				pageFlag: true,
				list: []
			}
		},
		onLoad() {
			this.getData()
		},
		onReachBottom() {
			if (this.pageFlag) {
				this.page++
				this.getData()
			}
		},
		onPullDownRefresh() {
			this.page = 1
			this.list = []
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(item) {
				if (item.describe === '活动支出') {
					this.$common.navigateTo({
						url: `/pages/order/detail?orderNo=${item.envelop_uuid}`
					})
				}else if (item.describe === '活动退票' || item.describe === '活动收入') {
					this.$common.navigateTo({
						url:  `/pages/activity/details?id=${item.envelop_uuid}`
					})
				} else {
					this.$common.navigateTo({
						url: '/pages/purse/priceDetailHb?id='+item.envelop_uuid
					})
				}
			},
			getData() {
				this.$http.get('/api/user/wallet-log', {
					page: this.page,
				}).then(res => {
					this.pageFlag = !!res.message.length
					this.list.push(...res.message)
					console.log(res.message)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 0 32rpx;

		.card {
			margin-top: 32rpx;
			width: 686rpx;
			height: 172rpx;
			background: #2F3341;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			opacity: 1;
			padding: 32rpx;

			.time {
				margin-top: 24rpx;
				font-size: 26rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				color: #D9D9D9;
				line-height: 38rpx;

				.detailTxt {
					font-size: 26rpx;
					line-height: 36rpx;
					letter-spacing: 2px;
					color: #a00000;
					background: linear-gradient(2.5764389898695255deg, #4BC6ED 0%, #BC93F2 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					margin-right: 12rpx;
				}
			}

			.name {
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #ECECEC;
				line-height: 46rpx;
			}

			.price {
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 700;
				color: #ECECEC;
				line-height: 46rpx;
			}
		}
	}
</style>