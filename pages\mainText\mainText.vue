<template>
	<view class="">
		<view style="width: 750rpx;overflow: hidden;">
			<u-navbar back-text="" title="正文" :background="{backgroundColor: '#191C26'}" :border-bottom="false"
				title-color="#fff" back-icon-color="#fff">
				<view slot="right" style="margin-right: 34rpx;" @click="rightTap">
					<image src="../../static/images/moreIcon.png" style="width: 13rpx;height: 38rpx;" mode=""></image>
				</view>

			</u-navbar>
			<view class="" style="height: 20rpx;" />
			<Post :showTop="false" :list="[info]" :moreFlag="false" @follow="follow" :followFlag="false"
				:footerFlag="false" timeFormat="yyyy/M/DD hh:mm:ss"></Post>
			<view class="issue">
				共{{info.comment_total}}条评论
			</view>
			<Comment :dataArr="reviewArr" @getMore="getMore" @reply="reply" @setCommentLike="setCommentLike"></Comment>
			<view class="" style="height: 180rpx;"></view>
			<view class="head t_display">
				<view class="inputBg">
					<image class="img24" src="../../static/images/editInput.png" style="margin-right: 8rpx;" mode="">
					</image>
					<u-input v-model="username" type="text" placeholder="请输入评论" :border="false" confirm-type="send"
						@confirm="confirm" />
				</view>
				<view class="t_display"
					style="justify-content: space-between;width:300rpx;transform: translateX(18rpx);">
					<view class="t_display">
						<image class="img42" src="../../static/images/pinglunIssue.png" mode=""></image>
						<view class="nums">
							{{info.comment_total}}
						</view>
					</view>
					<view class="t_display" @click="setLike({momentId:info.moment_id,isLike:info.is_like})">
						<image class="img42" src="../../static/images/dianzan.png" mode="" v-if="!info.is_like"></image>
						<image class="img42" src="@/static/images/like2.png" mode="widthFix" v-else></image>
						<view class="nums">
							{{info.like}}
						</view>
					</view>
					<view class="t_display" @click="share">
						<image class="img42" src="../../static/images/zhanfa.png" mode=""></image>
						<view class="nums">
							{{info.share_total}}
						</view>
					</view>
				</view>
			</view>
			<t-loading text="加载中.." :mask="true" :click="true" :show="show" ref="loading"></t-loading>
			<uni-popup ref="replyPopup">
				<view class="heads t_display">
					<view class="inputBg">
						<image class="img24" src="../../static/images/editInput.png" style="margin-right: 8rpx;"
							mode="">
						</image>
						<u-input v-model="replyVal" :focus="true" type="text" :placeholder="placeholder" :border="false"
							confirm-type="send" @confirm="confirmReply" />
					</view>
				</view>
			</uni-popup>
			<MyMore ref="MyMore" @first="first" @del="delMoment"></MyMore>
			<MorePopup ref="morePopup" :popupInfo="info"></MorePopup>
			<sharePopup ref="share" :post="info"></sharePopup>
			<SeePopup ref="seePopup" @choose="seePopChoose"></SeePopup>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import Post from "@/components/post/post.vue"
	import Comment from "./components/comment.vue"
	import MyMore from "@/pages/my/components/morePopup.vue"
	import SeePopup from "@/pages/my/components/seeLook.vue"
	import MorePopup from "./components/more.vue"
	export default {
		components: {
			SeePopup,
			MyMore,
			MorePopup,
			Comment,
			Post
		},
		onLoad(options) {
			this.momentId = options.momentId
			this.getInfo()
			this.getReview()
			setTimeout(() => {
				this.show = false
			}, 800)
		},
		data() {
			return {
				show: true,
				replyVal: "",
				replyShow: false,
				replyFlag: false,
				reviewArr: [],
				info: {},
				momentId: "",
				placeholder: "",
				username: "",
			}
		},
		methods: {
			seePopChoose(role) {
				this.$http.post('/api/moment/update-visible', {
					momentId: this.momentId,
					role
				}).then(res => {
					this.toast('设置成功')
					this.$refs.seePopup.close()
				})
			},
			first() {
				this.$refs.MyMore.close()
				this.$refs.seePopup.open()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			delMoment() {
				this.$http.post('/api/moment/del', {
					momentId: this.momentId
				}).then(res => {
					this.toast('删除成功')
					uni.$emit('filterMain', {
						momentId: this.momentId
					})
					uni.navigateBack()
				})
			},
			follow(item) { //"关注","已关注","互相关注"
				console.log("item:", item);
				let url = '';
				const {
					relation,
					uuid
				} = item
				if (relation == 0) {
					url = "/api/user/follow/add"
				} else if (relation == 1) {
					url = "/api/user/follow/del"
				}
				this.$http.post(url, {
					uuid: item.user_info.uuid
				}).then(res => {
					this.getInfo()
				})
			},
			confirmReply(val) {
				const index = this.replyItem.index
				this.$http.post('/api/moment/reply', {
					"commentId": this.replyItem.commentId,
					"momentId": this.replyItem.momentId,
					"content": val,
					"to_uid": this.replyItem.uid,
				}).then(res => {
					this.$set(this.reviewArr, index, {
						...this.reviewArr[index],
						little_reply: [res.message, ...this.reviewArr[index].little_reply]
					})
					this.getInfo()
					this.replyVal = ""
					this.$refs.replyPopup.close()
				})
			},
			reply(item) {
				console.log("item:", item);
				this.placeholder = '回复' + item.nickname
				this.replyItem = item
				this.$refs.replyPopup.open('bottom')
			},

			confirm(val) {
				this.$http.post('/api/moment/comment', {
					"momentId": this.momentId,
					"content": val
				}).then(res => {
					this.reviewArr.unshift(res.message)
					this.username = ""
					this.getInfo()
				})
			},
			setCommentLike(item) {
				this.$http.post('/api/comment/like', {
					commentId: item.comment_id,
					like: item.isLike ? 2 : 1
				}).then(res => {
					console.log(item);
					this.$set(this.reviewArr, item.index, {
						...this.reviewArr[item.index],
						is_like: !item.isLike,
						like: this.reviewArr[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			setLike(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					like: item.isLike ? 2 : 1
				}).then(res => {
					this.info.is_like = !item.isLike
					this.info.like += !item.isLike ? 1 : -1
				})
			},
			getMore(item) {
				const index = item.index
				this.$http.get('/api/reply/list', {
					commentId: item.commentId,
					maxId: item.maxId || ""
				}).then(res => {
					this.$set(this.reviewArr, index, {
						...this.reviewArr[index],
						little_reply: [
							...this.reviewArr[index].little_reply,
							...res.message
						]
					})
					// this.info = res.message
				})
			},
			getInfo() {
				this.$http.get('/api/moment/detail', {
					momentId: this.momentId,
				}).then(res => {
					this.info = res.message
				})
			},
			getReview() {
				this.$http.get('/api/comment/list', {
					momentId: this.momentId,
					page: 1,
					size: 10,
				}).then(res => {
					this.reviewArr = res.message
				})
			},
			share() {
				this.$refs.share.open()
			},
			rightTap() {
				const userInfo = uni.getStorageSync('userInfo')
				if (this.info.user_info.uuid == userInfo.uuid) {
					this.$refs.MyMore.open()
				} else {
					this.$refs.morePopup.open()
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.heads {
		width: 100%;
		height: 154rpx;
		background: #191C26;
		display: flex;
		padding: 24rpx 32rpx;
		align-items: center;

		.nums {
			font-size: 24rpx;
			font-family: DINPro-Medium, DINPro;
			font-weight: 500;
			color: #FFFFFF;
			margin-left: 8rpx;
		}

		.search {
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			width: 100%;
			// min-width: 420rpx;
			height: 62rpx;
			color: #fff;
			background: #323232;
			padding: 0 24rpx;
			display: flex;
			align-items: center;
			border-radius: 14rpx;
		}
	}

	.head {
		width: 100%;
		height: 154rpx;
		background: #191C26;
		position: fixed;
		bottom: 0;
		display: flex;
		padding: 24rpx 32rpx;

		// align-items: center;
		.nums {
			font-size: 24rpx;
			font-family: DINPro-Medium, DINPro;
			font-weight: 500;
			color: #FFFFFF;
			margin-left: 8rpx;
		}

		.search {
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			width: 400rpx;
			min-width: 420rpx;
			height: 62rpx;

			color: #fff;
			background: #323232;
			padding: 0 24rpx;
			display: flex;
			align-items: center;
			border-radius: 14rpx;
		}
	}

	.issue {
		padding: 19rpx 32rpx;
		font-size: 24rpx;
		font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.6);
	}
</style>