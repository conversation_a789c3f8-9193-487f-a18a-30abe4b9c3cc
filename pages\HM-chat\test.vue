<template>
	<view>lwy
		<button @click="sendText">sendText(to thl)</button>
		<button @click="sendImageMsg">sendImageMsg(to thl)</button>
		<button @click="sendGeoLocationMsg">sendGeoLocationMsg(to thl)</button>

	</view>
</template>

<script>
	import {
		apiLocationMe
	} from '@/api/common.js'
	export default {
		data() {
			return {
				Yxim: null
			}
		},
		onLoad(option) {
			this.$store.dispatch('SET_Yxim')
			this.Yxim = this.$store.state._Yxim
			this.$store.commit('SET_p2pSessionId', '')
			console.log(this.Yxim, ';;;;;;this.Yxim', this.$store.state._Yxim);
		},
		
		methods: {
			onMsg(msg) {
				switch (msg.type) {
					case 'custom':
						/**
						 * 收到自定义消息，用户d根据消息内容处理
						 */
						break;
					case 'notification':
						/**
						 * 收到群通知消息，用户根据群通知的消息进行进一步处理
						 */
						break;
						// 其它case
					default:
						break;
				}
			},
			// 发送地理位置
			sendGeoLocationMsg() {
				apiLocationMe().then(async res => {
					const result = await this.$Yxim.msg.sendGeoLocationMsg({
						scene: 'p2p', //消息的场景  "p2p" | "team" | "superTeam"
						to: '0panfibwyj', //接收人
						attach: {
							lat: res.message.latitude,
							lng: res.message.longitude,
							title:'我的位置'
						}, //发送得文本消息
					});
				})
			
			},
			async sendImageMsg() {
				uni.chooseImage({
					success: async (res) => {
						const file = res.tempFilePaths[0]
						const result = await this.$Yxim.msg.sendImageMsg({
							scene: 'p2p', //消息的场景  "p2p" | "team" | "superTeam"
							to: '0panfibwyj', //接收人
							filePath: file, //发送得文本消息\
							// body:'1',
							onUploadDone: (res) => {
								console.log(res, ';;;;;;;;;;');
							}
						});
					}
				})

			},

			async sendText() {
				console.log(this.$Yxim, '//////////');
				this.$Yxim.msg.sendTextMsg({
					scene: 'p2p', //消息的场景  "p2p" | "team" | "superTeam"
					to: '7r43w6fv1art7g', //接收人
					body: 'hello1111' + new Date(), //发送得文本消息
					onSendBefore: (error, msg) => {
						console.log(error, msg);
						// console.log('发送' + msg.scene + ' ' + msg.type + '消息' + (!error ? '成功' : '失败') +
						// 	', id=' + msg
						// 	.idClient);
						// pushMsg(msg);
					}
				});

			},
			getLocalMsgs() {
				this.Yxim.getLocalMsgs({
					limit: 100,
					done: (error, obj) => {
						console.log('获取本地消息' + (!error ? '成功' : '失败'), error, obj)
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
</style>