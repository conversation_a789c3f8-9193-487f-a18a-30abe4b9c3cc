<template>
	<view class="">
		<map :latitude="latitude" :longitude="longitude" :scale="16" :markers="markers"
			style="width: 100%; height: 100vh; border-radius: 15px;"></map>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				latitude: '',
				longitude: '',
				markers: [],
			}
		},
		onLoad(opthion) {
			this.getData(opthion.uuid)
			this.getUsers(opthion.uuid)
		},
		methods: {
			getData(uuid) {
				this.$http.get('/activity/detail', {
					uuid
				}).then(res => {
					let coordinate = res.message.activity.coordinate.split(',')
					this.latitude = coordinate[1]
					this.longitude = coordinate[0]
					let iconPath = '../../static/active/<EMAIL>'
					this.markers = [{
						iconPath, // 标记点图标路径
						id: 0, // 标记点ID
						latitude: this.latitude, // 标记点纬度
						longitude: this.longitude, // 标记点经度
						width: 100, // 图标的宽度
						height: 100, // 图标的高度
						zIndex: 1, // 层级顺序
					}]
				})
			},
			getUsers(uuid) {
				this.$http.get('/activity/map-activity-users', {
					uuid
				}).then(res => {
					let item = res.message
					console.log(item);
					for (let i = 0; i < item.length; i++) {
						uni.downloadFile({
							url: item[i].avatar,
							success: (res) => {
								if (res.statusCode === 200) {
									// 下载成功后，使用uni.saveFile保存到本地
									uni.saveFile({
										tempFilePath: res.tempFilePath,
										success: (saveRes) => {
											// saveRes.savedFilePath是保存后的本地文件路径
											console.log('图片保存成功，本地文件路径:', saveRes
												.savedFilePath);
											item[i].iconPath = saveRes
												.savedFilePath
											item[i].id = i + 1
											item[i].width = 100
											item[i].height = 100
											item[i].zIndex = 1
											// 现在你可以使用saveRes.savedFilePath作为本地文件路径使用
											// 例如，设置为map组件markers的iconPath属性
											this.markers.push(...item);
										},
										fail: (err) => {
											console.error('保存图片失败:', err);
										}
									});
								} else {
									console.error('下载图片失败，状态码:', res.statusCode);
								}
							},
							fail: (err) => {
								console.error('下载图片失败:', err);
							}
						});

					}
					// Promise.all(item.map(async (item2) => {
					// 	let imgInfo = await this.$u.imageToTempFilePath(item2
					// 		.iconPath); // 将网络图片转换为临时文件路径
					// 	item2.iconPath = imgInfo.tempFilePath; // 更新图标路径
					// })).then(() => {
					// 	console.log(item);
					// 	this.markers.push(...item);
					// });
					console.log(this.markers);
				})
			},
		}
	}
</script>

<style>
</style>