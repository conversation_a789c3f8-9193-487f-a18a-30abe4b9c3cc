<template>
	<view class="appPage">
		<view class="btn t_display">
			<view class="btn_item" v-for="(item,index) in btnArr"
				:style="{background:currentIdx==index ?'linear-gradient(135deg, #4CC7EE 0%, #BF96F5 100%)':'#272727'}"
				@click="currentIdx = index" :key="index">
				<image class="btn_img" :src="item.img" mode=""></image>
				<view class="">
					{{item.text}}
				</view>
			</view>
		</view>
		<view class="title">
			请描述具体问题
		</view>
		<view class="inputTextareaBg">
			<textarea v-model="form.content" placeholder-style="color: rgba(255,255,255,0.52)" maxlength="1000"
				placeholder="尽量详细描述您遇到的问题和现象，具体位置及表 现形式等" />
		</view>
		<view class="title">
			图片补充:（相关图片或视频能帮助您解决问题哦）
		</view>
		<scroll-view class="scroll-view_H" scroll-x="true">
			<image @click="upload" src="../../static/images/addBig.png" class="scroll-view-item_H" mode="aspectFit">
			</image>
			<view class="scroll-view-item_H" v-for="(src,index) in form.images" :key="index">
				<image class="img" :src="src" mode="">
				</image>
				<image class="close" @click="delImg(index)" src="../../static/images/close.png" mode="">
				</image>
			</view>
		</scroll-view>
		<view @click="submits(currentIdx)">
			<cusButton txt="提交" style="margin-top: 208rpx;" />
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIdx: 0,
				btnArr: [{
					img: "../../static/images/my.png",
					text: "功能询问"
				}, {
					img: "../../static/images/my.png",
					text: "产品故障"
				}, {
					img: "../../static/images/my.png",
					text: "性能问题"
				}, {
					img: "../../static/images/my.png",
					text: "意见建议"
				}, ],
				form: {
					"feed_type": 1,
					"content": "",
					"images": []
				}
			}
		},
		methods: {
			delImg(idx) {
				this.form.images.splice(idx, 1)
			},
			upload() {
				uni.chooseImage({
					count: 6,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							this.form.images.push(img)
						}, ))

					}
				});
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			submits(idx) {
				this.form.feed_type = idx + 1
				this.$http.post('/api/feedback/post', this.form).then(res => {
					this.toast('意见反馈成功')
					setTimeout(() => {
						uni.navigateBack()
					}, 500)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 44rpx 32rpx;

		.close {
			width: 32rpx;
			height: 32rpx;
			position: absolute;
			transform: translate(-40rpx, 5rpx);
		}

		.scroll-view_H {
			margin-top: 12rpx;
			white-space: nowrap;
			width: 100%;
		}

		.scroll-view-item_H {
			display: inline-block;
			width: 153rpx;
			height: 153rpx;
			text-align: center;
			border-radius: 14rpx;
			background-color: #777883;
			margin-right: 16rpx;
			position: relative;

			.img {
				width: 153rpx;
				height: 153rpx;
				border-radius: 14rpx;
			}
		}

		.inputTextareaBg {
			color: #fff;
			background: #201F1F;
			height: 500rpx;
			border-radius: 16rpx;
			padding: 32rpx 24rpx;
			font-size: 28rpx;
		}

		.title {
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			margin: 52rpx 0 28rpx 0;
		}

		.btn {
			justify-content: space-between;

			.btn_item {
				width: 144rpx;
				height: 144rpx;
				background: #272727;
				border-radius: 16rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				font-size: 28rpx;

				.btn_img {
					width: 50rpx;
					height: 50rpx;
					margin-bottom: 12rpx;
				}
			}
		}
	}
</style>