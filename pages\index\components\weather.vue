<template>
	<view class="">
		<view class="tianqi">
			<view class="temperature" v-if="temperature && district" style="">
				<div class="weatherLogo">
					<image mode="widthFix" v-if="weather=='qing'" src="@/static/images/index/qing.png" class="weather">
					</image>
					<image mode="widthFix" v-else-if="weather=='yu'" src="@/static/images/index/yu.png" class="weather">
					</image>
					<image mode="widthFix" v-else-if="weather=='xue'" src="@/static/images/index/yin.png"
						class="weather">
					</image>
				</div>
				<div class="num">{{ temperature }}</div>
			</view>
			<view class="district" v-if="district">
				{{ district&&district.length>5?district.slice(0,5):district }}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			temperature: {
				default: '0℃'
			},
			weather: {
				default: 'qing'
			},
			district: {
				default: ''
			}
		},
		data() {
			return {

			}
		}
	}
</script>

<style scoped lang="scss">
	.temperature {
		font-weight: 600;
		font-size: 20rpx;
		color: #FFFFFF;

		.num {
			width: 74rpx;
			height: 74rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
		}

		.weatherLogo {
			position: absolute;

			.weather {
				width: 74rpx;
				height: 74rpx;
			}
		}
	}

	.district {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 80rpx;
		width: 208rpx;
		height: 74rpx;
		background: rgba(63, 63, 63, 0.38);
		border-radius: 14rpx 14rpx 14rpx 14rpx;
		opacity: 1;
		font-size: 32rpx;
		font-family: PingFang SC-Medium, PingFang SC;
		color: #ffffff;
		line-height: 45rpx;
	}
</style>