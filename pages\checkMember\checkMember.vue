<template>
	<view class="appPage">
		<u-navbar :is-back="false" title="选择成员" :background="{ background: '#191C26' }" back-icon-color="#fff"
			title-color="#fff" is-back :border-bottom="false">
			<template class="" slot="right">
				<view class="confirm" @click="rightConfirm">确认</view>
			</template>
		</u-navbar>

		<scroll-view @scrolltolower="scrolltolower" @refresherrefresh="onRefresh" :scroll-y="true"
			refresher-enabled="true" :refresher-triggered="triggered" refresher-background="#191C26"
			style="height: 100vh">
			<view class="t_display" style="margin-top: 30rpx">
				<view class="Cinpout t_display">
					<uni-icons type="search" color="#A2A2A4" size="16"></uni-icons>
					<u-input v-model="value" placeholder="搜索对方id或手机号" type="text" clearable />
				</view>
				<view class="search" @click="search"> 搜索 </view>
			</view>
			<view :key="date">
				<view class="item_group" v-for="(item, index) in arr" :key="index"
					style="justify-content: space-between">
					<view class="t_display">
						<radio :checked="getFlag(item.uid)" :value="item.uid.toString()" @click="setIds(item)"
							style="margin-right: 30rpx">
						</radio>
						<image style="width: 102rpx; height: 102rpx; border-radius: 50%" :src="item.avatar"
							mode="aspectFill">
						</image>
						<view class="name" style="margin-left: 10px;">
							{{ item.nickname }}
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="img24"></view>
		<u-toast ref="notify" />
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirm" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgCurrent: 0,
				triggered: false,
				arr: [],
				currentId: '',
				idsArr: [],
				infoArr: [],
				val: true,
				value: '',
				statusBarHeight: 0,
				page: 1,
				checkFlag: false,
				count: 0,
				date: '1',
				mode: '', //1模糊位置 2冻结位置 3可见范围
			}
		},
		onUnload() {},
		onLoad(options) {
			this.getData()
			if (options.mode) {
				this.mode = parseInt(options.mode)
				if ([1].includes(this.mode)) {
					this.getIds()
				} else if (this.mode === 3 && options.uids) {
					this.idsArr = options.uids.split(',')
				}
			}
			// const system = uni.getStorageSync('system')
			// this.statusBarHeight = JSON.parse(system).statusBarHeight + 20

		},
		watch: {
			idsArr: {
				handler() {
					if ([1, 2].includes(parseInt(this.mode))) {
						this.$http
							.post('/location/ghost/switch', {
								mode: parseInt(this.mode),
								uids: this.idsArr,
							})
							.then((res) => {
								if (!res.message) {
									this.imgCurrent = 2
									this.$refs.vipTips.open()
									const index = this.idsArr.indexOf(this.currentId)
									this.idsArr.splice(index, 1)
									this.infoArr.splice(index, 1)
								}
							})
					}
				},
			},
		},
		methods: {
			confirmRight() {
				if ([1, 2].includes(parseInt(this.mode))) {
					this.$http
						.post('/location/ghost/switch', {
							mode: parseInt(this.mode),
							uids: this.idsArr,
						})
						.then((res) => {
							if (!res.message) {
								this.imgCurrent = 2
								this.$refs.vipTips.open()
							}
						})
				}
			},
			rightConfirm() {
				uni.$emit('infoArr', this.infoArr)
				if ([3, 4].includes(this.mode) || this.mode == 'createGroup') {
					setTimeout(() => {
						uni.$emit('infoArr', this.infoArr)
					}, 500)
				}
				uni.navigateBack()
				// else if ([1, 2].includes(parseInt(this.mode))) {
				// this.$http
				// 	.post('/location/ghost/switch', {
				// 		mode: parseInt(this.mode),
				// 		uids: this.idsArr,
				// 	})
				// 	.then((res) => {
				// 		if (!res.message) {
				// 			this.imgCurrent = 2
				// 			this.$refs.vipTips.open()
				// 		} else {
				// 			uni.navigateBack()
				// 		}
				// 	})
				// }

			},
			confirm() {
				uni.navigateTo({
					url: '/pages/vipCenter/vipCenter',
				})
			},
			getFlag(id) {
				return this.idsArr.includes(id)
			},
			setIds(item) {
				this.currentId = item.uid
				if (!this.idsArr.includes(item.uid)) {
					this.infoArr.push(item)
					this.idsArr.push(item.uid)
				} else {
					const index = this.idsArr.indexOf(item.uid)
					this.idsArr.splice(index, 1)
					this.infoArr.splice(index, 1)
				}
			},
			scrolltoupper() {
				this.page = 1
				this.arr = []
				this.getData()
			},
			scrolltolower() {
				if (this.count != 0) {
					this.page++
					this.getData()
				}
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: 'top',
				})
			},
			onRefresh() {
				this.triggered = true
				this.page = 1
				this.arr = []
				this.getData()
				setTimeout(() => {
					this.triggered = false
				}, 1000)
			},
			search() {
				this.page = 1
				this.arr = []
				this.getData()
			},
			getData() {
				this.$http
					.get('/api/user/friend/get', {
						page: this.page,
						keyword: this.value,
					})
					.then((res) => {
						let that = this
						this.arr.push(...res.message.list)
						this.count = res.message.list.length
						setTimeout(() => {
							this.triggered = false
						}, 1000)
					})
			},
			getIds() {
				this.$http
					.get('/location/ghost/get', {
						mode: this.mode,
					})
					.then((res) => {
						this.idsArr = res.message.map((item) => {
							return item.uid
						})
					})
			},
			goBack() {
				uni.navigateBack()
			},
			goNav(url) {
				uni.navigateTo({
					url,
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	/deep/ .uni-radio-input {
		background: transparent;
	}

	.appPage {
		padding: 0 30rpx;

		.confirm {
			background: linear-gradient(#4bc6ed, #bc93f2);
			width: 130rpx;
			height: 56rpx;
			line-height: 50rpx;
			text-align: center;
			border-radius: 16rpx;
			margin-right: 32rpx;
		}

		.item_group {
			margin-top: 56rpx;
			display: flex;
			align-items: center;

			.declined {
				width: 126rpx;
				height: 56rpx;
				border-radius: 16rpx;
				opacity: 1;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #767676;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #767676;
			}

			.yes {
				width: 126rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #ffffff;
				background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
				margin-left: 16rpx;
			}

			.no {
				font-size: 22rpx;
				font-weight: 400;
				text-align: center;
				width: 126rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid transparent;
				border-radius: 16rpx;
				background-clip: padding-box, border-box;
				background-origin: padding-box, border-box;
				background-image: linear-gradient(to right, #333, #333),
					linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
			}

			.outer_circle {
				position: relative;
				margin: 50px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: #ffffff;
			}

			.inner_circle {
				background-image: linear-gradient(to bottom,
						rgb(123, 93, 255) 0%,
						rgb(56, 225, 255) 100%);
				content: '';
				position: absolute;
				top: -20px;
				bottom: -20px;
				right: -20px;
				left: -20px;
				z-index: -1;
				border-radius: inherit;
			}

			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
				}

				.name {
					font-size: 26rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #ffffff;
				}
			}

			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
			}
		}

		.title {
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #a2a2a4;
			margin-top: 52rpx;
			margin-bottom: 60rpx;
		}

		.txt {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #ffffff;
			margin-left: 28rpx;
		}

		.search {
			margin-left: 24rpx;
			font-size: 26rpx;
			font-family: Source Han Sans-Medium, Source Han Sans;
			font-weight: 500;
			color: #ffffff;
		}

		.Cinpout {
			flex: 1;
			height: 80rpx;
			background: #272727;
			border-radius: 32rpx;
			padding: 0 52rpx;
		}
	}
</style>