<template>
	<view class="appPage">
		<view style="height: 100rpx;" />
		<image class="bg" :src="form.cover" mode="aspectFill" />
		<scroll-view scroll-y="true" class="sColumn">
			<view :style="{height:statusBarHeight+'rpx'}"></view>
			<view style="height: 250rpx;" />
			<view class="head" style="transform: translateY(-250rpx);">
				<view>
					<uni-icons v-show="true" type="back" size="24" color="#fff" @click="back"></uni-icons>
				</view>
				<view class="rightBtn" style="display: flex;" @click="editCover">
					<image class="rightIcon" src="../../static/images/editCover.png" mode=""
						style="margin-right: 15rpx;">
					</image>
					<view class="txt">
						更换封面
					</view>
				</view>
			</view>
			<view class="body">
				<view style="transform: translateY(-50rpx);padding: 0 32rpx;">
					<view class="ava">
						<image class="avatar" :src="form.avatar" mode="aspectFill">
						</image>
						<image class="camera" @click="setAvatar" src="../../static/images/camera.png" mode=""></image>
					</view>
					<view class="title">账号信息</view>
					<view class="inputBg2">
						<view class="leftTitle">
							昵称
						</view>
						<input v-model="form.nickname" type="text" maxlength="6" placeholder="请输入昵称" :border="false"
							clearable />
					</view>
					<view class="title">基本资料</view>
					<picker :value="form.sex-1" :range="sexList" @change="setSex">
						<view class="inputBg" @click="sexShow = true">
							<view class="leftTitle">
								性别
							</view>
							<view class="rightTitle">{{sexList[form.sex-1] }}</view>
							<uni-icons type="right" color="rgba(255, 255, 255, 0.64)" />
						</view>
					</picker>
					<!-- <picker mode="date" @change="setBirthday"> -->
					<view class="inputBg" @click="showPicker = true">
						<view class="leftTitle">
							生日
						</view>
						<view class="rightTitle">{{form.birthday}}</view>
						<uni-icons type="right" color="rgba(255, 255, 255, 0.64)" />
					</view>
					<!-- </picker> -->
					<u-picker mode="time" v-model="showPicker" :params="params" @confirm="setBirthday"></u-picker>

					<view class="inputBg">
						<view class="leftTitle">
							位置
						</view>
						<view class="rightTitle" @click="regionShow = true">{{form.hometown}}</view>
						<u-picker style="margin-bottom: 200rpx;" mode="region" v-model="regionShow"
							@confirm="setSddress"></u-picker>
						<uni-icons type="right" color="rgba(255, 255, 255, 0.64)" @click="regionShow = true" />
					</view>
					<view class="title">个人介绍</view>
					<view class="inputBg3">
						<textarea v-model="form.introduction" placeholder="请输入个人介绍" :height="150" :auto-height="false"
							maxlength="150" />
					</view>

				</view>
				<view class="" style="background-color: #191C26;width: 750rpx;height: 200rpx;"></view>
			</view>
		</scroll-view>
		<view class="submit-btn">
			<view class="submit" @click="submit()" style="background:linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)">
				完成
			</view>
		</view>

		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		config
	} from "@/config.js";
	// import UniAddressSelector from '@/components/UniAddressSelector/components/UniAddressSelector.vue';
	export default {
		components: {
			// UniAddressSelector
		},
		data() {
			return {
				params: {
					year: true,
					month: true,
					day: true,
					hour: false,
					minute: false,
					second: false
				},
				showPicker: false,
				areaDate: [],
				regionShow: false,
				sexShow: false,
				ImgUrl: 'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/',
				username: '',
				sexIndex: 0,
				sexList: ["男", "女"],
				form: {
					nickname: "123",
					sex: '请选择性别',
					birthday: '请选择生日',
					hometown: "请选择位置",
				},
				info: {
					user: {
						ext: {
							birthday: ""
						}
					},
					ext: {},
					relation: {}
				},
				statusBarHeight: 80,
				imgArr: [],
				uploadArr: [],
				list: [{
					name: '动态'
				}, {
					name: '喜欢'
				}],
				current: 0
			}
		},
		async onLoad() {
			const system = uni.getStorageSync('system')
			// this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
			this.$http.get('/api/user/info').then(res => {
				const data = res.message
				this.form = {
					"nickname": data.user_info.nickname,
					"avatar": data.user_info.avatar,
					"sex": data.user_info.ext.sex,
					"birthday": data.user_info.ext.birthday,
					"introduction": data.user_info.ext.introduction,
					"cover": data.user_info.ext.cover,
					"hometown": data.user_info.ext.hometown
				}
			})

		},
		async onShow() {
			uni.$on('crop', async (src) => {
				await Promise.all([src].map(async (item) => {
					const img = await this.$common.uploads(item, {
						type: 4
					})
					this.form.avatar = img
				}, ))
			})
		},
		onUnload() {
			uni.$off()
		},
		methods: {
			setAvatar() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.navigateTo({
							url: "/pages/tools/AvatarCropper/AvatarCropper?avatar=" + tempFilePaths
						})

					}
				});
			},
			submit() {
				this.$http.post('/api/user/update', this.form).then(res => {
					this.$http.get('/api/user/info').then(res => {
						const user_info = res.message.user_info
						uni.setStorageSync('userInfo', user_info)
						uni.navigateBack()

					})

				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			setSddress(val) {
				let str = ""
				Object.keys(val).map(item => {
					str += val[item].label
				})
				this.form.hometown = str
			},
			setBirthday(val) {
				this.form.birthday = val.year + "-" + val.month + "-" + val.day
			},
			editCover() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							this.form.cover = img
						}, ))
					}
				});
			},
			setSex(idx) {
				console.log(idx.detail.value);
				this.form.sex = idx.detail.value + 1
			},
			back() {
				uni.navigateBack()
			},
			getPhoto() {
				this.$http.get('/api/user/get-photo', {
					uid: this.info.user_info.uid
				}).then(res => {
					this.imgArr = res.data.message
				})
			},
			goNav(url) {
				this.navigateTo({
					url
				})
			},
			upload() {
				uni.chooseImage({
					count: 6,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						tempFilePaths.map(item => {
							this.$common.uploads(item, {
								type: 4
							}).then(res => {
								this.getPhoto()
							})
						})
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.u-picker {
		color: #000;
	}

	.submit-btn {
		width: 100vw;
		padding: 10px 20px;
		background-color: #191C26;
		position: fixed;
		bottom: 0;
	}

	.submit {
		margin-bottom: 30rpx;
		height: 94rpx;
		border-radius: 14rpx;
		font-size: 32rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #FFFFFF;
		text-align: center;
		line-height: 94rpx;

	}

	.title {
		font-size: 32rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 46rpx;
		margin: 45rpx 0 24rpx 24rpx;
	}

	.scroll-view_H {
		margin-top: 12rpx;
		white-space: nowrap;
		width: 100%;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 153rpx;
		height: 153rpx;
		text-align: center;
		border-radius: 14rpx;
		background-color: #777883;
		margin-right: 16rpx;
	}

	.icon {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}

	.appPage {
		height: 100vh;
		width: 100%;
		display: flex;
		flex-direction: column;

		.picture {
			width: 100%;
			white-space: nowrap;
			margin-top: 12rpx;
			height: 153rpx;
		}

		.bg {
			width: 750rpx;
			height: 495rpx;
			position: absolute;
		}

		/* 利用flex布局, */
		.sColumn {
			display: flex;
			flex: 1;
		}

		.body {
			display: flex;
			flex: 1;
			width: 100%;
			margin-top: 71rpx;
			background-color: #191C26;
			flex-direction: column;

			.inputBg3 {
				padding: 24rpx;
				color: #fff;
				background: #22252F;
				display: flex;
				border-radius: 16rpx;
				font-size: 26rpx;
				margin-bottom: 42rpx;
			}

			.inputBg2 {
				color: #fff;
				background: #22252F;
				height: 97rpx;
				display: flex;
				align-items: center;
				border-radius: 16rpx;
				padding: 0 24rpx;
				font-size: 26rpx;
				margin-bottom: 42rpx;

				.leftTitle {
					width: 60rpx;
					font-size: 29rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: rgba(255, 255, 255, 0.64);
					margin-right: 120rpx;
				}

				.rightTitle {
					font-size: 29rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
				}
			}

			.inputBg {
				color: #fff;
				background: #22252F;
				height: 97rpx;
				display: flex;
				align-items: center;
				border-radius: 16rpx;
				padding: 0 24rpx;
				font-size: 26rpx;
				margin-bottom: 42rpx;


				.leftTitle {
					font-size: 29rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: rgba(255, 255, 255, 0.64);
					// margin-right: 120rpx;
				}

				.rightTitle {
					text-align: center;
					width: 84%;
					text-align: start;
					padding-left: 120rpx;
					font-size: 29rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					// margin-right: 120rpx;
				}
			}

			.texa {
				padding: 24rpx;
			}

			.addImg {
				width: 154rpx;
				height: 154rpx;
				background: #777883;
				border-radius: 14rpx;
				display: inline-block;
				margin-right: 16rpx;
			}

			.introduce {
				margin-left: 10rpx;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 25rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.64);
				line-height: 40rpx;
			}

			.idInfo {
				font-size: 26rpx;
				font-family: Source Han Sans-Medium, Source Han Sans;
				font-weight: 500;
				color: #767676;
				line-height: 40rpx;
				margin-top: 8rpx;
			}

			.name {
				font-size: 48rpx;
				font-family: Source Han Sans-Bold, Source Han Sans;
				font-weight: 700;
				color: #FFFFFF;
				margin-top: 23rpx;
			}

			.ava {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;
				position: relative;

				.camera {
					width: 80rpx;
					height: 80rpx;
					position: absolute;
					bottom: 0;
					left: 130rpx;
				}

				.avatar {
					width: 173rpx;
					height: 173rpx;
					border-radius: 50%;
					border: 8rpx solid #191C26;
				}

				.edit {
					width: 56rpx;
					height: 56rpx;
				}
			}
		}

		.head {
			height: 100rpx;
			padding: 0 34rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.rightBtn {
			width: 180rpx;
			height: 68rpx;
			display: flex;
			align-items: center;
			background: rgba(0, 0, 0, 0.34);
			border-radius: 30rpx;
			padding-left: 20rpx;
			padding-right: 10rpx;

			.txt {
				font-size: 22rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 40rpx;
				transform: translateX(-15rpx);
				white-space: nowrap;
				margin-left: 10rpx;
			}


			.rightIcon {
				width: 38rpx;
				height: 38rpx;
			}
		}

	}
</style>