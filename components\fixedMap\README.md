# 高德地图组件 (FixedMap)

## 概述

这是一个基于高德地图 1.4.15 API 的 renderjs 地图组件，支持 3D 视图、缩放、旋转等高级功能，并具有良好的兼容性。

## 主要特性

### 🌟 高德地图 2.0 新特性

- **3D 视图模式**: 支持立体地图显示
- **俯仰角控制**: 可调整地图倾斜角度 (0-83 度)
- **旋转功能**: 支持地图旋转操作
- **增强缩放**: 扩展的缩放级别范围 (3-20 级)
- **流畅动画**: 优化的地图动画效果
- **自定义样式**: 使用项目专属地图样式 `amap://styles/48edbfe517aa7c40d462ffd134bb5289`

### 👤 用户头像标记

- **圆形头像**: 自动将用户头像显示为圆形标记
- **边框效果**: 白色边框和阴影效果
- **容错处理**: 头像加载失败时自动回退到默认图标
- **动态切换**: 支持头像和默认图标之间的切换

### 🎮 交互功能

- ✅ 拖拽移动
- ✅ 滚轮缩放
- ✅ 双击缩放
- ✅ 键盘操作
- ✅ 触摸手势
- ✅ 惯性移动

### 🎨 视觉效果

- 标准地图样式
- 自定义标记支持
- 平滑的视图切换
- 响应式布局

## 使用方法

### 基本用法

```vue
<template>
  <FixedMap
    :width="'100%'"
    :height="'400rpx'"
    :latitude="39.9042"
    :longitude="116.4074"
    :markers="markers"
  />
</template>

<script>
import FixedMap from "@/components/fixedMap/fixedMap.vue";

export default {
  components: { FixedMap },
  data() {
    return {
      markers: [
        {
          id: 1,
          latitude: 39.9042,
          longitude: 116.4074,
          iconPath: "/static/images/location.png",
          width: 30,
          height: 30,
          title: "位置标记",
        },
      ],
    };
  },
};
</script>
```

### 属性说明

| 属性名    | 类型   | 默认值   | 说明     |
| --------- | ------ | -------- | -------- |
| width     | String | '100%'   | 地图宽度 |
| height    | String | '300rpx' | 地图高度 |
| latitude  | Number | 39.9042  | 纬度     |
| longitude | Number | 116.4074 | 经度     |
| markers   | Array  | []       | 标记数组 |

### 标记对象结构

```javascript
{
  id: 1,                    // 标记ID
  latitude: 39.9042,        // 纬度
  longitude: 116.4074,      // 经度
  iconPath: '/path/to/icon.png', // 图标路径
  width: 30,                // 图标宽度
  height: 30,               // 图标高度
  title: '标记标题'          // 标记标题
}
```

## 技术实现

### API 版本

- 高德地图 API 2.0
- 使用 renderjs 技术实现

### 核心配置

```javascript
{
  viewMode: "3D",           // 3D视图模式
  pitch: 50,                // 俯仰角度
  rotation: 0,              // 旋转角度
  zoom: 15,                 // 缩放级别
  zooms: [3, 20],          // 缩放范围
  dragEnable: true,         // 启用拖拽
  zoomEnable: true,         // 启用缩放
  rotateEnable: true,       // 启用旋转
  pitchEnable: true,        // 启用倾斜
  scrollWheel: true,        // 启用滚轮
  doubleClickZoom: true,    // 启用双击缩放
  keyboardEnable: true,     // 启用键盘
  animateEnable: true       // 启用动画
}
```

## 与 1.4 版本的区别

### 性能提升

- 更快的渲染速度
- 更流畅的动画效果
- 更好的内存管理

### 功能增强

- 更丰富的 3D 效果
- 更精确的定位
- 更好的交互体验

### API 改进

- 简化的标记创建方式
- 批量操作支持
- 更好的错误处理

## 测试页面

访问 `pages/test/mapTest` 可以测试地图的各种功能。

## 注意事项

1. 需要有效的高德地图 API 密钥
2. 仅支持 H5 平台的 renderjs 功能
3. 建议在真机上测试 3D 效果
4. 确保网络连接正常以加载地图资源
