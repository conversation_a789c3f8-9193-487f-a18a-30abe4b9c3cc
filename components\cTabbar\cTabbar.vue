<template>
	<view class="">
		12321312
		<view class="tab_bar">
			<view class="tabbarBox">
				<view class="handleBox" v-for="(item,index) in tabBarList" :key="index">
					<view class="menuBox">
						<view class="menuIcon">
							<image v-if="item.pageIndex!=selectIndex" class="img" :src="item.iconPath"
								@click="goPages(item.pageIndex)"></image>
							<image v-else class="img" :src="item.selectIconPath"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		props: {
			page: {
				type: String,
				default: "/pages/index/index"
			}
		},
		watch: {
			page: {
				handler(value) {
					this.selectIndex = value;
				},
				immediate: true,
				deep: true
			}
		},
		data() {
			return {
				selectIndex: "",
				tabBarList: "",
			}
		},
		//uniapp子组件不支持应用生命周期，所以只能用vue生命周期
		created() {
			const imgSrc = "@/static/images/"
			//tabbar数据，这儿也支持后台定义通过接口获取数据
			this.tabBarList = [{
					"tabbarName": "1", //tababr名称
					"iconPath": "/static/images/index.png", //tabbar icon
					"selectIconPath": "/static/images/index2.png", //tabbar 选择icon
					"pageIndex": "/pages/index/index" //页面路径
				}, {
					"tabbarName": "4", //tababr名称
					"iconPath": "/static/images/topic.png", //tabbar icon
					"selectIconPath": "/static/images/topic2.png", //tabbar 选择icon
					"pageIndex": "/pages/topic/topic" //页面路径
				}, {
					"tabbarName": "3", //tababr名称
					"iconPath": "/static/images/send.png", //tabbar icon
					"selectIconPath": "/static/images/send2.png", //tabbar 选择icon
					"pageIndex": "/pages/send/send" //页面路径
				}, {
					"tabbarName": "2", //tababr名称
					"iconPath": "/static/images/event.png", //tabbar icon
					"selectIconPath": "/static/images/event2.png", //tabbar 选择icon
					"pageIndex": "/pages/events/events" //页面路径
				},
				{
					"tabbarName": "5", //tababr名称
					"iconPath": "/static/images/my.png", //tabbar icon
					"selectIconPath": "/static/images/my2.png", //tabbar 选择icon
					"pageIndex": "/pages/my/my" //页面路径
				},
			]
		},
		methods: {
			//进入tabble页
			goPages(pageIndex) {
				console.log("pageIndex:", pageIndex);
				if (pageIndex == "/pages/send/send") {
					uni.redirectTo({
						url: "/pages/send/send?pages=" + this.selectIndex
					})
					return
				}
				uni.redirectTo({
					url: pageIndex
				})
			},

		},
	}
</script>
<style lang="scss">
	.swiper {
		height: 100vh;
	}

	.swiper-item {
		display: block;
		height: 100vh;
		height: 100vh;
		text-align: center;
	}

	.tab_bar {
		width: 750rpx;
		height: 270rpx;
		position: fixed;
		bottom: 0rpx;
		background-color: #292929;
		/* 模糊大小就是靠的blur这个函数中的数值大小 */
		backdrop-filter: blur(10px);
		border-top-left-radius: 60rpx;
		border-top-right-radius: 60rpx;
		box-shadow: 0rpx 0rpx 32rpx 6rpx rgba(64, 64, 64, 0.5);

		.tabbarBox {
			width: calc(100%-40rpx);
			height: 190rpx;
			display: flex;
			align-items: center;
			margin: 20rpx 30rpx;
			/* #ifdef APP-IOS */
			margin-bottom: 20rpx;
			/* #endif */
			justify-content: space-between;
			background: #000;
			border-radius: 40rpx;

			.handleBox {
				.menuBox {
					padding: 0rpx 20rpx;
					width: 120rpx;
					height: 98%;
					text-align: center;

					.img {
						width: 50rpx;
						height: 50rpx;
					}
				}
			}
		}
	}

	.Text {
		font-size: 24rpx;
		font-family: Cochin, serif;
		font-weight: 900;
		color: #fff
	}

	.TextColor {
		@extend .Text;
		color: #d81e06;
	}
</style>