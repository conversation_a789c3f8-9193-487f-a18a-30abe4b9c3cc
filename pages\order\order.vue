<template>
	<view class="container" @click="hidePopup">
		<view class="inputS">
			<uni-icons type="search" color="#fff" style="margin-right: 8rpx"></uni-icons>
			<u-input style="height: 68rpx; font-size: 28rpx; line-height: 38rpx" v-model="searchValue" type="text" placeholder="搜索"
				@confirm="search" :border="false" confirm-type="search"clearable />
		</view>
		<view class="filter_wrapper">
			<view class="dropdown-container">
			    <view class="dropdown-btn" :class="source ? 'dropdown-btn-selected' : ''" @click.stop="toogleSource">
			       订单来源
			      <u-icon name="arrow-down" size="28" style="margin-left: 16rpx;"></u-icon>
			    </view>
			    <view class="dropdown-content" v-show="isShowSource">
			      <view class="dropdown-item" :class="item.value === source ? 'dropdown-item-selected' : ''" v-for="(item,index) in sourceOptions" @click.stop="selectSource(item.value)">{{ item.label }}</view>
			    </view>
		    </view>
			<view class="dropdown-container" style="margin-left: 12rpx;">
			    <view class="dropdown-btn" :class="type ? 'dropdown-btn-selected' : ''" @click.stop="toogleType">
			       活动订单
			      <u-icon name="arrow-down" size="28" style="margin-left: 16rpx;"></u-icon>
			    </view>
			    <view class="dropdown-content" v-show="isShowType">
			      <view class="dropdown-item" :class="item.value === type ? 'dropdown-item-selected' : ''" v-for="(item,index) in typeOptions" @click.stop="selectType(item.value)">{{ item.label }}</view>
			    </view>
			</view>
		</view>
		<scroll-view scroll-y="true" class="item-wrapper"  :refresher-triggered="sendFlag" @refresherrefresh="refresherrefresh" @scrolltolower="scrolltolower"
					:refresher-threshold="150"	refresher-background="grey41" style="width: 100%; height: 80vh" refresher-enabled>
			<view v-if="orderList.length > 0">
				<view class="item" v-for="item in orderList" @click="toDetail(item)">
					<view class="order-title"> {{ item.title }}</view>
					<view class="price-container">
						<view class="price">￥<text class="price-num">{{ item.price }}</text>.{{ item.fee }}门票价</view>
						<view v-if="item.order_status === 1" class="status_1">未核验</view>
						<view v-if="item.order_status === 2" class="status_2">已核验</view>
						<view v-if="item.order_status === 3" class="status_3">已退款</view>
					</view>
					<view class="divider" />
					<view class="order-info">
						<u-icon name="clock"></u-icon>
						时间：{{ item.timeline }}
					</view>
					<view class="order-info">
						<u-icon name="map"></u-icon>
						地址：{{ item.address }}
					</view>
				</view>
			</view>
			<view class="list" v-else>
							<view style="
							  width: 100%;
							  height: 400rpx;
							  line-height: 400rpx;
							  text-align: center;
							"> 暂无订单
							</view>
						</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchValue: '',
				isShowSource: false,
				sourceOptions: [ {
					label: '未核验',
					value: '1'
				},{
					label: '已核验',
					value: '2'
				}, {
					label: '已退款',
					value: '3'
				}],
				source: '',
				type: '',
				isShowType: false,
				typeOptions: [{
					label: '活动订单',
					value: '1'
				}],
				orderList: [],
				total: 0,
				page: 1,
				sendFlag: true
			}
		},
		onLoad() {
			this.getData()
		},
		onShow() {
			this.page = 1
			this.getData()
		},
		methods: {
			search() {
				this.getData()
			},
			refresherrefresh() {
				this.source = ''
				this.type = ''
				this.page = 1;
				this.getData()
				this.sendFlag = true
				setTimeout(() => {
					this.sendFlag = false
				}, 500)
			},
			scrolltolower() {
				if (this.total != 0) {
					this.page++;
					this.getData()
				}
			},
			getData() {
				this.$http.get('/api/pay/app-order', {
					page: this.page,
					keyword: this.searchValue,
					order_status: this.source || '',
					order_type: this.type || ''
				}).then(res => {
					console.log(res)
					this.orderList = this.page == 1 ? [...res.message] : [...this.orderList, ...res.message]
					this.total = res.message.length
				})
			},
			toogleSource() {
				this.isShowType = false
			    this.isShowSource = !this.isShowSource
			},
			toogleType() {
				this.isShowSource = false
				this.isShowType = !this.isShowType
			},
			selectSource(value) {
				this.isShowSource = false
				this.source = value
				this.getData()
			},
			selectType(value) {
				this.isShowType = false
				this.type = value
				this.getData()
			},
			hidePopup() {
				this.isShowSource = false
				this.isShowType = false
			},
			toDetail(order) {
				uni.navigateTo({
					url: `/pages/order/detail?orderNo=${order.order_no}`,
					  success: function(res) {
					    // 通过eventChannel向被打开页面传送数据
					    // res.eventChannel.emit('getOrderInfo', { ... order })
					  }
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 8rpx 32rpx 32rpx 32rpx;
		height: 100vh;
	}
	.inputS {
		// width: 600rpx;
		width: 100%;
		color: #fff;
		border: 2rpx solid rgba(255, 255, 255, 0.6);
		height: 66rpx;
		display: flex;
		align-items: center;
		border-radius: 50rpx;
		padding: 0 24rpx;
		margin-top: 17rpx;
		margin-bottom: 14rpx;
	}
	.filter_wrapper {
		margin-top: 32rpx;
	}
	.dropdown-container {
	  position: relative;
	  display: inline-block;
	}
	.dropdown-btn {
	 box-sizing: border-box;
	 border: 1px solid rgb(188, 188, 188);
	 border-radius: 60rpx;
	 width: 220rpx;
	 height: 68rpx;
	 font-size: 28rpx;
	 color: rgb(188, 188, 188);
	 display: flex;
	 align-items: center;
	 justify-content: center;
	 row-gap: 12rpx;
	}
	.dropdown-btn-selected {
		border: 1px solid rgb(82, 195, 238);
	    color:  rgb(82, 195, 238);
	}
	.dropdown-content {
	  position: absolute;
	  top: 100%;
	  left: 0;
	  width: 220rpx;
	  box-sizing: border-box;
	  border: 2rpx solid rgb(82, 195, 238);
	  border-radius: 20rpx;
	  background: rgb(35, 35, 45);
	  z-index: 100;
	  margin-top: 10rpx;
	}
	.dropdown-item {
	  padding: 20rpx;
	  color: rgb(174, 211, 225);
	  font-size: 28rpx;
	  font-weight: 400;
	  display: flex;
	  justify-content: center;
	}
	.dropdown-item:active {
	  background-color: rgb(43, 133, 167);
	}
	.dropdown-item-selected {
		background-color: rgb(43, 133, 167);
		border-radius: 20rpx;
	}
/* 	.dropdown-item-selected:first-child {
		border-radius: 20rpx 20rpx 0 0 ;
	}
	.dropdown-item-selected:last-child {
		border-radius: 0 0 20rpx 20rpx ;
	} */
	.item-wrapper {
		padding: 36rpx 0;
	}
	.item {
		border-radius: 36rpx;
		box-shadow: inset 0px 0px 6px 0px rgb(196, 158, 249);
		background: rgb(56, 58, 68);
		padding: 32rpx;
		margin-bottom: 32rpx;
	}
	.order-title {
		color: rgb(255, 255, 255);
		font-size: 36rpx;
		font-weight: 500;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}
	.price-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 28rpx 0;
	}
	.price {
		color: rgb(244, 213, 174);
		font-size: 24rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: left;
	}
	.price-num {
		font-size: 56rpx;
		font-weight: 500;
	}
	.status_1 {
		color: rgb(100, 61, 27);
		font-size: 24rpx;
		font-weight: 400;
		line-height: 14px;
		padding-right: 12rpx;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
		background: url('@/static/images/order/status_1.png');
		margin-right: 8rpx;
		width: 120rpx;
		text-align: right;
	}
	.status_2 {
		color: white;
		font-size: 24rpx;
		font-weight: 400;
		line-height: 14px;
		text-align: right;
		padding-right: 12rpx;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
		background: url('@/static/images/order/status_2.png');
		margin-right: 8rpx;
		width: 100rpx;
	}
	.status_3 {
		color: white;
		font-size: 24rpx;
		font-weight: 400;
		line-height: 14px;
		padding-right: 12rpx;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
		background: url('@/static/images/order/status_3.png');
		margin-right: 8rpx;
		width: 100rpx;
		text-align: right;
	}
	.divider {
		background: rgba(196, 196, 196, 0.12);
		height: 2rpx;
	}
	.order-info {
		color: rgb(188, 188, 188);
		font-size: 24rpx;
		font-weight: 500;
		lettrer-spacing: 0px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		padding: 16rpx 0;
	}
 </style>
