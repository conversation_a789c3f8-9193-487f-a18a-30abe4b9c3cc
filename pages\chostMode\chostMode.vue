<template>
	<view class="appPage">
		<view class="sex">
			<view class="left" @click="setSexcurrent(1)"
				:style="{'background':form.current==1 ? 'linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)':'#2B3647'}">
				<image style="width: 42rpx;height: 42rpx;margin-right: 10rpx;" src="../../static/images/mohu.png"
					mode=""></image>
				模糊位置
			</view>
			<view class="right" @click="setSexcurrent(2)"
				:style="{'background':form.current==2 ? 'linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)':'#2B3647 '}">
				<image style="width: 42rpx;height: 42rpx;margin-right: 10rpx;" src="../../static/images/dongjie.png"
					mode=""></image>
				冻结位置
			</view>
		</view>
		<view class="title">
			{{titleArr[form.current]}}
		</view>
		<view class="card">
			{{noteArr[form.current]}}
		</view>
		<view class="add t_display" v-if="form.current == 1">
			<view class="item" @click="goNav('/pages/checkMember/checkMember?mode='+form.current)">
				<image class="img124" src="../../static/images/add.png" mode=""></image>
				<view class="">
					添加
				</view>
			</view>
			<view class="item" v-for="(item,index) in arr" :key="index">
				<image class="img124" :src="item.avatar" mode="aspectFill" style="border-radius:62rpx"></image>
				<uni-icons class="close" type="clear" color="#fff" size="15" @click="deleteId(index,item.uid)">
				</uni-icons>
				<view class="texts">
					{{item.nickname}}
				</view>
			</view>
		</view>
		<view class="add2" v-else-if="form.current == 2">
			<view class="item_group" v-for="(item,index) in arr" :key="index" style="justify-content: space-between;">
				<view class="t_display" style="position: relative;">
					<image class="img74" style="border-radius: 50%;" :src="item.avatar" mode="">
					</image>
					<uni-icons class="closeFreeze" type="clear" color="#fff" size="15"
						@click="deleteId(index,item.uid)"></uni-icons>
					<view class="info">
						<view class="name">
							{{item.nickname}}
						</view>
						<view class="address">
							对方看到您的位置为{{item.location_info.formatted_address}}
						</view>
					</view>
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/checkMember/checkMember?mode=2')"
				style="border-bottom: none;">
				<image class="img74" src="../../static/images/add.png" mode=""></image>
				<view class="" style="margin-left: 20rpx;">
					添加朋友
				</view>
			</view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				titleArr: ["", "模糊位置", "冻结位置"],
				noteArr: ["", "被设为模糊位置的朋友只能看到您的大概位置", "对方看到你的位置为冻结前最后位置"],
				form: {
					current: 1,
				},
				arr: []
			}
		},
		onShow() {
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			deleteId(ids, uid) {
				this.$http.post('/location/ghost/switch', {
					"mode": 0,
					"uids": [uid]
				}).then(res => {
					this.arr.splice(ids, 1)
				})
			},
			deleteId2(ids, uid) {
				this.$http.post('/location/ghost/switch', {
					"mode": 2,
					"uids": [uid]
				}).then(res => {
					this.arr.splice(ids, 1)
				})
			},
			getData() {
				this.$http.get('/location/ghost/get', {
					mode: this.form.current
				}).then(res => {
					this.arr = res.message
				})
			},
			goNav(url) {
				this.navigateTo({
					url
				})
			},
			setSexcurrent(ids) {
				this.arr = []
				this.form.current = ids
				this.getData()
			}
		}
	}
</script>
<style lang="scss" scoped>
	/deep/ .u-collapse-head {
		padding: 0;
	}

	.closeFreeze {
		position: absolute;
		left: 54rpx;
		top: 10rpx;
	}

	.appPage {
		padding: 32rpx;

		.item_group {
			padding: 26rpx 0;
			display: flex;
			align-items: center;
			border-bottom: 1px solid rgba(255, 255, 255, 0.16);

			.declined {
				width: 126rpx;
				height: 56rpx;
				border-radius: 16rpx;
				opacity: 1;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #767676;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #767676;
			}

			.yes {
				width: 126rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #FFFFFF;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				margin-left: 16rpx;
			}

			.no {
				font-size: 22rpx;
				font-weight: 400;
				text-align: center;
				width: 126rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid transparent;
				border-radius: 16rpx;
				background-clip: padding-box, border-box;
				background-origin: padding-box, border-box;
				background-image: linear-gradient(to right, #333, #333), linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
			}

			.outer_circle {
				position: relative;
				margin: 50px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: #ffffff;
			}

			.inner_circle {
				background-image: linear-gradient(to bottom, rgb(123, 93, 255) 0%,
						rgb(56, 225, 255) 100%);
				content: '';
				position: absolute;
				top: -20px;
				bottom: -20px;
				right: -20px;
				left: -20px;
				z-index: -1;
				border-radius: inherit;
			}

			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
				}

				.address {
					font-size: 24rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
					line-height: 35rpx;
					max-width: 600rpx;
				}

				.name {
					font-size: 26rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 38rpx;
				}
			}


			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.add2 {
			margin-top: 32rpx;

			.item {
				padding: 14rpx 0;
				border-bottom: 1px solid rgba(255, 255, 255, 0.16);
			}
		}

		.add {
			margin-top: 32rpx;
			flex-wrap: wrap;
			row-gap: 46rpx;
			column-gap: 17rpx;

			.item {
				text-align: center;
				font-size: 28rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
				row-gap: 46rpx;
				column-gap: 17rpx;
				position: relative;
				padding: 0 7rpx;

				.texts {
					width: 120rpx;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}

				.close {
					position: absolute;
					right: 10rpx;
				}
			}
		}

		.card {
			height: 102rpx;
			background: #22252F;
			padding: 16rpx 32rpx;
			font-size: 24rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 35rpx;
			display: flex;
			align-items: center;
		}

		.title {
			margin: 85rpx 0 22rpx 0;
			font-size: 36rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
		}

		.sex {
			display: flex;
			text-align: center;
			line-height: 90rpx;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 313rpx;
				height: 90rpx;
				background: #1D1C21;
				border-radius: 14rpx;
			}

			.right {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 313rpx;
				height: 90rpx;
				background: #1D1C21;
				// background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 14rpx;
			}
		}
	}
</style>
