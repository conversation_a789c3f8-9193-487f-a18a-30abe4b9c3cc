<template>
	<uni-popup ref="popup" :safe-area="true" type="bottom">
		<view class="hb">
			<view class="title">
				选择红包发布
			</view>
			<view class="list">

				<view class="li" @click="send('store')" v-if="userInfo.is_business == 2">
					<view class="imagebox">
						<image src="@/static/map/hb-store.png" mode=""></image>
					</view>
					<text>商家红包</text>
				</view>
				<view class="li" v-else-if="userInfo.is_business!= 2" @click="joinStore('store')">
					<view class="imagebox">
						<image src="@/static/map/hb-store.png" mode=""></image>
					</view>
					<text>商家红包</text>
				</view>
				<view class="li" @click="send('user')">
					<view class="imagebox">
						<image src="@/static/map/hb-user.png" mode=""></image>
					</view>
					<text>个人红包</text>
				</view>
			</view>
			<view class="img24" />
			<!-- <u-toast ref='notify' /> -->
		</view>
	</uni-popup>
</template>

<script>
	import {
		apiGetRedpacketDetail,

	} from '@/api/common.js'
	export default {
		data() {
			return {
				redPacketInfo: {},
				userInfo: uni.getStorageSync('userInfo')
			}
		},
		filters: {


		},
		mounted() {

		},
		methods: {
			joinStore() {
				uni.navigateTo({
					url: '/pages/business/business?mode=noRegister'
				})
				// uni.navigateTo({
				// 	url: '/pages/merchantAuthentication/merchantAuthentication'
				// })
				this.close()
			},
			send(type) {
				this.$emit('sendHb', type)
				this.close()
			},
			open(option) {
				this.$http.get('/api/user/info').then(res => {
					this.userInfo = res.message.user_info
					uni.setStorageSync('userInfo', res.message.user_info)
					console.log('.............', res);
					this.$refs.popup.open('bottom')
				})
			},
			close() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 750rpx;
		height: 435rpx;
		padding: 38rpx 32rpx 0;
		box-sizing: border-box;
		background-color: #191C26;
		border-radius: 20rpx;
		position: relative;

		&::after {
			position: absolute;
			left: 0;
			bottom: -120rpx;
			height: 120rpx;
			width: 750rpx;
			content: '';
			z-index: 2;
			background-color: #191C26;
		}

		.title {
			width: 228rpx;
			height: 55rpx;
			font-size: 38rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			color: #FFFFFF;
			line-height: 55rpx;

			margin-bottom: 72rpx;

		}

		.list {
			display: flex;
			align-items: center;
			justify-content: space-around;

			.li {
				display: flex;
				flex-direction: column;
				align-items: center;

				.imagebox {
					width: 104rpx;
					height: 104rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255, 54, 38, 0.42);
					border-radius: 52rpx 52rpx 52rpx 52rpx;
					border-radius: 50%;
					opacity: 1;
					margin-bottom: 16rpx;

					image {
						width: 52rpx;
						height: 56rpx;
					}
				}

				text {
					width: 128rpx;
					height: 46rpx;
					font-size: 32rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: rgba(255, 255, 255, 0.72);
					line-height: 46rpx;
					text-align: center;

				}
			}
		}

	}
</style>