<template>
  <view class="adolescent-container">
    <view class="math-container">
      <view class="math-question-container">
        <image
          class="math-question-bg"
          src="/static/images/my/yanzhengma.png"
          mode="aspectFill"
        ></image>
        <view class="math-question-content">
          <text class="math-number">{{ num1 }}</text>
          <text class="math-operator">{{ operator }}</text>
          <text class="math-number">{{ num2 }}</text>
          <text class="math-equals">=?</text>
        </view>
      </view>
      <input
        class="math-input"
        type="number"
        v-model="userAnswer"
        placeholder="请输入答案"
        style="
          color: #000000 !important;
          -webkit-text-fill-color: #000000 !important;
        "
      />
      <view class="math-btn" @click="checkAnswer">确认</view>
      <view
        v-show="showResult"
        class="math-result"
        :class="{ correct: isCorrect, wrong: !isCorrect }"
      >
        {{ resultMessage }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      num1: 0,
      num2: 0,
      operator: "",
      correctAnswer: 0,
      userAnswer: "",
      showResult: false,
      isCorrect: false,
      resultMessage: "",
      operators: ["+", "-", "x", "÷"],
    };
  },
  // 设置页面导航栏样式，禁用返回按钮
  onBackPress() {
    // 拦截返回事件，返回true表示不执行默认的返回行为
    return true;
  },
  // 页面显示时监听物理返回键
  onShow() {
    // 监听安卓物理返回键
    plus.key.addEventListener("backbutton", this.handleBackPress);
  },
  // 页面隐藏时移除监听
  onHide() {
    plus.key.removeEventListener("backbutton", this.handleBackPress);
  },
  computed: {
    question() {
      return `${this.num1}`;
    },
  },
  onLoad() {
    this.generateQuestion();
  },
  methods: {
    // 处理返回按钮事件
    handleBackPress() {
      // 阻止默认返回行为
      return false;
    },
    generateQuestion() {
      // 生成运算符
      const operatorIndex = Math.floor(Math.random() * 4);
      this.operator = this.operators[operatorIndex]; // 应该是 "÷"

      // 根据不同运算符生成合适的数字
      switch (this.operator) {
        case "+":
          // 加法：确保两数之和不超过100
          this.num1 = Math.floor(Math.random() * 50) + 1;
          this.num2 = Math.floor(Math.random() * 50) + 1;
          break;

        case "-":
          // 减法：确保结果为正数且两数都不超过100
          this.num1 = Math.floor(Math.random() * 50) + 50; // 50-99
          this.num2 = Math.floor(Math.random() * this.num1); // 0到num1-1
          break;

        case "x":
          // 乘法：确保乘积不超过100
          this.num1 = Math.floor(Math.random() * 10) + 1; // 1-10
          // 确保乘积不超过100
          const maxNum2 = Math.floor(100 / this.num1);
          this.num2 = Math.floor(Math.random() * maxNum2) + 1;
          break;

        case "÷":
          // 除法：确保能够整除且结果为整数
          this.num2 = Math.floor(Math.random() * 10) + 1; // 1-10
          // 确保结果不超过10且num1不超过100
          const maxResult = Math.min(10, Math.floor(100 / this.num2));
          const result = Math.floor(Math.random() * maxResult) + 1;
          this.num1 = this.num2 * result; // 确保能整除
          break;
      }

      // 计算正确答案
      this.calculateCorrectAnswer();

      // 重置用户输入和结果显示
      this.userAnswer = "";
      this.showResult = false;
    },

    calculateCorrectAnswer() {
      switch (this.operator) {
        case "+":
          this.correctAnswer = this.num1 + this.num2;
          break;
        case "-":
          this.correctAnswer = this.num1 - this.num2;
          break;
        case "x":
          this.correctAnswer = this.num1 * this.num2;
          break;
        case "÷":
          this.correctAnswer = this.num1 / this.num2;
          break;
      }
    },

    checkAnswer() {
      const userNum = Number(this.userAnswer);
      this.isCorrect = userNum === this.correctAnswer;
      this.showResult = true;

      if (this.isCorrect) {
        this.resultMessage = "回答正确！";
        uni.navigateBack();
      } else {
        // this.resultMessage = `回答错误，正确答案是 ${this.correctAnswer}`;
        this.resultMessage = `回答错误`;
      }
    },
  },
};
</script>

<style>
.adolescent-container {
  width: 100%;
  height: 100vh;
  background-image: url("/static/images/my/qingshaonianbeijing.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.math-container {
  /* background-color: red; */
  width: 60%;
  max-width: 500rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 130rpx;
}

@font-face {
  font-family: "Akronim";
  src: url("/static/font/Akronim-Regular.ttf");
}

.math-question-container {
  position: relative;
  width: 100%;
  height: 120rpx;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.math-question-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.math-question-content {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;
  width: 100%;
}

.math-number {
  font-family: "Akronim", sans-serif;
  font-size: 40px;
  font-weight: bold;
  color: #333;
}

.math-operator {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin: 0 10rpx;
}

.math-equals {
  font-family: "Akronim", sans-serif;
  font-size: 40px;
  font-weight: bold;
  color: #333;
  margin-left: 10rpx;
}

.math-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #17a1ea;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 36rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-sizing: border-box;
}

.math-btn {
  width: 100%;
  height: 90rpx;
  background-color: #77d384;
  color: white;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.math-result {
  width: 100%;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
  text-align: center;
  box-sizing: border-box;
}

.correct {
  color: #4cd964;
}

.wrong {
  color: #ff3b30;
}
</style>
