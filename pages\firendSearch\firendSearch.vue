<template>
	<view class="appPage">
		<view :style="{height:statusBarHeight+'px'} "></view>
		<view class="t_display">
			<uni-icons @click="goBack" type="left" color="#fff" size="18" style="margin-right: 24rpx;"></uni-icons>
			<view class=" Cinpout t_display">
				<uni-icons type="search" color="#A2A2A4" size="16" style="margin-right: 10rpx;"></uni-icons>
				<u-input v-model="value" placeholder="搜索对方id或手机号" type="text" clearable @confirm="search" />
			</view>
			<view class="search" @click="search">
				搜索
			</view>
		</view>

		<view class="item_group" v-for="(item,index) in list" style="justify-content: space-between;" :key="index">
			<view class="t_display">
				<image style="width: 108rpx;height: 108rpx;border-radius: 50%;" :src="item.avatar" mode="aspectFill"
					@click="goNav('/pages/otherPage/otherPage?uuid='+item.uuid)">
				</image>
				<view class="info">
					<view class="t_display">
						<view class="name">
							{{item.nickname}}
						</view>
						<image v-if="false" style="width: 32rpx;height: 32rpx;" src="../../static/images/roleme.png"
							mode="">
						</image>
					</view>
					<view class="note t_display">
						ID:{{item.uuid}}
					</view>
				</view>
			</view>
			<view class="t_display">
				<view class="no" v-if="item.relation != 4 ">
					<!-- 1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
					<view class="themeColor" @click="follow(item,index)" v-if="[3,0].includes(item.relation) ">
						关注
					</view>
					<view class="grey" v-else-if="[1].includes(item.relation)" @click="cancel(item,index)">
						已关注
					</view>
					<view class="grey" v-else-if="[2].includes(item.relation)" @click="cancel(item,index)">
						互相关注
					</view>
				</view>
			</view>
		</view>
		<view style="border: 1rpx solid #333;margin-top: 24rpx;"></view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				list: [],
				statusBarHeight: 0,
			}
		},
		onLoad() {
			const system = uni.getStorageSync('system')
			this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
		},
		methods: {
			cancel(item, index) {
				this.$http.post('/api/user/follow/del', {
					uuid: item.uuid
				}).then(res => {
					this.$set(this.list, index, {
						...item,
						relation: res.message
					})
				})
			},
			follow(item, index) {
				this.$http.post('/api/user/follow/add', {
					uuid: item.uuid
				}).then(res => {
					this.$set(this.list, index, {
						...item,
						relation: res.message.user_info.relation
					})
					this.toast('关注成功')
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			search() {
				console.log('kenter');
				this.$http.get('/api/user/search-term', {
					keyword: this.value
				}).then(res => {
					console.log(res);
					this.list = res.message
				})
			},
			goBack() {
				uni.navigateBack()
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 0 30rpx;

		.item_group {
			margin-top: 56rpx;
			display: flex;
			align-items: center;

			.declined {
				width: 126rpx;
				height: 56rpx;
				border-radius: 16rpx;
				opacity: 1;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #767676;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #767676;
			}

			.yes {
				width: 126rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #FFFFFF;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				margin-left: 16rpx;
			}

			.no {
				font-size: 22rpx;
				font-weight: 400;
				text-align: center;
				width: 126rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid transparent;
				border-radius: 16rpx;
				background-clip: padding-box, border-box;
				background-origin: padding-box, border-box;
				background-image: linear-gradient(to right, #333, #333), linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
			}

			.outer_circle {
				position: relative;
				margin: 50px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: #ffffff;
			}

			.inner_circle {
				background-image: linear-gradient(to bottom, rgb(123, 93, 255) 0%,
						rgb(56, 225, 255) 100%);
				content: '';
				position: absolute;
				top: -20px;
				bottom: -20px;
				right: -20px;
				left: -20px;
				z-index: -1;
				border-radius: inherit;
			}

			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
				}

				.name {
					font-size: 34rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #FFFFFF;
				}
			}


			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.title {
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #A2A2A4;
			margin-top: 52rpx;
			margin-bottom: 60rpx;
		}

		.txt {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			margin-left: 28rpx;
		}

		.search {
			margin-left: 24rpx;
			font-size: 26rpx;
			font-family: Source Han Sans-Medium, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
		}

		.Cinpout {
			flex: 1;
			height: 80rpx;
			background: #272727;
			border-radius: 32rpx;
			padding: 0 52rpx;
		}
	}
</style>
