<template>
	<view>
		<uni-popup ref="popup" type="center">
			<view class="t_center">
				<image class="size" :src="'../../static/images/vip/tips'+imgCurrent+'.png'" mode="">
				</image>
				<cusButton txt="立即开通" class="btn" :radio="90" @confirm="confirm" />
				<image class="img52 close" @click="close" src="../../static/images/vip/close.png" mode=""></image>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: "vipTips",
		props: {
			imgCurrent: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {

			};
		},
		methods: {
			confirm() {
				this.$emit('confirm')
				this.close()
			},
			open() {
				this.$refs.popup.open()
			},
			close() {
				this.$refs.popup.close()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.size {
		width: 526rpx;
		height: 569rpx;
		display: block;
	}

	.btn {
		width: 100%;
		position: absolute;
		bottom: 150rpx;
		padding: 0 36rpx;
		z-index: 99999;
	}

	.close {
		margin-top: 52rpx;
	}
</style>
