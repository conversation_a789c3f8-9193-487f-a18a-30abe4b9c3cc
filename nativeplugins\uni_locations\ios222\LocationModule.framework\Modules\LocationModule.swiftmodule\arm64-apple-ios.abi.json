{"ABIRoot": {"kind": "Root", "name": "LocationModule", "printedName": "LocationModule", "children": [{"kind": "Import", "name": "HealthKit", "printedName": "HealthKit", "declKind": "Import", "moduleName": "LocationModule", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "LocationModule", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "LocationModule"}, {"kind": "TypeDecl", "name": "LocationSDK", "printedName": "LocationSDK", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "LocationSDK", "printedName": "LocationModule.LocationSDK", "usr": "c:@M@LocationModule@objc(cs)LocationSDK"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(im)init", "mangledName": "$s14LocationModule0A3SDKCACycfc", "moduleName": "LocationModule", "overriding": true, "objc_name": "init", "declAttributes": ["Dynamic", "ObjC", "Override", "AccessControl", "RawDocComment"], "init_kind": "Designated"}, {"kind": "Function", "name": "setBaseUrl", "printedName": "setBaseUrl(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)setBaseUrl:", "mangledName": "$s14LocationModule0A3SDKC10setBaseUrlyySSFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setToken", "printedName": "setToken(_:refreshToken:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)setToken:refreshToken:", "mangledName": "$s14LocationModule0A3SDKC8setToken_07refreshE0ySS_SStFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "debug", "printedName": "debug(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)debug:", "mangledName": "$s14LocationModule0A3SDKC5debugyySbFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "authWhenInUse", "printedName": "authWhenInUse()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)authWhenInUse", "mangledName": "$s14LocationModule0A3SDKC13authWhenInUseyyFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "authAlways", "printedName": "authAlways()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)authAlways", "mangledName": "$s14LocationModule0A3SDKC10authAlwaysyyFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "authStatus", "printedName": "authStatus()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)authStatus", "mangledName": "$s14LocationModule0A3SDKC10authStatusSiyFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setEnable", "printedName": "setEnable(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)setEnable:", "mangledName": "$s14LocationModule0A3SDKC9setEnableyySbFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "isEnable", "printedName": "isEnable()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "c:@M@LocationModule@objc(cs)LocationSDK(cm)isEnable", "mangledName": "$s14LocationModule0A3SDKC8isEnableSbyFZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@LocationModule@objc(cs)LocationSDK", "mangledName": "$s14LocationModule0A3SDKC", "moduleName": "LocationModule", "objc_name": "LocationSDK", "declAttributes": ["AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "LocationModule"}, {"kind": "Import", "name": "BackgroundTasks", "printedName": "BackgroundTasks", "declKind": "Import", "moduleName": "LocationModule"}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "LocationModule"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "LocationModule"}, {"kind": "TypeDecl", "name": "LocationService", "printedName": "LocationService", "children": [{"kind": "Var", "name": "shared", "printedName": "shared", "children": [{"kind": "TypeNominal", "name": "LocationService", "printedName": "LocationModule.LocationService", "usr": "c:@M@LocationModule@objc(cs)LocationService"}], "declKind": "Var", "usr": "s:14LocationModule0A7ServiceC6sharedACvpZ", "mangledName": "$s14LocationModule0A7ServiceC6sharedACvpZ", "moduleName": "LocationModule", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl", "RawDocComment"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "LocationService", "printedName": "LocationModule.LocationService", "usr": "c:@M@LocationModule@objc(cs)LocationService"}], "declKind": "Accessor", "usr": "s:14LocationModule0A7ServiceC6sharedACvgZ", "mangledName": "$s14LocationModule0A7ServiceC6sharedACvgZ", "moduleName": "LocationModule", "static": true, "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Function", "name": "stopLocationUpdates", "printedName": "stopLocationUpdates()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:14LocationModule0A7ServiceC04stopA7UpdatesyyF", "mangledName": "$s14LocationModule0A7ServiceC04stopA7UpdatesyyF", "moduleName": "LocationModule", "declAttributes": ["RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "enableFullBackgroundPersistence", "printedName": "enableFullBackgroundPersistence()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:14LocationModule0A7ServiceC31enableFullBackgroundPersistenceyyF", "mangledName": "$s14LocationModule0A7ServiceC31enableFullBackgroundPersistenceyyF", "moduleName": "LocationModule", "declAttributes": ["RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didUpdateLocations:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "Array", "printedName": "[CoreLocation.CLLocation]", "children": [{"kind": "TypeNominal", "name": "CLLocation", "printedName": "CoreLocation.CLLocation", "usr": "c:objc(cs)CLLocation"}], "usr": "s:Sa"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didUpdateLocations:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_18didUpdateLocationsySo010CLLocationE0C_SaySo0I0CGtF", "moduleName": "LocationModule", "objc_name": "locationManager:didUpdateLocations:", "declAttributes": ["Dynamic", "ObjC", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didFailWithError:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "Error", "printedName": "any Swift.Error", "usr": "s:s5ErrorP"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didFailWithError:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_16didFailWithErrorySo010CLLocationE0C_s0I0_ptF", "moduleName": "LocationModule", "objc_name": "locationManager:didFailWithError:", "declAttributes": ["Dynamic", "ObjC", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didChangeAuthorization:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "CLAuthorizationStatus", "printedName": "CoreLocation.CLAuthorizationStatus", "usr": "c:@E@CLAuthorizationStatus"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didChangeAuthorizationStatus:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_22didChangeAuthorizationySo010CLLocationE0C_So21CLAuthorizationStatusVtF", "moduleName": "LocationModule", "objc_name": "locationManager:didChangeAuthorizationStatus:", "declAttributes": ["Dynamic", "ObjC", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didVisit:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "CLVisit", "printedName": "CoreLocation.CLVisit", "usr": "c:objc(cs)CLVisit"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didVisit:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_8didVisitySo010CLLocationE0C_So7CLVisitCtF", "moduleName": "LocationModule", "objc_name": "locationManager:didVisit:", "declAttributes": ["Dynamic", "ObjC", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didEnterRegion:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "CLRegion", "printedName": "CoreLocation.CLRegion", "usr": "c:objc(cs)CLRegion"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didEnterRegion:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_14didEnterRegionySo010CLLocationE0C_So8CLRegionCtF", "moduleName": "LocationModule", "objc_name": "locationManager:didEnterRegion:", "declAttributes": ["Dynamic", "ObjC", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "locationManager", "printedName": "locationManager(_:didExitRegion:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CLLocationManager", "printedName": "CoreLocation.CLLocationManager", "usr": "c:objc(cs)CLLocationManager"}, {"kind": "TypeNominal", "name": "CLRegion", "printedName": "CoreLocation.CLRegion", "usr": "c:objc(cs)CLRegion"}], "declKind": "Func", "usr": "c:@CM@LocationModule@objc(cs)LocationService(im)locationManager:didExitRegion:", "mangledName": "$s14LocationModule0A7ServiceC15locationManager_13didExitRegionySo010CLLocationE0C_So8CLRegionCtF", "moduleName": "LocationModule", "objc_name": "locationManager:didExitRegion:", "declAttributes": ["Dynamic", "ObjC", "AccessControl"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "scheduleBackgroundTask", "printedName": "scheduleBackgroundTask()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:14LocationModule0A7ServiceC22scheduleBackgroundTaskyyF", "mangledName": "$s14LocationModule0A7ServiceC22scheduleBackgroundTaskyyF", "moduleName": "LocationModule", "declAttributes": ["AccessControl"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@LocationModule@objc(cs)LocationService", "mangledName": "$s14LocationModule0A7ServiceC", "moduleName": "LocationModule", "declAttributes": ["AccessControl", "RawDocComment", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "Optional", "printedName": "Optional", "declKind": "Enum", "usr": "s:Sq", "mangledName": "$sSq", "moduleName": "Swift", "genericSig": "<τ_0_0 where τ_0_0 : ~Copyable>", "sugared_genericSig": "<Wrapped where Wrapped : ~Copyable>", "declAttributes": ["Frozen"], "isExternal": true, "isEnumExhaustive": true, "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "BitwiseCopyable", "printedName": "BitwiseCopyable", "usr": "s:s15BitwiseCopyableP", "mangledName": "$ss15BitwiseCopyableP"}, {"kind": "Conformance", "name": "ExpressibleByNilLiteral", "printedName": "ExpressibleByNilLiteral", "usr": "s:s23ExpressibleByNilLiteralP", "mangledName": "$ss23ExpressibleByNilLiteralP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "AnyObject"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "EncodableWithConfiguration", "printedName": "EncodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "EncodingConfiguration", "printedName": "EncodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.EncodingConfiguration"}]}], "usr": "s:10Foundation26EncodableWithConfigurationP", "mangledName": "$s10Foundation26EncodableWithConfigurationP"}, {"kind": "Conformance", "name": "DecodableWithConfiguration", "printedName": "DecodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "DecodingConfiguration", "printedName": "DecodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.DecodingConfiguration"}]}], "usr": "s:10Foundation26DecodableWithConfigurationP", "mangledName": "$s10Foundation26DecodableWithConfigurationP"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "StringLiteral", "offset": 189, "length": 10, "value": "\"base_url\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "StringLiteral", "offset": 283, "length": 14, "value": "\"access_token\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "StringLiteral", "offset": 385, "length": 15, "value": "\"refresh_token\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "StringLiteral", "offset": 489, "length": 7, "value": "\"debug\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 512, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "StringLiteral", "offset": 582, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationModule.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 606, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationService.swift", "kind": "StringLiteral", "offset": 834, "length": 22, "value": "\"com.location.refresh\""}, {"filePath": "/Users/<USER>/projects/Location/LocationModule/LocationModule/LocationService.swift", "kind": "IntegerLiteral", "offset": 14014, "length": 1, "value": "0"}]}