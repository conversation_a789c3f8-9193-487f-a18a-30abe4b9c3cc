<template>
	<view class="">
		<!-- 主渲染内容 -->
		<view id="render-dom" class="render-content">
			<view class="activity-card" :style="{ backgroundImage: `url(${cardBgImage})` }">
				<view class="card-content">
					<view class="card-title">
						{{ cardData.title }}
					</view>
					<view class="latlnt" :style="{ color: fontColor }">
						"{{ cardData.lat.trim() }}'N,{{ cardData.lng.trim() }}°W</view>
					<view class="location" :style="{ color: fontColor }">
						<image :src="locationLogo" mode="" style="width: 35rpx;height: 35rpx;margin-right: 5rpx;">
						</image>
						<view class="">{{ cardData.address }}</view>
					</view>
					<view class="card-info">
						<view class="info-item">
							<text class="member-count" :style="{ color: fontColor }">已有<span
									style="color: #6E4FFA;margin: 0 8rpx;">
									{{ cardData.memberCount }}</span> 人加入</text>
						</view>
					</view>
					<view class="info-form">
						<text>Form</text>
						<view class="form-line"></view>
					</view>
					<view class="card-footer">
						<view class="logo-container">
							<view class="logo-image">
								<image :src="logo" alt="" />
							</view>
							<view class="logo-text">
								<text class="logo-title">LightingBall</text>
								<text class="logo-subtitle">扫一扫，发现更多精彩活动</text>
							</view>
						</view>
						<view class="qrcode-image">
							<image class="qrImg" :src="cardData.qrCodeImage" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 隐藏的canvas元素 -->
		<canvas canvas-id="shareCanvas" class="hidden-canvas"></canvas>
	</view>
</template>

<script>
	import bgImages from "./enum.js";

	export default {
		name: 'ActivityCard',
		props: {
			cardData: {
				type: Object,
				default: () => ({
					cardId: '',
					title: '',
					memberCount: '0',
					qrCodeImage: ''
				})
			},
		},

		created() {
			const bgIndex = Math.floor(Math.random() * 3) + 1;
			this.bgIndex = bgIndex
			// 确保使用正确的属性名称
			this.cardBgImage = bgImages[`shBgImage${bgIndex}`]
			this.locationLogo = bgImages[`locationLogo${bgIndex}`]
			this.fontColor = ["#55B888", "#228CFE", "#E75278"][bgIndex - 1]

			// 打印背景图信息以便调试
			// console.log('背景图索引:', bgIndex);
			// console.log('背景图路径:', this.cardBgImage);
			// console.log('可用的背景图:', Object.keys(bgImages));

			// 确保二维码图片已加载
			if (this.cardData.qrCodeImage) {
				// 预加载二维码图片
				uni.getImageInfo({
					src: this.cardData.qrCodeImage,
					success: (res) => {
						console.log('二维码图片加载成功:', res.path);
						this.qrCodeImagePath = res.path;
					},
					fail: (err) => {
						console.error('二维码图片加载失败:', err);
					}
				});
			}
		},
		data() {
			return {
				bgIndex: "",
				fontColor: "",
				cardBgImage: "",
				locationLogo: '',
				logo: bgImages.logo,
				bgImageIndex: "",
				canvasWidth: 600,
				canvasHeight: 750,
				qrCodeImagePath: '' // 存储二维码图片路径
			}
		},
		methods: {
			cusRenderDom() {
				uni.showLoading({
					title: '生成图片中...'
				});

				// 获取元素信息
				const query = uni.createSelectorQuery().in(this);
				query.select('#render-dom').boundingClientRect(data => {
					if (!data) {
						uni.hideLoading();
						console.error('无法获取渲染节点信息');
						return;
					}

					// 设置canvas大小
					this.canvasWidth = data.width;
					this.canvasHeight = data.height;

					// 开始绘制
					this.drawShareImage();
				}).exec();
			},

			// 绘制分享图片
			drawShareImage() {
				const ctx = uni.createCanvasContext('shareCanvas', this);

				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 设置白色背景
				ctx.setFillStyle('#FFFFFF');
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制背景图
				if (this.cardBgImage) {
					console.log('背景图路径:', this.cardBgImage);

					// 直接绘制背景图，不再使用getImageInfo
					ctx.drawImage(this.cardBgImage, 0, 0, this.canvasWidth, this.canvasHeight);

					// 继续绘制其他内容
					this.drawCardContent(ctx);
				} else {
					console.error('背景图路径为空');
					this.drawCardContent(ctx);
				}
			},

			// 绘制卡片内容
			drawCardContent(ctx) {
				// 绘制标题 - 调整到顶部区域
				ctx.setFillStyle('#000000');
				ctx.setTextAlign('center');
				ctx.setFontSize(30); // 对应60rpx
				ctx.font = "bold 30px 'TitleFont'"; // 使用自定义字体
				ctx.fillText(this.cardData.title || '商户名称商', this.canvasWidth / 2, 60);

				// 绘制经纬度框
				const latLngBoxY = 105;
				const latLngBoxHeight = 60;
				const latLngBoxWidth = this.canvasWidth - 80;

				// 绘制经纬度文本 - 调整到卡片上方区域
				ctx.setFillStyle(this.fontColor);
				ctx.setTextAlign('center');
				ctx.setFontSize(14); // 对应28rpx
				ctx.font = 'bold 14px sans-serif'; // 添加加粗效果
				const lat = this.cardData.lat ? this.cardData.lat.trim() : '40.6892';
				const lng = this.cardData.lng ? this.cardData.lng.trim() : '74.0445';
				ctx.fillText(`"${lat}'N, ${lng}°W`, this.canvasWidth / 2, latLngBoxY + 34);

				// 重置字体为正常样式，避免影响后续文本
				ctx.font = 'normal 14px sans-serif';

				// 绘制位置 - 调整到卡片中上部
				ctx.setTextAlign('center');
				if (this.locationLogo) {
					// 绘制位置图标
					const locationY = 180;
					ctx.drawImage(this.locationLogo, this.canvasWidth / 2 - 33, locationY, 18, 18);
					ctx.setFontSize(14); // 对应32rpx
					ctx.fillText(this.cardData.address || '河北省', this.canvasWidth / 2 + 10, locationY + 15);
				}
				// 绘制成员数量 - 移至中间偏下位置
				const memberCountY = 230;
				ctx.setTextAlign('center');

				// 计算文本宽度以便更好地排列
				const prefix = '已有';
				const suffix = this.bgIndex == 1 ? '人点赞了这家店' : '人加入';
				const number = this.cardData.memberCount || '0';

				// 设置字体颜色和大小
				ctx.setFillStyle(this.fontColor);
				ctx.setFontSize(14); // 对应28rpx

				// 计算整体宽度，以便居中对齐
				const prefixWidth = ctx.measureText(prefix).width;
				ctx.setFontSize(20); // 对应40rpx
				const numberWidth = ctx.measureText(number).width;
				ctx.setFontSize(14); // 对应28rpx
				const suffixWidth = ctx.measureText(suffix).width;

				// 计算总宽度和起始位置，使整体居中
				const totalWidth = prefixWidth + numberWidth + suffixWidth;
				const startX = this.canvasWidth / 2 - totalWidth / 2;

				// 绘制"已有"
				ctx.setFillStyle(this.fontColor);
				ctx.setFontSize(14); // 对应28rpx
				ctx.setTextAlign('left');
				ctx.fillText(prefix, startX + 5, memberCountY);

				// 绘制高亮数字
				ctx.setFillStyle('#6E4FFA');
				ctx.setFontSize(20); // 对应40rpx
				ctx.font = "20px 'NumFont'"; // 使用数字专用字体
				ctx.fillText(number, startX + prefixWidth + 10, memberCountY);
				// 重置字体为正常样式，避免影响后续文本
				ctx.font = 'normal 14px sans-serif';
				// 绘制后续文字
				ctx.setFillStyle(this.fontColor);
				ctx.setFontSize(14); // 对应28rpx
				ctx.fillText(suffix, startX + prefixWidth + numberWidth + 15, memberCountY);
				// 绘制底部区域
				const footerY = this.canvasHeight - 120;
				// 绘制Form文字和分隔线 - 移至底部Logo上方
				ctx.setTextAlign('left');
				ctx.setFillStyle('#666666');
				ctx.setFontSize(16); // 对应32rpx
				ctx.fillText('Form', 20, footerY + 10);

				// 绘制分隔线
				ctx.beginPath();
				ctx.setLineDash([5, 5]);
				ctx.moveTo(70, footerY + 5);
				ctx.lineTo(this.canvasWidth - 20, footerY + 5);
				ctx.setStrokeStyle('#CCCCCC');

				// 绘制Logo和二维码
				if (this.logo) {
					// 调整底部区域的左右边距
					const leftMargin = 20;
					const rightMargin = 20;
					const logoSize = 40;
					const qrSize = 60;

					// 绘制Logo - 调整左侧位置
					ctx.drawImage(this.logo, leftMargin, footerY + 42, logoSize, logoSize);

					// 绘制Logo文字
					ctx.setTextAlign('left');
					ctx.setFillStyle('#000000');
					ctx.setFontSize(16); // 对应32rpx
					ctx.fillText('LightingBall', leftMargin + logoSize + 10, footerY + 60);
					ctx.setFontSize(11); // 对应22rpx
					ctx.setFillStyle('#666666');
					ctx.fillText('打一打，发现更多好店', leftMargin + logoSize + 10, footerY + 75);

					// 绘制二维码 - 调整到右侧
					if (this.cardData.qrCodeImage) {
						// 使用预加载的二维码图片路径或原始路径
						const qrImagePath = this.qrCodeImagePath || this.cardData.qrCodeImage;
						console.log('绘制二维码，使用路径:', qrImagePath);

						// 调整二维码位置，与Logo在同一水平线上
						const qrX = this.canvasWidth - qrSize - rightMargin;
						const qrY = footerY + 30; // 与Logo在同一高度

						// 绘制二维码
						try {
							ctx.drawImage(qrImagePath, qrX, qrY, qrSize, qrSize);
							console.log('二维码绘制成功');
						} catch (error) {
							console.error('二维码绘制失败:', error);
						}
					}
				}

				ctx.stroke();

				// 延迟完成绘制，确保所有内容绘制完成
				setTimeout(() => {
					this.finishDrawing(ctx);
				}, 300);
			},

			// 完成绘制并导出图片
			finishDrawing(ctx) {
				ctx.draw(true, () => {
					// 延迟一下确保绘制完成
					setTimeout(() => {
						// 将Canvas导出为图片
						uni.canvasToTempFilePath({
							canvasId: 'shareCanvas',
							success: (res) => {
								uni.hideLoading();
								// 发送生成的图片路径
								this.$emit("renderOver", res.tempFilePath);
							},
							fail: (err) => {
								uni.hideLoading();
								console.error('导出图片失败', err);
								// 通知失败
								this.$emit("renderOver", "");
							}
						}, this);
					}, 300);
				});
			},

			renderOver(e) {
				// 兼容旧方法
				this.$emit("renderOver", e);
			}
		}
	}
</script>

<style lang="scss">
	.render-content {
		width: 600rpx;
		height: 750rpx;
		margin: 30rpx auto;
	}

	.hidden-canvas {
		position: fixed;
		left: -9999rpx;
		width: 600rpx;
		height: 750rpx;
	}

	.location {
		display: flex;
		justify-content: center;
		margin-top: 50rpx;
		font-size: 32rpx;
		font-family: 'TitleFont', sans-serif;
	}

	.latlnt {
		font-size: 28rpx;
		text-align: center;
		margin-top: 44rpx;
		font-weight: bold;
	}

	@font-face {
		font-family: 'TitleFont';
		src: url('/static/font/jinbu.ttf');
		font-weight: normal;
		font-style: normal;
	}

	@font-face {
		font-family: 'NumFont';
		src: url('/static/font/num.otf');
		font-weight: normal;
		font-style: normal;
	}

	.info-form {
		padding: 0 25px;
		display: flex;
		margin-top: -20px;

		text {
			font-size: 28rpx;
			color: #666;
			margin-right: 20rpx;
		}

		.form-line {
			margin-top: 20rpx;
			width: 100%;
			border-top: 2px dashed #ccc;
		}
	}

	.activity-card {
		width: 600rpx;
		height: 750rpx;
		margin: 30rpx auto;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		border-radius: 30rpx;
		padding: 20rpx 0;
		position: relative;
		overflow: hidden;

		.card-content {
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		.card-title {
			text-align: center;
			width: 100%;
			padding: 20px 20rpx 0 20rpx;
			font-family: 'TitleFont', sans-serif;
			font-size: 60rpx;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			font-weight: bold;
			color: #000;
			line-height: 1.3;
			margin-bottom: 20rpx;
		}

		.card-info {
			display: flex;
			justify-content: center;

			.info-item {
				display: flex;
				justify-content: center;
				margin-bottom: 20rpx;

				text {
					font-size: 22rpx;
					color: #000;
				}

				.member-count {
					color: #00A080;
					font-weight: bold;
					margin-left: 20px;

					span {
						font-family: 'NumFont', sans-serif;
						font-size: 32rpx;
						color: #00A080;
					}
				}
			}
		}

		.card-footer {
			padding: 0 24px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20px;

			.logo-container {
				display: flex;
				align-items: center;

				.logo-image {
					width: 80rpx;
					height: 80rpx;
					border-radius: 26rpx;
					background-color: #000;
					margin-right: 20rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 100%;
						height: 100%;
					}

					text {
						color: #fff;
						font-size: 24rpx;
					}
				}

				.logo-text {
					display: flex;
					flex-direction: column;

					.logo-title {
						font-size: 32rpx;
						font-weight: bold;
						color: #000;
					}

					.logo-subtitle {
						font-size: 22rpx;
						color: #666;
					}
				}
			}

			.qrcode-image {
				width: 120rpx;
				height: 120rpx;

				.qrImg {
					width: 116rpx;
					height: 116rpx;
					box-sizing: border-box;
				}
			}
		}
	}
</style>