<template>
	<view style="width: 750rpx;height:1000px">
		<!-- <map style="width: 750rpx;" :style="{height:mapHeight+'px'}" id="esaymap" ref="esaymap" :scale="scale" :latitude="nowLat ? nowLat : centerLat"
			:longitude="nowLng ? nowLng : centerLng" :markers="markers" :polygons="polygonsData"
			:enable-zoom="isEnableZoom" :enable-scroll="isEnableScroll" :enable-satellite="isShowWxMap"
			:enable-rotate="isEnableRotate" @markertap="chooseItem" @tap="clickMap" @regionchange='change'>
			
		</map> -->
		<!-- :style="{height:mapHeight+'px'}" -->
		<map style="width: 750rpx;height:1000px" id="esaymap" ref="esaymap" :scale="scale"
			:latitude="nowLat ? nowLat : centerLat" :longitude="nowLng ? nowLng : centerLng" :markers="markers"
			:polygons="polygonsData" :enable-zoom="isEnableZoom" :enable-scroll="isEnableScroll"
			:enable-satellite="isShowWxMap" :enable-rotate="isEnableRotate" @markertap="onMarkerTap" @tap="clickMap"
			@regionchange='change'>

		</map>
		<view class="rightbox">
			<!-- <view class="boxitem" @click="changeTab(1)">
				<image class="itemimg" :src="tabIndex ? myaddressOnImg : myaddressImg" mode=""></image>
				<view class="itemname" :class="tabIndex ? 'active' : ''">我的位置</view>
			</view> -->
			<view class="boxitem" @click="changeTab(2)" v-if="wxMapShow">
				<image class="itemimg" :src="tabIndex2 ? wxmapOnImg:wxmapImg" mode=""></image>
				<view class="itemname" :class="tabIndex2 ? 'active' : ''">卫星地图</view>
			</view>
		</view>
		<!-- 自定义气泡 -->
		<!-- <view v-if="showCustomCallout" class="address-bubble-content"
			:style="{ bottom: calloutBottom + 'px', left: calloutLeft + 'px' ,top:calloutTop+ 'px',right:calloutRight+ 'px'}">
			<text class="address-bubble-describe-blod">【将模型地点设置为】：</text>
			<text class="address-bubble-describe">
				<img class="address-bubble-describe-icon" mode="widthFix" src="../../../static/images/map-address-icon.png" alt="" />
				{{address}}
			</text>
			<view class="address-bubble-button">
				<button  @click="onCancel" class="address-bubble-cancel"><text
						class="address-bubble-cancel-text">取消</text></button>
				<button  @click="onConfirm" class="address-bubble-confirm"><text
						class="address-bubble-confirm-text">确认</text></button>
			</view>
		</view> -->
		<!-- <view class="address-bubble-content"
			style="{ bottom:'20px', left: '20px' ,top:'auto',right:'20px'}">
			<text class="address-bubble-describe-blod">【将模型地点设置为】：</text>
			<text class="address-bubble-describe">
				<img class="address-bubble-describe-icon" mode="widthFix" src="../../../static/images/map-address-icon.png" alt="" />
				测阿三大苏打测阿三大苏打测阿三大苏打测阿三大苏打测阿三大苏打
			</text>
			<view class="address-bubble-button">
				<button  @click="onCancel" class="address-bubble-cancel">
					<text class="address-bubble-cancel-text">取消</text>
				</button>
				<button  @click="onConfirm" class="address-bubble-confirm">
					<text class="address-bubble-confirm-text">确认</text>
				</button>
			</view>
		</view> -->
		<view v-if="showCustomCallout" class="marker-address-container">
			<view class="marker-address-content">
				<text class="marker-address-title">【将模型地点设置为】：</text>
				<view class="marker-address-describe">
					<image class="marker-address-describe-icon" mode="widthFix"
						src="@/static/images/map-address-icon.png">
					</image>
					<text class="marker-address-describe-text">{{address}}</text>
				</view>
				<view class="marker-address-button-group">
					<text class="marker-address-button-cancel" @click="onConfirm">确认</text>
					<text class="marker-address-button-line"></text>
					<text class="marker-address-button-confirm" @click="onCancel">取消</text>
				</view>
			</view>
		</view>
		<!-- 搜索 -->
		<view class="search-container" :style="{top:searchTop+'px'}">
			<view class="search-input">
				<view class="search-input-text">
					<!-- <uni-icons type="search" color="#999999" style="margin-right: 8px;"></uni-icons> -->
					<image class="search-input-text-icon" style="width:20px;height:20px;"
						src="@/static/images/search-icon.png"></image>
					<!-- <uni-icons type="search" color="#000000" style="margin-right: 8rpx;"></uni-icons> -->
					<input id='tipinput' ref="searchRef" class="form-item-input" :focus="inputFocus" v-model="keyword"
						@input="getAddress" placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="搜索地址" />
					<!-- <input id='tipinput' class="form-item-input" focus="true"
							placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="搜索地址" /> -->
				</view>
				<text class="search-input-button" @click="getAddress">搜索</text>
			</view>
			<view v-if="addressArr && addressArr.length>0" class="search-result">
				<text class="search-result-title">搜索地址结果</text>
				<scroll-view scroll-y="true" style="max-height: 400rpx;" @scrolltolower="addressBottom">
					<view class="search-result-list">
						<view class="search-result-list-item" v-for="(item,index) in addressArr" :key="index"
							@click="goNavLoaction(item)">
							<image class="position-icon" src="@/static/images/position-icon.png">
							</image>
							<text class="search-result-list-item-position">{{item.name}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 提交按钮 -->
		<!-- 	<view class="submit-container">
			<text class="submit-btn" @click="submit">提交审核</text>
		</view> -->
		<!-- 详情 -->
		<cover-view class="detailbox" v-if="isShowDetail">
			<cover-image class="closeicon" :src="closeImg" @click="closeDetail"></cover-image>
			<cover-view class="boxl">
				<cover-view class="boxlhd">{{detailData.name || '--'}}</cover-view>
				<cover-view class="boxlbd">{{detailData.address || '--'}}</cover-view>
			</cover-view>
			<cover-view class="boxr" @click="goRoute">
				<cover-image class="boxrimg" :src="goImg" mode=""></cover-image>
			</cover-view>
		</cover-view>
	</view>
</template>

<script>
	import {
		config
	} from "@/config.js"
	import {
		request
	} from "@/utils/request.js"
	const BASE_URL = config.BASE_URL_App
	export default {
		props: {
			// 商户id
			businessId: {
				type: String,
				default: ''
			},
			//中心点纬度
			centerLat: {
				type: String,
				default: ''
			},
			//中心点经度
			centerLng: {
				type: String,
				default: ''
			},
			//标记点数据
			markerData: {
				type: Array,
				default () {
					return []
				}
			},
			//多边形数据
			polygons: {
				type: Array,
				default () {
					return []
				}
			},
			//标记点图标宽度
			markerIconWidth: {
				type: Number,
				default: 22
			},
			//标记点图标高度
			markerIconHeight: {
				type: Number,
				default: 32
			},
			//标记点图标路径
			markerIconUrl: {
				type: String,
				default: ''
			},
			//缩放级别 取值范围为3-20
			scale: {
				type: Number,
				default: 16
			},
			//是否显示指南针
			isShowCompass: {
				type: Boolean,
				default: false
			},
			//是否支持缩放
			isEnableZoom: {
				type: Boolean,
				default: true
			},
			//是否支持拖动
			isEnableScroll: {
				type: Boolean,
				default: true
			},
			//是否支持旋转
			isEnableRotate: {
				type: Boolean,
				default: false
			}
		},
		watch: {
			markerData: {
				immediate: true, //初始化的时候是否调用
				deep: true, //是否开启深度监听
				handler(newValue, oldValue) {
					this.markerDatas = newValue
					this.showMarkers()
				}
			},
			polygons: {
				immediate: true, //初始化的时候是否调用
				deep: true, //是否开启深度监听
				handler(newValue, oldValue) {
					this.polygonsData = [...newValue]
				}
			}
		},
		data() {
			return {
				// mapHeight: uni.getSystemInfoSync().screenHeight - uni.getSystemInfoSync().statusBarHeight - 44,
				mapHeight: 1000,
				searchTop: uni.getSystemInfoSync().statusBarHeight + 44 + 16,
				maps: null,
				markerImg: require('../static/marker.png'),
				goImg: require('../static/go.png'),
				myaddressImg: require('../static/myaddress.png'),
				wxmapImg: require('../static/wxmap.png'),
				myaddressOnImg: require('../static/myaddress-on.png'),
				wxmapOnImg: require('../static/wxmap-on.png'),
				closeImg: require('../static/close.png'),
				polygonsData: [], //polygons区域数据
				markers: [], //markers数据
				detailData: {}, //选中展示详情数据
				nowLat: '', //我的当前位置
				nowLng: '',
				tabIndex: false,
				tabIndex2: false,
				isShowWxMap: false, //是否展示卫星地图 
				isShowDetail: false, //是否展示详情弹框
				wxMapShow: false, //是否展示卫星地图按钮（小程序展示）
				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: '',
					output: 'JSON',
					city_limit: false,
				},
				addressArr: [],
				keyword: "", //绑定的搜索关键字的的内容
				total: 0,
				showCustomCallout: false,
				// activeMarkerId: null,
				// activeMarkerName: '',
				calloutTop: "auto",
				calloutBottom: 0,
				calloutLeft: 0,
				calloutRight: 0,
				mapRegion: null,
				currentAddress: null,
				address: "",
				inputFocus: false, //控制输入框的焦点
			}
		},
		onReady() {

		},
		mounted() {
			console.log("mapheight", this.mapHeight)
			const type = uni.getSystemInfoSync().uniPlatform
			if (type == 'mp-weixin') {
				this.wxMapShow = true
			}
			this.showMarkers()
			if (!this.centerLat || !this.centerLng) this.getLocation()
		},
		methods: {
			onMarkerTap(e) {
				// 获取点击的 marker 的信息
				const markerId = e.markerId;
				const marker = this.markers.find(m => m.id === markerId);
				if (marker) {
					// 计算 marker 在屏幕上的位置
					const {
						latitude,
						longitude
					} = marker;
					const position = this.convertCoordinateToPosition(latitude, longitude);
					if (position) {
						// this.calloutTop = position.y - 50; // 50 是自定义气泡的高度的一半，调整气泡垂直居中
						// this.calloutLeft = position.x - 100; // 100 是自定义气泡的宽度的一半，调整气泡水平居中
						this.calloutTop = "auto";
						this.calloutLeft = 20;
						this.calloutRight = 20;
						this.calloutBottom = 20;
						this.showCustomCallout = true;
					}
				}
			},
			convertCoordinateToPosition(lat, lng) {
				console.log(this.mapRegion)
				// 将经纬度转换为屏幕上的位置
				if (!this.mapRegion) return null;

				const {
					latitude,
					longitude,
					latitudeDelta,
					longitudeDelta
				} = this.mapRegion;
				const mapWidth = 750;
				const mapHeight = this.mapHeight;
				console.log(this.mapRegion)
				// 将经纬度转换为相对于地图区域的相对位置
				const relativeY = (lat - latitude) / latitudeDelta;
				const relativeX = (lng - longitude) / longitudeDelta;

				// 将相对位置转换为屏幕坐标
				const y = mapHeight * (0.5 - relativeY);
				const x = mapWidth * (relativeX + 0.5);
				console.log(x)
				console.log(y)
				return {
					x,
					y
				};
			},
			// onRegionChange(e) {
			//      // 更新地图区域和尺寸
			//      this.mapRegion = e.detail;
			//      this.mapWidth = 750; // 假设地图宽度是750rpx，你需要根据实际情况来获取
			//      this.mapHeight = 500; // 假设地图高度是500px，你需要根据实际情况来获取
			//    },
			onConfirm() {
				console.log(this.currentAddress);
				// 处理确认逻辑
				this.showCustomCallout = false;
				this.submit()
			},
			onCancel() {
				console.log(this.currentAddress);
				// 处理取消逻辑
				this.showCustomCallout = false;
				this.currentAddress = null;
			},
			dingwei() {
				this.changeTab(1);
			},
			change(e) {
				console.log("change event detail:", e.detail); // 打印整个detail对象
				if (e.detail) {
					console.log("latitudeDelta:", e.detail.latitudeDelta); // 单独打印latitudeDelta
				} else {
					console.log("e.detail is not defined or not an object");
				}
				let mapObjs = uni.createMapContext('esaymap', this)
				mapObjs.getScale({
					success: (res) => {
						// this.scale = Math.floor(res.scale)
						this.$emit("changeZoom", res.scale);
					},
					fail: (res) => {
						console.log(res);
					}
				})
				this.mapRegion = e.detail;
				// console.log(this.scale);
			},
			//右侧类型切换
			changeTab(index) {
				if (index == 1) {
					this.tabIndex = !this.tabIndex
					if (this.tabIndex) this.getLocation()
					else this.showMarkers()
				} else {
					this.tabIndex2 = !this.tabIndex2
					if (this.tabIndex2) this.isShowWxMap = true
					else this.isShowWxMap = false
				}
			},
			//获取当前的地理位置
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					highAccuracyExpireTime: 3500,
					success: (res) => {
						this.nowLat = res.latitude
						this.nowLng = res.longitude
						let arr = [{
							id: 9999,
							latitude: res.latitude || '', //纬度
							longitude: res.longitude || '', //经度
							width: this.markerIconWidth, //宽
							height: this.markerIconHeight, //高
							iconPath: this.markerImg
						}];
						this.markers = [...arr];
						let mapObjs = uni.createMapContext('esaymap', this)
						mapObjs.moveToLocation({
							latitude: res.latitude,
							longitude: res.longitude
						}, {
							complete: res => {}
						})
					},
					fail: (res) => {
						if (res.errMsg == "getLocation:fail auth deny") {
							uni.showModal({
								content: '检测到您没打开获取信息功能权限，是否去设置打开？',
								confirmText: "确认",
								cancelText: '取消',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting({
											success: (res) => {}
										})
									} else {
										return false;
									}
								}
							})
						}
					}
				})
			},
			//到这去
			goRoute() {
				uni.openLocation({
					latitude: +this.detailData.latitude,
					longitude: +this.detailData.longitude,
					scale: 17,
					name: this.detailData.name || '--',
					address: this.detailData.address || '--'
				});
			},
			//地图打点展示marker
			showMarkers() {
				this.markers = []
				if (this.markerDatas && this.markerDatas.length > 0) {
					var arr = []
					for (var i = 0; i < this.markerDatas.length; i++) {
						arr.push({
							id: Number(this.markerDatas[i].id),
							latitude: this.markerDatas[i].latitude || '', //纬度
							longitude: this.markerDatas[i].longitude || '', //经度
							iconPath: this.markerDatas[i].markerUrl ? this.markerDatas[i].markerUrl : this
								.markerImg, //显示的图标        
							rotate: 0, // 旋转度数
							width: this.markerDatas[i].iconWidth ? this.markerDatas[i].iconWidth : this
								.markerIconWidth, //宽
							height: this.markerDatas[i].iconHeight ? this.markerDatas[i].iconHeight : this
								.markerIconHeight, //高
							callout: { //自定义标记点上方的气泡窗口 点击有效
								content: this.markerDatas[i].name, //文本
								// content:`<button class="confirm-btn">确认</button><button class="cancel-btn">取消</button>`,
								color: this.markerDatas[i].calloutColor || '#ffffff', //文字颜色
								fontSize: this.markerDatas[i].calloutFontSize || 14, //文本大小
								borderRadius: this.markerDatas[i].calloutBorderRadius || 6, //边框圆角
								padding: this.markerDatas[i].calloutPadding || 6,
								bgColor: this.markerDatas[i].calloutBgColor || '#0B6CFF', //背景颜色
								display: this.markerDatas[i].calloutDisplay || 'BYCLICK', //常显
							},
						})
					}
					this.markers = arr

				}
			},
			//点击标记点
			chooseItem(e) {
				let markerId = e.detail.markerId
				for (var i = 0; i < this.markerDatas.length; i++) {
					if (this.markerDatas[i].id == markerId) {
						this.isShowDetail = true
						this.detailData = this.markerDatas[i]
						this.$emit("clickMarker", this.markerDatas[i])
						break
					}
				}
			},
			//点击地图(仅微信小程序支持)
			clickMap(e) {
				// #ifdef MP-WEIXIN
				let lat = e.detail.latitude.toFixed(5)
				let lng = e.detail.longitude.toFixed(5)
				this.$emit("clickMap", {
					latitude: lat,
					longitude: lng
				})
				// #endif
			},
			//关闭详情弹框
			closeDetail() {
				this.detailData = {}
				this.isShowDetail = false
			},
			// 搜索地址
			getAddress() {
				this.markerData = [];
				// this.MapSearchForm.location = uni.getStorageSync('location')
				this.MapSearchForm.keywords = this.keyword;
				console.log("关键字", this.MapSearchForm);
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v5/place/text',
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: (res) => {
						console.log("keywords3", res); // 直接打印 res 对象
						if (res.statusCode == 200 && res.data && res.data.pois) {
							this.total = res.data.count;
							console.log("keywords4", this.total); // 现在应该可以正确打印
							if (this.MapSearchForm.page_num == 1) {
								this.addressArr = res.data.pois;
							} else {
								this.addressArr = [...this.addressArr, ...res.data.pois];
							}
							console.log("数组", this.addressArr);
						} else {
							this.toast('搜索地址信息出错');
						}
					},
					fail: (err) => {
						this.toast(err.errMsg);
					}
				});
			},
			addressBottom() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
					this.getAddress()
				}
			},
			// 在需要计算的位置调用此方法
			convertLatLonToPixel(lat, lon) {
				return new Promise((resolve, reject) => {
					const mapContext = uni.createMapContext('esaymap', this);

					// 使用 mapContext 的 getScale 方法获取缩放级别
					mapContext.getScale({
						success: (res) => {
							const scale = res.scale; // 缩放级别
							const mapWidth = 750 // 地图的宽度
							const mapHeight = this.mapHeight; // 地图的高度

							// 使用经纬度转换为像素的公式
							const x = (lon + 180) * (mapWidth / 360);
							const y = (90 - lat) * (mapHeight / 180);

							resolve({
								x,
								y
							});
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},

			// 调用convertLatLonToPixel方法并计算top和left
			calculatePosition(lat, lon) {
				this.convertLatLonToPixel(lat, lon).then(res => {
					// this.calloutTop = res.y; // 50 是自定义气泡的高度的一半，调整气泡垂直居中
					// this.calloutLeft = res.x - 100; // 100 是自定义气泡的宽度的一半，调整气泡水平居中
					this.calloutTop = "auto";
					this.calloutLeft = 20;
					this.calloutRight = 20;
					this.calloutBottom = 20;
					this.showCustomCallout = true;
				}).catch(err => {
					console.error(err);
				});
			},
			goNavLoaction(item) {
				this.markerData = [];
				this.markers = [];
				// this.isShowMap = false;
				console.log(item)
				const locat = item.location.split(",")
				console.log(locat)
				this.keyword = ""; //清空
				this.addressArr = [];
				this.address = item.pname + item.cityname + item.adname + item.address
				let marker = {
					id: 0,
					name: item.name, //标记点展示名字
					address: this.address,
					latitude: locat[1], //标记点纬度
					longitude: locat[0], //标记点经度
					markerUrl: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
					iconWidth: 25, //标记点图标宽度
					iconHeight: 29, //标记点图标高度
					title: item.name,
					// calloutColor: '#212121', //气泡窗口 文本颜色
					// calloutFontSize: 14, //气泡窗口 文本大小
					// calloutBorderRadius: 6, //气泡窗口 边框圆角
					// calloutPadding: 8, //气泡窗口 文本边缘留白
					// calloutBgColor: '#ffffff', //气泡窗口 背景颜色
					// calloutDisplay: 'ALWAYS', //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
				}

				// let marker = {
				// 	id: 0,
				// 	name: item.name, //标记点展示名字
				// 	address: address,
				// 	latitude: locat[1], //标记点纬度
				// 	longitude: locat[0], //标记点经度
				// 	iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
				// 	title: item.name,
				// 	callout: {
				// 		// content: `<view class="address-bubble-content">
				// 		// 			<view class="address-bubble-describe">${address}</view>
				// 		// 			<view class="address-bubble-button">
				// 		// 				<button id="confirmBtn" class="address-bubble-confirm">确认</button>
				// 		// 				<button id="cancelBtn" class="address-bubble-cancel">取消</button>
				// 		// 			</view>
				// 		// 		</view>`,
				// 		content: `地址: ${item.pname}${item.cityname}${item.adname}${item.address}`,
				// 		display: 'BYCLICK',
				// 		textAlign: 'center',
				// 		borderRadius: '6',
				// 	}
				// }

				this.markerData.push(marker);
				console.log(this.markerData)
				console.log("添加")
				// 更新地图的中心坐标
				this.nowLat = locat[1];
				this.nowLng = locat[0];
				this.calculatePosition(locat[1], locat[0]);

				this.currentAddress = {
					address: this.address,
					latitude: marker.latitude,
					longitude: marker.longitude,
				}
				// const position = this.convertCoordinateToPosition(locat[1], locat[0]);
				// console.log(position)
				// if (position) {
				//   this.calloutTop = position.y - 50; // 50 是自定义气泡的高度的一半，调整气泡垂直居中
				//   this.calloutLeft = position.x - 100; // 100 是自定义气泡的宽度的一半，调整气泡水平居中
				//   this.showCustomCallout = true;
				// }
				// // this.calloutTop = 200;
				// // this.calloutLeft = 50;
				// // this.showCustomCallout = true;
				// console.log(this.calloutTop)




				// const markerId = Number(this.markerDatas[0].id);
				// console.log(markerId)
				// const marker = this.markers.find(m => m.id === markerId);
				// if (marker) {
				//   // 计算 marker 在屏幕上的位置
				//   const { latitude, longitude } = marker;
				//   console.log(latitude, longitude)
				//   const position = this.convertCoordinateToPosition(latitude, longitude);
				//   console.log(position)
				//   if (position) {
				//     this.calloutTop = position.y - 50; // 50 是自定义气泡的高度的一半，调整气泡垂直居中
				//     this.calloutLeft = position.x - 100; // 100 是自定义气泡的宽度的一半，调整气泡水平居中
				//     this.showCustomCallout = true;
				//   }
				//   console.log(this.calloutTop)
				// }
				// 让输入框失去焦点
				console.log("失去焦点啊啊啊", this.$refs.searchRef)
				this.$refs.searchRef.blur();
				this.inputFocus = false;
			},
			// 提交审核
			submit() {
				if (this.currentAddress == null) {
					uni.showToast({
						title: `请选择地点！`
					})
					return;
				}
				const token = uni.getStorageSync('token')
				console.log('token', token);
				console.log("提交审核", this.currentAddress, this.businessId)
				uni.request({
					url: BASE_URL + '/api/user/business/set-location',
					// url: '/api/user/business/set-location',
					method: 'POST',
					header: {
						// 'version_type': uni.getSystemInfoSync().platform, //android或者ios
						// 'edition_number': uni.getStorageSync('versionCode'), // 打包时manifest设置的版本号 
						'content-type': 'application/json',
						'Authorization': 'Bearer ' + token
					},
					data: {
						address: this.currentAddress.address,
						latitude: this.currentAddress.latitude,
						longitude: this.currentAddress.longitude,
						business_id: this.businessId
					},
					success: (res) => {
						console.log(res)
						if (res.data.code == 200) {
							console.log("提交成功", {
								...res
							})
							// 显示提交成功弹框
							uni.showToast({
								title: `提交成功！`
							})
							// uni.navigateBack()
							// uni.navigateTo({
							// 	url: '/pages/shop/shop'
							// });
						}

					},
					fail: (err) => {
						// 请求失败的处理逻辑
						console.error('fail:', err);
					}
				});
			},

		}
	}
</script>

<style lang="scss">
	/* 添加必要的样式 */
	// .address-bubble-container{
	// 	position: relative;
	.address-bubble-content {
		// position: absolute;
		// min-height:50px;
		// width: 90%;
		background-color: #ffffff;
		border-radius: 10px;
		padding: 20px;
		box-sizing: border-box;

		// left:20px;
		// right:20px;
		// bottom:20px;
		// box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		// position: fixed;
		// left: 20px;
		// right: 20px;
		// bottom: 20px;
		// z-index: 100;
		// padding: 20px;
		// background-color: #ffffff;
		.address-bubble-describe-blod {
			color: #212121;
			font-family: HarmonyOS Sans;
			font-size: 16px;
			font-weight: 600;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: left;
			text-indent: 0;
			margin-bottom: 20px;
		}

		.address-bubble-describe {
			color: #515E63;
			font-family: HarmonyOS Sans;
			font-size: 14px;
			font-weight: normal;
			line-height: 22px;
			margin-bottom: 20px;
			display: flex;
			align-items: flex-start;
			justify-content: flex-start;

			.address-bubble-describe-icon {
				width: 12px;
				height: auto;
				margin-right: 4px;
				margin-top: 4px;
			}
		}

		.address-bubble-button {
			width: 100%;
			height: 24px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;

			.address-bubble-confirm {
				width: 50%;
				height: 24px;
				border: none;
				outline: none;
				display: flex;
				align-items: center;
				justify-content: center;
				border-left: 1px solid #EBEBEB;
				box-sizing: border-box;
				background-color: #ffffff;
				border-radius: 0;

				.address-bubble-confirm-text {
					width: 100%;
					color: #FF4C4C;
					font-family: HarmonyOS Sans;
					font-size: 16px;
					font-weight: 600;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

			.address-bubble-cancel {
				width: 50%;
				height: 24px;
				box-sizing: border-box;
				border: none;
				outline: none;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #ffffff;
				border-radius: 0;

				.address-bubble-cancel-text {
					width: 100%;
					color: #5D96FF;
					font-family: HarmonyOS Sans;
					font-size: 16px;
					font-weight: 600;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

			uni-button:after {
				border: none;
			}
		}

	}

	.triangle {
		position: absolute;
		bottom: -20px;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-top: 20px solid #ffffff;
	}

	// }

	.custom-callout {
		position: absolute;
		width: 200rpx;
		background-color: #ffffff;
		border-radius: 10rpx;
		padding: 10rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		/* 其他样式 */
	}

	// 底部地址
	.marker-address-container {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 20px;
		z-index: 10000;
		padding: 20px;
		box-sizing: border-box;

		.marker-address-content {
			width: 335px;
			background-color: #ffffff;
			border-radius: 10px;
			padding: 20px;
			box-sizing: border-box;

			.marker-address-title {
				color: rgb(33, 33, 33);
				font-family: 思源黑体;
				font-size: 16px;
				font-weight: 500;
				line-height: 23px;
				letter-spacing: 0px;
				text-align: left;
				margin-bottom: 20px;
			}

			.marker-address-describe {
				width: 300px;
				margin-bottom: 20px;
				display: flex;
				flex-direction: row;
				align-items: flex-start;
				justify-content: flex-start;
				overflow: visible;

				.marker-address-describe-icon {
					width: 12px;
					height: auto;
					margin-right: 4px;
					margin-top: 4px;
				}

				.marker-address-describe-text {
					// display: block; /* 将文本作为块级元素，确保可以换行 */
					width: 280px;
					color: rgb(81, 94, 99);
					font-family: 思源黑体;
					font-size: 14px;
					font-weight: 400;
					line-height: 20px;
					letter-spacing: 0px;
					text-align: left;
					word-break: break-all;
					/* 允许在单词内部进行换行 */
					white-space: normal;
				}
			}

			.marker-address-button-group {
				width: 300px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				.marker-address-button-cancel {
					width: 135px;
					height: 40px;
					border-radius: 6px;
					background-image: linear-gradient(to right, #BE93FA, #52C3EE);
					color: #ffffff;
					font-family: 思源黑体;
					font-size: 14px;
					font-weight: normal;
					line-height: 40px;
					text-align: center;
				}

				.marker-address-button-line {
					width: 1px;
					height: 24px;
					background-color: #EBEBEB;
					margin: 0 12px;
				}

				.marker-address-button-confirm {
					width: 135px;
					height: 40px;
					border-radius: 6px;
					border: 1px solid #FF5D50;
					color: #FF5D50;
					font-family: 思源黑体;
					font-size: 14px;
					font-weight: normal;
					line-height: 40px;
					text-align: center;
					background-color: #ffffff;
				}
			}
		}
	}

	.search-container {
		position: fixed;
		left: 0;
		right: 0;
		z-index: 10000;
		padding: 16px 20px 0 20px;
		box-sizing: border-box;

		.search-input {
			width: 335px;
			height: 38px;
			border-radius: 50px;
			background-color: #ffffff;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: 3px 3px 3px 16px;
			box-sizing: border-box;

			.search-input-text {
				flex: 1;
				height: 32px;
				display: flex;
				align-items: center;
				flex-direction: row;
				justify-content: flex-start;
				padding: 3px 3px 3px 0px;
				box-sizing: border-box;

				.search-input-text-icon {
					width: 30px;
					height: auto;
				}

				.form-item-input {
					flex: 1;
					font-size: 14px;
					color: #9BA0AE;
					padding: 0 14px;
					box-sizing: border-box;
				}

				v-deep.uni-input-input {
					color: #212121 !important;
					font-family: 阿里巴巴普惠体;
					font-size: 16px;
					font-weight: normal;
				}
			}

			.search-input-button {
				width: 60px;
				height: 32px;
				border-radius: 50px;
				background-image: linear-gradient(to right, #BE93FA, #52C3EE);
				color: #ffffff;
				font-family: 思源黑体;
				font-size: 14px;
				font-weight: normal;
				line-height: 32px;
				text-align: center;
			}
		}

		.search-result {
			margin-top: 10px;
			width: 335px;
			border-radius: 10px;
			background: rgb(255, 255, 255);
			padding: 0 12px;
			box-sizing: border-box;

			.search-result-title {
				color: #989898;
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 400;
				line-height: 14px;
				letter-spacing: 0px;
				text-align: center;
				padding: 12px 0;
				box-sizing: border-box;
			}

			.search-result-list {
				border-top: 1px solid #F7F7F7;

				.search-result-list-item {
					padding: 12px 0;
					box-sizing: border-box;
					display: flex;
					flex-direction: row;
					align-items: center;

					.position-icon {
						width: 16px;
						height: 16px;
						margin-right: 2px;
					}

					.search-result-list-item-position {
						flex: 1;
						color: rgb(33, 33, 33);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: normal;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}

	// 提交按钮
	.submit-container {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 100;
		padding: 20px;
		box-sizing: border-box;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;

		.submit-btn {
			flex: 1;
			height: 52px;
			color: rgb(255, 255, 255);
			font-family: 阿里巴巴普惠体;
			font-size: 16px;
			font-weight: normal;
			line-height: 52px;
			letter-spacing: 0px;
			text-align: center;
			// background-color: #52C3EE;
			background-image: linear-gradient(to right, #BE93FA, #52C3EE);
			// background-image: url('../../static/images/vip/active-add-button.png');
			// background-repeat: no-repeat;
			// background-size: 100% 100%;
			border-radius: 10px;
		}
	}

	.rightbox {
		padding: 0 8rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(200, 200, 200, 0.5);
		border-radius: 14rpx;
		position: fixed;
		top: 154rpx;
		right: 20rpx;
	}

	.boxitem {
		display: flex;
		flex-direction: column;
		text-align: center;
		padding-bottom: 8rpx;
		border-bottom: 2rpx solid #E4E4E4;
	}

	.itemimg {
		width: 40rpx;
		height: 40rpx;
		margin: 16rpx auto 4rpx;
	}

	.itemname {
		font-size: 22rpx;
		font-weight: 400;
		color: #333333;
		line-height: 42rpx;
	}

	.active {
		color: #2765F1;
	}

	.detailbox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: calc(100% - 128rpx);
		padding: 24rpx 32rpx;
		background: #FFFFFF;
		border-radius: 16rpx;
		position: fixed;
		bottom: 32rpx;
		left: 32rpx;
	}

	.closeicon {
		width: 40rpx;
		height: 40rpx;
		position: absolute;
		right: 16rpx;
		top: 12rpx;
	}

	.boxl {
		width: calc(100% - 84rpx);
	}

	.boxlhd {
		margin-bottom: 16rpx;
		white-space: pre-wrap;
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		line-height: 48rpx;
	}

	.boxlbd {
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
		line-height: 46rpx;
		white-space: pre-wrap;
	}

	.boxr {
		width: 96rpx;
		display: flex;
		align-items: center;
		position: relative;
	}

	.boxr::before {
		width: 2rpx;
		height: 96rpx;
		background: #e3e3e3;
		content: "";
		position: relative;
		left: 0;
		z-index: 99;
	}

	.boxrimg {
		width: 64rpx;
		height: 64rpx;
		margin: 0 auto;
	}

	// .address-bubble-content {
	// 	.address-bubble-describe {
	// 		color: rgb(33, 33, 33);
	// 		font-family: HarmonyOS Sans;
	// 		font-size: 14px;
	// 		font-weight: 500;
	// 		line-height: 16px;
	// 		letter-spacing: 0px;
	// 		text-align: left;
	// 		text-indent: 0;
	// 	}

	// 	.address-bubble-button {
	// 		display: flex;
	// 		align-items: center;
	// 		justify-content: space-between;
	// 		margin-top: 8px;

	// 		.address-bubble-confirm {
	// 			width: 200px;
	// 			height: 22px;
	// 			border-radius: 60px;
	// 			background: rgb(83, 194, 238);
	// 			color: rgb(255, 255, 255);
	// 			font-family: HarmonyOS Sans;
	// 			font-size: 10px;
	// 			font-weight: 400;
	// 			line-height: 12px;
	// 			letter-spacing: 0px;
	// 			text-align: center;
	// 			border: none;
	// 			outline: none;
	// 		}

	// 		.address-bubble-cancel {
	// 			width: 200px;
	// 			height: 22px;
	// 			box-sizing: border-box;
	// 			border: 0.8px solid rgb(120, 120, 120);
	// 			border-radius: 60px;
	// 			color: rgb(120, 120, 120);
	// 			font-family: HarmonyOS Sans;
	// 			font-size: 10px;
	// 			font-weight: 400;
	// 			line-height: 12px;
	// 			letter-spacing: 0px;
	// 			text-align: center;
	// 		}
	// 	}

	// }
</style>
<style>
	/deep/#easy-map .amap-container .amap-logo {
		display: none !important;
		z-index: -100 !important;
	}
</style>