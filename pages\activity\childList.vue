<template>
	<view class="appPage">
		<u-navbar :customBack="goBack" back-text="" title="发布" :background="{ backgroundColor: '#191C26' }"
			:border-bottom="false" height="60" title-color="#fff" back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">子活动管理</view>
				</view>
			</view>
			<view class="navbar-right" slot="right" v-if="self">
				<view class="rightBtn" style="margin-right: 40rpx" @click="toCreate(main_uuid)">
					新建
				</view>
			</view>
		</u-navbar>
		<scroll-view scroll-y="true">
			<view class="list" v-if="self">
				<view class="t_display">
					<image src="../../static/images/time.png" mode="aspectFill"></image>
					是否要求按活动顺序加入
				</view>
				<view class="t_display" style="position: relative; right: -50rpx">
					<switch style="transform: scale(0.7)" type="switch" :checked="isChecked"
						@change="handleSwitchChange" />
				</view>
			</view>
			<view>
				<view v-if="self">
					<uni-swipe-action>
						<uni-swipe-action-item v-for="(item, index) in activeArr" @change="(e)=>change(index, e)"
							:right-options="options" :show="item.isOpened" :auto-close="true" :threshold="20">
							<view class="list" style="display: flex; justify-content: space-between"
								@click="toDetails(item.uuid)">
								<view class="t_display" style="overflow: hidden;
					-webkit-line-clamp: 1;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					word-break: break-all;
					width: 70%;">
									<!-- <image style="width: 24rpx; height: 24rpx" src="../../static/active/svg.png"
										mode="aspectFill">
									</image> -->
									{{ item.title }}
								</view>
								<view class="" v-if="item.member"
									style="width: 120rpx; height: 54rpx; position: relative; margin-top: 12rpx;">
									<image src="../../static/active/participated.png" mode="aspectFill"
										style="width: 100%; height: 100%; top: -26rpx;">
									</image>
								</view>
								<view class="t_display">
									<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"
										style="margin-right: 40rpx"></uni-icons>
								</view>
							</view>
							<template v-slot:right>
								<view class="slot-button up" @click="()=>{if(item.up){
										cancelUp({position:'right',content:{text:'up',uuid:item.uuid}},item.uuid,item.title)
									}else{
										bindClick({position:'right',content:{text:'up',uuid:item.uuid}},item.uuid,item.title)} 
									}">
									<image v-if="item.up" src="../../static/active/uping.png" mode="aspectFill"
										style="width: 24rpx; height: 32rpx; margin-left: 36rpx;"></image>
									<image v-else src="../../static/active/up.png" mode="aspectFill"
										style="width: 24rpx; height: 32rpx; margin-left: 36rpx;"></image>
								</view>
								<view class="slot-button del"
									@click="showModal({position:'right',content:{text:'del',uuid:item.uuid}},item.uuid,item.title)">
									<image src="../../static/active/del.png" mode="aspectFill"
										style="width: 24rpx; height: 32rpx;margin-left: 36rpx;"></image>
								</view>
							</template>
							<view v-if="item.up"
								style="width: 100%; height: 1rpx; border: 1rpx solid rgba(196, 196, 196, 0.12); margin-bottom: 60rpx;">

							</view>
						</uni-swipe-action-item>
					</uni-swipe-action>
				</view>
				<view v-else>
					<view class="list" v-for="(item, index) in activeArr" @click="toDetails(item.uuid)"
						style="width: 100%; display: flex; justify-content: space-between">
						<view class="t_display" style="overflow: hidden;
					-webkit-line-clamp: 1;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					overflow: auto;
					word-break: break-all;width: 68%;">
							<!-- <image style="width: 24rpx; height: 24rpx" src="../../static/active/svg.png"
								mode="aspectFill">
							</image> -->
							{{ item.title }}
						</view>
						<view class="" v-if="item.member"
							style="width: 120rpx; height: 54rpx; position: relative; margin-top: 12rpx;">
							<image src="../../static/active/participated.png" mode=""
								style="width: 90%; height: 100%; top: -26rpx;">
							</image>
						</view>
						<view class="t_display">
							<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"
								style="margin-right: 40rpx"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<u-modal v-model="show" @confirm="confirm" ref="uModal" :async-close="true" :show-title="false"
			show-cancel-button :content="content" confirm-color="red"></u-modal>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				main_uuid: '',
				activeArr: [],
				self: true,
				delInfo: {},
				isChecked: false,
				show: false,
				content: "",
				options: [{
						text: '',
						name: 'up',
						style: {
							backgroundColor: 'rgb(190, 148, 250)',
							backgroundImage: 'url(../../static/active/up.png)',
							height: '60px'
						},
					},
					{
						text: '',
						name: 'del',
						style: {
							backgroundColor: 'rgb(255, 76, 76)',

							height: '60px'
						},
					},
				],
				isOpened: 'none',
			}
		},
		onLoad(opthion) {
			this.main_uuid = opthion.id
		},
		// onShow() {
		// 	// 禁用滑动返回
		// 	plus.navigator.setGestureNavigation({
		// 		back: false
		// 	});
		// },
		mounted() {
			this.getData(this.main_uuid)
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			toDetails(e) {
				uni.navigateTo({
					url: '/pages/activity/details?id=' + e + "&type=child",
				})
			},
			toCreate(e) {
				uni.navigateTo({
					url: '/pages/activity/create?id=' + e,
				})
			},
			getData(e) {
				this.$http
					.get('/activity/child-list', {
						uuid: e,
					})
					.then((res) => {
						console.log(res)
						res.message.list.map(item => {
							item.isOpened = "none"
						})
						this.activeArr = res.message.list
						this.self = res.message.self
						this.isChecked = res.message.order_limit
					})
			},
			onMoveUp(e) {
				this.$http
					.post('/activity/up', {
						uuid: e,
					})
					.then((res) => {
						this.getData(this.main_uuid)
					})
				console.log('up')
			},
			onDelete(e) {
				console.log('e', e);
				this.$http.post('/activity/del', {
					uuid: e,
				}).then((res) => {
					this.getData(this.main_uuid)
				}).catch((err) => {
					console.log('err', err);
				})
			},
			handleSwitchChange(e) {
				console.log(e)
				this.$http.post('/activity/set-order-join-switch', {
					main_uuid: this.main_uuid,
					opt: e.target.value
				}).then((res) => {
					this.getData(this.main_uuid)
				}).catch((err) => {
					console.log('err', err);
				})
			},
			change(index, e) {
				this.activeArr.map((item, key) => {
					if (index === key) {
						item.isOpened = e
					} else {
						item.isOpened = "none"
					}
				})
				// this.activeArr[index].isOpened = e
				// console.log('返回：', this.activeArr)
			},
			showModal(e, uuid, title) {
				this.delInfo = {
					title,
					uuid,
					e
				}
				this.content = "是否删除子活动" + `${this.delInfo.title}`
				console.log(this.delInfo);
				this.show = true;
			},
			confirm() {
				this.show = false;
				const {
					e,
					uuid,
					title
				} = this.delInfo
				this.bindClick(
					e, uuid, title
				)
			},
			bindClick(e, uuid, title) {

				if (e.content.text == 'up') {
					this.onMoveUp(uuid)
				} else {
					this.onDelete(e.content.uuid)
				}
			},
			cancelUp(e, uuid, title) {
				this.$http.post('/activity/cancel-up', {
					uuid,
				}).then((res) => {
					this.getData(this.main_uuid)
				}).catch((err) => {
					console.log('err', err);
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.appPage {
		background: rgb(41, 44, 51);
		padding: 60rpx 40rpx 40rpx 40rpx;
		height: 100vh;
	}

	.list {
		width: 100%;
		margin-bottom: 60rpx;
		padding-left: 40rpx;
		font-size: 36rpx;
		font-weight: 400;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		border-radius: 20rpx;
		background: rgb(56, 58, 67);

		image {
			width: 28rpx;
			height: 28rpx;
			margin-right: 60rpx;
		}
	}

	.slot-button {
		width: 104rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
	}

	.up {
		background-color: rgb(190, 148, 250);
		height: 120rpx;
	}

	.del {
		background-color: rgb(255, 76, 76);
		border-radius: 0 10px 10px 0;
		height: 120rpx;
	}
</style>