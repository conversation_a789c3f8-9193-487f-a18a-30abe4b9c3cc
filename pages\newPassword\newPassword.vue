<template>
	<view class="appPage">
		<view style="margin: 30rpx 0 ;">
			<uni-icons type="left" color="#fff" size="26" @click="goBack"></uni-icons>
		</view>
		<view class="inputBg">
			<u-input v-model="pwd" type="password" placeholder="请输入新密码" :border="false" clearable />
		</view>
		<view class="inputBg">
			<u-input v-model="secondPwd" type="password" placeholder="再次输入新密码" :border="false" clearable />
		</view>
		<view class="tips">
			密码至少需要8位，至少含数字/字母/字符2总组合
		</view>
		<view class="login" @click="goLogin">完成
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pwd: "",
				secondPwd: ""
			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goBack() {
				uni.navigateBack()
			},
			goLogin() {
				if (this.pwd != this.secondPwd) return this.toast('两次输入的密码不一致，请重新输入');
				this.$http.post('/api/user/first-password', {
					"password": this.pwd
				}).then(res => {
					this.toast('密码修改成功');

					setTimeout(() => {
						uni.navigateBack()
					}, 500)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 32rpx;

		.login {
			height: 94rpx;
			line-height: 94rpx;
			margin-top: 100rpx;
			border-radius: 14rpx;
			font-size: 32rpx;
			text-align: center;
			background: linear-gradient(#4BC6ED, #BC93F2);
		}

		.inputBg {
			color: #fff;
			background: #201F1F;
			height: 97rpx;
			// line-height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
			margin-bottom: 42rpx;
		}

		.tips {
			margin-top: 32rpx;
			font-size: 22rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: #848484;
			line-height: 32rpx;
		}
	}
</style>