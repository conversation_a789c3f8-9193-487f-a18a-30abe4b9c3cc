<template>
	<view class="">
		<uni-popup ref="popupMoress" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="t_display" style="margin-bottom: 20rpx;">
					<image class="avatar" :src="popupInfo.user_info? popupInfo.user_info.avatar:''" mode="aspectFill">
					</image>
					<view class="name">{{popupInfo.user_info?popupInfo.user_info.nickname:''}}</view>
				</view>
				<view class="item t_display" @click="black(popupInfo.user_info.uid)">
					<image class="disable" src="../../../static/images/lahei.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">拉黑</view>
						<view style="font-size: 24rpx;">将此人加入黑名单</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" @click="report(popupInfo.user_info.uid)">
					<image class="disable" src="../../../static/images/jubao.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">举报</view>
						<view style="font-size: 24rpx;">举报该内容</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" @click="notRecommend">
					<image class="disable" src="../../../static/images/lahei.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">减少此类推荐</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="close">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			popupInfo: {
				default: {
					user_info: {
						nickname: ""
					}
				}
			}
		},
		data() {
			return {

			}
		},
		methods: {
			notRecommend() {
				this.close()
				this.$http.post('/api/moment/decrease-recommend', {
					"momentId": this.popupInfo.moment_id
				})
			},
			report(uid) {
				this.close()
				uni.navigateTo({
					url: "/pages/report/report?params=" + uid + '&type=' + 1
				})
				// this.$http.post('/api/report', {
				// 	"moment_id": this.popupInfo.moment_id || 0,
				// 	"report_uid": uid,
				// 	"report_id": 1,
				// 	"remark": "默认的举报",
				// }).then(res => {
				// 	this.toast('举报成功')
				// })
			},
			black(uid) {
				this.close()
				this.$http.post('/api/user/black-set', {
					uid,
					"opt": 1
				}).then(res => {
					this.toast('拉黑成功')
				})
			},
			close() {
				this.$refs.popupMoress.close()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			open() {
				this.$refs.popupMoress.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// padding-top: 30rpx;
		// height: 304rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;


		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			padding-bottom: 18rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 20rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}
</style>