<template>
	<view class="appPage">
		<view class="head" v-if="current!=1">
			<view class="inputBg">
				<uni-icons type="search" color="#fff" style="margin-right: 8rpx;"></uni-icons>
				<u-input v-model="username" type="text" placeholder="搜索用户备注或昵称" @confirm="search" :border="false"
					clearable />
			</view>
			<span class="search" @click="search">
				搜索
			</span>
		</view>
		<view class="title">
			{{['我的关注','我的粉丝','朋友'][current]}}<span style="margin-left: 10rpx;">({{info.count}})人</span>
		</view>
		<view class="item" v-for="(item,index) in list" :key="index">
			<view class="t_display" @click="goNav('/pages/otherPage/otherPage?uuid='+item.uuid)">
				<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
				<view class="name">{{item.nickname}}</view>
			</view>
			<view class="t_display">
				<!-- //动态里的relation字段关系是1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
				<view class="rightBtn" @click="goSend(item)" v-if="current==2">
					发私信
				</view>
				<view class="fous" @click="addFous(item.uuid,index)" v-else-if="[3].includes(item.relation)">
					回关
				</view>
				<view class="rightBtn" v-else>
					{{["关注","已关注","互相关注","关注"][item.relation]}}
				</view>
				<image @click="showPopup(item,index)" style="width: 13rpx;height: 38rpx;" v-if="current!=1"
					src="@/static/images/moreIcon.png" mode=""></image>
			</view>
		</view>
		<uni-popup ref="popup" type="bottom" background-color="#fff" :safe-area="false">
			<view class="cPopup">
				<view class="t_display">
					<image class="avatar" :src="popupInfo.avatar" mode="aspectFill"></image>
					<view class="name">{{popupInfo.nickname}}</view>
				</view>
				<!-- <view class="item t-display" @click="delFans(popupInfo.uid)" v-if="current==2">
					<view class="rightInfo">
						移除粉丝
					</view>
					<image class="img32" src="../../../static/images/yichufensi.png" mode=""></image>
				</view> -->
				<view class="item t-display" @click="cancelBlack(popupInfo.uuid)">
					<view class="rightInfo">
						取消关注
					</view>
					<image class="img32" src="../../../static/images/quxiaoguanzhu.png" mode=""></image>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="cancel">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			titleNumber: Number,
			current: {
				type: Number,
				default: 0,
			},
			page: {
				type: Number,
				default: 1,
			},
		},
		data() {
			return {
				popupInfo: "",
				totalCount: 0,
				username: "",
				list: [],
				flag: true,
				info: ""
			}
		},
		watch: {
			current: {
				handler(oldVal, newVal) {
					this.list = []
					this.username = ""
					this.flag = true
					this.getData()
				}
			},
			page: {
				handler(oldVal, newVal) {
					this.getData()
				}
			},
		},
		created() {
			this.getData()
		},
		methods: {
			search() {
				this.flag = true
				this.list = []
				this.page = 1
				this.getData()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			delFans(uid) {
				this.$http.post('/api/user/fans/del', {
					uid
				}).then(res => {
					this.list[ids].relation = res.message.user_info.relation
				})
			},
			addFous(uuid, ids) {
				this.$http.post('/api/user/follow/add', {
					uuid
				}).then(res => {
					this.list[ids].relation = res.message.user_info.relation
				})
			},
			goSend(item) {
				let params = {
					account: item.im_id,
					chatHeadImg: item.avatar,
					chatName: item.nickname,
					uid: 'p2p-' + item.im_id,
					roomtype: 'p2p',
					nuck: item.nickname,
				}
				uni.navigateTo({
					url: "/pages/HM-chat/HM-chat?userItem=" + encodeURIComponent(JSON.stringify(params))
				})
			},
			cancelBlack(uuid) {
				this.$http.post('/api/user/follow/del', {
					uuid,
				}).then(res => {
					this.list.splice(this.popupInfo.index, 1)
					this.$refs.popup.close()
				})
			},
			getData() {
				if (!this.flag) return
				switch (this.current) {
					case 0:
						this.$http.get('/api/user/follow/get', {
							keyword: this.username,
							page: this.page
						}).then(res => {
							console.log(this.page == 1, '=========follow=========', this.page);
							this.info = res.message
							this.flag = !!res.message.list.length
							if (this.page == 1) {
								this.list = res.message.list
							} else {
								this.list.push(...res.message.list)
							}
						})
						break;
					case 1:
						this.$http.get('/api/user/fans/get', {
							keyword: this.username,
							page: this.page
						}).then(res => {
							this.info = res.message
							this.flag = !!res.message.list.length
							if (this.page == 1) {
								this.list = res.message.list
							} else {
								this.list.push(...res.message.list)
							}
							// this.list.push(...this.list, ...res.message.list)
						})
						break;
					case 2:
						this.$http.get('/api/user/friend/get', {
							keyword: this.username,
							page: this.page
						}).then(res => {
							this.info = res.message
							this.flag = !!res.message.list.length
							// this.list.push(...this.list, ...res.message.list)
							if (this.page == 1) {
								this.list = res.message.list
							} else {
								this.list.push(...res.message.list)
							}
						})
						break;
					default:
						break;
				}
			},
			cancel() {
				this.$refs.popup.close()
			},
			showPopup(item, index) {
				this.popupInfo = {
					...item,
					index
				}
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.title {
		font-size: 26rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		color: #FFFFFF;
		margin: 0 32rpx;
	}

	.rightBtn {
		width: 148rpx;
		height: 58rpx;
		background: #3D404A;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 58rpx;
		margin-right: 32rpx;
	}

	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: space-between;

			.rightInfo {
				// margin-left: 35rpx;
			}

			.disable {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.head {
		display: flex;
		align-items: center;

		.search {
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			width: 600rpx;
			margin: 32rpx;
			color: #fff;
			background: #22252F;
			height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
		}
	}


	.item {
		padding: 24rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;



		.fous {
			width: 148rpx;
			height: 58rpx;
			text-align: center;
			line-height: 58rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
			margin-right: 32rpx;
		}

		.avatar {
			width: 108rpx;
			height: 108rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 42rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>