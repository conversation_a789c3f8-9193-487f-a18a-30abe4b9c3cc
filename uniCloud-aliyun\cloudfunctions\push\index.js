// 简单的使用示例
'use strict';
const uniPush = uniCloud.getPushManager({
	appId: "__UNI__2DF6F7C"
}) //注意这里需要传入你的应用appId，用于指定接收消息的客户端
exports.main = async (event, context) => {
	const {
		ctid,
		title,
		path,
		content
	} = event.queryStringParameters
	return await uniPush.sendMessage({
		"push_clientid": ctid, //填写上一步在uni-app客户端获取到的客户端推送标识push_clientid
		"title": title || "LightingBall",
		"content": content || "",
		"payload": path
	})
};