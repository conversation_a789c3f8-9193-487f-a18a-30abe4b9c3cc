<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">我的商户</view>
		</view>
		<uni-swipe-action class="list">
			<!-- 基础用法 -->
			<view class="list-item-container" v-for="(item, index) in activeList">
				<uni-swipe-action-item class="list-item" :right-options="item.options" @click="onClick($event,item.id)" @change="change($event,index,item.id)">
					<view class="item" @click="goInfo(item.id)">
						<image v-if="item.audit_status == 3" class="activeStatus" src="../../static/images/vip/fail.png" mode=""></image>
						<image v-else-if="item.audit_status == 2" class="activeStatus" src="../../static/images/vip/success.png" mode=""></image>
						<image v-else class="activeStatus" src="../../static/images/vip/status.png" mode=""></image>
						<view class="activeimageContainer">
							<image class="activeimage" :src="item.cover" mode="aspectFill"></image>
						</view>
						<view class="item-info">
							<view class="item-title">
								{{ item.title }}
							</view>
							<view class="item-time">
								<image class="time-clock" src="../../static/images/vip/clock.png" mode=""></image>
								<view class="time">{{item.timeline}}</view>
							</view>
						</view>
					</view>
				</uni-swipe-action-item>
				<view class="delete-tips" v-if="index == deleteIndex"><image class="delete-tips-icon" src="../../static/images/tips-icon.svg"></image>删除（如已发布，并且有时间，未到时间不可删除）</view>
			</view>
		</uni-swipe-action>
		<view class="bottom">
			<!-- <image class="add" src="../../static/images/vip/add_active.png" mode=""></image> -->
			<button class="add-button" type="default" @click="goAddActive">
				<image class="icon-button-add" src="../../static/images/vip/icon-button-add-active.png" mode=""></image>
				<view class="add-text">添加新活动</view>
			</button>
			<!-- <button class="submit-button" type="default">提交审核</button> -->
			<!-- <image class="submit" src="../../static/images/vip/submit_active.png" mode=""></image> -->
		</view>
		<!-- <liu-popup v-if="isPopupVisible" type="center" ref="center" width="323px" height="141px" radius="20px">
			<view class="pop">
				<view class="pop-title">是否删除该活动</view>
				<view class="pop-button">
					<view class="fail" @click="fail">取消</view>
					<view class="success" @click="success">确认删除</view>
				</view>
			</view>
		</liu-popup> -->
		<view v-if="isPopupVisible" class="pop-container">
			<view class="pop">
				<view class="pop-title">是否删除该活动</view>
				<view class="pop-button">
					<view class="fail" @click="fail">取消</view>
					<view class="success" @click="success">确认删除</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { config } from '@/config.js';
export default {
	data() {
		return {
			isPopupVisible:false,
			on_id: 0,
			options: [
				{
					text: '删除',
					id: 1,
					style: {
						backgroundColor: '#dd524d'
					}
				}
			],
			activeList: [
				// {
				// 	id: 1,
				// 	status: 1,
				// 	options: [
				// 		{
				// 			text: '删除',
				// 			id: 1,
				// 			style: {
				// 				backgroundColor: '#dd524d'
				// 			}
				// 		}
				// 	]
				// },
				// {
				// 	id: 2,
				// 	status: 1,
				// 	options: [
				// 		{
				// 			text: '删除',
				// 			id: 2,
				// 			style: {
				// 				backgroundColor: '#dd524d'
				// 			}
				// 		}
				// 	]
				// },
				// {
				// 	id: 3,
				// 	status: 0,
				// 	options: [
				// 		{
				// 			text: '删除',
				// 			id: 3,
				// 			style: {
				// 				backgroundColor: '#dd524d'
				// 			}
				// 		}
				// 	]
				// },
				// {
				// 	id: 4,
				// 	status: 0,
				// 	options: [
				// 		{
				// 			text: '删除',
				// 			id: 4,
				// 			style: {
				// 				backgroundColor: '#dd524d'
				// 			}
				// 		}
				// 	]
				// }
			],
			shopInfo: {},
			deleteIndex:-1,//删除下标
			pageRoute:""
		};
	},
	onLoad() {
		// this.getShopInfo();
	},
	onShow() {
		// 获取当前页面栈
		       const pages = getCurrentPages();
		       // 当前页面是页面栈的最后一个元素
		       const prevPage = pages[pages.length - 2];
		       // 获取上一个页面的路由
		       if (prevPage) {
		           console.log("上一页路由",prevPage.route); // 输出上一个页面的路由
				   this.pageRoute = prevPage.route;
		       }
		this.getShopInfo();
		uni.setNavigationBarColor({
			frontColor: '#ffffff', // 前景色值，包括按钮、标题、状态栏的颜色
			backgroundColor: '#000000', // 背景颜色值，包括背景图
			animation: {
				duration: 400,
				timingFunc: 'easeIn'
			}
		});
	},
	methods: {
		back() {
			if(this.pageRoute == "pages/activeAdd/activeAdd" || this.pageRoute == "pages/activeInfo/activeInfo"){
				uni.navigateBack({
					delta: 3
				});
			}else{
				uni.navigateBack()
			}
			// uni.navigateTo({
			//         url: '/pages/shop/shop'
			//     })
		},
		onBackPress(options) {
		        // 检查是否已经处理过返回逻辑
		        if (!this.hasHandledBack) {
		          this.hasHandledBack = true; // 标记为已处理
		          this.back(); // 调用自定义的back方法
		          return true; // 阻止默认行为
		        }
		        // 如果已经处理过，则不再处理，允许默认返回行为
		        return false;
		  },
		onClick(e,id) {
			this.isPopupVisible = true;
			console.log('点击了' + (e.position === 'left' ? '左侧' : '右侧') + e.content.text + '按钮');
		},
		change(e, index,id) {
			console.log('当前状态：' + e + '，下标：' + index);
			this.on_id = id;
			this.deleteIndex = this.deleteIndex == index ? -1 : index;
		},
		openPopup(e) {
			this.isPopupVisible = true;
			if (this.$refs.center) {
			        this.$refs.center.open();
			      }
		},
		fail() {
			this.isPopupVisible = false;
		},
		success() {
			this.isPopupVisible = false;
			this.$http.post('/api/user/business/activity/del', {
				"id": this.on_id
			}).then((res)=>{
				this.getActiveList();
			})
		},
		goInfo(id) {
			console.log(id);
			uni.navigateTo({
				url: '/pages/activeInfo/activeInfo?id=' + id
			});
		},
		goAddActive() {
			uni.setStorageSync('activeInfo', '');
			uni.navigateTo({
				url: '/pages/activeAdd/activeAdd?shopId=' + this.shopInfo.id
			});
		},
		getShopInfo() {
			this.$http.get('/api/user/business/get').then((res) => {
				this.shopInfo = res.message;
				this.getActiveList();
			});
		},
		getActiveList() {
			this.$http.get('/api/user/business/activity/self/list', { business_id: this.shopInfo.id,page:1,size:10 }).then((res) => {
				console.log(res);
				this.activeList = res.message.list;
				for (var i = 0; i < this.activeList.length; i++) {
					this.activeList[i].options = [
						{
							text: '删除',
							id: this.activeList[i].id,
							style: {
								backgroundColor: '#dd524d'
							}
						}
					];
				}
			});
		}
	}
};
</script>

<style lang="scss">
@font-face {
	font-family: '阿里巴巴普惠体';
	/* 你可以给字体起一个别名 */
	src: url('~@/static/images/vip/font/AlibabaPuHuiTi.ttf') format('truetype');
	/* 还可以加上 ttf 格式作为备选 */
	font-weight: normal;
	font-style: normal;
}
page{
	background: #F5F7FB;
}
.pop-container{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 998;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	.pop {
		width: 323px;
		height: 141px;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-content: center;
		align-items: center;
		color: #000000;
		background-color: #ffffff;
		border-radius: 20px;
		.pop-title {
			color: rgb(33, 33, 33);
			font-family: HarmonyOS Sans;
			font-size: 18px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: center;
		}
		.pop-button {
			width: 100%;
			display: flex;
			flex-direction: row;
			justify-content: space-evenly;
			.fail {
				box-sizing: border-box;
				border: 1px solid #787878;
				border-radius: 60px;
				width: 136px;
				height: 36px;
				display: flex;
				justify-content: center;
				align-items: center;
				color:#787878;
			}
			.success {
				width: 136px;
				height: 36px;
				border-radius: 60px;
				background: rgb(255, 76, 76);
				display: flex;
				justify-content: center;
				align-items: center;
				color:#ffffff;
			}
		}
	}
}


.content {
	width: 100%;
	height: 100vh;
	overflow-y: hidden;
	padding-bottom: 35%;
	padding-top: 100px;
	background-color: #F5F7FB;
	background-color: #f5f7fb;
	.navigation-bar {
		width: 100%;
		display: flex;
		align-items: center;
		height: 170px;
		background-image: url('../../static/images/vip/newBackground.png'); /* 背景图路径 */
		background-size: cover;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
	}
	.navigation-zhezhao {
		width: 100%;
		height: 170px;
		background-image: url('../../static/images/vip/nav-zhezhao.png'); /* 背景图路径 */
		background-size: 100%;
		background-repeat: no-repeat;
		background-position: bottom;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		padding-bottom: 10%;
		.back {
			width: 31px;
			height: 31px;
			margin-left: 2%;
			z-index: 1;
		}
		.nav-title {
			width: 82%;
			height: 30px;
			color: #000000;
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: center;
			z-index: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
	.list {
		width: 95%;
		// height:calc(100vh-170px);
		height: calc(100vh - 170px);
		overflow-y: auto;
		// height: 130px;
		margin-left: 2.5%;
		// margin-bottom: 30%;
		position: relative;
		.list-item-container{
			margin-bottom: 20px;
			.list-item{
				background: #ff4c4c;
				border-radius: 10px;
				.item {
					width: 100%;
					height: 142px;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					align-content: center;
					border-radius: 10px;
					background: #ffffff;
					padding: 10px;
					box-sizing: border-box;
					position:relative;
					.activeStatus {
						position: absolute;
						top: 0;
						left: 0;
						width: 90px;
						height: 25px;
						z-index: 100;
					}
					.activeimageContainer{
						width: 122px;
						height: 122px;
						border-radius: 6px;
						margin-right:10px;
						display: flex;
						align-items: center;
						justify-content: center;
						overflow: hidden;
						position: relative;
					}
					.activeimage {
						position: absolute;
						width:100%;
						// height:100%;
						// width: 122px;
						// height: 122px;
						// border-radius: 6px;
						// object-fit: cover;
						// overflow: hidden;
					}
			
					.item-info {
						height: 122px;
						flex:1;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
			
						.item-title {
							color: #212121;
							font-family: 阿里巴巴普惠体;
							font-size: 18px;
							font-weight: 700;
							line-height: 22px;
							letter-spacing: 0px;
							text-align: left;
							padding-top:4px;
							box-sizing: border-box;
						}
			
						.item-time {
							line-height: 20px;
							display: flex;
							align-items: center;
							justify-content: flex-start;
							flex-direction: row;
							color: #989898;
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							letter-spacing: 0px;
							text-align: left;
							padding-bottom: 10px;
							box-sizing: border-box;
							.time-clock {
								width: 16px;
								height: 16px;
								margin-right: 5px;
							}
						}
					}
				}
			}
			.delete-tips{
				color: rgb(152, 152, 152);
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				margin-top:10px;
				display: flex;
				align-items: center;
				.delete-tips-icon{
					width:18px;
					height: 18px;
					margin-right:4px;
				}
			}
		}
	
		
	}

	.bottom {
		width: 100%;
		display: flex;
		flex-direction: column;
		position: fixed;
		align-items: center;
		bottom: 0px;
		.add-button {
			width: 95%;
			height: 56px;
			border-radius: 10px;
			margin-bottom: 3%;
			background: #ffffff;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 1px solid #B2ABDA;
			.icon-button-add {
				width: 18px;
				height: 18px;
				margin-right:2px;
			}
			.add-text {
				color: #B2ABDA;
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 500;
				line-height: 14px;
				letter-spacing: 0px;
				text-align: center;
			}
		}
		.submit-button {
			margin-bottom: 3%;
			width: 90%;
			height: 50px;
			border-radius: 10px;
			background-image: url('../../static/images/vip/submit-button.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #ffffff;
			font-family: HarmonyOS Sans;
			font-size: 16px;
			font-weight: 400;
			line-height: 22px;
			letter-spacing: 0px;
			text-align: center;
		}
	}
}
</style>
