let myMap;
import {
	config
} from '@/config.js'
import {
	newMarker,
	getGeocoder
} from '@/utils/MapTools.js'
var object3Dlayer = null;
var gltfLoader = null;
var zoomScale, zoomHeight,url;
var zooms = [5,17];
const {
	avatarW,
	avatarH,
	avatarHoverW,
	avatarHoverH,
	packetW,
	packetH
} = config
export default {
	data() {
		return {
		}
	},
	mounted() {
		if (typeof window.AMap === 'function') {
			this.initMap()

		} else {
			window._AMapSecurityConfig = {
				securityJsCode: '********************************',
			}
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
			script.onload = this.initMap.bind(this)
			document.head.appendChild(script)
			
			// const script1 = document.createElement('script')
			// script1.src = "https://a.amap.com/jsapi_demos/static/gltf/duck/three.js";
			// script1.onload = this.initMap.bind(this)
			// document.head.appendChild(script1)
			
			// const script2 = document.createElement('script')
			// script2.src = "https://a.amap.com/jsapi_demos/static/gltf/duck/GLTFLoader.js";
			// script2.onload = this.initMap.bind(this)
			// document.head.appendChild(script2)
		}

	},
	onUnload() {},
	methods: {
		initMap() {
			console.log("center测试测试",this.center)
			console.log("地址",this.modelUrl)
			console.log(this.zoom)
			// myMap = new AMap.Map('AmapRender', {
			// 	// center:[
			// 	//     116.2683411,
			// 	//     39.9062119
			// 	// ],
			// 	center:this.center,
			// 	zoom: 15,
			// 	zooms: [5, 20],
			// 	viewMode: '3D',
			// 	resizeEnable: true,
			// 	draggable: true,
			// 	zoomEnable: true,
			// 	pitch: 65,
			// 	rotation: 25,
			// });
			myMap = new AMap.Map('AmapRender', {
				pitch: 50, //地图俯仰角度，有效范围 0 度- 83 度
				viewMode: '3D', //地图模式
				rotateEnable: true, //是否开启地图旋转交互 鼠标右键 + 鼠标画圈移动 或 键盘Ctrl + 鼠标左键画圈移动
				pitchEnable: true, //是否开启地图倾斜交互 鼠标右键 + 鼠标上下移动或键盘Ctrl + 鼠标左键上下移动
				// zoom: 17, //初始化地图层级
				// rotation: -15, //初始地图顺时针旋转的角度
				// center: [116.333926, 39.997245] //初始地图中心经纬度
				center: this.center,
				zoom: this.zoom,
				// zooms: [3, 20], //地图显示的缩放级别范围
				// zooms: [5, 17],
				zooms:zooms,
				// features: ['bg', 'road', 'building'],
				resizeEnable: true,
				mapStyle: 'amap://styles/957040a4d7e11f8e53f93977962d6779', //设置地图的显示样式
				draggable: true,
				// zoomEnable: true,
				// pitch: 65,
				// rotation: 25,
			});
			// 地图加载完成！
			myMap.on("complete", async () => {
				myMap.setCenter(this.center)
				// myMap.setCenter([
				//     116.2683411,
				//     39.9062119
				// ])
			console.log(this.center)
				// 添加自定义图片标点
				    var marker = new AMap.Marker({
				      position: this.center, // 标点的经纬度位置
				      icon: 'http://img.lluuxiu.com/photo/20240921/c4494ba7-8f9f-427d-bfb3-64b8535241ae.png', // 自定义图片的URL
				      offset: new AMap.Pixel(-10, -10), // 标点的偏移量
				      zIndex: 101, // 标点的层级
				    });
				
				    // 将标点添加到地图上
				    myMap.add(marker);
				// 创建Object3DLayer图层
				object3Dlayer = new AMap.Object3DLayer();
				myMap.add(object3Dlayer);
				var druckMeshes, cityMeshes;
				// AMap.plugin(['AMap.ControlBar', 'AMap.ToolBar'], function () { //异步加载插件
				// //   var controlBar = new AMap.ControlBar({ //控制地图旋转插件
				// //       position: {
				// //           right: '10px',
				// //           top: '10px'
				// //       }
				// //   });
				// //   myMap.addControl(controlBar);
				//   var toolBar = new AMap.ToolBar({ //地图缩放插件
				//       position: {
				//           right: '40px',
				//           top: '110px'
				//       }
				//   });
				//   myMap.addControl(toolBar)
				// })
				// 加载AMap.GltfLoader插件
				zoomScale = 10;
				zoomHeight = 10;
				AMap.plugin(["AMap.GltfLoader"], () => {
					var paramDuck = {
						position:new AMap.LngLat(this.center[0],this.center[1]),
						scale: zoomScale, // 非必须，默认1
						height: zoomHeight, // 非必须，默认0
						scene: 0, // 非必须，默认0
					};
					// 创建AMap.GltfLoader插件实例
					gltfLoader = new AMap.GltfLoader();

					// 调用load方法，加载 glTF 模型资源
					// var urlDuck = '/static//img/AA046/44d/BB11SS.gltf';  // 静态模型，上线时替换成服务器链接即可
					// var urlDuck = 'https://img.lluuxiu.com/serverConfig/SS7/ss7.gltf';
					// var urlDuck = 'http://hfz.dowv.cn/static/AA045/AA045.gltf';
					// console.log("3d", urlDuck)
					// this.modelUrl
					url = this.modelUrl;
					// url = "https://img.lluuxiu.com/serverConfig/business/ASS3/ASS3A.gltf"
					// url = "https://img.lluuxiu.com/serverConfig/aatwo/aad.gltf"
					console.log("url啊啊啊啊啊",url);
					gltfLoader.load(url, (gltfDuck) => {
						// console.log("模型对象", gltfDuck)
						druckMeshes = gltfDuck;
						gltfDuck.setOption(paramDuck);
						gltfDuck.rotateX(90);
						gltfDuck.rotateZ(-20);
						object3Dlayer.add(gltfDuck);
						console.log("模型场景对象", object3Dlayer)

					});
				});
			});
			myMap.on('click', async (ev) => {
				myMap && myMap.setCenter([ev.lnglat.lng, ev.lnglat.lat])
				const res = await getGeocoder(AMap, {
					lnglat: [ev.lnglat.lng, ev.lnglat.lat]
				})

				this.callMethod('mapLocation', {
					city: res.regeocode.addressComponent.city || res.regeocode.addressComponent
						.province,
					address: res.regeocode.formattedAddress,
				})
			}, this);
			myMap.on('zoomend', (ev) => {
				const zoom = myMap.getZoom()
				console.log("地图比例",zoom);
				console.log("模型比例",zoomScale);
				console.log("模型高度",zoomHeight);
				
				console.log("地图比例最大最小",zooms);
				

				if (zoom >= 17){
					zoomScale = 5;
					zoomHeight = 5;
				}else if (zoom >= 16){
					zoomScale = 10;
					zoomHeight = 10;
				}else if (zoom >= 15){
					zoomScale = 25;
					zoomHeight = 25;
				}else if (zoom >= 14){
					zoomScale = 50;
					zoomHeight = 50;
				}else if (zoom >= 13){
					zoomScale = 100;
					zoomHeight = 100;
				}else if (zoom >= 12){
					zoomScale = 200;
					zoomHeight = 200;
				}else if (zoom >= 11){
					zoomScale = 400;
					zoomHeight = 400;
				}else if (zoom >= 10){
					zoomScale = 700;
					zoomHeight = 700;
				}else if (zoom >= 9){
					zoomScale = 1000;
					zoomHeight = 1000;
				}else if (zoom >= 8){
					zoomScale = 2500;
					zoomHeight = 2500;
				}else if (zoom >= 7){
					zoomScale = 6000;
					zoomHeight = 6000;
				}else if (zoom >= 6){
					zoomScale = 12000;
					zoomHeight = 12000;
				}else if (zoom >= 5){
					zoomScale = 16000;
					zoomHeight = 16000;
				}
				
				
				
				 console.log("模型比例",zoomScale);
				 console.log("模型高度",zoomHeight);
				if (object3Dlayer && gltfLoader) {
				  // 移除旧的模型
				   object3Dlayer.clear(); // 使用 clear 方法来清除图层中的所有对象
				   // 添加新模型
				  myMap.setCenter(this.center)
				  // 创建Object3DLayer图层
				  object3Dlayer = new AMap.Object3DLayer();
				  myMap.add(object3Dlayer);
				  var druckMeshes, cityMeshes;
				AMap.plugin(["AMap.GltfLoader"], () => {
					var paramDuck = {
						position:new AMap.LngLat(this.center[0],this.center[1]),
						scale: zoomScale, // 非必须，默认1
						height: zoomHeight, // 非必须，默认0
						scene: 0, // 非必须，默认0
						zIndex: 100,
					};
					// 创建AMap.GltfLoader插件实例
					gltfLoader = new AMap.GltfLoader();
								
					gltfLoader.load(url, (gltfDuck) => {
						// console.log("模型对象", gltfDuck)
						druckMeshes = gltfDuck;
						gltfDuck.setOption(paramDuck);
						gltfDuck.rotateX(90);
						gltfDuck.rotateZ(-20);
						object3Dlayer.add(gltfDuck);
						console.log("模型场景对象", object3Dlayer)
				
					});
				});
				}
			}, this);
		},
		receiveClear(newValue, oldValue, ownerVm, vm) {
			myMap && myMap.clearMap()
		},
		
		async receiveEvent(newValue, oldValue, ownerVm, vm) {
			const {
				key,
				params,
				interval
			} = newValue
			console.log(newValue, 'receiveEvent===事件透传');
			// 设置zoom
			if (myMap) {
			    myMap.setZoom(params);
			}
			if (object3Dlayer && gltfLoader) {
			  // 移除旧的模型
			   object3Dlayer.clear(); // 使用 clear 方法来清除图层中的所有对象
			   // 添加新模型
			  myMap.setCenter(this.center)
			  // 创建Object3DLayer图层
			  object3Dlayer = new AMap.Object3DLayer();
			  myMap.add(object3Dlayer);
			  var druckMeshes, cityMeshes;
	
			  switch (params) {
			    case 16:
			      zoomScale = 10;
			      zoomHeight = 10;
				  url = "https://img.lluuxiu.com/serverConfig/business/ASS1/ASS1A.gltf"
				  zooms = [13,17];
			      break;
			    case 13:
			      zoomScale = 100;
			      zoomHeight = 100;
				  url = "https://img.lluuxiu.com/serverConfig/business/ASS1/ASS1A.gltf"
				  zooms = [10,17];
			      break;
				  case 10:
				    zoomScale = 700;
				    zoomHeight = 700;
					url = "https://img.lluuxiu.com/serverConfig/aatwo/aad.gltf"
					zooms = [6,17];
				    break;
					case 6:
					  zoomScale = 9000;
					  zoomHeight = 9000;
					  zooms = [5,17];
					  url = "https://img.lluuxiu.com/serverConfig/business/ASS3/ASS3A.gltf"
					  break;
			    default:
			      // 如果 params 不是 16 或 13，可以设置一个默认值或者不进行任何操作
			      zoomScale = 10; // 默认值
			      zoomHeight = 10; // 默认值
			      break;
			  }
			  myMap.setZooms(zooms);
			  // var url = "http://hfz.dowv.cn/static/ASS1/ASS1A.gltf"
			  AMap.plugin(["AMap.GltfLoader"], () => {
			  	var paramDuck = {
			  		position:new AMap.LngLat(this.center[0],this.center[1]),
			  		scale: zoomScale, // 非必须，默认1
			  		height: zoomHeight, // 非必须，默认0
			  		scene: 0, // 非必须，默认0
			  	};
			  	// 创建AMap.GltfLoader插件实例
			  	gltfLoader = new AMap.GltfLoader();
				
			  	gltfLoader.load(url, (gltfDuck) => {
			  		// console.log("模型对象", gltfDuck)
			  		druckMeshes = gltfDuck;
			  		gltfDuck.setOption(paramDuck);
			  		gltfDuck.rotateX(90);
			  		gltfDuck.rotateZ(-20);
			  		object3Dlayer.add(gltfDuck);
			  		console.log("模型场景对象", object3Dlayer)
			  
			  	});
			  });
			}
		},

		// 调用 view 层的方法
		callMethod(act = '', params, cb) {
			this.$ownerInstance.callMethod('callApp', {
				act,
				option: params,
				cb: cb
			})
		},

	}
}