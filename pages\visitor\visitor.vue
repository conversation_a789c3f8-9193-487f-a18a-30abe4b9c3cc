<template>
	<view>
		<view class="" style="height: 18rpx;"></view>
		<scroll-view scroll-y="true" style="height: 100vh;" :lower-threshold="200" crolltoupper="upper"
			@scrolltolower="lower">
			<view class="t_betweent_center" style="padding: 32rpx;">
				<view class="">
					<view class="t_zt">隐身访问</view>
					<view class="t_font_grey" style="margin-top: 16rpx;">打开后访问他人主页不留足迹</view>
				</view>
				<u-switch v-model="checked" inactive-color="#eee" size="45" @input="invisibleAccess"></u-switch>
			</view>
			<view class="item" v-for="(item,index) in arr" :key="index">
				<view class="t_display" @click="goNav('/pages/otherPage/otherPage?uuid='+item.uuid)">
					<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
					<view class="name">{{item.nickname}}</view>
				</view>
				<!-- //0陌生人 1是我关注的  2是互相关注的 3关注我的 4我自己 -->
				<view class="fous" @click="addFous(item.uuid,index)" v-if="[0].includes(item.relation)">
					关注
				</view>
				<view class="fous" style="background: #3D404A;color: #999;" @click="cancelFous(item.uuid,index)"
					v-if="[1].includes(item.relation)">
					已关注
				</view>
				<view class="fous" @click="addFous(item.uuid,index)" v-if="[3].includes(item.relation)">
					回关
				</view>
				<view class="fous" style="background: #3D404A;" v-else-if="item.relation == 2"
					@click="cancelFous(item.uuid,index)">
					互相关注
				</view>
			</view>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				checked: false,
				arr: [],
				total: 0,
				pageConfig: {
					page: 1,
					size: 10,
				}
			}
		},
		async onLoad() {
			this.getData()
			await this.$http.get('/api/user/info').then(async (res) => {
				this.checked = res.message.switch.includes(4)
			})
			// this.checked = uni.getStorageSync('switch').includes(4)
		},
		methods: {
			invisibleAccess(val) {
				this.$http.post('/api/user/set-switch', {
					"switch_type": 4,
					"is_on": ~~val
				})
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			cancelFous(uuid, ids) {
				this.$http.post('/api/user/follow/del', {
					uuid
				}).then(res => {
					this.arr[ids].relation = res.message
				})
			},
			addFous(uuid, ids) {
				this.$http.post('/api/user/follow/add', {
					uuid
				}).then(res => {
					this.arr[ids].relation = 1
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			lower() {
				if (this.total > 0) {
					this.pageConfig.page++
					this.getData()
				}
			},
			getData() {
				this.$http.get('/api/guest/list', {
					...this.pageConfig
				}).then(res => {
					this.total = res.message.length
					this.arr.push(...res.message)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.item {
		padding: 24rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.fous {
			width: 148rpx;
			height: 58rpx;
			text-align: center;
			line-height: 58rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
		}

		.avatar {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 42rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>