<template>
	<uni-popup ref="popup" type="center">
		<view class="hb" v-if="redPacketInfo">
			<image class="hbHead" src="@/static/map/hbHead.png" mode="widthFix"></image>
			<view class="avatarBox">
				<image class="avatar" :src="redPacketInfo.self_packet.user_info.avatar" mode=""></image>
				<view class="guanzhu"
					v-if="redPacketInfo.hasOwnProperty('friend_relation')&&redPacketInfo.friend_relation===0">
					关注
				</view>
			</view>

			<view class="userName">
				{{redPacketInfo.user_info.nickname}}
			</view>
			<view class="money">
				{{redPacketInfo.amount}}
			</view>
			<view class="hbTip">
				已存入钱包，可直接消费提现
			</view>
			<view class="hbCount">
				{{redPacketInfo.quantity}}个红包共{{redPacketInfo.total_amount}}元
			</view>
			<view class="getRecord">
				<view class="li" v-for="(item,index) in redPacketInfo.red_packet_list" :key="index">
					<image class="li-avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
					<view class="li-info">
						<view class="li-user">
							<view class="li-userName">
								{{item.user_info.nickname}}
							</view>
							<view class="li-time">
								{{item.receive_at}}
							</view>
						</view>
						<view class="li-money">
							{{item.amount}}币
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-toast ref='notify' />
	</uni-popup>
</template>

<script>
	import {
		apiGetRedpacketDetail
	} from '@/api/common.js'
	export default {
		data() {
			return {
				redPacketInfo: null
			}
		},
		props: {

		},
		mounted() {
			// this.open()
		},
		methods: {
			open(envelope_uuid) {
				console.log(envelope_uuid, 'envelope_uuidenvelope_uuidenvelope_uuidenvelope_uuid');
				apiGetRedpacketDetail({
					envelope_uuid: envelope_uuid
				}).then(res => {
					if (res.code == 200) {
						this.redPacketInfo = res.message
						this.$refs.popup.open('center')
					}
				})

			},
			close() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 634.3rpx;
		height: 70vh;
		background: #191c26;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		position: relative;
		overflow-y: scroll;

		.hbHead {
			width: 634.3rpx;
			height: 162.82rpx;

		}

		.avatarBox {
			margin-top: 42rpx;
			text-align: center;

			.avatar {
				width: 139rpx;
				height: 139rpx;
				border: 3rpx solid #FFFFFF;
				border-radius: 50%;
				border: 4rpx solid #FFFFFF;
				box-sizing: $uni-bg-color;
			}

			.guanzhu {
				width: 109.39rpx;
				height: 46.64rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 40rpx 40rpx 40rpx 40rpx;
				opacity: 1;
				border: 4rpx solid #FFFFFF;
				font-size: 26rpx;
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: -36rpx auto 0;
				position: relative;
				z-index: 2;
			}
		}

		.userName {
			height: 41rpx;
			font-size: 28rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 41rpx;
			text-align: center;
			margin-top: 12rpx;
		}

		.money {
			height: 136rpx;
			font-size: 94rpx;
			text-align: center;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #C09C50;
			line-height: 136rpx;
			margin-top: 32rpx;
		}

		.hbTip {

			font-size: 24rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #C09C50;
			line-height: 35rpx;
			text-align: center;
			margin-top: 12rpx;
			padding-bottom: 42rpx;
			border-bottom: 16rpx solid #2F3341;
		}

		.hbCount {

			font-size: 28rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: rgba(255, 255, 255, 0.74);
			line-height: 41rpx;
			padding: 32rpx;
			box-sizing: border-box;
			border-bottom: 2rpx solid #2F3341;
		}

		.getRecord {
			width: 100%;

			.li {
				width: 100%;
				display: flex;
				align-items: center;
				padding-left: 32rpx;
				box-sizing: border-box;

				.li-avatar {
					width: 86rpx;
					height: 86rpx;
					border-radius: 150rpx 150rpx 150rpx 150rpx;
					opacity: 1;
					border: 4rpx solid #FFFFFF;
					margin-right: 26rpx;
				}

				.li-info {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 2rpx solid #2F3341;
					padding: 32rpx 32rpx 32rpx 0;

					.li-user {
						flex: 1;

						.li-userName {
							height: 46rpx;
							font-size: 32rpx;
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 500;
							color: #FFFFFF;
							line-height: 46rpx;

						}

						.li-time {
							height: 35rpx;
							font-size: 24rpx;
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 500;
							color: rgba(255, 255, 255, 0.72);
							line-height: 35rpx;
							margin-top: 2rpx;

						}
					}

					.li-money {
						height: 46rpx;
						font-size: 32rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						color: #FFFFFF;
						line-height: 46rpx;
					}
				}
			}
		}
	}
</style>