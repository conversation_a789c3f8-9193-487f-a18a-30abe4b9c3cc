<template>
	<view>
		<uni-popup ref="popup" type="center">
			<view class="t_center">
				<image class="size" :src="require('../../../static/images/sy/'+(status?'kaiqi.png': 'guanbi.png')) "
					mode=""></image>
				<!-- 	<image class="size" :src="'../../../static/images/sy/'+status?'guanbi.png': 'kaiqi.png'" mode="">
				</image> -->
				<div style="position: absolute;top: 110px;">
					<div class="t_center">
						<label class="radio" @click="setFLag">
							<radio :checked="flag" style="transform:scale(0.7)" /><text
								style="color: #989898;">不再提示此弹窗</text>
						</label>
					</div>
					<div class="btn">
						<image @click="close" src="../../../static/images/sy/quxiao.png" mode=""
							style="width: 136px;height: 38px;">
						</image>

						<div style="width: 11px;"></div>
						<image @click="confirm"
							:src="require( '../../../static/images/sy/'+(status?'queren.png': 'queren2.png'))" mode=""
							style="width: 136px;height: 38px;">
						</image>
					</div>
				</div>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: "vipTips",
		props: {
			status: {
				type: Boolean,
				default: true
			},
			imgCurrent: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				flag: false
			};
		},
		methods: {
			setFLag() {
				this.flag = !this.flag
			},
			confirm() {
				console.log('this.flag', this.flag);
				if (this.flag) {
					uni.setStorageSync('hmTips', this.flag)
				}
				this.$emit('confirm')
				this.close()
			},
			open() {
				this.$refs.popup.open()
			},
			close() {
				this.$refs.popup.close()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.size {
		width: 323px;
		height: 206px;
		display: block;
	}

	.btn {
		height: 38px;
		display: flex;
		justify-content: space-around;
		margin-top: 30rpx;
	}

	.close {
		margin-top: 52rpx;
	}
</style>
