<template>
	<view>
		<scroll-view :scroll-with-animation="true" scroll-y="true" style="height: 100vh;" class="scroll-view_back"
			@scrolltolower="scrolltolower">
			<view style="border: 1rpx solid #333;"></view>
			<!-- <block v-for="(item,index) in itemArr" :key="index"> -->
			<view class="item_group" style="justify-content: space-between;" v-for="(item,index) in commentList"
				:key="index">
				<view class="t_display">
					<image @click="goNav('/pages/otherPage/otherPage?uuid='+item.user_info.uuid)"
						style="width: 108rpx;height: 108rpx;border-radius: 50%;" :src="item.user_info.avatar"
						mode="aspectFill">
					</image>
					<view class="info" @click="goNavChat(item)">
						<view class="name t_display">
							{{item.user_info.nickname}}
							<image class="img24" src="../../static/images/smallFirend.png" mode=""
								v-if="item.relation ==2" style="margin-left: 5rpx;"></image>
						</view>
						<view class="note t_display">
							点赞了你的帖子
							<view class="time">
								{{gettimeFmt(item.like_item.created_at)}}
							</view>
						</view>
					</view>
				</view>
				<view class="rightCard rightSize" v-if="!item.image" @click="goNavChat(item)">{{item.content}}</view>
				<image class="rightSize" :src="item.image" mode="aspectFill" v-else @click="goNavChat(item)"></image>
			</view>
			<view style="border: 1rpx solid #333;"></view>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		apiNotifyLike
	} from '@/api/common.js'
	import {
		timeFmt
	} from '@/utils/common.js'
	export default {
		data() {
			return {
				userName: uni.getStorageSync('nickname'),
				reviewArr: [],
				commentList: [],
				size: 10
			}
		},
		onLoad(options) {
			this.init()
		},
		methods: {
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNavChat(item) {
				console.log(item);
				// let params = {
				// 	account: item.user_info.im_id,
				// 	chatHeadImg: item.user_info.avatar,
				// 	chatName: item.user_info.nickname,
				// 	nameuid: 'p2p-' + item.user_info.im_id,
				// 	roomtype: 'p2p',
				// 	nuck: item.user_info.nickname,
				// }
				// uni.navigateTo({
				// 	url: "/pages/HM-chat/HM-chat?userItem=" + encodeURIComponent(JSON.stringify(params))
				// })
				uni.navigateTo({
					url: '/pages/mainText/mainText?momentId=' + item.like_item.moment_id
				})
			},
			init() {
				apiNotifyLike({
					size: this.size,
					maxId: this.commentList.length ? this.commentList[this.commentList.length - 1].search_id : ''
				}).then(res => {
					if (res.code == 200) {
						this.commentList = this.commentList.concat(...res.message)
					}
				})
			},
			scrolltolower() {
				this.init()
			},
			gettimeFmt(str) {
				return timeFmt(str, 'yyyy-MM-DD hh:mm:ss')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.rightSize {
		width: 108rpx;
		height: 132rpx;
		border-radius: 14rpx;
	}

	.rightCard {
		padding: 8rpx 18rpx;
		background: #323232;
		font-size: 22rpx;
		line-height: 40rpx;
		overflow: hidden;
		-webkit-line-clamp: 3;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
	}

	.item_group {
		display: flex;
		padding: 24rpx 32rpx;
		align-items: center;

		.rightIcon {
			width: 108rpx;
			height: 132rpx;
			border-radius: 14rpx;
		}

		.right {
			width: 12rpx;
			height: 38rpx;
		}

		.info {
			margin-left: 32rpx;

			.note {
				font-size: 28rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.64);

				.time {
					font-size: 22rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.42);
					line-height: 40rpx;
					margin-left: 18rpx;
				}
			}

			.name {
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}


		.text {
			margin-left: 32rpx;
			font-size: 34rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
</style>
