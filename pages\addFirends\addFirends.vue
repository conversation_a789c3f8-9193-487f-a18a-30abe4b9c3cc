<template>
	<view class="appPage">
		<view class="Cinpout t_display" style="justify-content: space-between;margin-top: 36rpx;">
			<view style="color: #A2A2A4;padding-right: 100px; " @click="goNav('/pages/firendSearch/firendSearch')">
				<uni-icons type="search" color="#A2A2A4" size="16" style="margin-right: 10rpx;"></uni-icons>
				搜索对方id或手机号
			</view>
			<!-- <u-input v-model="value" placeholder="搜索对方id或手机号" type="text" :clearable="false" /> -->
			<uni-icons type="scan" color="#A2A2A4" size="16" @click="goScan" style="padding: 10px;"></uni-icons>
		</view>
		<view class="t_display" @click="goNav('/pages/newFirendMsg/newFirendMsg')"
			style="margin-top: 54rpx;justify-content: space-between;">
			<view class="t_display">
				<uni-icons type="staff" color="#fff" size="20"></uni-icons>
				<view class="txt">
					好友申请
				</view>
			</view>
			<uni-icons type="right" color="#A2A2A4" size="20"></uni-icons>
		</view>
		<view class="t_betweent" style="align-items: center;">
			<view class="title">
				可能认识的人
			</view>
			<image class="img32" src="../../static/images/shuaxin.png" @click="getContacts" mode=""></image>
		</view>

		<view class="item_group" v-for="(item,index) in list" style="justify-content: space-between;" :key="index">
			<view class="t_display">
				<image class="img80" style="border-radius: 50%;" :src="item.avatar" mode="aspectFill"
					@click="goNav('/pages/otherPage/otherPage?uuid='+item.uuid)">
				</image>
				<view class="info">
					<view class="t_display">
						<view class="name">
							{{item.nickname}}
						</view>
					</view>
					<view class="note t_display">
						{{item.intro}}
					</view>
				</view>
			</view>
			<view class="t_display">
				<!-- <view class="yes" @click="ack(item.uuid,index)" style="color: #FFFFFF;background: #272727;">
					移除
				</view> -->
				<view class="yes" @click="ack(item.uuid,index)">
					关注
				</view>
			</view>
			<u-toast ref='notify' />
		</view>
	</view>
</template>

<script>
	import {
		popup
	} from '@/uni_modules/x-perm-apply-instr/js_sdk/native_popup.js'

	export default {
		data() {
			return {
				value: '',
				list: []

			}
		},
		onLoad() {
			// 显示
			popup.show({
				title: '通讯录权限申请说明',
				content: '发现通讯录朋友 进入系统设置将「通讯录」设为开启 将朋友纳入我的地图内。'
			})
			this.getData()


		},
		methods: {
			goScan() {
				// 只允许通过相机扫码
				this.$common.scan()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			update() {
				this.$http.post('/api/user/friend/recommended', {
					phones: arr.length ? arr : {}
				}).then(res => {
					this.list = res.message
				})
			},
			ack(uuid, index) {
				this.$http.post('/api/user/friend/add', {
					uuid,
					"msg": "",
					"remark": ""
				}).then(res => {
					console.log(res);
					this.list.splice(index, 1)
				})
			},
			getContacts: function() {
				var _this = this;
				// 获取通讯录对象  
				plus.contacts.getAddressBook(plus.contacts.ADDRESSBOOK_PHONE, function(addressbook) {
					popup.close()
					// 查找联系人  
					addressbook.find(["displayName", "phoneNumbers"], function(contacts) {
						// uni.showToast({
						// 	icon: "none",
						// 	duration: 5000,
						// 	title: JSON.stringify(contacts)
						// })
						let arr = {}
						try {
							contacts.map(item => {
								let str = item.phoneNumbers[0].value
								arr[str] = item.displayName
								return
							});
						} catch (e) {
							//TODO handle the exception
							_this.$http.post('/api/user/friend/recommended', {
								phones: arr.length ? arr : {}
							}).then(res => {
								_this.list = res.message
							})
							return
						}
						_this.$http.post('/api/user/friend/recommended', {
							phones: arr.length ? arr : {}
						}).then(res => {
							_this.list = res.message
						})
					}, function() {
						this.$refs.notify.show({
							title: '获取联系人失败',
							position: "top"
						})
						_this.$http.post('/api/user/friend/recommended', {
							phones: {}
						}).then(res => {
							_this.list = res.message
						})
					}, {
						multiple: true
					});
				}, function(e) {
					this.$refs.notify.show({
						title: '获取通讯录对象失败',
						position: "top"
					})
					_this.$http.post('/api/user/friend/recommended', {
						phones: {}
					}).then(res => {


						_this.list = res.message
					})
				});
			},
			getData() {
				this.getContacts()

			},
			goNav(url) {
				this.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 0 30rpx;

		.item_group {
			padding: 24rpx 0;
			display: flex;
			border-bottom: 1px solid #2C2C2C;
			align-items: center;

			.declined {
				width: 126rpx;
				height: 56rpx;
				border-radius: 16rpx;
				opacity: 1;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #767676;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #767676;
			}

			.yes {
				width: 142rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: #000000;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				margin-left: 16rpx;
			}

			.no {
				font-size: 22rpx;
				font-weight: 400;
				text-align: center;
				width: 126rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid transparent;
				border-radius: 16rpx;
				background-clip: padding-box, border-box;
				background-origin: padding-box, border-box;
				background-image: linear-gradient(to right, #333, #333), linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
			}

			.outer_circle {
				position: relative;
				margin: 50px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: #ffffff;
			}

			.inner_circle {
				background-image: linear-gradient(to bottom, rgb(123, 93, 255) 0%,
						rgb(56, 225, 255) 100%);
				content: '';
				position: absolute;
				top: -20px;
				bottom: -20px;
				right: -20px;
				left: -20px;
				z-index: -1;
				border-radius: inherit;
			}

			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 24rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #A2A2A4;
				}

				.name {
					font-size: 28rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
				}
			}


			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.title {
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #A2A2A4;
			margin-top: 52rpx;
			margin-bottom: 30rpx;
		}

		.txt {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			margin-left: 28rpx;
		}

		.Cinpout {
			height: 80rpx;
			background: #272727;
			border-radius: 32rpx;
			padding: 0 52rpx;
		}
	}
</style>