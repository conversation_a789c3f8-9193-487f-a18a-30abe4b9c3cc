<template>
	<view class="appPage">
		<view class="card t_display" v-for="(item,index) in list" :key="index">
			<image class="img140 ava" src="../../static/images/ava.png" mode=""></image>
			<view class="right" style="">
				<view class="name">{{item.remark}}</view>
				<view class="address">{{item.location}}</view>
				<view class="t_display address" style="justify-content: space-between;">
					<view>去过{{item.stay_num}}次</view>
					<view>{{item.last_stay}}</view>
				</view>
			</view>
			<view class="close" @click="del(item.id,index)">
				<image class="img74" src="../../static/images/shanchuAddress.png" mode=""></image>
			</view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
			}
		},
		onLoad() {
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			del(id, index) {
				this.$http.post('/address/del', {
					id
				}).then(res => {
					this.list.splice(index, 1)
				})
			},
			getData() {
				this.$http.get('/address/get').then(res => {
					this.list = res.message
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 40rpx;

		.right {
			height: 110rpx;
			margin-left: 26rpx;
			flex: 1;
			transform: translateY(-10rpx);
			display: flex;
			flex-direction: column;
			justify-content: space-around;
		}

		.card {
			padding: 24rpx;
			padding-bottom: 10rpx;
			background: #272727;
			border-radius: 18rpx;
			position: relative;
			margin-top: 40rpx;

			.close {
				position: absolute;
				right: -30rpx;
				top: -30rpx;
			}

			.address {
				font-size: 20rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #A7A7A7;
			}

			.name {
				font-size: 28rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #FFFFFF;
			}

			.ava {
				border-radius: 16rpx;

			}
		}
	}
</style>