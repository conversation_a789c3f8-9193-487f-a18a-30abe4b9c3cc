<template>
	<uni-popup ref="popup" mode="center">
		<view class="yd-content">
			<swiper>
				<swiper-item>
					<image src="../../../static/map/yd1.png" mode=""></image>
				</swiper-item>
				<swiper-item>
					<image src="../../../static/map/yd3.png" mode=""></image>
				</swiper-item>
				<swiper-item>
					<image src="../../../static/map/yd2.png" mode=""></image>
				</swiper-item>
			</swiper>
			<image class="ccclse" src="../../../static/map/ccclse.png" mode="" @click="close"></image>
			<u-toast ref='notify' />
		</view>
	</uni-popup>
</template>

<script>
	export default {
		data() {
			return {}
		},
		mounted() {
			this.$refs.popup.open('center')
		},
		methods: {
			close() {
				this.$refs.popup.close('center')
				this.$emit('close')
			}
		}
	}
</script>

<style scoped lang="scss">
	.yd-content {
		.ccclse {
			width: 72rpx;
			height: 72rpx;
			margin-top: 32rpx;
			margin-left: 299rpx;
		}
	}

	uni-swiper {
		width: 671rpx;
		height: 1053rpx;

		image {
			width: 671rpx;
			height: 1053rpx;
		}
	}
</style>