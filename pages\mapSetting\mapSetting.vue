<template>
	<view>
		<view class="item">
			<view class="">
				<view class="" style="font-size: 26rpx;line-height: 35rpx;">
					幽灵模式
				</view>
				<view class="bottom">
					可设置冻结位置模糊位置
				</view>
			</view>
			<uni-icons type="forward" color="#fff" @click="goNav('/pages/chostMode/chostMode')"></uni-icons>
		</view>
		<view class="item">
			<view class="">
				<view class="" style="font-size: 26rpx;line-height: 35rpx;">
					可见范围
				</view>
				<view class="bottom">
					指定好友得可见范围
				</view>
			</view>
			<uni-icons type="forward" color="#fff" @click="goNav('/pages/seeRadius/seeRadius')"></uni-icons>
		</view>
		<view class="item">
			<view class="">
				<view class="" style="font-size: 26rpx;line-height: 35rpx;">
					常用地址
				</view>
				<view class="bottom">
					自动更新您常用的地址
				</view>
			</view>
			<uni-icons type="forward" color="#fff" @click="goNav('/pages/commonAddress/commonAddress')"></uni-icons>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.item {
		padding: 32rpx;
		display: flex;
		justify-content: space-between;

		.bottom {
			font-size: 24rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: rgba(255, 255, 255, 0.72);
			line-height: 35rpx;
		}
	}
</style>