<template>
	<view class="">
		<uni-popup ref="popups" :safe-area="true">
			{{type}}
			<view class="hb">
				<image class="bg" :src="require('../../../static/images/message/bg'+random+'.png')" mode=""></image>
				<image @click="close" class="close" src="../../../static/images/message/close.png" mode=""></image>
				<view style="padding:38rpx 72rpx;">
					<view class="content">
						<scroll-view :scroll-y="true" style="height: 450rpx;">
							<view class="left">
								<view class="leftInfo">
									<image class="img88" :src="leftAvatar" mode="" style="border-radius: 50%;"></image>
									<view class="name">{{leftName}}</view>
								</view>
								<view class="verticalLine"></view>
								<view class="txt">
									<textarea style="width: 65vw;" v-model="writeTxt" placeholder="输入您想发送的内容"
										maxlength="60" :disabled="['2','3','4'].includes(type)"></textarea>
								</view>
							</view>

							<view class="right" v-if="['2','3'].includes(type)">
								<view class="txt">
									<textarea style="width: 59vw;text-align: right;" v-model="txt"
										:placeholder="(type=='2' && myUuid)?'对方还未回复':'输入您想发送的内容'" maxlength="60"
										placeholder-style="text-align: right;"
										:disabled="['3'].includes(type) || (type=='2' && myUuid)"></textarea>
								</view>
								<view class="verticalLine"></view>
								<view class="leftInfo">
									<image class="img88" :src="rightAvatar" mode="" style="border-radius: 50%;"
										mode="aspectFit"></image>
									<view class="name">{{rightName}}</view>
								</view>
							</view>

						</scroll-view>
						<!-- //1.发送  2.回信  3.查看 -->
						<view class="btn" v-if="['1', '2'].includes(type) && !myUuid" @click="submit">
							<image :src="require('../../../static/images/message/btn'+random+'.png')" mode="" />
							<view class="btnText">发送</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			type: {
				//1.发送  2.回信  3.查看
				type: String || Number,
				default: "",
			}
		},
		data() {
			return {
				myUuid: "",
				writeTxt: "",
				txt: "",
				random: 1,
				redPacketInfo: {},
				id: "",
				leftAvatar: require('@/static/images/message/nimingtouxiang.png'),
				leftName: "匿名",
				rightAvatar: '',
				rightName: "",
			}
		},
		mounted() {

		},
		watch: {
			type: {
				handler(newVal, oldVal) {
					console.log(newVal, oldVal);
				},
				immediate: true
			}
		},
		methods: {
			submit() {
				switch (this.type) {
					case "1":
						this.$emit('send', this.writeTxt)
						break;
					case "2":
						if (this.txt == "") return this.toast('请回复留言')
						this.reply()
						break;
					default:
						break;
				}
			},
			open(info) {
				this.myUuid = uni.getStorageSync('userInfo').uuid == info.from_user.uuid

				console.log('if-----------', info.from_user.uuid);
				if (info) {
					this.writeTxt = info.detail.content
					this.id = info.detail.id
					if (!info.anonymous) {
						this.leftAvatar = info.from_user.avatar
						this.leftName = info.from_user ? info.from_user.nickname : ''
					}
					if (info.detail.reply_at > 0) {
						this.txt = info.detail.reply
					} else {
						this.txt = ""
					}
					this.rightAvatar = info.to_user ? info.to_user.avatar : ''
					this.rightName = info.to_user ? info.to_user.nickname : ''
				} else {
					this.writeTxt = ""
				}

				this.random = (Math.floor(Math.random() * 3) + 1)
				this.$refs.popups.open()
			},
			openSend(info) {
				const userInfo = uni.getStorageSync('userInfo')
				if (info) {
					this.leftAvatar = info.anonymous == 1 ? require('@/static/images/message/nimingtouxiang.png') :
						userInfo.avatar
					this.leftName = info.anonymous == 1 ? '匿名' : userInfo.nickname
					this.writeTxt = ""
				}
				this.random = (Math.floor(Math.random() * 3) + 1)
				this.$refs.popups.open()
			},
			close() {
				this.writeTxt = ""
				this.$refs.popups.close()
			},
			//回复
			reply() {

				this.$http.post('/api/location-msg/reply', {
					"id": this.id,
					"content": this.txt
				}).then(res => {
					this.$emit('getMapMsg')
					this.close()
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.hb {
		width: 750rpx;
		height: 55vh;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		opacity: 1;
		background-size: 100% 100%;
		background-attachment: fixed;
		position: relative;
		transform: translateY(-30rpx);

		.close {
			background: #F84C3B;
			width: 62rpx;
			height: 62rpx;
			position: absolute;
			right: 21rpx;
			top: 23rpx;
			border-radius: 50%;
		}

		.bg {
			width: 100%;
			height: 100%;
			position: absolute;
		}

		.content {
			width: 100%;

			position: absolute;
			top: 240rpx;
			align-items: flex-start;

			.verticalLine {
				width: 2rpx;
				height: 36rpx;
				margin: 24rpx;
				border: 2rpx solid;
				border-image: linear-gradient(93deg, rgba(75.00000312924385, 198.00000339746475, 237.0000010728836, 1), rgba(188.0000039935112, 147.00000643730164, 242.00000077486038, 1)) 2 2;
			}

			.right {
				display: flex;
				align-items: flex-start;
				width: 80vw;
				height: 200rpx;
				color: #000;
				overflow: hidden;

				.leftInfo {
					width: 120rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					text-align: center;

					.name {
						font-weight: 700;
						font-size: 28rpx;
						margin-top: 12rpx;
						overflow: hidden;
						text-align: center;
						text-overflow: ellipsis;
						white-space: nowrap;
						width: 110rpx;
					}
				}

			}

			.left {
				display: flex;
				align-items: flex-start;
				width: 80vw;
				height: 200rpx;
				color: #000;
				margin-bottom: 20rpx;


				.leftInfo {
					margin-left: 10rpx;
					width: 120rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					text-align: center;

					.name {
						font-weight: 700;
						font-size: 28rpx;
						margin-top: 12rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						width: 120rpx;
					}
				}

				.txt {
					width: 30vw;
				}

			}

			.btn {
				width: 592rpx;
				height: 82rpx;
				position: absolute;
				z-index: 99;
				top: 480rpx;
				left: calc(50% - 366rpx);

				image {
					width: 100%;
					height: 100%;
					position: absolute;
				}

				.btnText {
					height: 82rpx;
					position: relative;
					color: #3D3D3D;
					z-index: 999;
					font-size: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

		}

	}
</style>