<template>
	<view class="container">
		<view :center="center" :change:center="center" id="AmapRender" class="map" :event="event"
			:change:event="AmapRender.receiveEvent" :clearMap="clearMap" :change:clearMap="AmapRender.receiveClear"
			:modelUrl="modelUrl" :change:modelUrl="modelUrl" :zoom="zoom" :change:zoom="zoom" :style="{width:width}">
		</view>
	</view>
</template>
<script module="AmapRender" lang="renderjs">
	import renderJs from './renderJs.js'
	export default renderJs
</script>
<script>
	export default {
		props: {
			width: {
				type: String,
				default: '710rpx'
			},
			location: {
				type: Array,
				default: []
			},
			modelUrl: {
				type: String,
				default: "https://img.lluuxiu.com/serverConfig/business/ASS1/ASS1A.gltf"
				// default:"https://img.lluuxiu.com/serverConfig/SS7/ss7.gltf"
			},
			zoom: {
				type: Number,
				default: 16
			}
		},
		data() {
			return {
				// zoom: 13,
				event: {
					key: '',
					params: {}
				},
				clearMap: false,
				center: null

			}
		},
		watch: {
			// location() {
			// 	this.event = {
			// 		key: 'location',
			// 		params: this.location
			// 	}
			// },
			modelUrl(newVal, oldVal) {
				console.log("新模型地址", newVal)
				console.log("旧模型地址", oldVal)
				// this.modelUrl = newVal;
				this.event = {
					key: 'modelUrl',
					params: this.modelUrl
				}
				// 当modelUrl变化时，执行更新样式的逻辑
				// this.updateModel();
				// this.reloadModel(newVal);
			},
			zoom(newVal, oldVal) {
				console.log("新模型地址", newVal)
				console.log("旧模型地址", oldVal)
				this.event = {
					key: 'zoom',
					params: this.zoom
				}
			}
		},
		mounted() {
			console.log("模型啊去啊大大大", this.modelUrl)
			console.log("商户信息地址传过来的地址", this.location)
			// console.log("用户地址",uni.getStorageSync('userLoglat'));
			// this.center = uni.getStorageSync('userLoglat')
			// let loglatStr = uni.getStorageSync('userLoglat');
			// this.center = loglatStr.split(',').map(item => parseFloat(item)); // 转换成数字数组

			// // 尝试从本地存储获取用户地址数据
			// let userLoglat = uni.getStorageSync('userLoglat');

			// // 检查获取到的数据
			// if (userLoglat) {
			// 	// 如果数据存在，解析它并设置为中心点
			// 	// this.center = JSON.parse(userLoglat);
			// 	this.center = userLoglat;
			// 	console.log("用户地址", this.center);
			// } else {
			// 	// 如果没有找到用户地址数据，获取当前用户的定位
			// 	console.log("没有找到用户地址数据，正在尝试获取当前位置...");
			// 	this.getCurrentPosition();
			// }

			// 如果没有找到商家地址数据，获取当前用户的定位
			// if (!this.location) {
			// 	console.log("没有找到用户地址数据，正在尝试获取当前位置...");
			// 	this.getCurrentPosition();
			// } else {
			// 	this.center = this.location;
			// }
			if (!Array.isArray(this.location) || this.location.length === 0) {
				console.log("没有找到用户地址数据，正在尝试获取当前位置...");
				this.getCurrentPosition();
			} else {
				this.center = this.location;
			}

		},
		methods: {
			// 获取当前位置
			getCurrentPosition() {
				uni.getLocation({
					type: 'gcj02', // 国测局坐标
					success: (res) => {
						console.log("当前位置", [res.longitude, res.latitude]);
						this.center = [res.longitude, res.latitude];
					},
					fail: (err) => {
						console.error("获取位置信息失败:", err);
						// 处理错误，比如提示用户开启定位权限
					}
				});
			},
			clear() {
				this.clearMap = !this.clearMap
			},
			async callApp({
				act,
				option,
				cb,
				interval
			} = {}) {

				switch (act) {
					case 'mapLocation':
						this.$emit('mapLocation', option)
						break;
					case 'mapLocationMoveend':
						console.log(option, '====123');
						this.$emit('mapLocationMoveend', option)
						break;

					default:
						console.log(option, '====default');
						break;
				}


			},



		}
	}
</script>

<style scoped lang="scss">
	.container {
		position: relative;
		height: 100%;

		#AmapRender {
			width: 100%;
			height: 100%;
			background-color: transparent;
		}
	}
</style>