<template>
	<view class="appPage">
		<uni-popup ref="payStatus" type="bottom" :safe-area="false">
			<view class="pay">
				<view class="title">
					<view></view>
					<view>选择支付方式</view>
					<image @click="close" class="img32" src="../../../static/images/vip/rightClose.png" mode="" />
				</view>
				<view id="price">
					<p class="price">￥<span class="number">{{price}}</span></p>
				</view>
				<view class="tips">到期后自动续费，可随时取消</view>
				<view class="payCard t_betweent">
					<view class="t_display" v-if="platform == 'android'">
						<image class="img42" src="../../../static/images/pay/zhifubao.png" mode=""></image>
						<view class="" style="margin-left: 24rpx;">
							支付宝支付
						</view>
					</view>
					<view class="t_display" v-else>
						<image class="img42" src="../../../static/images/vip/vipLogo.png" mode=""></image>
						<view class="" style="margin-left: 24rpx;">
							Apple pay
						</view>
					</view>

					<image class="img32" src="../../../static/images/pay/radio2.png" mode=""></image>
				</view>
				<cusButton :txt="'确定协议并支付'+price+'元'" :radio="140" @confirm="create" style="margin: 32rpx 0;">
				</cusButton>
				<!-- <view class="check">
					<radio :checked="checked" @click="checked = !checked" style="transform:scale(0.7)" />
					<span style="line-height: 45rpx;">支付即同意<text class="t_zt"
							@click="goNav('/pages/privacyAgreement/userAgreement')">《会员协议》</text>及<text class="t_zt"
							@click="goNav('/pages/privacyAgreement/privacyAgreement')">《自动续费》</text></span>
				</view> -->
				<view class="img42" />
			</view>
		</uni-popup>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	import toast from '../../../uview-ui/libs/function/toast'
	export default {
		props: {
			price: {
				default: ""
			},
			gid: {
				default: ""
			}
		},
		data() {
			return {
				platform: uni.getSystemInfoSync().platform,
				checked: false
			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			open() {
				this.$refs.payStatus.open()
			},
			close() {
				this.$refs.payStatus.close()
			},

			create() {
				if (this.platform == 'android') {
					this.recharge()
				} else {
					this.$emit('confirm', this.price)
				}
			},
			recharge() {
				this.$http.post('/api/pay/goods/vip/buy/ali', {
					gid: this.gid
				}).then(res => {
					console.log('res', res);
					//统一各平台的客户端支付API
					uni.requestPayment({
						provider: 'alipay', //服务提供商（支付宝）（服务提供商，通过uni.getProvider获取）
						orderInfo: res.message, //后台返回的支付宝订单数据
						success(res) {
							this.toast('支付成功')
							this.$http.get('/api/user/info').then(res => {
								uni.setStorageSync('userInfo', res.message.user_info)
							})
						},
						fail(err) {
							console.log('fail:' + JSON.stringify(err));
						}
					});
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.pay {
		color: #000;
		padding: 32rpx 32rpx 20rpx 32rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;

		.payCard {
			margin-top: 24rpx;
			background: #F9F9F9;
			border-radius: 16rpx;
			padding: 32rpx;
		}

		.title {
			display: flex;
			justify-content: space-between;
			color: #000;
		}

		.tips {
			font-size: 26rpx;
			color: #979797;
		}

		#price {
			margin: 68rpx 0 40rpx 0;
			background: linear-gradient(to right, #4BC6ED, #BC93F2);
			font-style: normal;
			text-transform: none;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			text-align: center;

			.price .number {
				font-weight: 500;
				font-size: 100rpx;
			}
		}
	}
</style>