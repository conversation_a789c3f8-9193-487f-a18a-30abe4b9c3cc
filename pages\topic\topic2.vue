<template>
	<!-- 话题页面 -->
	<view>
		<u-navbar backIconName="search" back-text="" title="发布" :custom-back="customBack"
			:background="{backgroundColor: '#191C26'}" :border-bottom="false" title-color="#fff" back-icon-color="#fff">
			<view slot="content" style="width: 240rpx;">
				<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" bg-color="#191C26"
					active-color="#fff" inactive-color="rgba(255,255,255,0.72)" :show-bar="false"></u-tabs>
			</view>
			<view class="navbar-right t_display" slot="right" style="margin-right: 34rpx;" v-if="false">
				<view class="" style="color: rgba(255,255,255,0.72);">
					时间
				</view>
				<image src="../../static/images/time.png" class="img24" mode="" style="margin-left: 16rpx;"></image>
			</view>
		</u-navbar>
		<swiper class="swiperC" :current="current" @change="setCurrent" style="height: 80vh;" disable-touch>
			<swiper-item>
				<view style="display: block;width: 750rpx;">
					<scroll-view @scrolltolower="scrolltolowerS" :refresher-threshold="150" scroll-y="true"
						refresher-background="grey41" :scroll-x="false" style="height: 80vh;width: 750rpx;"
						refresher-enabled @refresherrefresh="onRefreshS" :lower-threshold="200"
						:refresher-triggered="triggeredS">
						<Post :showTop="false" :list="dataArrS" @more="goMore" @share="share" @setLike="setLikes"
							:previewFlag="true" :showAuthority="false" @goAddress="goNavLoaction">
						</Post>
						<view class="img140"></view>
					</scroll-view>
				</view>
			</swiper-item>
			<swiper-item>
				<view style="display: block;width: 750rpx;">
					<scroll-view @scrolltolower="scrolltolowerB" scroll-y="true" refresher-background="grey41"
						:lower-threshold="200" :refresher-threshold="150" style="height: 80vh;" refresher-enabled
						@refresherrefresh="onRefreshB" :refresher-triggered="triggeredB">
						<Post :list="dataArrB" @more="goMore" @share="share" @setLike="setLike" :previewFlag="true"
							:showAuthority="false" @goAddress="goNavLoaction">
						</Post>
						<view class="img140"></view>
					</scroll-view>
				</view>
			</swiper-item>
		</swiper>
		<sharePopup ref="share" :post="shareItem" :securityBottom="0"></sharePopup>
		<uni-popup ref="popup" type="bottom" background-color="#fff">
			<view class="cPopup">
				<view class="t_display">
					<image class="avatar" :src="popupInfo.user_info.avatar" mode="aspectFill"></image>
					<view class="name">{{popupInfo.user_info.nickname}}</view>
				</view>
				<view class="item t_display" @click="cancelBlack(popupInfo.moment_id,popupInfo.index)"
					style="margin: 20rpx;margin-top: 40rpx;">
					<image class="disable" src="../../static/images/shanchu.png" mode=""></image>
					<view class="rightInfo">
						删除
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="cancel">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
		<simpleShare ref="simpleShare" :zIndxe="9999"></simpleShare>
	</view>
</template>

<script>
	import Post from "@/components/post/post.vue"
	import simpleShare from '@/components/mark-simpleshare/mark-simpleshare.vue';

	export default {
		components: {
			Post,
			simpleShare
		},
		data() {
			return {
				triggeredS: false,
				triggeredB: false,
				recommendTotal: true,
				show: true,
				popupInfo: {
					user_info: {
						avatar: "",
						nickname: ""
					}
				},
				list: [{
					name: '热门'
				}, {
					name: '关注'
				}],
				dataArrS: [],
				dataArrB: [],
				page: 1,
				current: 0,
				followTotal: true,
				shareItem: {},
				tab2: {
					momentId: 0,
					option: 2,
				}

			}
		},
		created() {
			this.getData()
		},
		// watch: {
		// 	current: {
		// 		handler() {
		// 			this.getData()
		// 		}
		// 	}
		// },
		methods: {
			goNavLoaction(item) {
				this.$emit('goAddress', item)
			},
			filterData(momentId) {
				this.dataArrS = this.dataArrS.filter(item => {
					return item.moment_id != momentId
				})
				this.$forceUpdate()
			},
			onRefreshS() {
				this.triggeredS = true;
				setTimeout(() => {
					this.dataArrS = []
					this.page = 1
					this.getRecommend()
					this.triggeredS = false;
				}, 500)
			},
			onRefreshB() {
				this.triggeredB = true;
				setTimeout(() => {
					const momentId = this.dataArrB.length ? this.dataArrB[0].moment_id : ''
					this.tab2.momentId = momentId
					// this.dataArrB = []
					this.getFollowUp()
					this.triggeredB = false;
				}, 500)
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			refresherpulling() {
				console.log("----------refresherpulling---------");
			},
			refresherrefresh() {
				console.log("---------refresherrefresh-----------");
			},
			setLikes(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					"like": item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrS, item.index, {
						...this.dataArrS[item.index],
						is_like: !item.isLike,
						like: this.dataArrS[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			setLike(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					"like": item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrB, item.index, {
						...this.dataArrB[item.index],
						is_like: !item.isLike,
						like: this.dataArrB[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			cancelBlack(momentId, index) {
				switch (this.current) {
					case 0:
						this.$http.post('/api/moment/del', {
							momentId,
						}).then(res => {
							this.dataArrS.splice(index, 1)
							this.$refs.popup.close()
						})
						break;
					case 1:
						this.$http.post('/api/moment/del', {
							momentId,
						}).then(res => {
							this.dataArrB.splice(index, 1)
							this.$refs.popup.close()
						})
						break;
					default:
						break;
				}

			},
			scrolltolowerS(s) {
				if (this.recommendTotal) {
					this.page++;
					this.getRecommend()
				}
			},
			scrolltolowerB(s) {
				this.tab2.momentId = this.dataArrB[this.dataArrB.length - 1].moment_id
				if (this.followTotal) {
					this.getFollow()
				}
			},
			getFirstData() {
				this.dataArrB = []
				this.dataArrS = []
				this.recommendTotal = true
				this.page = 1
				this.getData()
			},
			getData() {
				this.getFollowUp()
				this.getRecommend()
				// if (this.current == 1) {
				// 	this.getFollow()
				// } else if (this.current == 0) {
				// 	this.getRecommend()
				// }
				setTimeout(() => {
					this.show = false
				}, 500)
			},
			getRecommend() {
				this.$http.get('/api/moment/recommend-list', {
					page: this.page
				}).then(res => {
					this.recommendTotal = res.message.length
					this.dataArrS.push(...res.message)
				})
			},
			getFollowUp() {
				this.$http.get('/api/moment/list', {
					option: 2, //用户上滑还是下滑，1上滑拉取旧的 2下滑拉取新的 ，新进页面先传2
					// momentId: this.dataArrB.length ? this.dataArrB[0].moment_id : 0,
					range: 2 //1拉取仅好友的，2拉取关注人的
				}).then(res => {
					//先不做更新
					// this.dataArrB.unshift(...res.message)
					this.dataArrB = [...res.message]
				})
			},
			getFollow(option = 1, customFlag) {
				this.$http.get('/api/moment/list', {
					option: option, //用户上滑还是下滑，1上滑拉取旧的 2下滑拉取新的 ，新进页面先传2
					momentId: this.tab2.momentId,
					range: 2 //1拉取仅好友的，2拉取关注人的
				}).then(res => {
					this.followTotal = res.message.length
					this.dataArrB.push(...res.message)
					if (res.message.length > 0) {
						this.tab2.momentId = this.dataArrB[this.dataArrB.length - 1].moment_id
					}
				})
			},
			share(item) {
				this.shareItem = item
				// this.$refs.simpleShare.show('Lihgtingball', '', '',
				// 	'https://img1.baidu.com/it/u=2275643212,2174089542&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067',
				// 	'/pages/xxxx', 0);

				this.$refs.share.open()
			},
			cancel() {
				this.$refs.popup.close()
			},
			goMore(item) {
				console.log(item);
				this.popupInfo = item
				this.$refs.popup.open()
			},
			customBack() {
				uni.navigateTo({
					url: "/pages/searchPost/searchPost"
				})
			},
			change(index) {
				this.current = index;
			},
			setCurrent(ids) {
				this.current = ids.detail.current;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.swiperC {
		// margin-top: 20rpx;
		// height: 100vh;
	}
</style>