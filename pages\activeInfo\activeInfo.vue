<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
			
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<!-- <view class="nav-title">{{ active_title.substring(0, 10) + (active_title.length > 10 ? '...' : '') }}</view> -->
			<view class="nav-title">商户活动详情</view>
			<!-- <view class="nav-edit" @click="goEditActive()">编辑</view> -->
		</view>
		<!-- <view class="audit-status">
			<view class="audit-status-title">
				<view>活动审核状态</view>
				<view class="audit-status-title-label1" v-if="activeInfo.audit_status == 2">已通过</view>
				<view class="audit-status-title-label2" v-else-if="activeInfo.audit_status == 3">未通过</view>
			</view>
			<view v-if="activeInfo.audit_status == 3" class="audit-status-content">
				<view class="audit-status-content-title">
					未通过原因：
				</view>
				<view>
					{{activeInfo.audit_remark}}
				</view>
			</view>
		</view> -->
		<view class="list">
			<view class="item">
				<view class="activeimageContainer">
					<image class="activeimage" :src="activeInfo.cover" mode="aspectFill"></image>
				</view>
				<view class="item-info">
					<view class="item-title">{{activeInfo.title}}</view>
					<view class="item-time" v-if="activeInfo.timeline">
						<image class="time-clock" src="../../static/images/vip/clock.png" mode=""></image>
						<view class="time">{{activeInfo.timeline}}</view>
					</view>
				</view>
			</view>
		</view>
		<view class="info">
			<view class="title">
				<view class="icon">
					<image class="title-icon" src="../../static/images/vip/icon-info.png" mode=""></image>
				</view>
				<view class="title-text">
					活动内容
				</view>
			</view>
			<view class="info-content" v-html="activeInfo.content">
			</view>
		</view>
	</view>
</template>

<script>
	// import "../../static/images/vip/font/font.css";
	import { config } from '@/config.js';
export default {
	data() {
		return {
			activeId: 0,
			active_title: "",
			active_image: "",
			begin_time: "",
			end_time: "",
			info_content: "", 
			status:2,
			activeInfo:{},//活动详情
		};
	},
	onLoad(options) {
		if(options.id){
			this.activeId = options.id;
			this.getActiveInfo();
			console.log(this.activeId);
		}
	},
	onShow() {
		uni.setNavigationBarColor({
			frontColor: '#ffffff', // 前景色值，包括按钮、标题、状态栏的颜色
			backgroundColor: '#000000', // 背景颜色值，包括背景图
			animation: {
				duration: 400,
				timingFunc: 'easeIn'
			}
		});
	},
	methods: {
		back() {
			uni.navigateBack();
			// uni.navigateTo({
			//         url: '/pages/active/active'
			//     })
		},
		getActiveInfo(){
			this.$http.get('/api/user/business/activity/detail',{id:this.activeId}).then((res) => {
				console.log(res);
				// this.active_title = res.message.title;
				// this.active_image = res.message.cover;
				// this.begin_time = res.message.begin_time;
				// this.end_time = res.message.end_time;
				// this.info_content = res.message.content;
				this.activeInfo = res.message
			});
		},
		// 编辑活动
		goEditActive(){
			uni.navigateTo({
				url: '/pages/activeAdd/activeAdd?activeId='+this.activeId
			});
		}
	}
};
</script>

<style lang="scss">
	
	page{
		background-color: #F5F7FB;
	}
.content {
	width: 100%;
	height: 100%;
	padding-top: 100px;
	background-color: #F5F7FB;
		overflow:scroll;
	.navigation-bar {
		width: 100%;
		display: flex;
		align-items: center;
		height: 100px;
		background-image: url('../../static/images/vip/newBackground.png'); /* 背景图路径 */
		background-size: cover;
		position: fixed;
		z-index: 1000;
		top: 0;
		left: 0;
		.back {
			width: 31px;
			height: 31px;
			margin-left: 2%;
			position: relative;
			z-index: 2;
		}
		.nav-title {
			width: 82%;
			height: 30px;
			color: #000000;
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: center;
			z-index: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
	.navigation-zhezhao{
		width: 100%;
		height: 100px;
		background-image: url('../../static/images/vip/nav-zhezhao.png'); /* 背景图路径 */
		background-size: 100%;
		background-repeat: no-repeat;
		background-position: bottom;
		position: fixed;
		z-index: 1000;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		.back {
			width: 31px;
			height: 31px;
			margin-left: 2%;
			position: relative;
			z-index: 2;
		}
		.nav-title {
			width: 82%;
			height: 30px;
			color: #000000;
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: center;
			z-index: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		.nav-edit{
			width:30px;
			margin-right:2%;
			color: #725EE9;
			font-family: 阿里巴巴普惠体;
			font-size: 14px;
			font-weight: 400;
			line-height: 19px;
		}
	}
	.audit-status{
		position: relative;
		z-index: 1;
		width: 95%;
		margin-left: 2.5%;
		margin-top: 5%;
		padding:17px 20px;
		box-sizing: border-box;
		border-radius: 10px;
		background: #F5F7FB;
		border: 1px solid #B2ABDA;
		.audit-status-title{
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #212121;
			font-family: HarmonyOS Sans;
			font-size: 16px;
			font-weight: 600;
			line-height: 20px;
			letter-spacing: 0px;
			text-align: left;
			.audit-status-title-label1{
				border-radius: 4px;
				border: 1px solid #E3B772;
				font-family: 'HarmonyOS Sans';
				font-weight: 400;
				font-size: 12px;
				line-height: 14px;
				color: #E3B772;
				letter-spacing: 0px;
				text-align: left;
				padding: 6px 12px;
			}
			.audit-status-title-label2{
				border-radius: 4px;
				border: 1px solid #F35323;
				font-family: 'HarmonyOS Sans';
				font-weight: 400;
				font-size: 12px;
				line-height: 14px;
				color: #F35323;
				letter-spacing: 0px;
				text-align: left;
				padding: 6px 12px;
			}
		}
		.audit-status-content{
			display: flex;
			align-items: flex-start;
			justify-content:  space-evenly;
			padding-top:13px;
			box-sizing: border-box;
			color: #666666;
			font-family: HarmonyOS Sans;
			font-size: 12px;
			font-weight: 400;
			line-height: 14px;
			border-top:1px solid #EAEAEA;
			margin-top:13px;
			.audit-status-content-title{
				width:45%;
				max-width: 150px;
			}
		}
	}
	.list {
		width: 95%;
		margin-left: 2.5%;
		margin-top: 5%;
		position: relative;
		z-index: 1;
		.item {
			width: 100%;
			display: flex;
			justify-content: space-evenly;
			align-items: center;
			align-content: center;
			border-radius: 10px;
			background: #F5F7FB;
			margin-top: 10px;
			border: 1px solid #B2ABDA;
			padding: 10px;
			box-sizing: border-box;
			.activeimageContainer{
				width: 122px;
				height: 122px;
				border-radius: 6px;
				margin-right:10px;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				position: relative;
			}
			.activeimage {
				position: absolute;
				width:100%;
			}

			.item-info {
				height: 122px;
				flex:1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.item-title {
					color: #212121;
					font-family: 'ali';
					font-size: 18px;
					font-weight: 700;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: left;
					padding-top:4px;
					box-sizing: border-box;
				}

				.item-time {
					line-height: 20px;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					flex-direction: row;
					color: rgb(152, 152, 152);
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 400;
					line-height: 14px;
					letter-spacing: 0px;
					text-align: left;
					padding-bottom: 10px;
					box-sizing: border-box;
					.time-clock {
						width: 16px;
						height: 16px;
						margin-right: 5px;
					}
				}
			}
		}
	}
	.info{
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center; 
		.title{
			width: 95%;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			margin-top: 20px;
			.icon{
				width: 20px;
				height: 20px;
				.title-icon{
					width: 20px;
					height: 20px;
				}
			}
			.title-text{
				color: #212121;
				font-family: 'ali';
				font-size: 16px;
				font-weight: 400;
				line-height: 20px;
				letter-spacing: 0px;
				text-align: left;
				margin-left: 5px;
				
			}
		}
		.info-content::v-deep{
			width: 95%;
			color: #212121;
			font-family: 'HarmonyOS Sans';
			font-size: 14px;
			font-weight: 400;
			line-height: 20px;
			letter-spacing: 0px;
			text-align: left;
			margin-top: 10px;
			border-radius: 10px;
			background: #F5F7FB;
			border: 1px solid #B2ABDA;
			padding: 10px 3%;
			margin-bottom: 3%;
			min-height: 200px;
			overflow: hidden;
			p{
				margin-bottom:16px;
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				letter-spacing: 0px;
				text-align: left;
			}
			img{
				width:100%;
				height: auto;
				border-radius: 10px;
				margin:16px 0;
			}
		}
	}
}
</style>
