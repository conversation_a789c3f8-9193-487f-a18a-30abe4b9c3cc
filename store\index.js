import Vue from 'vue'
import Vuex from 'vuex'
import NIMSDK from 'nim-web-sdk-ng/dist/NIM_UNIAPP_SDK'
Vue.use(Vuex)
import {
	diaplayTime
} from '@/utils/common.js'
import {
	apiRemarkList
} from '@/api/common.js'
let lifeData = {};

try {
	// 尝试获取本地是否存在lifeData变量，第一次启动APP时是不存在的
	lifeData = uni.getStorageSync('lifeData');
} catch (e) {

}

// 需要永久存储，且下次APP启动需要取出的，在state中的变量名
let saveStateKeys = ['userInfo', 'isLogin', 'setting', 'school'];

// 保存变量到本地存储中
const saveLifeData = function(key, value) {
	// 判断变量名是否在需要存储的数组中
	if (saveStateKeys.indexOf(key) != -1) {
		// 获取本地存储的lifeData对象，将变量添加到对象中
		let tmp = uni.getStorageSync('lifeData');
		// 第一次打开APP，不存在lifeData变量，故放一个{}空对象
		tmp = tmp ? tmp : {};
		tmp[key] = value;
		// 执行这一步后，所有需要存储的变量，都挂载在本地的lifeData对象中
		uni.setStorageSync('lifeData', tmp);
	}
}
const store = new Vuex.Store({
	// 下面这些值仅为示例，使用过程中请删除
	state: {
		// 如果上面从本地获取的lifeData对象下有对应的属性，就赋值给state中对应的变量
		userInfo: lifeData.userInfo ? lifeData.userInfo : {},
		isLogin: lifeData.isLogin ? lifeData.isLogin : false,
		school: lifeData.school ? lifeData.school : {},
		setting: lifeData.setting ? lifeData.setting : {},
		postsFlag: {},
		// 如果version无需保存到本地永久存储，无需lifeData.version方式
		version: '1.0.1',
		_Yxim: null,
		Yxim_info: {
			friendApply: [],
			p2pSessionId: '',
			allsessions: [],
			curChatRoom: [],
			teamList: [],
			limit: 0,
			friends: [],
			noMore: false,
			remarkList: [],
			p2p_lastMsg: '',
			msgReceiptSessionId: '',
			msgReceiptTime: ''
		},
		hbLocation: {},
		msgFirend: [],
	},
	actions: {
		async GET_REMARK_LIST({
			state,
			commit,
			dispatch
		}) {
			apiRemarkList().then(res => {
				if (res.code == 200) {
					commit('SET_REMARK_LIST', res.message)
				}
			})
		},
		async SET_Yxim({
			state,
			commit,
			dispatch
		}) {

			let option = {}
			// // #ifdef APP-PLUS
			// option = {
			// 	account: 'lwy',
			// 	token: 'admin123',
			// }
			// // #endif
			// // #ifdef H5
			// option = {
			// 	account: 'thl',
			// 	token: 'admin123',
			// }
			// // #endif
			try {

				const im = uni.getStorageSync('im')
				const appkey = 'ce937a9d4b0eb48dab5f582bc9965817'
				const Yxim = NIMSDK.getInstance({
					appkey,
					// ...option,
					account: im.account,
					token: im.token,
					// debugLevel: 'debug'
				});

				await Yxim.connect()
				setInterval(() => {
					if (Yxim.status === 'unconnected') {
						Yxim.connect()
						dispatch('GET_ALL_SESSIONS')
					}
				}, 3000)
				Vue.prototype.$Yxim = Yxim;
				state._Yxim = Yxim
				// dispatch('GET_TEAM_LIST')
				const eventList = [
					'logined',
					'multiPortLogin',
					'kicked',
					'willReconnect',
					'disconnect',
					'msg',
					'syncdone',
					'proxyMsg', 'syncRoamingMsgs', 'syncOfflineMsgs', 'cloudSession',
					'syncMyNameCard', 'syncdone', 'sessions', 'updateMyNameCard', 'updateBlackList',
					'updateMuteList',
					'sysMsg', 'syncSysMsgs', 'syncFriend', 'friends', 'users', 'updateSystemMessages',
					'sysMsgUnread', 'msgReceipts',
					'pushEvents',
					'teamMsgReceipts', 'updateSession', "recvMessageReceipts",
					'teams',
					'createTeam',
					'updateTeamMember',
					'updateTeam', 'addTeamMembers', 'updateTeamManagers', 'transferTeam',
					'removeTeamMembers',
					'dismissTeam', 'updateTeamMembersMute', 'cloudSession'
				]


				eventList.forEach((key) => {
					Yxim.on(key, async (res) => {
						switch (key) {
							case "logined":
								dispatch('GET_ALL_SESSIONS')
								break;
							case "msgReceipts":
								// dispatch('GET_ALL_SESSIONS')
								state.Yxim_info.msgReceiptTime = res[0].msgReceiptTime
								state.Yxim_info.msgReceiptSessionId = res[0].sessionId
								break;
							case "teams":
								break;
							case "disconnect":
								await Yxim.connect()
								break;
							case "msg": //xin消息

								dispatch('GET_ALL_SESSIONS')
								// Yxim.msg.sendMsgReceipt({
								// 	msg: res,
								// 	done: (error, obj) => {
								// 		console.log('发送消息已读回执');
								// 	}
								// });

								commit('SET_onMsg', res)
								break;
							case "syncOfflineMsgs":

								break;
							case "updateSession":
								setTimeout(() => {
									dispatch('GET_ALL_SESSIONS')
								}, 1000)
								// commit('SET_onMsg',res)
								break;
							case "sysMsg":
								if (res.type == 'customP2p') {
									const {
										attach
									} = res
									const {
										msg_type,
										content
									} = attach
									if (msg_type == 10001) {
										commit('SET_FRIEND_ADD', {
											...content.from_user,
											request_id: content.request_id
										})
									}
								} else if (res.type == "teamInvite") {
									if (res.content == 'pleaseJoinTeam') {
										Yxim.team.acceptTeamInvite({
											from: res.from,
											teamId: res.to
										})
									}
								}
								break;
							case "addTeamMembers":
								const {
									team
								} = res
								// const has = state.Yxim_info.teamList.findIndex(item =>
								// 	item.teamId == team.teamId) > -1
								// if (!has) {
								// 	Yxim.team.getTeamsById({
								// 		teamId: team.teamId
								// 	}).then(res => {
								// 		state.Yxim_info.teamList.push(res)
								// 	})
								// }
								break;
							default:
								break;
						}
					});
				})

			} catch (e) {

				//TODO handle the exception
			}
		},
		sendMsgReceipt({
			state,
			commit
		}, msg) {
			state._Yxim.msg.sendMsgReceipt({
				msg: msg,
				done: (error, obj) => {
					console.log('发送消息已读回执');
				}
			});
		},

		GET_TEAM_LIST({
			state,
			commit
		}) {
			state._Yxim.team.getTeams().then(res => {
				commit('SET_TEAM_LIST', res)
			})
		},
		GET_MY_FRIEND({
			state,
			commit
		}) {
			state._Yxim.friend.getFriends().then(friends => {
				commit('SET_MY_FRIEND', friends)
			})
		},
		async SET_YXIM_DISCONNECT({
			state,
			commit
		}) {
			console.log('退出云信');
			await state._Yxim.disconnect()
			await state._Yxim.destroy()

		},

		async GET_ALL_SESSIONS({
			state,
			commit
		}, maxTimestamp) {
			state.Yxim_info.limit = 100
			state._Yxim.cloudSession.queryCloudSessionList({
				includedLastMsg: true,
				limit: state.Yxim_info.limit,
				maxTimestamp: maxTimestamp || 0,
				minTimestamp: 0
			}).then(async res => {
				if (res.sessionList.length == 0) {
					commit('SET_SESSION_NOMORE', true)
					return false
				} else {
					commit('SET_SESSION_NOMORE', false)
				}
				const data = []
				for (let i = 0; i < res.sessionList.length; i++) {
					if (maxTimestamp && i == 0) {
						continue
					}
					const item = res.sessionList[i]
					// console.log("------------item-----------", item);
					const type = item.sessionId.split('-')[0]
					let userRes = []
					let app_remark_name = ''
					let teamInfo = null
					let unreadCount = 0
					let session = null
					if (type == 'team') {
						const users = await state._Yxim.team.getTeamMembers({
							teamId: item.lastMsgInfo.lastMsg.target
						})
						teamInfo = await state._Yxim.team.getTeamInfo({
							teamId: item.lastMsgInfo.lastMsg.target
						})
						userRes = await state._Yxim.user
							.getUsersNameCardFromServer({
								"accounts": users.map(item => item.account)
							})
						session = await state._Yxim.session.getSession({
							id: item.sessionId
						})
					} else if (type == 'p2p') {
						const accountId = item.sessionId.split('-')[1]
						userRes = await state._Yxim.user.getUsersNameCardFromServer({
							"accounts": [item.sessionId.split('-')[1]]
						})
						session = await state._Yxim.session.getSession({
							id: item.sessionId
						})
						try {
							app_remark_name = state.Yxim_info.remarkList[accountId] || null
						} catch (e) {
							//TODO handle the exception
							app_remark_name = null
						}
					}
					data.push({
						app_remark_name,
						avatarList: userRes.map(per => {
							return {
								url: per.avatar ||
									'https://img2.baidu.com/it/u=**********,*********&fm=253&fmt=auto?w=800&h=1067'
							}
						}),
						nuck: type === 'team' ? teamInfo.name : (app_remark_name ||
							userRes[
								0].nick),
						uid: item.sessionId,
						chatName: item.sessionId,
						unread: item.lastMsgInfo.lastMsg.status,
						unreadNum: item.unread || 0,
						newMsg: item.lastMsgInfo.lastMsg.body,
						msgType: item.lastMsgInfo.lastMsg.type,
						newMsgTime: item.lastMsgInfo.lastMsg.time,
						chatHeadImg: "/uni_modules/tianxie-chat/static/head-img/head_02.png",
						setTopPerson: "0",
						type,
						account: type === 'team' ? '' : userRes[0].account,
						idServer: item.lastMsgInfo.lastMsg.idServer,
						teamId: item.lastMsgInfo.lastMsg.target,
						updateTime: item.updateTime,
						lastMsgInfo: item.lastMsgInfo,
						unreadCount: session ? session.unread : 0
					})
				}
				commit('SET_all_sessions', data)
			})
		}
	},
	mutations: {
		SET_msgFirend(state, arr) {
			state.msgFirend = arr
		},
		SET_SESSION_ResetSessionUnreadCount(state, id) {
			state._Yxim.session.resetSessionUnreadCount({
				id
			})
		},
		SET_HB_LOCATION(state, value) {
			state.hbLocation = value
		},
		SET_REMARK_LIST(state, value) {
			state.Yxim_info.remarkList = value
		},
		SET_FRIEND_ADD(state, value) {
			state.Yxim_info.friendApply.push(value)
		},
		SET_FRIEND_REFUSE(state, value) {
			const index = state.Yxim_info.friendApply.findIndex(item => item.request_id == value.request_id)
			state.Yxim_info.friendApply.splice(index, 1)
		},
		SET_SESSION_NOMORE(state, value) {
			state.Yxim_info.noMore = value
		},
		SET_MY_FRIEND(state, value) {
			state.Yxim_info.friends = value
		},
		SET_TEAM_LIST(state, value) {
			state.Yxim_info.teamList = value
		},
		SET_all_sessions(state, value) {
			state.Yxim_info.allsessions = value
		},
		SET_mergeSessions(state, sessions) {
			// state.Yxim_info.sessions =
		},
		SET_p2pSessionId(state, value) {
			state.Yxim_info.p2pSessionId = value
		},
		SET_Clear_CurChatRoom(state) {
			state.Yxim_info.curChatRoom = []
			state.Yxim_info.p2pSessionId = ''

		},
		SET_onMsg(state, value) {
			if (value.scene == 'p2p') {

				if (`${value.scene}-${value.to}` === state.Yxim_info.p2pSessionId ||
					`${value.scene}-${value.from}` === state.Yxim_info.p2pSessionId) {
					state.Yxim_info.p2p_lastMsg = value
					if (value.status === 'unread') {
						state._Yxim.msg.sendMsgReceipt({
							msg: value,
							done: (error, obj) => {
								console.log('发送消息已读回执');
							}
						});
					}
					state.Yxim_info.curChatRoom.push(value)
				}
			} else if (value.scene == 'team') {
				if (`${value.scene}-${value.to}` === state.Yxim_info.p2pSessionId) {
					state.Yxim_info.curChatRoom.push(value)
				}
			}
		},
		SET_SendMsgReceipt(state, value) {
			if (value.scene == 'p2p') {

				if (`${value.scene}-${value.to}` === state.Yxim_info.p2pSessionId ||
					`${value.scene}-${value.from}` === state.Yxim_info.p2pSessionId) {
					if (value.status === 'unread') {
						console.log(value);

						state._Yxim.msg.sendMsgReceipt({
							msg: value,
							done: (error, obj) => {
								console.log('发送消息已读回执');
							}
						});
					}
				}
			}
		},
		SET_Detory_Yxim() {

		},
		//同步本地 云信 会话列表
		SET_Yxim_sessions(state, value) {
			state.Yxim.sessions = value
		},
		$uStore(state, payload) {
			// 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
			let nameArr = payload.name.split('.');
			let saveKey = '';
			let len = nameArr.length;
			if (nameArr.length >= 2) {
				let obj = state[nameArr[0]];
				for (let i = 1; i < len - 1; i++) {
					obj = obj[nameArr[i]];
				}
				obj[nameArr[len - 1]] = payload.value;
				saveKey = nameArr[0];
			} else {
				// 单层级变量，在state就是一个普通变量的情况
				state[payload.name] = payload.value;
				saveKey = payload.name;
			}
			// 保存变量到本地，见顶部函数定义
			saveLifeData(saveKey, state[saveKey])
		}
	}
})

export default store