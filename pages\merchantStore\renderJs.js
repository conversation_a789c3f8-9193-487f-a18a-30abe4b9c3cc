import {
	config
} from '@/config.js'
import ren_juhe from '../../static/map/ren_juhe.png'
import * as turf from '@turf/turf'
const dl25 =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/cfbdf532-7e6b-4e0b-b83b-17848cef6c8c.png'
const dl50 =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0d6bb079-bb1c-4689-ab81-655c772895f3.png'
const dl75 =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/3d82a020-3a80-48ba-b5d1-a6539d9884f8.png'
const dl100 =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0dcdc02a-dac1-471e-8bf8-676b75f389e2.png'
const yellow_packet =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240202/2b05f6ef-3f28-4c5f-90b9-45943a3b17e8.png'
const red_packet =
	'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240202/75ff7e37-0837-4196-a0ce-de1b050184e4.png'

const {
	avatarW,
	avatarH,
	avatarHoverW,
	avatarHoverH,
	packetW,
	packetH
} = config
let myMap;
let map_person
import {
	newMarker,
	newIcon,
	newText,
	newPerSon,
	getGeocoder,
	newLabelMarkers,
} from '@/utils/MapTools.js'
import {
	apiLocationReport,
	apiLocationVisibleGet,
	apiLocationMe,
	apiAddressSet,
	apiAddressGet,
	apiLocationSearchPeople,
	apiGetNight,
	apiToken
} from '@/api/common.js'

export default {
	data() {
		return {
			zoom17: 1,
			startClickTime: 0,
			me: null,
			downTime: null,
			MaxDist: 1000, //米
			moon_modal: null,
			house_modal: null,
			showMeDesc: false,
			overlay_me: {},
			meMarket: null,
			houseMarkets: null,
			overlay_person: {},
			overlay_person_jh: {},
			cluster_person: null,
			overlay_moon: {},
			overlay_address: {},
			overlay_redPacket: {},
			cluster_address: null,
			houseMessage: [],
			gltfObj: null,
			houseMeshes: [],
			object3Dlayer: null,
			temperature: '',
			weather: null,
			poiMarket: null,
			current_edit_person_marker: null,
			person_showDetail_arr: []
		}
	},
	mounted() {
		/**
		 * 	初始化高德Api
		 */
		if (typeof window.AMap === 'function') {
			this.initMap()
		} else {
			window._AMapSecurityConfig = {
				securityJsCode: '********************************',
			}
			/**
			 * 	打开房子弹窗
			 */
			window.openSetting = (e, a, b) => {
				const arr = e.split(',')
				this.callMethod('open_housePopup', {
					id: arr[0],
					location: arr[1],
					remark: arr[2]
				})
			}

			/**
			 * 	打开月亮弹窗
			 */
			window.openSettingMoon = (e, a, b) => {

				const arr = e.split(',')
				this.callMethod('open_moonPopup', {
					id: arr[0],
					location: arr[1],
					remark: arr[2]
				})
			}
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
			script.onload = this.initMap.bind(this)
			document.head.appendChild(script)
		}
	},
	onUnload() {

	},
	methods: {
		/**
		 * 	初始化地图  AmapRender
		 */
		initMap() {
			myMap = new AMap.Map('AmapRender', {
				pitch: 50, //地图俯仰角度，有效范围 0 度- 83 度
				viewMode: '3D', //地图模式
				rotateEnable: true, //是否开启地图旋转交互 鼠标右键 + 鼠标画圈移动 或 键盘Ctrl + 鼠标左键画圈移动
				pitchEnable: true, //是否开启地图倾斜交互 鼠标右键 + 鼠标上下移动或键盘Ctrl + 鼠标左键上下移动
				// zoom: 17, //初始化地图层级
				// rotation: -15, //初始地图顺时针旋转的角度
				// center: [116.333926, 39.997245] //初始地图中心经纬度
				center: this.center,
				zoom: 15,
				zooms: [5, 20], //地图显示的缩放级别范围
				// features: ['bg', 'road', 'building'],
				resizeEnable: true,
				mapStyle: 'amap://styles/957040a4d7e11f8e53f93977962d6779', //设置地图的显示样式
				draggable: true
			});

			// 地图加载完成！
			myMap.on("complete", async () => {
				const center = myMap.getCenter()

				this.callMethod('map:complete', {
					maxDist: this.getMaxDist(),
					longitude: center.lng,
					latitude: center.lat,
				})
				// 获取地图当前可视区域
				const bounds = myMap.getBounds()
				// 赋值当前可是区域
				this.current_bounds_path = bounds.path
				// 创建地图中用户图层
				this.overlay_person = new window.AMap.OverlayGroup(); //初始化附近人群组
				this.overlay_person_jh = new window.AMap.OverlayGroup(); //初始化附近人群组
				this.overlay_me = new AMap.OverlayGroup();
				this.overlay_moon = new AMap.OverlayGroup();
				this.overlay_address = new AMap.OverlayGroup();
				this.overlay_redPacket = new AMap.OverlayGroup();
				myMap.add(this.overlay_moon);
				myMap.add(this.overlay_me);
				myMap.add(this.overlay_person);
				myMap.add(this.overlay_person_jh);
				myMap.add(this.overlay_address);
				myMap.add(this.overlay_redPacket)
				this.overlay_person_jh.hide()
				// 异步加载路径规划插件
				AMap.plugin('AMap.Driving', (e) => {
					this.mapDriving = new AMap.Driving({
						policy: AMap.DrivingPolicy.LEAST_TIME,
					})
				})
				// 创建3D模型
				AMap.plugin('AMap.Object3D', (e) => {
					this.object3Dlayer = new AMap.Object3DLayer()
					myMap.add(this.object3Dlayer);
					myMap.plugin(["AMap.GltfLoader"], () => {
						this.gltfObj = new AMap.GltfLoader();
					})
				})
				// 天气插件
				AMap.plugin('AMap.Weather', () => {
					this.weather = new AMap.Weather();
					//查询实时天气信息, 查询的城市到行政级别的城市，如朝阳区、杭州市

				});
				AMap.plugin(["AMap.MarkerClusterer"], async () => {

					const _renderClusterMarker = (context) => {
						const clusterData = context.clusterData; // 聚合中包含数据
						const index = context.index; // 聚合的条件
						const count = context.count; // 聚合中点的总数
						const marker = context.marker; // 聚合点标记对象
						const styleObj = {
							bgColor: 'rgba(0,0,0,.8)',
							borderColor: 'rgba(255,255,255,1)',
							// // text: clusterData[0][index['mainKey']],
							size: Math.round(Math.pow(count / (this.person_markers
									.length || 1),
								1 /
								5) * 70),
							color: '#ffffff',
							textAlign: 'center',
							boxShadow: '0px 0px 5px rgba(0,0,0,0.8)'
						}
						// 自定义点标记样式
						const div = document.createElement('div');
						div.innerHTML = count
						div.className = 'amap-cluster';
						div.style.width = 40 + 'px';
						div.style.height = 40 + 'px';
						div.style.lineHeight = 25 + 'px';
						if (styleObj.index <= 2) {
							div.style.height = styleObj.size + 'px';
							// 自定义点击事件
							context.marker.on('click', function(e) {
								const curZoom = map.getZoom();
								if (curZoom < 20) {
									curZoom += 1;
								}
								map.setZoomAndCenter(curZoom, e.lnglat);
							});
						}
						div.style.backgroundImage =
							`url(https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20231203/659faea8-89d2-4485-ae82-80f14d7933aa.png)`;
						div.style.backgroundRepeat = 'no-repeat'
						div.style.backgroundSize = '100% 100%'
						div.style.color = styleObj.color;
						div.style.textAlign = styleObj.textAlign;
						context.marker.setContent(div)
						context.marker.setAnchor('center');
						context.marker.setzIndex(40);
					};
					this.cluster_person = new AMap.MarkerClusterer(
						myMap, [], {
							gridSize: 10, // 聚合网格像素大小
							renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
						}
					);
				});
			});
			// 地图点击事件！
			myMap.on('touchstart', (ev) => {
				this.startClickTime = new Date().getTime()
			}, this);
			// 触发鼠标在地图上单击抬起时的事件
			myMap.on('touchend', (ev) => {
				const now = new Date().getTime()
				// 监听地图长按事件
				if (this.startClickTime && now - this.startClickTime > 300) {
					this.mapLongTap(ev)
				}
			}, this);
			myMap.on('rotateend', (ev) => {}, this);
			myMap.on('click', (ev) => {
				// 删除弹层
				this.moon_modal && this.moon_modal.hide()
				this.house_modal && this.house_modal.hide()
				this.poiMarket && this.poiMarket.hide()
				this.callMethod('close_house_popup')
				this.callMethod('close_dian_popup')
				this.onMeshesClick(ev)
			}, this);
			myMap.on('movestart', (ev) => {
				// 重置地图点击
				this.startClickTime = null
			}, this);
			myMap.on('dragging', (ev) => {
				// 重置地图点击
				this.startClickTime = null
			}, this);
			myMap.on('moveend', async (ev) => {
				// 获取地图中心点坐标
				const center = myMap.getCenter()
				// 动态设置中心点区域名称
				getGeocoder(AMap, {
					lnglat: [center.lng, center.lat]
				}, true).then(geoRes => {
					if (geoRes.info == 'OK') {
						const strr = (geoRes.regeocode.addressComponent.city || geoRes.regeocode
							.addressComponent.province)

						console.log('11geoRes', geoRes, '');
						const district = geoRes.regeocode.addressComponent.district || geoRes
							.regeocode.addressComponent.country
						this.callMethod('changeCountry', district)
						this.weather.getLive(strr, (err, data) => {
							if (!err) {
								this.temperature = data.temperature + '℃'
								this.callMethod('temperature:change', {
									temperature: this.temperature,
									weather: data.weather.indexOf('雨') > -1 ? 'yu' :
										data.weather.indexOf('雪') > -1 ? 'xue' :
										'qing'
								})
							} else {
								this.callMethod('temperature:change', '')
							}
						});
					} else {
						this.callMethod('changeCountry', '')
					}
				})
				// 获取当前可是区域的最大半径，加载半径内用户
				this.getPeopleInMaxDist()
			}, this);
			myMap.on('zoomend', (ev) => {
				// 获取当前可是区域的最大半径，加载半径内用户
				this.getPeopleInMaxDist()
				const zoom = myMap.getZoom()

				if (zoom < 13) {
					this.zoom13 = 1
					this.overlay_person.hide()
					this.overlay_person_jh.show()
				} else {
					this.zoom13 = 2
					this.overlay_person.show()
					this.overlay_person_jh.hide()
				}
				if (zoom >= 17) {
					if (this.zoom17 == 2) return false
					this.zoom17 = 2
					this.cluster_address.clearMarkers()
					this.houseMessage.forEach(item => {
						if (item.coordinate.indexOf('undefined') > -1) return false
						const position = item.coordinate.split(',')
						const paramHouse = {
							position: position, // 必须
							scale: 100, // 非必须，默认1
							height: 100, // 非必须，默认0
							scene: 0, // 非必须，默认0
							zIndex: 18,
							extData: item
						};
						this.gltfObj.load(config.ossBaseUrl + 'config/map/1.12.gltf', (gltfHouse) => {
							this.houseMeshes.push(gltfHouse)
							gltfHouse.setOption(paramHouse);
							gltfHouse.rotateX(0);
							gltfHouse.rotateZ(0);
							gltfHouse.rotateY(0);
							this.object3Dlayer.add(gltfHouse);
						});
					})
				} else {
					if (this.zoom17 == 1) return false
					this.zoom17 = 1
					const markersCount = this.cluster_address && this.cluster_address.getClustersCount()
					if (this.cluster_address && markersCount <= 0) {
						this.object3Dlayer.clear()
						this.cluster_address.setMarkers(this.houseMarkets)
					}
				}
			});
		},
		/**
		 * 	长按事件-扎点
		 */
		async mapLongTap(ev) {
			const lnglat = ev.lnglat;

			const res = await getGeocoder(window.AMap, {
				lnglat: [lnglat.lng, lnglat.lat]
			})
			this.callMethod('mapLongClick', {
				lnglat: lnglat,
				location: res.regeocode.formattedAddress,
				addressComponent: res.regeocode.addressComponent

			})
		},
		/**
		 * 	调用 view 层的方法
		 */
		callMethod(act = '', params) {
			this.$ownerInstance.callMethod('callApp', {
				act,
				option: params,
			})
		},
		/**
		 * 	获取当前可是区域的最大半径，加载半径内用户
		 */
		getPeopleInMaxDist() {
			const r = this.getMaxDist()
			const center = myMap.getCenter()
			if (!this.downTime) {
				this.downTime = setTimeout(() => {
					clearTimeout(this.downTime)
					this.downTime = null
					this.callMethod('change:maxDist', {
						maxDist: r,
						longitude: center.lng,
						latitude: center.lat,

					})
				}, 2000)
			}
		},
		/**
		 * 房子  模型点击 
		 */
		onMeshesClick(ev) {
			const pixel = ev.pixel;
			const px = new AMap.Pixel(pixel.x, pixel.y);
			const obj = myMap.getObject3DByContainerPos(px, [this.object3Dlayer], false) || {};
			if (obj && obj.object) {
				const meshId = obj.object.id;

				this.houseMeshes.forEach(async item => {
					if (item && item.layerMesh) {
						for (let i = 0; i < item.layerMesh.length; i++) {
							if (meshId === item.layerMesh[i].id) {

								const extData = item.gltf.option.extData

								const lng = extData.coordinate.split(',')[0]
								const lat = extData.coordinate.split(',')[1]


								const startLocation = [this.me.longitude, this.me
									.latitude
								]
								const endLocation = extData.coordinate.split(',')
								const result = await this.getDriver(startLocation,
									endLocation)
								if (result.info == 'OK') {

									const distance = result.routes[0].distance
									extData.distance = distance && distance > 1000 ? ((distance / 1000)
										.toFixed(
											2) + ' 千米') : (distance + ' 米') || (0 +
										' 米')
									// this.$refs.storePopup.open(extData)
									this.callMethod('open_storePopup', {
										...extData
									})
								}
							}
						}
					}
				})
			}
		},
		/**
		 * 	获取视野的最大半径
		 */
		getMaxDist() {
			let r = 0;
			const bounds = myMap.getBounds()
			const center = myMap.getCenter()
			try {
				this.current_bounds_path = bounds.path
				this.current_bounds_path.forEach(item => {
					const p1 = new AMap.LngLat(item[0], item[1])
					const p2 = new AMap.LngLat(center.lng, center.lat)
					const dist = p1.distance(p2)
					if (dist > r) r = dist
				})
				return parseInt(r)
			} catch (e) {
				//TODO handle the exception
				return 200000000
			}

		},

		/**
		 *  重置地图中心点
		 * 	center 变动回调  
		 */
		receive_Center(newValue, oldValue, ownerVm, vm) {
			this.resetPeople()
			try {
				if (newValue === null) return

				myMap && myMap.setCenter(this.center)
				myMap && myMap.setZoom(15)
			} catch (e) {
				//TODO handle the exception
			}

		},
		resetPeople() {
			if (!this.cluster_person) return false
			const markers = this.cluster_person.getMarkers()
			markers.forEach(item => {
				if (this.person_showDetail_arr.includes(item.De.extData.uid)) {
					const Icon = newIcon(AMap, {
						image: item.De.extData.avatar,
						size: new AMap.Size(50, 63),
						imageSize: new AMap.Size(50, 63),
					})
					item.setIcon(Icon)
				}
			})
			this.person_showDetail_arr = []
		},

		// 红包标注
		receive_redPacket(newValue, oldValue, ownerVm, vm) {
			if (newValue === null) return
			this.overlay_redPacket && this.overlay_redPacket.clearOverlays()
			const markers = []
			const icon_red = newIcon(AMap, {
				image: red_packet,
				size: new AMap.Size(packetW, packetH),
				imageSize: new AMap.Size(packetW, packetH),
			})
			const icon_yellow = newIcon(AMap, {
				image: yellow_packet,
				size: new AMap.Size(packetW, packetH),
				imageSize: new AMap.Size(packetW, packetH),
			})
			newValue.forEach(item => {
				const marker_red = new AMap.Marker({
					position: [item.Longitude, item.Latitude],
					icon: item.is_business ? yellow_packet : icon_red,
					offset: new AMap.Pixel(-packetW / 2, -packetH),
					extData: {
						...item,
					},
					zIndex: 90
				})
				markers.push(marker_red)
				marker_red.on('touchstart', (ev) => {
					this.callMethod('view_redPacket_detail', ev.target.De.extData)
					myMap.setCenter([item.longitude, item.latitude])
					myMap.setZoom(18)

				})
			})
			this.overlay_redPacket.addOverlays(markers)
		},
		/**
		 *  重置地图用户W
		 * 	person 变动回调  
		 */
		receive_Person(newValue, oldValue, ownerVm, vm) {

			if (newValue === null) return
			map_person = newValue

			const markers = []
			const markers_jh = []
			map_person.forEach(item => {
				if (!item.i_can_see) return false
				const hasIndex = this.person_showDetail_arr.indexOf(item.uid)
				const icon_per = newIcon(AMap, {
					image: hasIndex > -1 ? item.map_user_mark : item.avatar,
					size: hasIndex > -1 ? new AMap.Size(avatarHoverW, avatarHoverH) : new AMap.Size(
						avatarW, avatarH),
					imageSize: hasIndex > -1 ? new AMap.Size(avatarHoverW, avatarHoverH) : new AMap
						.Size(avatarW, avatarH),
				})


				const marker_per = new AMap.Marker({
					position: [item.longitude, item.latitude],
					icon: icon_per,
					offset: hasIndex > -1 ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH) : new AMap
						.Pixel(-avatarW / 2, -avatarH),
					extData: {
						...item,
						icon_per,
						showDesc: 0
					},
					zIndex: 90
				})

				marker_per.on('click', (ev) => {
					let showDetailUid = this.person_showDetail_arr[0] || ''
					const isSameClick = showDetailUid && showDetailUid == item.uid
					const markers = this.cluster_person.getMarkers()
					if (isSameClick) {
						this.person_showDetail_arr = []
					} else {
						this.person_showDetail_arr = [item.uid]
					}
					for (let i = 0; i < markers.length; i++) {
						const curItem = markers[i].De.extData
						const curItemUid = markers[i].De.extData.uid
						if (isSameClick) {

							if (showDetailUid === curItemUid) {
								const perIcon = newIcon(AMap, {
									image: curItem.avatar,
									size: new AMap.Size(avatarW, avatarH),
									imageSize: new AMap.Size(avatarW, avatarH)
								})
								markers[i].setIcon(perIcon)
								markers[i].setOffset(new AMap.Pixel(-avatarW / 2,
									-avatarH))
								break;
							}
						} else {
							if (item.uid === curItemUid) {
								const perIcon = newIcon(AMap, {
									image: item.map_user_mark,
									size: new AMap.Size(avatarHoverW, avatarHoverH),
									imageSize: new AMap.Size(avatarHoverW, avatarHoverH),
								})
								markers[i].setIcon(perIcon)
								markers[i].setOffset(new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH))

							} else if (curItemUid == showDetailUid) {
								const perIcon1 = newIcon(AMap, {
									image: curItem.avatar,
									size: new AMap.Size(avatarW, avatarH),
									imageSize: new AMap.Size(avatarW, avatarH)
								})
								markers[i].setIcon(perIcon1)
								markers[i].setOffset(new AMap.Pixel(-avatarW / 2,
									-avatarH))
								showDetailUid = ''

							}

						}
					}


					// TODO
					console.log('-----------------------');
					this.labelMarker && this.labelMarker.hide()
					this.MoonLabelMarker && this.MoonLabelMarker.hide()
					this.callMethod('closeStoreDialog')
					myMap.setCenter([item.longitude, item.latitude])
					myMap.setZoom(13)

					this.current_edit_person_marker = newMarker
					if (!isSameClick) {
						this.callMethod('open_personPopup', ev.target.De.extData)
					}

				})
				markers.push(marker_per)
			})
			this.person_markers = markers
			// 自定义聚合点样式
			this.cluster_person.setMarkers(this.person_markers)

		},
		/**
		 *  重置地图房子
		 * 	house 变动回调  
		 */
		receive_House(newValue, oldValue, ownerVm, vm) {
			if (newValue === null) return
			this.houseMessage = newValue
			const address_markers = []
			this.overlay_address.clearOverlays()
			this.cluster_address && this.cluster_address.clearMarkers()
			newValue.forEach(item => {
				const div = document.createElement('div')
				const img = document.createElement('img')
				img.style.width = '30px'
				img.style.height = '30px'
				img.src = config.ossBaseUrl + 'config/map/housePng.png'
				div.appendChild(img)
				try {
					if (item.coordinate.indexOf('undefined') > -1) return false
					const position = item.coordinate.split(',')
					const row = new AMap.Marker({
						position: position,
						content: div,
						offset: new AMap.Pixel(-15, -15),
						extData: {
							...item
						},
						zIndex: 40
					})

					address_markers.push(row)
				} catch (err) {

				}
			})
			this.overlay_address && this.overlay_address.addOverlays(address_markers)
			this.overlay_address.on('click', async (ev) => {
				const extData = ev.target.De.extData
				const lng = ev.target.De.position.lng
				const lat = ev.target.De.position.lat
				myMap.setCenter([lng, lat])
				if (extData.type == 1) {
					if (this.house_modal) {
						myMap.remove(this.house_modal)
						this.house_modal = null
					}
					this.house_modal = new AMap.Marker({
						position: new AMap.LngLat(lng, lat),
						offset: new AMap.Pixel(-94, -120), //设置文本标注偏移量
						// zIndex: 99,
						content: `<div style='width:172px;height:70px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
												<div style="
															font-size: 14px;
															font-family: Source Han Sans-Bold, Source Han Sans;
															font-weight: 700;
															color: #FFFFFF;
															line-height: 46rpx;">
															家（${extData.remark}）
												</div>
												<div style="position:absolute;
															right:10px;
															top:10px; 
												" onclick="openSetting('${extData.id}:${extData.location}:${extData.remark}')">
													<img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
												</div>
												<div style="
															font-size: 12px;
															font-family: Source Han Sans-Regular, Source Han Sans;
															font-weight: 400;
															color: rgba(255,255,255,0.64);
															line-height: 50rpx;
															margin-top:5px;
														">
															最近一次(${extData.last_stay})
															<div>在这里睡了${extData.stay_num}晚</div>
												</div>
												<div style="width: 0;
															height: 0;
															border: 10px solid;
															border-color: #000 transparent transparent transparent;
															position:absolute;
															left:50%;
															bottom:-20px;
															transform:translateX(-50%);
															">
												</div>
													
											
											</div>`
					});
					myMap.add(this.house_modal)
				}
				const startLocation = [this.me.longitude, this.me
					.latitude
				]
				const endLocation = extData.coordinate.split(',')
				const result = await this.getDriver(startLocation,
					endLocation)
				if (result.info == 'OK') {
					const distance = result.routes[0].distance
					extData.distance = distance > 1000 ? ((distance / 1000)
						.toFixed(
							2) + ' 千米') : (distance + ' 米') || (0 +
						' 米')
					this.callMethod('open_storePopup', {
						...extData
					})
				}
			})
			AMap.plugin(["AMap.MarkerClusterer"], async () => {
				// 自定义聚合点样式
				const _renderClusterMarker = (context) => {
					const clusterData = context.clusterData; // 聚合中包含数据
					const index = context.index; // 聚合的条件
					const count = context.count; // 聚合中点的总数
					const marker = context.marker; // 聚合点标记对象
					const styleObj = {
						bgColor: 'rgba(0,0,0,.8)',
						borderColor: 'rgba(255,255,255,1)',
						// // text: clusterData[0][index['mainKey']],
						size: Math.round(Math.pow(count / (address_markers.length || 1),
							1 /
							5) * 70),
						color: '#ffffff',
						textAlign: 'center',
						boxShadow: '0px 0px 5px rgba(0,0,0,0.8)'
					}
					// 自定义点标记样式
					const div = document.createElement('div');
					div.innerHTML = count
					div.className = 'amap-cluster';
					div.style.width = 40 + 'px';
					div.style.height = 40 + 'px';
					div.style.lineHeight = styleObj.size + 'px';
					if (styleObj.index <= 2) {
						div.style.height = styleObj.size + 'px';
						// 自定义点击事件
						context.marker.on('click', function(e) {
							const curZoom = map.getZoom();
							if (curZoom < 20) {
								curZoom += 1;
							}
							map.setZoomAndCenter(curZoom, e.lnglat);
						});
					}
					div.style.backgroundImage =
						`url( ${config.ossBaseUrl}config/map/housePng.png)`;
					div.style.backgroundRepeat = 'no-repeat'
					div.style.backgroundSize = '100% 100%'
					div.style.color = styleObj.color;
					div.style.textAlign = styleObj.textAlign;
					context.marker.setContent(div)
					context.marker.setAnchor('center');
					context.marker.setzIndex(40);
				};
				this.cluster_address = new AMap.MarkerClusterer(
					myMap, address_markers, {
						gridSize: 1, // 聚合网格像素大小
						renderClusterMarker: _renderClusterMarker
					}
				);

			});
			this.houseMarkets = address_markers
		},
		/**
		 *  获取路径规划
		 */
		getDriver(startLngLat, endLngLat) {


			return new Promise((resolve, reject) => {
				this.mapDriving.search(startLngLat, endLngLat, function(status, result) {
					// 未出错时，result即是对应的路线规划方案
					if (status === 'complete') {


						resolve(result)
					} else {


						reject(result)
					}
				})
			})
		},
		/**
		 *  重置地图本人位置
		 * 	me 变动回调  
		 */
		receive_Me(newValue, oldValue, ownerVm, vm) {

			if (newValue == null) return
			// console.log(newValue, 'newValue');
			// 当前用户地图扎点
			if (this.overlay_me.getOverlays().length <= 0) {
				const avatarIcon = newIcon(AMap, {
					image: newValue.avatar,
					size: new AMap.Size(avatarW, avatarH),
					imageSize: new AMap.Size(avatarW, avatarH),
				})

				const onlineIcon = newIcon(AMap, {
					image: 'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/9ba8726c-dd1f-4ea0-91fb-465bb74e39b8.png',
					size: new AMap.Size(30, 15),
					imageSize: new AMap.Size(30, 15),
				})
				let dlImg = ''
				if (newValue.electricity > 80) {
					dlImg = dl100
				} else if (newValue.electricity > 55) {
					dlImg = dl75
				} else if (newValue.electricity > 30) {
					dlImg = dl50
				} else {
					dlImg = dl25
				}
				const Dlmarker = new AMap.Marker({
					position: [newValue.longitude, newValue.latitude],
					// 将 html 传给 content
					content: `<div style="color:#000;position:relative;">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);line-height:12px;text-align:center">${newValue.electricity}%</div></div>`,
					// 以 icon 的 [center bottom] 为原点
					offset: new AMap.Pixel(-8, -10),
					extData: {
						id: 'Dlmarker'
					}
				});
				const Fzmarker = new AMap.Marker({
					position: [newValue.longitude, newValue.latitude],
					// 将 html 传给 content

					content: `<div style="position:relative;">
						<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/a055781f-bc9b-46e9-b94b-5f7133a43385.png">
						</div>`,
					offset: new AMap.Pixel(25, -33),
					// 以 icon 的 [center bottom] 为原点
					extData: {
						id: 'Fzmarker'
					}
				});
				const Sdmarker = new AMap.Marker({
					position: [newValue.longitude, newValue.latitude],
					// 将 html 传给 content
					content: `<div style="position:relative;">
					<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" 
					src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
					
							<div style=" transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(0).toFixed(0)}
							<div>km/h</div>
							</div>
							</div>`,
					offset: new AMap.Pixel(-35, -45),
					// 以 icon 的 [center bottom] 为原点
					extData: {
						id: 'Sdmarker'
					}
				});
				const online = newMarker(AMap, {
					position: [newValue.longitude, newValue.latitude],
					offset: new AMap.Pixel(-10, -95),
					icon: onlineIcon,
					zIndex: 100,
					extData: {
						id: 'online'
					}
				})
				const avatar = newMarker(AMap, {
					position: [newValue.longitude, newValue.latitude],
					offset: new AMap.Pixel(-avatarW / 2, -avatarH),
					icon: avatarIcon,
					zIndex: 100,
					extData: {
						id: 'avatar'
					}
				})
				avatar.on('click', () => {
					this.showMeDesc = !this.showMeDesc
					this.overlay_me.eachOverlay((overlay, index) => {
						const extData = overlay.getExtData()
						const {
							id
						} = extData
						// console.log(1111);
						if (this.showMeDesc) {
							if (id !== 'avatar') {
								overlay.show()
							}
						} else {
							if (id !== 'avatar') {
								overlay.hide()
							}
						}

					})
				})

				this.overlay_me.addOverlay(avatar)
				this.overlay_me.addOverlay(Dlmarker)
				// this.overlay_me.addOverlay(Fzmarker)
				this.overlay_me.addOverlay(Sdmarker)
				// this.overlay_me.addOverlay(online)
				Dlmarker.hide()
				Fzmarker.hide()
				Sdmarker.hide()
				online.hide()
				myMap.setCenter([newValue.longitude, newValue.latitude])
			} else {
				const lastPosition = this.overlay_me.getOverlays()[0].getPosition()
				const endLnglat = new AMap.LngLat(this.me.longitude, this.me.latitude)
				const from = turf.point([lastPosition.lng, lastPosition.lat]);
				const to = turf.point([endLnglat.lng, endLnglat.lat]);
				const options = {
					units: 'kilometers'
				};
				const distance = turf.distance(from, to, options);
				const speed = distance * (3600 * 1000 / 2000)
				// this.meMarket.moveTo(endLnglat, speed)



				this.overlay_me.eachOverlay((overlay, index) => {
					const extData = overlay.getExtData()
					const {
						id
					} = extData
					if (id === 'avatar') {
						const iconlast = overlay.getIcon()
						console.log('iconlast.De.image!=newValue.avatar', iconlast.De.image == newValue.avatar,
							iconlast.De.image, newValue.avatar);
						if (iconlast.De.image != newValue.avatar) {
							const avatarIconNew = newIcon(AMap, {
								image: newValue.avatar,
								size: new AMap.Size(avatarW, avatarH),
								imageSize: new AMap.Size(avatarW, avatarH),
							})
							overlay.setIcon(avatarIconNew)
						}

					}
					if (id === 'Sdmarker') {
						overlay.setContent(
							`<div style="position:relative;">
							<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)"
							src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
							<div style="transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(speed).toFixed(0)}<div>km/h</div>
							</div>`
						)
					}
					if (id === 'Dlmarker') {
						let dlImg = ''
						if (newValue.electricity > 80) {
							dlImg = dl100
						} else if (newValue.electricity > 55) {
							dlImg = dl75
						} else if (newValue.electricity > 30) {
							dlImg = dl50
						} else {
							dlImg = dl25
						}
						overlay.setContent(
							`<div style="color:#000;position:relative;">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);fonst-size:12px;line-height:12px;text-align:center">${newValue.electricity}%</div></div>`
						)
					}
					// this.overlay_me.removeOverlay(overlay)
					overlay.moveTo(endLnglat, speed)
					// overlay.setPosition(new AMap.LngLat(newValue.longitude, newValue.latitude))
					// this.overlay_me.addOverlay(overlay)
				})
			}
			return
			const meIcon = newIcon(AMap, {
				image: this.showMeDesc ? newValue.map_user_mark : newValue.avatar,
				size: this.showMeDesc ? new AMap.Size(avataHoverrW, avataHoverrH) : new AMap.Size(avatarW,
					avatarH),
				imageSize: this.showMeDesc ? new AMap.Size(avatarHoverW, avatarHoverH) : new AMap.Size(avatarW,
					avatarH),
			})
			if (!this.meMarket) {
				this.meMarket = newMarker(AMap, {
					position: [newValue.longitude, newValue.latitude],
					offset: this.showMeDesc ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH) : new AMap.Pixel(
						-avatarW / 2, -avatarH),
					icon: meIcon,
					zIndex: 100
				})
				// this.meMarket.setSize(new AMap.Size(150, 63))
				this.meMarket.setMap(myMap)
				myMap.setCenter([newValue.longitude, newValue.latitude])
				this.meMarket.on('click', () => {
					this.triggerMeInfo()
				})
			} else {
				this.meMarket.setIcon(meIcon)
				const lastPosition = this.meMarket.getPosition()
				console.log(lastPosition, 'lastPosition');
				const endLnglat = new AMap.LngLat(this.me.longitude, this.me.latitude)
				const from = turf.point([lastPosition.lng, lastPosition.lat]);
				const to = turf.point([endLnglat.lng, endLnglat.lat]);
				const options = {
					units: 'kilometers'
				};
				const distance = turf.distance(from, to, options);
				const speed = distance * (3600 * 1000 / 2000)
				this.meMarket.moveTo(endLnglat, speed)
				// this.meMarket.setPosition()
			}

		},
		// 切换用户展示
		triggerMeInfo() {
			this.showMeDesc = !this.showMeDesc
			const icon = this.meMarket.getIcon()
			const setIcon = this.showMeDesc ? this.me.map_user_mark : this.me.avatar
			const changeIcon = newIcon(AMap, {
				image: setIcon,
				size: this.showMeDesc ? new AMap.Size(avatarHoverW, avatarHoverH) : new AMap.Size(avatarW,
					avatarH),
				imageSize: this.showMeDesc ? new AMap.Size(avatarHoverW, avatarHoverH) : new AMap.Size(avatarW,
					avatarH),
			})
			this.meMarket.setIcon(changeIcon)
			this.meMarket.setOffset(this.showMeDesc ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH) : new AMap.Pixel(
				-avatarW / 2, -avatarH))

		},
		/**
		 *  view 层 弹窗回调
		 * 	moon 变动回调  
		 */
		receive_toolEvent(newValue, oldValue, ownerVm, vm) {
			if (newValue === null) return
			const {
				act,
				params
			} = newValue
			switch (act) {
				case 'moonPopupClose':
					this.moon_modal && this.moon_modal.hide()
					break;

				case "housePopupClose":
					this.house_modal && this.house_modal.hide()
					break;
				case "move-view-center":
					if (params) {
						myMap && myMap.setCenter([params.lnogitude, params.latitude])
					}
					break;
				case "poiMapAddMarker":
					if (myMap) {
						this.poiMarket = newMarker(AMap, {
							position: [params.lnglat.lng, params.lnglat.lat],
							offset: new window.AMap.Pixel(-25, -63),
							zIndex: 100
						})
						// this.meMarket.setSize(new AMap.Size(150, 63))
						this.poiMarket.setMap(myMap)
						myMap.setCenter([params.lnglat.lng, params.lnglat.lat])
					}
					break;
				case "person_guanzhu":
					if (myMap) {
						const markers = this.cluster_person.getMarkers()
						markers.forEach(item => {
							if (params.uid == item.De.extData.uid) {

								item.setExtData({
									...params
								})
							}
						})
					}
					break;
				case "mapStyleChange":
					if (myMap) {
						myMap.setMapStyle(params);
					}
					break;
				case "person_GhostSwitch":
					if (myMap) {
						console.log('[[[[[params]]]]]', params);
						const markers = this.cluster_person.getMarkers()
						markers.forEach(item => {
							if (params.uid == item.De.extData.uid) {

								item.setExtData({
									...params
								})
							}
						})
					}
					break;
				default:
					break;
			}
		},
		/**
		 *  重置地图月亮
		 * 	moon 变动回调  
		 */
		receive_Moon(newValue, oldValue, ownerVm, vm) {
			if (newValue === null) return


			const markers = newValue.map(item => {
				const moonIcon = newIcon(AMap, {
					image: config.ossBaseUrl + 'config/map/moon.png',
					size: new AMap.Size(30, 30),
					imageSize: new AMap.Size(30, 30)
				})
				const moonMarket = newMarker(AMap, {
					position: [item.longitude, item.latitude],
					icon: moonIcon,
					extData: item,
					offset: new AMap.Pixel(-15, -30),
					zIndex: 30
				})

				moonMarket.on('click', () => {
					this.moon_modal && this.moon_modal.hide()
					myMap.setCenter([item.longitude, item.latitude])
					this.moon_modal = new AMap.Marker({
						position: new AMap.LngLat(item.longitude, item.latitude),
						offset: new AMap.Pixel(-71, -120), //设置文本标注偏移量
						zIndex: 200,
						content: `<div style='width:122px;height:50px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
									<div style="
												font-size: 12px;
												font-family: Source Han Sans-Bold, Source Han Sans;
												font-weight: 700;
												color: #FFFFFF;
												line-height: 46rpx;">
												留宿地
									</div>
									<div style="position:absolute;
												right:10px;
												top:10px; 
									"onclick="openSettingMoon('${item.id},${item.location},${item.remark}')">
										<img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
									</div>
									<div style="
												font-size: 10px;
												font-family: Source Han Sans-Regular, Source Han Sans;
												font-weight: 400;
												color: rgba(255,255,255,0.64);
												line-height: 50rpx;
												margin-top:5px;
											">
												你在那里待了${item.stay_num}晚
												<div>${this.timeFmt(item.last_day, 'yyyy年M月D号')}</div>
									</div>
									<div style="width: 0;
												height: 0;
												border: 10px solid;
												border-color: #000 transparent transparent transparent;
												position:absolute;
												left:50%;
												bottom:-20px;
												transform:translateX(-50%);
												">
									</div>
										
								
								</div>`
					});
					myMap.add(this.moon_modal)
					// 显示月亮弹窗
				})


				return moonMarket
			})
			this.overlay_moon && this.overlay_moon.clearOverlays()
			this.moon_modal && this.moon_modal.hide()
			this.overlay_moon && this.overlay_moon.addOverlays(markers)
		},
		timeFmt(date = '', fmt) {
			try {
				date = new Date(date || '')


				var a = ['日', '一', '二', '三', '四', '五', '六']
				var o = {
					'M+': date.getMonth() + 1, // 月份
					'D+': date.getDate(), // 日
					'h+': date.getHours(), // 小时
					'm+': date.getMinutes(), // 分
					's+': date.getSeconds(), // 秒
					'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
					'S': date.getMilliseconds(), // 毫秒
					'w': date.getDay(), // 周
					'W': a[date.getDay()], // 大写周
					'T': 'T'
				}
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
				}
				for (var k in o) {
					if (new RegExp('(' + k + ')').test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[
								k])
							.length)))
					}
				}
				return fmt
			} catch (err) {


			}
		}
	}
}