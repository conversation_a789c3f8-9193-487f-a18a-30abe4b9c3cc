<template>
	<!-- 地址搜索 -->
	<view class="sscontent">
		<image class="fanhuiicon" @click="goback" src="../../static/map/fh1.png"></image>
		<MapTool ref="mapTool" :location="location" @mapLocationMoveend="mapLocation" width="750rpx" />
		<image class="backCenter" src="@/static/map/backcenter.png" @click="backCenter"></image>
		<view class="conent">
			<view class="search">
				<uni-icons type="search" size="24"></uni-icons>
				<input class="uni-input" @input="serchAddress" placeholder="请输入地址" />
			</view>
			<scroll-view scroll-y="true" style="height: calc(40vh - 20rpx);" lower-threshold="300"
				@scrolltolower="load_more">
				<block v-for="(item,index) in address_data_lists" :key="index">
					<view class="item" @tap="clickAddress(item,index)">
						<image style="width: 42rpx ;height: 42rpx;margin-right: 20rpx;" src="../../static/map/dw1.png">
						</image>
						<view style="width: 90%;">
							<view style="font-size: 24rpx;color: #fff;font-weight: bold;margin-bottom: 10rpx;">
								{{item.name}}
							</view>
							<view class="text-overflow" style="font-size: 20rpx;color: #ddd;">
								{{item.pname+item.cityname+item.adname+item.address}}
							</view>
						</view>
						<image style="width: 58rpx;height: 58rpx;" v-if=" current == index"
							src="../../static/map/dwc.png"></image>
					</view>
				</block>
			</scroll-view>
			<view class="btns" @click="send">确定</view>
			<u-toast ref='notify' />
		</view>
	</view>
</template>

<script>
	import MapTool from "@/components/map/index.vue";
	import {
		back
	} from '@/utils/common.js'
	export default {
		components: {
			MapTool
		},
		data() {
			return {
				value: "",
				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: uni.getStorageSync('userLocation').split('市')[1],
					city: uni.getStorageSync('userLocation').split('市')[0].split('省')[1],
					region: "",
					output: "JSON",
					city_limit: true,
					sortrule: "distance"
				},
				address_data_lists: [],
				address: {},
				scr_height: 0,
				total: 0,
				location: [128.961009, 39.991443],
				current: null
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: res => {
					this.scr_height = res.windowHeight - uni.upx2px(80 + 20 + 20);
				}
			});
		},
		mounted() {
			this.backCenter()
		},
		methods: {
			close() {
				this.$refs.popup.close();
			},
			goback() {
				console.log(back);
				uni.navigateBack()
			},
			backCenter() {
				this.location = []
				this.$http.get('/api/user/info').then(async res => {
					this.MapSearchForm = {
						...this.MapSearchForm,
						keywords: res.message.geo_info
							.formatted_address.split('市')[1],
						city: res.message.geo_info
							.formatted_address.split('市')[0].split('省')[1],
					}
					await this.getAdress(() => {
						setTimeout(() => {
							this.clickAddress(this.address_data_lists[0], 0)
						}, 0)
					});

				})
			},
			send() {
				this.$store.commit('SET_HB_LOCATION', this.address)
				uni.navigateBack()
			},
			open() {
				this.$refs.popup.open();
				this.getAdress();
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			async mapLocation(option) {
				// console.log(option, 'option');
				this.MapSearchForm.keywords = option.address
				this.MapSearchForm.city = option.city
				this.current = null
				await this.getAdress();
				// console.log(this.address_data_lists, 'address_data_lists');
				this.goReapAddress(option)
			},
			clickAddress(address, index) {
				this.$refs.mapTool.clear()
				this.location = address.location.split(",");
				this.goReapAddress(address, index)
			},
			goReapAddress(address, index) {
				this.current = index;
				this.address = address;
				this.address.locationName = address.pname + address.cityname + address.adname + address.address
			},
			/// 获取地址根据关键字
			async getAdress(fn) {
				await uni.request({
					method: "GET",
					url: "https://restapi.amap.com/v5/place/text",
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: res => {
						if (res.statusCode == 200) {
							console.log(res);
							this.total = res.data.count;
							if (this.MapSearchForm.page_num == 1) {
								this.address_data_lists = res.data.pois;
								fn && fn()
								return
							}
							this.address_data_lists = [
								...this.address_data_lists,
								...res.data.pois
							];
							fn && fn()
							// this.goReapAddress(this.address_data_lists[0], 0);
						}

					},
					fail: (err) => {
						this.toast(err.errMsg);
					}
				});
			},
			serchAddress(e) {
				this.current = null
				this.MapSearchForm.keywords = e.detail.value;
				this.MapSearchForm.page_num = 1;
				this.address_data_lists = [];
				this.getAdress();
			},
			//下滑加载更多
			load_more() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
				}
				this.getAdress();
			}
		}
	};
</script>

<style lang="less" scoped>
	.sscontent {
		width: 100%;
		height: 100vh;
		background-color: #fff;
		border-radius: 20rpx;
		box-sizing: border-box;
		position: relative;

		.backCenter {
			width: 74rpx;
			height: 74rpx;
			position: absolute;
			right: 32rpx;
			top: 549rpx;
		}

		.fanhuiicon {
			width: 24rpx;
			height: 48rpx;
			position: absolute;
			left: 32rpx;
			top: 100rpx;
			z-index: 2;
		}

		.head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-weight: bold;

			.gb {
				color: #333;
				font-size: 24rpx;
			}

			.fs {
				color: #71d2cf;
			}
		}

		.conent {
			background-color: #191c26;
			border-radius: 20rpx;
			padding-top: 20rpx;
			height: calc(100vh - 500rpx);
			box-sizing: border-box;
			position: relative;

			.search {
				margin: 20rpx;
				padding-left: 10rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				background-color: #22252f;
				overflow: hidden;
				height: 80rpx;

				.uni-input {
					padding-left: 10rpx;
				}
			}

			.item {
				padding: 20rpx 30rpx 10px 30rpx;
				color: #fff;
				display: flex;
				align-items: center;

			}

			.btns {
				width: 686rpx;
				height: 94rpx;
				text-align: center;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 94rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 140rpx 140rpx 140rpx 140rpx;
				margin: auto;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
			}
		}


	}
</style>