<template>
	<view class="">
		<uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="item t_display" style="margin-top: 18rpx;" @click="top">
					<image class="disable" src="../../../static/images/my/zhiding.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">{{isUp?'取消置顶':'置顶'}}</view>
						<!-- <view style="font-size: 24rpx;">设置这条动态的可见范围</view> -->
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" style="margin-top: 38rpx;" @click="edit">
					<image class="disable" src="../../../static/images/my/edit.png" mode=""></image>
					<view class="rightInfo">
						编辑动态
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" style="margin-top: 38rpx;" @click="choose(5)">
					<image class="disable" src="../../../static/images/see1.png" mode=""></image>
					<view class="rightInfo">
						所有人可见
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" style="margin-top: 38rpx;" @click="choose(1)">
					<image class="disable" src="../../../static/images/see2.png" mode=""></image>
					<view class="rightInfo">
						好友可见
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display" style="margin-top: 38rpx;" @click="del">
					<image class="disable" src="../../../static/images/shanchu.png" mode=""></image>
					<view class="rightInfo">
						删除动态
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="close">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			isUp: {
				type: Boolean,
				default: false,
			}
		},
		components: {

		},
		data() {
			return {

			}
		},
		methods: {
			edit() {
				this.$emit('edit')
			},
			top() {
				this.$emit('top', this.isUp)
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			del() {
				this.$emit('del')
			},
			first() {
				this.$emit('first')
			},
			choose(ids) {
				this.$emit('choose', ids)
			},
			close() {
				this.$refs.popup.close()
			},
			open() {
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		padding-top: 30rpx;
		// height: 304rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;


		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			border-top-left-radius: 40rpx;
			// margin-top: 27rpx;
			padding-bottom: 38rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}
</style>