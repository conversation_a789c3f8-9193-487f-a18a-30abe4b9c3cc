let myMap;
import {
	config
} from '@/config.js'
import {
	newMarker,
	getGeocoder
} from '@/utils/MapTools.js'
var object3Dlayer = null;
var gltfLoader = null;
var zoomScale, zoomHeight,url;
var zooms = [5,17];
var markers = [];
const {
	avatarW,
	avatarH,
	avatarHoverW,
	avatarHoverH,
	packetW,
	packetH
} = config
export default {
	data() {
		return {
		}
	},
	mounted() {
		if (typeof window.AMap === 'function') {
			this.initMap()

		} else {
			window._AMapSecurityConfig = {
				securityJsCode: '********************************',
			}
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
			script.onload = this.initMap.bind(this)
			document.head.appendChild(script)
			
			// const script1 = document.createElement('script')
			// script1.src = "https://a.amap.com/jsapi_demos/static/gltf/duck/three.js";
			// script1.onload = this.initMap.bind(this)
			// document.head.appendChild(script1)
			
			// const script2 = document.createElement('script')
			// script2.src = "https://a.amap.com/jsapi_demos/static/gltf/duck/GLTFLoader.js";
			// script2.onload = this.initMap.bind(this)
			// document.head.appendChild(script2)
		}

	},
	onUnload() {},
	methods: {
		initMap() {
			console.log("初始化")
			// console.log("center测试测试",this.center)
			// console.log("地址",this.modelUrl)
			console.log("zoom",this.zoom)
			// myMap = new AMap.Map('AmapRender', {
			// 	// center:[
			// 	//     116.2683411,
			// 	//     39.9062119
			// 	// ],
			// 	center:this.center,
			// 	zoom: 15,
			// 	zooms: [5, 20],
			// 	viewMode: '3D',
			// 	resizeEnable: true,
			// 	draggable: true,
			// 	zoomEnable: true,
			// 	pitch: 65,
			// 	rotation: 25,
			// });
			myMap = new AMap.Map('AmapRender', {
				pitch: 50, //地图俯仰角度，有效范围 0 度- 83 度
				viewMode: '3D', //地图模式
				rotateEnable: true, //是否开启地图旋转交互 鼠标右键 + 鼠标画圈移动 或 键盘Ctrl + 鼠标左键画圈移动
				pitchEnable: true, //是否开启地图倾斜交互 鼠标右键 + 鼠标上下移动或键盘Ctrl + 鼠标左键上下移动
				// zoom: 17, //初始化地图层级
				// rotation: -15, //初始地图顺时针旋转的角度
				// center: [116.333926, 39.997245] //初始地图中心经纬度
				center: this.center,
				zoom: this.zoom,
				// zooms: [3, 20], //地图显示的缩放级别范围
				// zooms: [5, 17],
				zooms:zooms,
				// features: ['bg', 'road', 'building'],
				resizeEnable: true,
				mapStyle: 'amap://styles/957040a4d7e11f8e53f93977962d6779', //设置地图的显示样式
				draggable: true,
				// zoomEnable: true,
				// pitch: 65,
				// rotation: 25,
			});
			// 地图加载完成！
			myMap.on("complete", async () => {
				myMap.setCenter(this.center)
				// myMap.setCenter([
				//     116.2683411,
				//     39.9062119
				// ])
				// AMap.plugin(['AMap.ControlBar', 'AMap.ToolBar'], function () { //异步加载插件
				// //   var controlBar = new AMap.ControlBar({ //控制地图旋转插件
				// //       position: {
				// //           right: '10px',
				// //           top: '10px'
				// //       }
				// //   });
				// //   myMap.addControl(controlBar);
				//   var toolBar = new AMap.ToolBar({ //地图缩放插件
				//       position: {
				//           right: '40px',
				//           top: '110px'
				//       }
				//   });
				//   myMap.addControl(toolBar)
				// })
				const icon_per = new AMap.Icon({
				        image:"http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png",
				        size: new AMap.Size(100, 100), // 设置图标大小，例如 50x50 像素
				        imageSize: new AMap.Size(100, 100) // 设置图标实际大小，例如 50x50 像素
				    });
				// 添加自定义图片标点
				    var marker = new AMap.Marker({
				      position: this.center, // 标点的经纬度位置
				      icon: icon_per, // 自定义图片的URL
				      // offset: new AMap.Pixel(-100, -50), // 标点的偏移量
					  offset: new AMap.Pixel(-50, -50), // 调整偏移量
				      zIndex: 101, // 标点的层级
					  extData: { id: 16 } // 存储额外的数据
				    });
					console.log(marker)
				    myMap.add(marker);
					markers.push(marker);
				
			});
			myMap.on('click', async (ev) => {
				myMap && myMap.setCenter([ev.lnglat.lng, ev.lnglat.lat])
				const res = await getGeocoder(AMap, {
					lnglat: [ev.lnglat.lng, ev.lnglat.lat]
				})

				this.callMethod('mapLocation', {
					city: res.regeocode.addressComponent.city || res.regeocode.addressComponent
						.province,
					address: res.regeocode.formattedAddress,
				})
			}, this);
			myMap.on('zoomend', (ev) => {
				const zoom = myMap.getZoom()
				console.log("地图比例",zoom);

				
				
				
				markers.forEach(marker => {
					 const markerData = marker.getExtData();
					    // 检查标点是否在当前地图视图的范围内
					    console.log("地图标点id", markerData.id);
					if (zoom>=markerData.id) {
					  // 如果在范围内，则显示标点
					  marker.show();
					} else {
					  // 如果不在范围内，则隐藏标点
					  marker.hide();
					}
				  });
				
			}, this);
			
		},
		receiveClear(newValue, oldValue, ownerVm, vm) {
			myMap && myMap.clearMap()
		},
		
		async receiveEvent(newValue, oldValue, ownerVm, vm) {
			const {
				key,
				params,
				interval
			} = newValue
			console.log(newValue, 'receiveEvent===事件透传');
			// 设置zoom
			if (myMap) {
			    myMap.setZoom(params);
				// 移除地图上现有的所有标点
				 myMap.clearMap();
				 markers = []
			}
			
			if (myMap) {
			    // 定义不同params对应的标点图标URL
			    // const markerIcons = {
			    //   16: 'http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png',
			    //   13: 'http://img.lluuxiu.com/photo/20240921/c4494ba7-8f9f-427d-bfb3-64b8535241ae.png',
			    //   10: 'http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png',
			    //   6: 'http://img.lluuxiu.com/photo/20240921/c4494ba7-8f9f-427d-bfb3-64b8535241ae.png',
			    // };
				const markerIcons = {
				  16: 'http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png',
				  13: 'http://img.lluuxiu.com/photo/20241205/b69c5d91-d2f8-4dc4-86a8-99d58f465fd8.png',
				  10: 'http://img.lluuxiu.com/photo/20241205/9b2c6359-4713-4662-91fd-15e73bcd81a1.png',
				  6: 'http://img.lluuxiu.com/photo/20241205/b19ad54a-ec0c-462c-b0d5-8ebd8a686d0e.png',
				};
			
			    // 获取对应params的标点图标URL
			    const markerIcon = markerIcons[params] || 'http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png'; // 默认图标
				console.log(markerIcon)					
				const icon_per = new AMap.Icon({
				        image:markerIcon,
				        size: new AMap.Size(100, 100), // 设置图标大小，例如 50x50 像素
				        imageSize: new AMap.Size(100, 100) // 设置图标实际大小，例如 50x50 像素
				    });
				// 检查是否已存在具有相同id的标点
				const existingMarker = markers.find(marker => marker.getExtData().id === params);
				console.log("已经有标点",existingMarker)
				// if (existingMarker) {
				//   // 如果标点已存在，则显示它
				//   existingMarker.show();
				// } else {
				  // 如果标点不存在，则创建新的标点
				  var marker = new AMap.Marker({
					position: this.center, // 使用 this.center 的值
					icon: icon_per, // 设置标点图标
					offset: new AMap.Pixel(-50, -50), // 标点偏移量
					zIndex: 101, // 标点层级
					extData: { id: params } // 存储额外的数据
				  });
				  
				  // 将标点添加到地图上
				  myMap.add(marker);
				  markers.push(marker); // 将新标点添加到markers数组中
				// }
			//     // 创建新的标点
			//     var marker = new AMap.Marker({
			//       position: this.center, // 使用 this.center 的值
			//       icon: markerIcon, // 设置标点图标
			//       offset: new AMap.Pixel(-10, -10), // 标点偏移量
			//       zIndex: 101, // 标点层级
			// 	  extData: { id: params } // 存储额外的数据
			//     });
			
			//     // 将标点添加到地图上
			//     myMap.add(marker);
			// 	markers.push(marker);
			  }
		},

		// 调用 view 层的方法
		callMethod(act = '', params, cb) {
			this.$ownerInstance.callMethod('callApp', {
				act,
				option: params,
				cb: cb
			})
		},

	}
}