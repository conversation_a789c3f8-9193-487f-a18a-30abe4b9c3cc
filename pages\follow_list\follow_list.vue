<template>
	<!-- 话题页面 -->
	<view>
		<u-navbar back-text="" title="发布" :custom-back="customBack" :background="{backgroundColor: '#191C26'}"
			:border-bottom="false" title-color="#fff" back-icon-color="#fff">
			<view slot="content" style="width: 340rpx;">
				<v-tabs v-model="current" activeColor="#fff" color="rgba(255,255,255,0.72)" bgColor="#191C26"
					:scroll="false" lineColor="#fff" lineHeight="7rpx" :tabs="['关注', '粉丝', '朋友']"
					@change="setCurrent"></v-tabs>
			</view>
		</u-navbar>
		<List :current="current" :page="page" />
		<!-- <t-loading text="加载中.." :mask="true" :click="true" :show="show" ref="loading"></t-loading> -->
		<sharePopup ref="share" :post="shareItem" :securityBottom="0"></sharePopup>
		<uni-popup ref="popup" type="bottom" background-color="#fff" :safe-area="false">
			<view class="cPopup">
				<view class="t_display">
					<image class="avatar" :src="popupInfo.user_info.avatar" mode="aspectFill"></image>
					<view class="name">{{popupInfo.user_info.nickname}}</view>
				</view>
				<view class="item t_display" @click="cancelBlack(popupInfo.momentId,popupInfo.index)"
					style="margin: 20rpx;margin-top: 40rpx;">
					<image class="disable" src="../../static/images/shanchu.png" mode=""></image>
					<view class="rightInfo">
						删除
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="cancel">
					取消
				</view>
				<view class="" style="height: 20rpx;">
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import Post from "@/components/post/post.vue"
	import List from "./components/list.vue"
	export default {
		components: {
			Post,
			List
		},

		data() {
			return {
				activeTab: 0,
				triggeredS: false,
				triggeredB: false,
				recommendTotal: true,
				show: true,
				popupInfo: {
					user_info: {
						avatar: "",
						nickname: ""
					}
				},
				list: [{
					name: '推荐'
				}, {
					name: '关注'
				}],
				dataArrS: [],
				dataArrB: [],
				page: 1,
				current: 0,
				followTotal: true,
				shareItem: {},
				tab2: {
					momentId: 0,
					option: 2,
				}

			}
		},
		onReachBottom() {
			this.page++
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			share(item) {
				this.shareItem = item
				this.$refs.share.open()
			},
			cancel() {
				this.$refs.popup.close()
			},
			goMore(item) {
				console.log(item);
				this.popupInfo = item
				this.$refs.popup.open()
			},
			customBack() {
				uni.navigateBack()
			},
			change(index) {
				this.current = index;
			},
			setCurrent() {
				this.page = 1
			},
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.swiperC {}
</style>