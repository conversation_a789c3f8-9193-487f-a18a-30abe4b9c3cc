 <template>
 	<view class="container">
 		<u-navbar :customBack="goBack" back-text="" title="发布" :background="{backgroundColor: '#191C26'}"
 			:border-bottom="false" title-color="#fff" back-icon-color="#fff">
 			<view slot="content" style="width: 240rpx;">
 				<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" bg-color="#191C26"
 					active-color="#fff" inactive-color="rgba(255,255,255,0.72)"></u-tabs>
 			</view>
 			<view class="navbar-right" slot="right">
 				<!-- <uni-icons type="search" color="#fff" size="24" @click="goSearch"> </uni-icons> -->
 				<uni-icons type="plus" color="#fff" size="24" style="margin:0 20rpx;"
 					@click="popupFlag=!popupFlag"></uni-icons>
 			</view>
 		</u-navbar>

 		<view class="swiperC" circular :current="current" @change="setCurrent">
 			<view v-if="current == 0">
 				<tianxie-chat-list @goNav="goNav" :topItemArr="itemArr" :noticeCntInfo="noticeCntInfo"
 					:pageUrl="pageUrl" :userList="userList" @bindClick="bindClick" @scrollBnt="scrollBnt"
 					@goChatPage="goChatPage"></tianxie-chat-list>
 			</view>
 			<view v-if="current == 1">
 				<Firend @send="send"></Firend>
 			</view>
 		</view>
 		<chunLei-popups v-model="popupFlag" :popData="popupData" @tapPopup="tapPopup" :value="true" :x="344" :y="70"
 			placement="top-end">
 		</chunLei-popups>
 		<u-toast ref='notify' />
 	</view>
 </template>

 <script>
 	import chunLeiPopups from "@/components/chunLei-popups/chunLei-popups.vue";
 	import {
 		data
 	} from "browserslist";
 	import {
 		apiNotifyCnt,
 		apiUserBlackList
 	} from '@/api/common.js'
 	import Firend from "./firend.vue"
 	export default {
 		components: {
 			chunLeiPopups,
 			Firend
 		},
 		data() {
 			return {
 				current: 0,
 				popupData: [
 					// {
 					// 		title: '创建群聊',
 					// 		icon: '../../static/images/addGroup.png',
 					// 		// disabled: true
 					// 	},
 					{
 						title: '添加好友',
 						icon: '../../static/images/addFirend.png'
 					},
 				],
 				popupFlag: false,
 				list: [{
 					name: '消息'
 				}, {
 					name: '好友'
 				}],
 				itemArr: [{
 						title: "新朋友通知",
 						avatar: "../../static/images/newFirend.png",
 						note: "收到0个新的好友申请",
 						path: ""
 					},
 					{
 						title: "收到的点赞",
 						avatar: "../../static/images/topTop.png",
 						note: "收到0个点赞",
 						path: ""
 					},
 					{
 						title: "收到的评论",
 						avatar: "../../static/images/pinglun.png",
 						note: "收到0条评论",
 						path: ""
 					},
 					// {
 					// 	title: "@消息",
 					// 	avatar: "../../static/images/messageAT.png",
 					// 	note: "收到多个消息",
 					// 	path: ""
 					// },
 				],
 				limit: 100,
 				account: uni.getStorageSync('im').account,
 				//pageUrl跳转到聊天室路径
 				pageUrl: "/pages/HM-chat/HM-chat",

 				Yxim_sessions: this.$store.state.Yxim_info.sessions,
 				noticeCntInfo: {
 					at: "0",
 					comment: "0",
 					friendApply: "0",
 					like: "0",
 				},
 				blackList: []
 			}
 		},
 		computed: {
 			//新增加的聊天人员列表，请求完数据后需清空，等待下次数据请求在赋值
 			userList() {
 				const data = this.$store.state.Yxim_info.allsessions.map(item => {
 					return item
 				})
 				console.log('........聊天消息列表...........', data);
 				return data
 			}
 		},
 		onLoad() {
 			this.$store.dispatch('GET_ALL_SESSIONS')
 			this.getUserBlackList()
 		},
 		onShow() {
 			plus.runtime.setBadgeNumber(0)
 			plus.push.clear();

 			this.getData()
 		},
 		methods: {
 			toast(title) {
 				this.$refs.notify.show({
 					title,
 					position: "top"
 				})
 			},
 			getUserBlackList() {
 				apiUserBlackList().then(res => {
 					if (res.code == 200) {
 						this.blackList = res.message
 					}
 				})
 			},
 			goChatPage(item) {
 				if (this.blackList.includes(item.uuid)) {
 					this.toast('黑名单用户');
 					return false
 				}
				if (item.type === 'team') {
					this.navigateTo({
						url: "/pages/HM-chat/HM-chatNew" + "?userItem=" + encodeURIComponent(JSON.stringify(item)),
					})
				} else {
					this.navigateTo({
						url: this.pageUrl + "?userItem=" + encodeURIComponent(JSON.stringify(item)),
					})
				}
 		
 			},
 			async getData() {
 				const res = await apiNotifyCnt()
 				if (res.code == 200) {
 					this.noticeCntInfo = res.message
 					this.itemArr[0].num = this.noticeCntInfo.friendApply
 					this.itemArr[0].note = `收到${this.noticeCntInfo.friendApply}个新的好友申请`
 					this.itemArr[1].num = this.noticeCntInfo.like
 					this.itemArr[1].note = `收到${this.noticeCntInfo.like}个点赞`
 					this.itemArr[2].num = this.noticeCntInfo.comment
 					this.itemArr[2].note = `收到${this.noticeCntInfo.comment}条评论`
 					// this.itemArr[3].num = this.noticeCntInfo.at

 				}
 			},
 			goSearch() {
 				this.navigateTo({
 					url: '/pages/chat-list/search'
 				})
 			},
 			bindClick(option) {
 				this.$store.state._Yxim.cloudSession.deleteCloudSessionList({
 					sessionIdList: [option.item.uid]
 				})
 				this.$store.state.Yxim_info.allsessions.splice(option.index, 1)
 			},
 			test() {

 			},
 			getAllSessions() {

 			},
 			goNav(ids) {
 				console.log(123);
 				const path = ["/pages/newFirendMsg/newFirendMsg", "/pages/giveMessage/giveMessage",
 					"/pages/comment/comment", "/pages/at/at"
 				]
 				this.navigateTo({
 					url: path[ids]
 				})
 			},
 			send(item) {
 				let params = {
 					account: item.im_id,
 					chatHeadImg: item.avatar,
 					chatName: item.nickname,
 					uid: 'p2p-' + item.im_id,
 					roomtype: 'p2p',
 					nuck: item.nickname
 				}
 				this.navigateTo({
 					url: `/pages/HM-chat/HM-chat?userItem=${encodeURIComponent(JSON.stringify(params))}`
 				})
 			},
 			tapPopup(val) {
 				if (val.title == "添加好友") {
 					this.navigateTo({
 						url: "/pages/addFirends/addFirends"
 					})
 				} else if (val.title == "创建群聊") {
 					this.navigateTo({
 						url: "/pages/creatGroup/creatGroup"
 					})
 				}
 			},
 			setCurrent(ids) {
 				this.current = ids.detail.current
 			},
 			goBack() {
 				uni.navigateBack()
 			},
 			change(index) {
 				this.current = index;
 			},
 			//列表滑动到底部
 			scrollBnt(val) {
 				if (val && !this.$store.state.Yxim_info.noMore) {
 					console.log('this.userList[this.userList.length - 1].updateTime', this.userList[this.userList.length -
 						1].updateTime);
 					// 继续加载
 					this.$store.dispatch('GET_ALL_SESSIONS', this.userList[this.userList.length - 1].updateTime)
 					// uni.showToast({
 					// 	title: "到底了!",
 					// 	icon: 'none',
 					// 	duration: 3000,
 					// })
 					// console.log("到底部了!");
 				}
 			},
 		}
 	}
 </script>
 <style lang="scss" scoped>
 	.container {
 		.swiperC {
 			height: 100vh;
 		}
 	}
 </style>