/**
 * 当前通知权限开启查询
 * @returns {Boolean} true：已开启   false：未开启
 */
export const isNotificationsEnabled = () => {
	// #ifdef APP-PLUS
	if (plus.os.name == 'Android') {
		// 判断是Android
		var main = plus.android.runtimeMainActivity()
		var NotificationManagerCompat = plus.android.importClass(
			'android.support.v4.app.NotificationManagerCompat'
		)
		if (NotificationManagerCompat == null) {
			NotificationManagerCompat = plus.android.importClass(
				'androidx.core.app.NotificationManagerCompat'
			)
		}

		// 返回是否有权限
		return NotificationManagerCompat.from(main).areNotificationsEnabled()
	} else if (plus.os.name == 'iOS') {
		// 判断是iOS
		var isOn = undefined
		var types = 0
		var app = plus.ios.invoke('UIApplication', 'sharedApplication')
		var settings = plus.ios.invoke(app, 'currentUserNotificationSettings')
		if (settings) {
			types = settings.plusGetAttribute('types')
			plus.ios.deleteObject(settings)
		} else {
			types = plus.ios.invoke(app, 'enabledRemoteNotificationTypes')
		}
		plus.ios.deleteObject(app)
		isOn = 0 != types
		// 返回是否有权限
		return isOn
	}
	// #endif
}

/**
 * 前往系统设置的功能
 * @param {Boolean} type  true：开启		false：关闭
 * @returns
 */
export const permissions = (type) => {
	// #ifdef APP-PLUS
	if (plus.os.name == 'Android') {
		// 判断是Android
		var main = plus.android.runtimeMainActivity()
		var pkName = main.getPackageName()
		var uid = main.getApplicationInfo().plusGetAttribute('uid')

		uni.showModal({
			title: `通知权限${type ? '开启' : '关闭'}提醒`,
			content: `是否前往设置${type ? '开启' : '关闭'}通知权限？`,
			success: res => {
				if (res.confirm) {
					var Intent = plus.android.importClass('android.content.Intent')
					var Build = plus.android.importClass('android.os.Build')
					//android 8.0引导
					if (Build.VERSION.SDK_INT >= 26) {
						var intent = new Intent(
							'android.settings.APP_NOTIFICATION_SETTINGS'
						)
						intent.putExtra('android.provider.extra.APP_PACKAGE', pkName)
					} else if (Build.VERSION.SDK_INT >= 21) {
						//android 5.0-7.0
						var intent = new Intent(
							'android.settings.APP_NOTIFICATION_SETTINGS'
						)
						intent.putExtra('app_package', pkName)
						intent.putExtra('app_uid', uid)
					} else {
						//(<21)其他--跳转到该应用管理的详情页
						intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
						var uri = Uri.fromParts(
							'package',
							mainActivity.getPackageName(),
							null
						)
						intent.setData(uri)
					}
					// 跳转到该应用的系统通知设置页
					main.startActivity(intent)
				} else if (res.cancel) {
					console.log('点击了取消')
				}
			}
		})
	} else if (plus.os.name == 'iOS') {
		// 判断是iOS
		// var app = plus.ios.invoke('UIApplication', 'sharedApplication')
		// plus.ios.deleteObject(app)

		// uni.showModal({
		// 	title: `通知权限${type ? '开启' : '关闭'}提醒`,
		// 	content: `是否前往设置${type ? '开启' : '关闭'}通知权限？`,
		// 	success: res => {
		// 		if (res.confirm) {
		// 			plus.runtime.openURL('app-settings:');
		// 			// var app = plus.ios.invoke('UIApplication', 'sharedApplication')
		// 			// var setting = plus.ios.invoke(
		// 			// 	'NSURL',
		// 			// 	'URLWithString:',
		// 			// 	'app-settings:'
		// 			// )
		// 			// plus.ios.invoke(app, 'openURL:', setting)
		// 			// plus.ios.deleteObject(setting)
		// 			// plus.ios.deleteObject(app)
		// 		} else if (res.cancel) {
		// 			console.log('点击了取消')
		// 		}
		// 	}
		// })
	}
	// #endif
}