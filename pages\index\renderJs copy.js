import { config } from "@/config.js";
import ren_juhe from "../../static/map/ren_juhe.png";
import * as turf from "@turf/turf";
const dl25 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/cfbdf532-7e6b-4e0b-b83b-17848cef6c8c.png";
const dl50 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0d6bb079-bb1c-4689-ab81-655c772895f3.png";
const dl75 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/3d82a020-3a80-48ba-b5d1-a6539d9884f8.png";
const dl100 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0dcdc02a-dac1-471e-8bf8-676b75f389e2.png";
const yellow_packet =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240202/2b05f6ef-3f28-4c5f-90b9-45943a3b17e8.png";
const red_packet =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240202/75ff7e37-0837-4196-a0ce-de1b050184e4.png";
const msg_icon =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/109501716954269_.pic.jpg";
const { avatarW, avatarH, avatarHoverW, avatarHoverH, packetW, packetH } =
  config;
let myMap;
let layer;
let map_person;
let map_merchantstore;
let avatar = null; // 添加全局变量avatar
import {
  newMarker,
  newIcon,
  newText,
  newPerSon,
  getGeocoder,
  newLabelMarkers,
} from "@/utils/MapTools.js";
import {
  apiLocationReport,
  apiLocationVisibleGet,
  apiLocationMe,
  apiAddressSet,
  apiAddressGet,
  apiLocationSearchPeople,
  apiGetNight,
  apiToken,
} from "@/api/common.js";

export default {
  data() {
    return {
      timer: null,
      map: null,
      amap: null,
      autoComplete: null,
      citySearch: null,
      center: null,
      userView: {},
      location: {},
      showTime: false,
      userCenterFlag: 1,
      people: [],
      house: [],
      moon: [],
      msgArr: [],
      person: [],
      event: null,
      me: null,
      maxDist: 500,
      marker_me: null,
      marker_house: null,
      marker_person: null,
      marker_moon: null,
      marker_msg: null,
      marker_redPacket: null,
      marker_merchantStore: null,
      overlay_person: null,
      overlay_house: null,
      overlay_moon: null,
      overlay_msg: null,
      overlay_redPacket: null,
      overlay_merchant: null,
      pulseMarkers: {},
      contentHTML: {},
      personHash: {},
      personFollowHash: {},
      personGhostHash: {},
      houseHash: {},
      userInfo: {},
      followSuccess: false,
      followName: "",
      lastTime: new Date().getTime(),
      district: "",
      temperature: "",
      weatherType: "",
      weaImage: "",
      weatherVisible: false,
      houseMarkers: [],
      personMarkers: [],
      mMsgMarkers: [],
      redPacketMarkers: [],
      storeMarkers: [],
      isDevMode: false,
      lastClusterZoom: null,
      showDetailUid: null,
      _3dgltfMeshes: [],
      rippleIntervals: [],
      rippleCircles: [],
      zoomLevel: 16,
      avatarPulseAnimation: null, // 头像脉动动画定时器ID
      // 水波纹效果的默认参数
      rippleEffectParams: {
        maxRadius: 60, // 最大半径
        initialRadius: 20, // 初始半径
        initialOpacity: 0.9, // 初始不透明度
        animationDuration: 1000, // 动画持续时间(毫秒)
        rippleCount: 2, // 水波纹层数
        strokeWeight: 2, // 描边宽度（从0修改为2，添加边框）
        fillOpacity: 0.8, // 填充不透明度
        yOffset: -7, // 水波纹垂直方向的偏移量（负值表示向上）
        xOffset: 2, // 水波纹水平方向的偏移量（正值表示向右）
        gradientColors: ["#4BC6ED", "#BC93F2"], // 渐变色
        rotationAngle: 0, // 水波纹旋转角度(度数)
        shape: "square", // 水波纹形状：'square'或'circle'
        animationType: "fade", // 动画类型：'fade'或'pulse'
        zIndex: 50, // 水波纹的层级
        strokeColor: "rgba(255, 255, 255, 0.8)", // 添加白色描边
        boxShadow: "0 0 8px rgba(255, 255, 255, 0.6)", // 添加白色阴影
      },
      // 不同场景的水波纹颜色
      rippleColors: {
        self: "rgba(51, 153, 255, 0.8)", // 自己的位置
        person: "rgba(255, 107, 107, 0.9)", // 点击其他用户
        reset: "#1989fa", // 默认颜色
      },
    };
  },
  mounted() {
    /**
     * 	初始化高德Api
     */
    if (typeof window.AMap === "function") {
      this.initMap();
    } else {
      window._AMapSecurityConfig = {
        securityJsCode: "********************************",
      };
      /**
       * 	打开房子弹窗
       */
      window.openSetting = (e, a, b) => {
        const arr = e.split(",");
        this.callMethod("open_housePopup", {
          id: arr[0],
          location: arr[1],
          remark: arr[2],
        });
      };

      /**
       * 	打开月亮弹窗
       */
      window.openSettingMoon = (e, a, b) => {
        const arr = e.split(",");
        this.callMethod("open_moonPopup", {
          id: arr[0],
          location: arr[1],
          remark: arr[2],
        });
      };
      // 动态引入较大类库避免影响页面展示
      const script = document.createElement("script");
      script.src =
        "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
      // script.src = "https://webapi.amap.com/maps?v=2.0&key=26ab172d25bd6002eb28192f569071a3";
      script.onload = this.initMap.bind(this);
      document.head.appendChild(script);
    }
  },
  onUnload() {
    // 清除脉动动画定时器
    if (this.pulseAnimationInterval) {
      clearInterval(this.pulseAnimationInterval);
      this.pulseAnimationInterval = null;
    }

    // 清除头像脉动动画
    if (this.avatarPulseAnimation) {
      cancelAnimationFrame(this.avatarPulseAnimation);
      this.avatarPulseAnimation = null;
    }

    // 清除新添加的定时器
    if (this.avatarPulseTimer) {
      clearTimeout(this.avatarPulseTimer);
      this.avatarPulseTimer = null;
    }

    // 清除水波纹动画
    if (this.rippleAnimationInterval) {
      clearInterval(this.rippleAnimationInterval);
      this.rippleAnimationInterval = null;
    }

    // 移除水波纹圆圈
    this.rippleCircles.forEach((circle) => {
      if (circle) {
        myMap && myMap.remove(circle);
      }
    });
    this.rippleCircles = [];
  },
  methods: {
    /**
     * 开始头像脉动动画
     * @param {Object} marker - 包含头像的标记
     */
    startAvatarPulseAnimation(marker) {
      // 如果已经有动画在运行，先清除它
      if (this.avatarPulseTimer) {
        clearTimeout(this.avatarPulseTimer);
        this.avatarPulseTimer = null;
      }

      if (this.avatarPulseAnimation) {
        cancelAnimationFrame(this.avatarPulseAnimation);
        this.avatarPulseAnimation = null;
      }

      // 安全检查：确保marker是有效的
      if (!marker) {
        console.log("无效的marker对象");
        return;
      }

      try {
        // 直接获取DOM元素，不使用getContentDom方法
        let avatarImg = null;
        let contentDom = null;

        // 尝试获取内容DOM
        try {
          if (
            marker.getContentDom &&
            typeof marker.getContentDom === "function"
          ) {
            contentDom = marker.getContentDom();
          }
        } catch (e) {
          console.log("获取contentDom失败:", e);
        }

        // 如果获取到了contentDom，尝试从中获取img元素
        if (contentDom) {
          try {
            avatarImg = contentDom.querySelector("img");
          } catch (e) {
            console.log("从contentDom获取img失败:", e);
          }
        }

        // 如果上面的方法失败，尝试从marker的content属性获取
        if (!avatarImg) {
          try {
            if (marker.getContent && typeof marker.getContent === "function") {
              const content = marker.getContent();
              if (typeof content === "string" && content.includes("<img")) {
                const tempDiv = document.createElement("div");
                tempDiv.innerHTML = content;
                avatarImg = tempDiv.querySelector("img");
              } else if (content instanceof HTMLElement) {
                avatarImg = content.querySelector("img");
              }
            }
          } catch (e) {
            console.log("从content获取img失败:", e);
          }
        }

        // 如果还是找不到头像元素，尝试从DOM中查找
        if (!avatarImg) {
          try {
            // 尝试通过标记的ID或其他属性查找
            const extData = marker.getExtData ? marker.getExtData() : null;
            if (extData && extData.id === "avatar") {
              // 尝试在整个文档中查找头像
              const avatarWrappers =
                document.querySelectorAll(".avatar-wrapper");
              if (avatarWrappers.length > 0) {
                avatarImg = avatarWrappers[0].querySelector("img");
              }
            }
          } catch (e) {
            console.log("从DOM查找img失败:", e);
          }
        }

        // 如果还是找不到，放弃动画
        if (!avatarImg) {
          console.log("无法找到头像图片元素");
          return;
        }

        // 设置初始样式
        avatarImg.style.transformOrigin = "center center";

        // 使用贝塞尔曲线实现平滑动画
        let startTime = null;
        const duration = 1000; // 动画总时长从3000ms减为2000ms，加快动画速度
        let animationActive = true; // 标记动画是否应该继续

        // 贝塞尔曲线函数，用于计算动画中间值
        const cubicBezier = (t, p0, p1, p2, p3) => {
          const u = 1 - t;
          const tt = t * t;
          const uu = u * u;
          const uuu = uu * u;
          const ttt = tt * t;

          return uuu * p0 + 3 * uu * t * p1 + 3 * u * tt * p2 + ttt * p3;
        };

        // 定义贝塞尔曲线控制点 - 这些值可以调整来改变动画曲线
        const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放，减小拉伸幅度
        const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放，减小拉伸幅度

        // 动画函数
        const animate = (timestamp) => {
          try {
            // 如果动画已停止，不继续执行
            if (!animationActive) return;

            if (!startTime) startTime = timestamp;
            const elapsed = timestamp - startTime;

            // 计算动画进度 (0-1)
            let progress = elapsed / duration;

            // 如果动画完成一个周期，重置开始时间
            if (progress >= 1) {
              startTime = timestamp;
              progress = 0;
            }

            // 使用贝塞尔曲线计算当前X和Y的缩放值
            const scaleX = cubicBezier(
              progress,
              bezierX[0],
              bezierX[1],
              bezierX[2],
              bezierX[3]
            );

            const scaleY = cubicBezier(
              progress,
              bezierY[0],
              bezierY[1],
              bezierY[2],
              bezierY[3]
            );

            // 应用变换 - 使用matrix而不是直接使用scale，以实现更精确的控制
            try {
              avatarImg.style.transform = `matrix(${scaleX}, 0, 0, ${scaleY}, 0, 0)`;
            } catch (e) {
              console.log("应用变换失败，可能元素已不存在:", e);
              animationActive = false;
              return;
            }

            // 继续下一帧动画
            this.avatarPulseAnimation = requestAnimationFrame(animate);
          } catch (e) {
            console.log("动画帧处理出错:", e);
            animationActive = false;
          }
        };

        // 开始动画
        this.avatarPulseAnimation = requestAnimationFrame(animate);

        // 设置清理函数
        this.cleanupAvatarAnimation = () => {
          animationActive = false;
          if (this.avatarPulseAnimation) {
            cancelAnimationFrame(this.avatarPulseAnimation);
            this.avatarPulseAnimation = null;
          }
        };
      } catch (e) {
        console.log("启动动画时出错:", e);
        if (this.avatarPulseAnimation) {
          cancelAnimationFrame(this.avatarPulseAnimation);
          this.avatarPulseAnimation = null;
        }
      }
    },
    /**
     * 	初始化地图  AmapRender
     */
    initMap() {
      console.log("======this.center========", this.center);
      myMap = new AMap.Map("AmapRender", {
        pitch: 50, //地图俯仰角度，有效范围 0 度- 83 度
        viewMode: "3D", //地图模式
        rotateEnable: true, //是否开启地图旋转交互 鼠标右键 + 鼠标画圈移动 或 键盘Ctrl + 鼠标左键画圈移动
        pitchEnable: true, //是否开启地图倾斜交互 鼠标右键 + 鼠标上下移动或键盘Ctrl + 鼠标左键上下移动
        // zoom: 17, //初始化地图层级
        // rotation: -15, //初始地图顺时针旋转的角度
        // center: [116.333926, 39.997245], //初始地图中心经纬度
        center:
          this.center && this.center[0] !== "undefined"
            ? this.center
            : [116.333926, 39.997245],
        zoom: 15,
        zooms: [4, 20], //地图显示的缩放级别范围
        // features: ['bg', 'road', 'building'],
        resizeEnable: true,
        mapStyle: "amap://styles/957040a4d7e11f8e53f93977962d6779", //设置地图的显示样式
        draggable: true,
      });

      // console.log("审图号：", myMap.getMapApprovalNumber());
      layer = new AMap.LabelsLayer({
        zooms: [3, 20],
        zIndex: 1000,
        // 开启标注避让，默认为开启，v1.4.15 新增属性
        collision: true,
        // 开启标注淡入动画，默认为开启，v1.4.15 新增属性
        animation: true,
      });
      myMap.add(layer);

      // 地图加载完成！
      myMap.on("complete", async () => {
        const center = myMap.getCenter();

        this.callMethod("map:complete", {
          maxDist: this.getMaxDist(),
          longitude: center.lng,
          latitude: center.lat,
        });
        // 获取地图当前可视区域
        const bounds = myMap.getBounds();
        // 赋值当前可是区域
        this.current_bounds_path = bounds.path;
        // 创建地图中用户图层
        this.overlay_person = new window.AMap.OverlayGroup(); //初始化附近人群组
        this.overlay_person_jh = new window.AMap.OverlayGroup(); //初始化附近人群组
        this.overlay_me = new AMap.OverlayGroup();
        myMap.add(this.overlay_me);
        this.overlay_moon = new AMap.OverlayGroup();
        this.overlay_address = new AMap.OverlayGroup();
        this.overlay_redPacket = new AMap.OverlayGroup();
        this.overlay_msg = new AMap.OverlayGroup();
        this.overlay_merchant = new AMap.OverlayGroup();
        myMap.add(this.overlay_moon);
        myMap.add(this.overlay_person);
        myMap.add(this.overlay_person_jh);
        myMap.add(this.overlay_address);
        myMap.add(this.overlay_redPacket);
        myMap.add(this.overlay_msg);
        myMap.add(this.overlay_merchant);
        this.overlay_person_jh.hide();
        // 异步加载路径规划插件
        AMap.plugin("AMap.Driving", (e) => {
          this.mapDriving = new AMap.Driving({
            policy: AMap.DrivingPolicy.LEAST_TIME,
          });
        });
        // 创建3D模型
        AMap.plugin("AMap.Object3D", (e) => {
          this.object3Dlayer = new AMap.Object3DLayer();
          this.object3DlayerStore = new AMap.Object3DLayer();
          myMap.add(this.object3DlayerStore);
          myMap.add(this.object3Dlayer);
          myMap.plugin(["AMap.GltfLoader"], () => {
            this.gltfObj = new AMap.GltfLoader();
            this.gltfLoader = new AMap.GltfLoader();
          });
        });
        // 天气插件
        AMap.plugin("AMap.Weather", () => {
          this.weather = new AMap.Weather();
          //查询实时天气信息, 查询的城市到行政级别的城市，如朝阳区、杭州市
        });
        AMap.plugin(["AMap.MarkerClusterer"], async () => {
          const _renderClusterMarker = (context) => {
            const clusterData = context.clusterData; // 聚合中包含数据
            const index = context.index; // 聚合的条件
            const count = context.count; // 聚合中点的总数
            const marker = context.marker; // 聚合点标记对象
            const styleObj = {
              bgColor: "rgba(0,0,0,.8)",
              borderColor: "rgba(255,255,255,1)",
              // // text: clusterData[0][index['mainKey']],
              size: Math.round(
                Math.pow(count / (this.person_markers.length || 1), 1 / 5) * 70
              ),
              color: "#ffffff",
              textAlign: "center",
              boxShadow: "0px 0px 5px rgba(0,0,0,0.8)",
            };
            // 自定义点标记样式
            const div = document.createElement("div");
            div.innerHTML = count;
            div.className = "amap-cluster";
            div.style.width = 40 + "px";
            div.style.height = 40 + "px";
            div.style.lineHeight = 25 + "px";
            if (styleObj.index <= 2) {
              div.style.height = styleObj.size + "px";
              // 自定义点击事件
              context.marker.on("click", function (e) {
                const curZoom = map.getZoom();
                if (curZoom < 20) {
                  curZoom += 1;
                }
                map.setZoomAndCenter(curZoom, e.lnglat);
              });
            }
            div.style.backgroundImage = `url(https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20231203/659faea8-89d2-4485-ae82-80f14d7933aa.png)`;
            div.style.backgroundRepeat = "no-repeat";
            div.style.backgroundSize = "100% 100%";
            div.style.color = styleObj.color;
            div.style.textAlign = styleObj.textAlign;
            context.marker.setContent(div);
            context.marker.setAnchor("center");
            context.marker.setzIndex(40);
          };
          this.cluster_person = new AMap.MarkerClusterer(myMap, [], {
            gridSize: 10, // 聚合网格像素大小
            renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
          });
        });
      });
      // 地图点击事件！
      myMap.on(
        "touchstart",
        (ev) => {
          this.startClickTime = new Date().getTime();
        },
        this
      );
      // 触发鼠标在地图上单击抬起时的事件
      myMap.on(
        "touchend",
        (ev) => {
          const now = new Date().getTime();
          // 监听地图长按事件
          if (this.startClickTime && now - this.startClickTime > 300) {
            this.mapLongTap(ev);
          }
        },
        this
      );
      myMap.on("rotateend", (ev) => {}, this);
      myMap.on(
        "click",
        (ev) => {
          // 删除弹层
          this.moon_modal && this.moon_modal.hide();
          this.house_modal && this.house_modal.hide();
          this.poiMarket && this.poiMarket.hide();
          this.callMethod("close_house_popup");
          this.callMethod("close_dian_popup");
          // this.callMethod('to_merchant_store',ev)
          this.click3dgltf(ev);
          this.onMeshesClick(ev);
        },
        this
      );
      myMap.on(
        "movestart",
        (ev) => {
          // 重置地图点击
          this.startClickTime = null;
        },
        this
      );
      myMap.on(
        "dragging",
        (ev) => {
          // 重置地图点击
          this.startClickTime = null;
        },
        this
      );
      myMap.on(
        "moveend",
        async (ev) => {
          // 获取地图中心点坐标
          const center = myMap.getCenter();
          // 动态设置中心点区域名称
          getGeocoder(
            AMap,
            {
              lnglat: [center.lng, center.lat],
            },
            true
          ).then((geoRes) => {
            if (geoRes.info == "OK") {
              const strr =
                geoRes.regeocode.addressComponent.city ||
                geoRes.regeocode.addressComponent.province;

              // console.log('11geoRes', geoRes, '');
              const district =
                geoRes.regeocode.addressComponent.district ||
                geoRes.regeocode.addressComponent.country;
              this.callMethod("changeCountry", district);
              this.weather.getLive(strr, (err, data) => {
                if (!err) {
                  this.temperature = data.temperature + "℃";
                  this.callMethod("temperature:change", {
                    temperature: this.temperature,
                    weather:
                      data.weather.indexOf("雨") > -1
                        ? "yu"
                        : data.weather.indexOf("雪") > -1
                        ? "xue"
                        : "qing",
                  });
                } else {
                  this.callMethod("temperature:change", "");
                }
              });
            } else {
              this.callMethod("changeCountry", "");
            }
          });
          // 获取当前可是区域的最大半径，加载半径内用户
          this.getPeopleInMaxDist();
        },
        this
      );
      myMap.on("zoomend", (ev) => {
        // 获取当前可是区域的最大半径，加载半径内用户
        this.getPeopleInMaxDist();
        const zoom = myMap.getZoom();
        // 修改商家模型
        this.storeMarkers.forEach((marker) => {
          const markerData = marker.getExtData();
          // 检查标点是否在当前地图视图的范围内
          // console.log("地图标点id", markerData.id, zoom);
          if (zoom >= markerData.id) {
            // console.log("显示");
            // 如果在范围内，则显示标点
            marker.show();
          } else {
            // console.log("隐藏");
            // 如果不在范围内，则隐藏标点
            marker.hide();
          }
        });

        if (zoom < 13) {
          this.zoom13 = 1;
          this.overlay_person.hide();
          this.overlay_person_jh.show();
        } else {
          this.zoom13 = 2;
          this.overlay_person.show();
          this.overlay_person_jh.hide();
        }
        if (zoom >= 30) {
          if (this.zoom17 == 2) return false;
          this.zoom17 = 2;
          this.cluster_address.clearMarkers();
          this.houseMessage.forEach((item) => {
            if (item.coordinate.indexOf("undefined") > -1) return false;
            const position = item.coordinate.split(",");
            const paramHouse = {
              position: position, // 必须
              scale: 100, // 非必须，默认1
              height: 100, // 非必须，默认0
              scene: 0, // 非必须，默认0
              zIndex: 18,
              extData: item,
            };
            this.gltfObj.load(
              config.ossBaseUrl + "config/map/1.12.gltf",
              (gltfHouse) => {
                this.houseMeshes.push(gltfHouse);
                gltfHouse.setOption(paramHouse);
                gltfHouse.rotateX(0);
                gltfHouse.rotateZ(0);
                gltfHouse.rotateY(0);
                this.object3Dlayer.add(gltfHouse);
              }
            );
          });
        } else {
          if (this.zoom17 == 1) return false;
          this.zoom17 = 1;
          const markersCount =
            this.cluster_address && this.cluster_address.getClustersCount();
          if (this.cluster_address && markersCount <= 0) {
            this.object3Dlayer.clear();
            this.cluster_address.setMarkers(this.houseMarkets);
          }
        }
      });
    },
    /**
     * 	长按事件-扎点
     */
    async mapLongTap(ev) {
      const lnglat = ev.lnglat;

      const res = await getGeocoder(window.AMap, {
        lnglat: [lnglat.lng, lnglat.lat],
      });
      this.callMethod("mapLongClick", {
        lnglat: lnglat,
        location: res.regeocode.formattedAddress,
        addressComponent: res.regeocode.addressComponent,
      });
    },
    /**
     * 	调用 view 层的方法
     */
    callMethod(act = "", params) {
      this.$ownerInstance.callMethod("callApp", {
        act,
        option: params,
      });
    },
    /**
     * 	获取当前可是区域的最大半径，加载半径内用户
     */
    getPeopleInMaxDist() {
      const r = this.getMaxDist();
      const center = myMap.getCenter();
      if (!this.downTime) {
        this.downTime = setTimeout(() => {
          clearTimeout(this.downTime);
          this.downTime = null;
          this.callMethod("change:maxDist", {
            maxDist: r,
            longitude: center.lng,
            latitude: center.lat,
          });
        }, 2000);
      }
    },
    /**
     * 房子  模型点击
     */
    onMeshesClick(ev) {
      const pixel = ev.pixel;
      const px = new AMap.Pixel(pixel.x, pixel.y);
      const obj =
        myMap.getObject3DByContainerPos(px, [this.object3Dlayer], false) || {};
      if (obj && obj.object) {
        const meshId = obj.object.id;

        this.houseMeshes.forEach(async (item) => {
          if (item && item.layerMesh) {
            for (let i = 0; i < item.layerMesh.length; i++) {
              if (meshId === item.layerMesh[i].id) {
                const extData = item.gltf.option.extData;

                const lng = extData.coordinate.split(",")[0];
                const lat = extData.coordinate.split(",")[1];

                const startLocation = [this.me.longitude, this.me.latitude];
                const endLocation = extData.coordinate.split(",");
                const result = await this.getDriver(startLocation, endLocation);
                if (result.info == "OK") {
                  const distance = result.routes[0].distance;
                  extData.distance =
                    distance && distance > 1000
                      ? (distance / 1000).toFixed(2) + " 千米"
                      : distance + " 米" || 0 + " 米";
                  // this.$refs.storePopup.open(extData)
                  this.callMethod("open_storePopup", {
                    ...extData,
                  });
                }
              }
            }
          }
        });
      }
    },
    /**
     * 	获取视野的最大半径
     */
    getMaxDist() {
      let r = 0;
      const bounds = myMap.getBounds();
      const center = myMap.getCenter();
      try {
        this.current_bounds_path = bounds.path;
        this.current_bounds_path.forEach((item) => {
          const p1 = new AMap.LngLat(item[0], item[1]);
          const p2 = new AMap.LngLat(center.lng, center.lat);
          const dist = p1.distance(p2);
          if (dist > r) r = dist;
        });
        return parseInt(r);
      } catch (e) {
        //TODO handle the exception
        return 200000000;
      }
    },

    /**
     *  重置地图中心点
     * 	center 变动回调
     */
    receive_Center(newValue, oldValue, ownerVm, vm) {
      this.resetPeople();
      try {
        if (newValue === null) return;

        myMap && myMap.setCenter(this.center);
        myMap && myMap.setZoom(15);
      } catch (e) {
        //TODO handle the exception
      }
    },
    resetPeople() {
      if (!this.cluster_person) return false;
      const markers = this.cluster_person.getMarkers();
      markers.forEach((item) => {
        if (this.person_showDetail_arr.includes(item.De.extData.uid)) {
          // 创建包含头像和水波纹容器的HTML
          const avatarHtml = `
            <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
              <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
              <img src="${item.De.extData.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
            </div>
          `;

          // 设置HTML内容作为标记
          const markerContent = document.createElement("div");
          markerContent.innerHTML = avatarHtml;
          item.setContent(markerContent);

          // 获取水波纹容器并应用水波纹效果
          setTimeout(() => {
            const rippleContainer =
              markerContent.querySelector(".ripple-container");
            if (rippleContainer) {
              this.createRippleEffect(
                item.getPosition(),
                this.rippleColors.reset,
                {
                  containerElement: rippleContainer,
                }
              );
            }
          }, 50);
        }
      });
      this.person_showDetail_arr = [];
    },

    // 红包标注
    receive_redPacket(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.overlay_redPacket && this.overlay_redPacket.clearOverlays();
      const markers = [];
      const icon_red = newIcon(AMap, {
        image: red_packet,
        size: new AMap.Size(packetW, packetH),
        imageSize: new AMap.Size(packetW, packetH),
      });
      const icon_yellow = newIcon(AMap, {
        image: yellow_packet,
        size: new AMap.Size(packetW, packetH),
        imageSize: new AMap.Size(packetW, packetH),
      });
      newValue.forEach((item) => {
        const marker_red = new AMap.Marker({
          position: [item.Longitude, item.Latitude],
          icon: item.is_business ? yellow_packet : icon_red,
          offset: new AMap.Pixel(-packetW / 2, -packetH),
          extData: {
            ...item,
          },
          zIndex: 90,
        });
        markers.push(marker_red);
        marker_red.on("touchstart", (ev) => {
          this.callMethod("view_redPacket_detail", ev.target.De.extData);
          myMap.setCenter([item.longitude, item.latitude]);
          myMap.setZoom(18);
        });
      });
      this.overlay_redPacket.addOverlays(markers);
    },
    // 留言标注
    receive_msg(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.overlay_msg && this.overlay_msg.clearOverlays();
      const markers = [];
      const icon_msg = newIcon(AMap, {
        image: msg_icon,
        size: new AMap.Size(packetW, packetH),
        imageSize: new AMap.Size(packetW, packetH),
      });
      newValue.forEach((item) => {
        const marker_msg = new AMap.Marker({
          position: [item.Longitude, item.Latitude],
          icon: icon_msg,
          offset: new AMap.Pixel(-packetW / 2, -packetH),
          extData: {
            ...item,
          },
          zIndex: 90,
        });
        markers.push(marker_msg);
        marker_msg.on("touchstart", (ev) => {
          this.callMethod("view_msg_detail", ev.target.De.extData);
          myMap.setCenter([item.longitude, item.latitude]);
          myMap.setZoom(18);
        });
      });
      this.overlay_msg.addOverlays(markers);
    },
    /**
     *  重置地图用户W
     * 	person 变动回调
     */
    receive_Person(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      map_person = newValue;

      const markers = [];
      const markers_jh = [];
      map_person.forEach((item) => {
        if (!item.i_can_see) return false;
        const hasIndex = this.person_showDetail_arr.indexOf(item.uid);
        const icon_per = newIcon(AMap, {
          image: hasIndex > -1 ? item.map_user_mark : item.avatar,
          size:
            hasIndex > -1
              ? new AMap.Size(avatarHoverW, avatarHoverH)
              : new AMap.Size(avatarW, avatarH),
          imageSize:
            hasIndex > -1
              ? new AMap.Size(avatarHoverW, avatarHoverH)
              : new AMap.Size(avatarW, avatarH),
        });

        // 创建包含头像和水波纹容器的HTML
        const avatarHtml = `
          <div class="avatar-wrapper" style="position: relative; width: ${
            hasIndex > -1 ? avatarHoverW : avatarW
          }px; height: ${
          hasIndex > -1 ? avatarHoverH : avatarH
        }px; display: flex; justify-content: center; align-items: center;">
            <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
            <img src="${
              hasIndex > -1 ? item.map_user_mark : item.avatar
            }" style="width: 100%; height: 100%; position: relative; z-index: 2; transform-origin: center center;" />
          </div>
        `;

        const marker_per = new AMap.Marker({
          position: [item.longitude, item.latitude],
          content: avatarHtml,
          offset:
            hasIndex > -1
              ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
              : new AMap.Pixel(-avatarW / 2, -avatarH),
          extData: {
            ...item,
            icon_per,
            showDesc: 0,
          },
          zIndex: 90,
        });

        // 如果用户已被选中，立即启动动画
        if (hasIndex > -1) {
          // 获取水波纹容器并应用水波纹效果
          setTimeout(() => {
            try {
              const contentDom = marker_per.getContentDom();
              if (contentDom) {
                const rippleContainer =
                  contentDom.querySelector(".ripple-container");
                const avatarImg = contentDom.querySelector("img");

                if (rippleContainer) {
                  // 创建持续的水波纹动画 - 与自己位置的水波纹相同
                  const startContinuousRipple = () => {
                    // 创建持续的水波纹动画
                    const rippleInterval = setInterval(() => {
                      const position = marker_per.getPosition();
                      // 检查位置是否有效
                      if (
                        !position ||
                        (position.getLng &&
                          (isNaN(position.getLng()) ||
                            isNaN(position.getLat())))
                      ) {
                        console.warn(
                          "无效的标记位置，跳过水波纹创建:",
                          position
                        );
                        return;
                      }
                      // 使用蓝色水波纹，更适合表示自己的位置
                      this.createRippleEffect(
                        position,
                        this.rippleColors.self,
                        {
                          maxRadius: 98,
                          initialRadius: 50,
                          initialOpacity: 0.9,
                          animationDuration: 1000,
                          rippleCount: 2,
                          strokeWeight: 0,
                          fillOpacity: 0.6,
                          yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
                          xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
                          containerElement: rippleContainer,
                        }
                      );
                    }, 2000); // 每2秒创建一组新的水波纹

                    // 立即创建第一组水波纹
                    const firstPosition = marker_per.getPosition();
                    if (
                      firstPosition &&
                      (!firstPosition.getLng ||
                        (!isNaN(firstPosition.getLng()) &&
                          !isNaN(firstPosition.getLat())))
                    ) {
                      this.createRippleEffect(
                        firstPosition,
                        this.rippleColors.self,
                        {
                          maxRadius: 98,
                          initialOpacity: 0.9,
                          initialRadius: 50,

                          animationDuration: 1000,
                          rippleCount: 2,
                          strokeWeight: 0,
                          fillOpacity: 0.6,
                          yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
                          xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
                          containerElement: rippleContainer,
                        }
                      );
                    }
                  };

                  // 启动水波纹
                  startContinuousRipple();
                }

                // 启动头像脉动动画 - 与自己位置的动画相同
                if (avatarImg) {
                  // 设置初始样式
                  avatarImg.style.transformOrigin = "center center";

                  // 使用贝塞尔曲线实现平滑动画
                  let startTime = null;
                  const duration = 1000; // 动画总时长
                  let animationActive = true; // 标记动画是否应该继续

                  // 贝塞尔曲线函数，用于计算动画中间值
                  const cubicBezier = (t, p0, p1, p2, p3) => {
                    const u = 1 - t;
                    const tt = t * t;
                    const uu = u * u;
                    const uuu = uu * u;
                    const ttt = tt * t;

                    return (
                      uuu * p0 + 3 * uu * t * p1 + 3 * u * tt * p2 + ttt * p3
                    );
                  };

                  // 定义贝塞尔曲线控制点 - 与自己位置的动画相同
                  const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放
                  const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放

                  // 动画函数
                  const animate = (timestamp) => {
                    try {
                      // 如果动画已停止，不继续执行
                      if (!animationActive) return;

                      if (!startTime) startTime = timestamp;
                      const elapsed = timestamp - startTime;

                      // 计算动画进度 (0-1)
                      let progress = elapsed / duration;

                      // 如果动画完成一个周期，重置开始时间
                      if (progress >= 1) {
                        startTime = timestamp;
                        progress = 0;
                      }

                      // 使用贝塞尔曲线计算当前X和Y的缩放值
                      const scaleX = cubicBezier(
                        progress,
                        bezierX[0],
                        bezierX[1],
                        bezierX[2],
                        bezierX[3]
                      );

                      const scaleY = cubicBezier(
                        progress,
                        bezierY[0],
                        bezierY[1],
                        bezierY[2],
                        bezierY[3]
                      );

                      // 应用变换
                      try {
                        avatarImg.style.transform = `matrix(${scaleX}, 0, 0, ${scaleY}, 0, 0)`;
                      } catch (e) {
                        console.log("应用变换失败，可能元素已不存在:", e);
                        animationActive = false;
                        return;
                      }

                      // 继续下一帧动画
                      requestAnimationFrame(animate);
                    } catch (e) {
                      console.log("动画帧处理出错:", e);
                      animationActive = false;
                    }
                  };

                  // 开始动画
                  requestAnimationFrame(animate);
                }
              }
            } catch (e) {
              console.error("启动动画时出错:", e);
            }
          }, 50);
        }

        marker_per.on("click", (ev) => {
          let showDetailUid = this.person_showDetail_arr[0] || "";
          const isSameClick = showDetailUid && showDetailUid == item.uid;
          const markers = this.cluster_person.getMarkers();

          // 在点击位置创建水波纹效果
          const clickPosition = marker_per.getPosition();

          // 检查位置是否有效
          if (
            !clickPosition ||
            (clickPosition.getLng &&
              (isNaN(clickPosition.getLng()) || isNaN(clickPosition.getLat())))
          ) {
            console.warn("点击时获取到无效的标记位置:", clickPosition);
          } else {
            // 创建包含水波纹的HTML元素
            const rippleDiv = document.createElement("div");
            rippleDiv.className = "person-click-ripple";
            rippleDiv.style.position = "absolute";
            rippleDiv.style.top = "0";
            rippleDiv.style.left = "0";
            rippleDiv.style.width = "100%";
            rippleDiv.style.height = "100%";
            rippleDiv.style.zIndex = "50";
            rippleDiv.style.pointerEvents = "none";

            // 将水波纹元素添加到标记中
            const contentDom = marker_per.getContentDom();
            if (contentDom) {
              contentDom.appendChild(rippleDiv);

              // 给头像添加水波纹效果
              this.createRippleEffect(clickPosition, this.rippleColors.reset, {
                containerElement: rippleDiv,
              });
            } else {
              // 如果没有获取到内容DOM，就直接在地图上创建水波纹
              this.createRippleEffect(clickPosition, this.rippleColors.person);
            }
          }

          if (isSameClick) {
            // 如果是再次点击已选中的用户，取消选中状态
            this.person_showDetail_arr = [];
          } else {
            // 设置新选中的用户
            this.person_showDetail_arr = [item.uid];
          }
          for (let i = 0; i < markers.length; i++) {
            const curItem = markers[i].De.extData;
            const curItemUid = markers[i].De.extData.uid;
            if (isSameClick) {
              if (showDetailUid === curItemUid) {
                // 使用HTML标记代替图标
                const currentSize = avatarW;

                // 添加收缩动画效果
                const animateIconShrink = () => {
                  // 创建大一点的头像HTML
                  const largeAvatarHtml = `
                    <div class="avatar-wrapper" style="position: relative; width: ${
                      currentSize + 10
                    }px; height: ${
                    currentSize + 10
                  }px; display: flex; justify-content: center; align-items: center;">
                      <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                      <img src="${
                        curItem.avatar
                      }" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                    </div>
                  `;

                  // 设置大头像
                  markers[i].setContent(largeAvatarHtml);
                  markers[i].setOffset(
                    new AMap.Pixel(-(currentSize + 10) / 2, -(currentSize + 10))
                  );

                  // 稍后恢复正常大小
                  setTimeout(() => {
                    // 创建正常大小的头像HTML
                    const normalAvatarHtml = `
                      <div class="avatar-wrapper" style="position: relative; width: ${currentSize}px; height: ${currentSize}px; display: flex; justify-content: center; align-items: center;">
                        <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                        <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                      </div>
                    `;

                    // 设置正常大小头像
                    markers[i].setContent(normalAvatarHtml);
                    markers[i].setOffset(
                      new AMap.Pixel(-currentSize / 2, -currentSize)
                    );
                  }, 200);
                };

                animateIconShrink();
                showDetailUid = "";
                break;
              }
            } else {
              if (item.uid === curItemUid) {
                // 使用HTML标记代替图标
                const avatarHtml = `
                  <div class="avatar-wrapper" style="position: relative; width: ${avatarHoverW}px; height: ${avatarHoverH}px; display: flex; justify-content: center; align-items: center;">
                    <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                    <img src="${item.map_user_mark}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                  </div>
                `;

                markers[i].setContent(avatarHtml);
                markers[i].setOffset(
                  new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
                );

                // 添加长大动画效果
                const animateIconGrow = () => {
                  // 创建小一点的头像HTML
                  const smallAvatarHtml = `
                    <div class="avatar-wrapper" style="position: relative; width: ${
                      avatarHoverW - 10
                    }px; height: ${
                    avatarHoverH - 10
                  }px; display: flex; justify-content: center; align-items: center;">
                      <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                      <img src="${
                        item.map_user_mark
                      }" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                    </div>
                  `;

                  // 设置小头像
                  markers[i].setContent(smallAvatarHtml);
                  markers[i].setOffset(
                    new AMap.Pixel(
                      -(avatarHoverW - 10) / 2,
                      -(avatarHoverH - 10)
                    )
                  );

                  // 稍后设置正常大小
                  setTimeout(() => {
                    markers[i].setContent(avatarHtml);
                    markers[i].setOffset(
                      new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
                    );
                  }, 200);
                };

                animateIconGrow();

                // 添加水波纹效果
                setTimeout(() => {
                  const contentDom = markers[i].getContentDom();
                  if (contentDom) {
                    const rippleContainer =
                      contentDom.querySelector(".ripple-container");
                    const avatarImg = contentDom.querySelector("img");

                    if (rippleContainer) {
                      // 获取位置并确保它是有效的
                      const position = markers[i].getPosition();
                      if (
                        !position ||
                        (position.getLng &&
                          (isNaN(position.getLng()) ||
                            isNaN(position.getLat())))
                      ) {
                        console.warn("无效的标记位置:", position);
                        return;
                      }

                      // 创建持续的水波纹动画 - 与自己位置的水波纹相同
                      const rippleInterval = setInterval(() => {
                        const pos = position;
                        // 检查位置是否有效
                        if (
                          !pos ||
                          (pos.getLng &&
                            (isNaN(pos.getLng()) || isNaN(pos.getLat())))
                        ) {
                          console.warn("无效的标记位置，跳过水波纹创建:", pos);
                          return;
                        }
                        // 使用蓝色水波纹，与自己位置一致
                        this.createRippleEffect(pos, this.rippleColors.self, {
                          maxRadius: 98,
                          initialOpacity: 0.9,
                          initialRadius: 50,

                          animationDuration: 1000,
                          rippleCount: 2,
                          strokeWeight: 0,
                          fillOpacity: 0.6,
                          yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
                          xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
                          containerElement: rippleContainer,
                        });
                      }, 2000); // 每2秒创建一组新的水波纹

                      // 立即创建第一组水波纹
                      this.createRippleEffect(
                        position,
                        this.rippleColors.self,
                        {
                          maxRadius: 98,
                          initialOpacity: 0.9,
                          initialRadius: 50,
                          animationDuration: 1000,
                          rippleCount: 2,
                          strokeWeight: 0,
                          fillOpacity: 0.6,
                          yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
                          xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
                          containerElement: rippleContainer,
                        }
                      );
                    }

                    // 启动头像脉动动画 - 与自己位置的动画相同
                    if (avatarImg) {
                      // 设置初始样式
                      avatarImg.style.transformOrigin = "center center";

                      // 使用贝塞尔曲线实现平滑动画
                      let startTime = null;
                      const duration = 1000; // 动画总时长
                      let animationActive = true; // 标记动画是否应该继续

                      // 贝塞尔曲线函数，用于计算动画中间值
                      const cubicBezier = (t, p0, p1, p2, p3) => {
                        const u = 1 - t;
                        const tt = t * t;
                        const uu = u * u;
                        const uuu = uu * u;
                        const ttt = tt * t;

                        return (
                          uuu * p0 +
                          3 * uu * t * p1 +
                          3 * u * tt * p2 +
                          ttt * p3
                        );
                      };

                      // 定义贝塞尔曲线控制点 - 与自己位置的动画相同
                      const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放
                      const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放

                      // 动画函数
                      const animate = (timestamp) => {
                        try {
                          // 如果动画已停止，不继续执行
                          if (!animationActive) return;

                          if (!startTime) startTime = timestamp;
                          const elapsed = timestamp - startTime;

                          // 计算动画进度 (0-1)
                          let progress = elapsed / duration;

                          // 如果动画完成一个周期，重置开始时间
                          if (progress >= 1) {
                            startTime = timestamp;
                            progress = 0;
                          }

                          // 使用贝塞尔曲线计算当前X和Y的缩放值
                          const scaleX = cubicBezier(
                            progress,
                            bezierX[0],
                            bezierX[1],
                            bezierX[2],
                            bezierX[3]
                          );

                          const scaleY = cubicBezier(
                            progress,
                            bezierY[0],
                            bezierY[1],
                            bezierY[2],
                            bezierY[3]
                          );

                          // 应用变换
                          try {
                            avatarImg.style.transform = `matrix(${scaleX}, 0, 0, ${scaleY}, 0, 0)`;
                          } catch (e) {
                            console.log("应用变换失败，可能元素已不存在:", e);
                            animationActive = false;
                            return;
                          }

                          // 继续下一帧动画
                          requestAnimationFrame(animate);
                        } catch (e) {
                          console.log("动画帧处理出错:", e);
                          animationActive = false;
                        }
                      };

                      // 开始动画
                      requestAnimationFrame(animate);
                    }
                  }
                }, 50);
              } else if (curItemUid == showDetailUid) {
                // 使用HTML标记代替图标

                // 添加收缩动画效果
                const animateIconShrink = () => {
                  // 创建大一点的头像HTML
                  const largeAvatarHtml = `
                    <div class="avatar-wrapper" style="position: relative; width: ${avatarHoverW}px; height: ${avatarHoverH}px; display: flex; justify-content: center; align-items: center;">
                      <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                      <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                    </div>
                  `;

                  // 设置大头像
                  markers[i].setContent(largeAvatarHtml);
                  markers[i].setOffset(
                    new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
                  );

                  // 稍后恢复正常大小
                  setTimeout(() => {
                    // 创建正常大小的头像HTML
                    const normalAvatarHtml = `
                      <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
                        <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                        <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                      </div>
                    `;

                    // 设置正常大小头像
                    markers[i].setContent(normalAvatarHtml);
                    markers[i].setOffset(
                      new AMap.Pixel(-avatarW / 2, -avatarH)
                    );
                  }, 200);
                };

                animateIconShrink();
                showDetailUid = "";
              }
            }
          }
          // TODO
          this.labelMarker && this.labelMarker.hide();
          this.MoonLabelMarker && this.MoonLabelMarker.hide();
          this.callMethod("closeStoreDialog");
          myMap.setCenter([item.longitude, item.latitude]);
          myMap.setZoom(15);

          this.current_edit_person_marker = newMarker;
          if (!isSameClick) {
            this.callMethod("open_personPopup", ev.target.De.extData);
          }
        });

        // 添加触摸事件支持，以便在移动设备上也能触发动画效果
        marker_per.on("touchstart", (ev) => {
          // 触发与点击相同的处理逻辑
          marker_per.emit("click", ev);
        });

        markers.push(marker_per);
      });
      this.person_markers = markers;
      // 自定义聚合点样式
      this.cluster_person.setMarkers(this.person_markers);
    },
    /**
     *  重置地图房子
     * 	house 变动回调
     */
    receive_House(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.houseMessage = newValue;
      const address_markers = [];
      this.overlay_address.clearOverlays();
      this.cluster_address && this.cluster_address.clearMarkers();
      newValue.forEach((item) => {
        const div = document.createElement("div");
        const img = document.createElement("img");
        img.style.width = "40px";
        img.style.height = "40px";
        img.src = config.ossBaseUrl + "config/map/housePng.png";
        div.appendChild(img);
        try {
          if (item.coordinate.indexOf("undefined") > -1) return false;
          const position = item.coordinate.split(",");
          const row = new AMap.Marker({
            position: position,
            content: div,
            offset: new AMap.Pixel(-15, -15),
            extData: {
              ...item,
            },
            zIndex: 40,
          });

          address_markers.push(row);
        } catch (err) {}
      });
      this.overlay_address && this.overlay_address.addOverlays(address_markers);
      this.overlay_address.on("click", async (ev) => {
        const extData = ev.target.De.extData;
        const lng = ev.target.De.position.lng;
        const lat = ev.target.De.position.lat;
        myMap.setCenter([lng, lat]);
        if (extData.type == 1) {
          if (this.house_modal) {
            myMap.remove(this.house_modal);
            this.house_modal = null;
          }
          this.house_modal = new AMap.Marker({
            position: new AMap.LngLat(lng, lat),
            offset: new AMap.Pixel(-94, -120), //设置文本标注偏移量
            zIndex: 99,
            content: `<div style='width:172px;height:70px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
												<div style="
															font-size: 14px;
															font-family: Source Han Sans-Bold, Source Han Sans;
															font-weight: 700;
															color: #FFFFFF;
															line-height: 46rpx;">
															家（${extData.remark}）
												</div>
												<div style="position:absolute;
															right:10px;
															top:10px; 
												" onclick="openSetting('${extData.id}:${extData.location}:${extData.remark}')">
													<img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
												</div>
												<div style="
															font-size: 12px;
															font-family: Source Han Sans-Regular, Source Han Sans;
															font-weight: 400;
															color: rgba(255,255,255,0.64);
															line-height: 50rpx;
															margin-top:5px;
														">
															最近一次(${extData.last_stay})
															<div>在这里睡了${extData.stay_num}晚</div>
												</div>
												<div style="width: 0;
															height: 0;
															border: 10px solid;
															border-color: #000 transparent transparent transparent;
															position:absolute;
															left:50%;
															bottom:-20px;
															transform:translateX(-50%);
															">
												</div>
													
											
											</div>`,
          });
          setTimeout(() => {
            myMap.add(this.house_modal);
          }, 300);
        }
        const startLocation = [this.me.longitude, this.me.latitude];
        const endLocation = extData.coordinate.split(",");
        const result = await this.getDriver(startLocation, endLocation);
        if (result.info == "OK") {
          const distance = result.routes[0].distance;
          extData.distance =
            distance && distance > 1000
              ? (distance / 1000).toFixed(2) + " 千米"
              : distance + " 米" || 0 + " 米";
          // this.$refs.storePopup.open(extData)
          this.callMethod("open_storePopup", {
            ...extData,
          });
        }
      });
      AMap.plugin(["AMap.MarkerClusterer"], async () => {
        // 自定义聚合点样式
        const _renderClusterMarker = (context) => {
          const clusterData = context.clusterData; // 聚合中包含数据
          const index = context.index; // 聚合的条件
          const count = context.count; // 聚合中点的总数
          const marker = context.marker; // 聚合点标记对象
          const styleObj = {
            bgColor: "rgba(0,0,0,.8)",
            borderColor: "rgba(255,255,255,1)",
            // // text: clusterData[0][index['mainKey']],
            size: Math.round(
              Math.pow(count / (address_markers.length || 1), 1 / 5) * 70
            ),
            color: "#ffffff",
            textAlign: "center",
            boxShadow: "0px 0px 5px rgba(0,0,0,0.8)",
          };
          // 自定义点标记样式
          const div = document.createElement("div");
          div.innerHTML = count;
          div.className = "amap-cluster";
          div.style.width = 40 + "px";
          div.style.height = 40 + "px";
          div.style.lineHeight = styleObj.size + "px";
          if (styleObj.index <= 2) {
            div.style.height = styleObj.size + "px";
            // 自定义点击事件
            context.marker.on("click", function (e) {
              const curZoom = map.getZoom();
              if (curZoom < 20) {
                curZoom += 1;
              }
              map.setZoomAndCenter(curZoom, e.lnglat);
            });
          }
          div.style.backgroundImage = `url( ${config.ossBaseUrl}config/map/housePng.png)`;
          div.style.backgroundRepeat = "no-repeat";
          div.style.backgroundSize = "100% 100%";
          div.style.color = styleObj.color;
          div.style.textAlign = styleObj.textAlign;
          context.marker.setContent(div);
          context.marker.setAnchor("center");
          context.marker.setzIndex(40);
        };
        this.cluster_address = new AMap.MarkerClusterer(
          myMap,
          address_markers,
          {
            gridSize: 1, // 聚合网格像素大小
            renderClusterMarker: _renderClusterMarker,
          }
        );
      });
      this.houseMarkets = address_markers;
    },
    /**
     *  获取路径规划
     */
    getDriver(startLngLat, endLngLat) {
      return new Promise((resolve, reject) => {
        this.mapDriving.search(
          startLngLat,
          endLngLat,
          function (status, result) {
            // 未出错时，result即是对应的路线规划方案
            if (status === "complete") {
              resolve(result);
            } else {
              reject(result);
            }
          }
        );
      });
    },
    /**
     *  重置地图本人位置
     * 	me 变动回调
     */
    receive_Me(newValue, oldValue, ownerVm, vm) {
      if (newValue == null) return;
      // console.log(newValue, 'newValue');

      // 当前用户地图扎点
      if (this.overlay_me.getOverlays().length <= 0) {
        // 创建包含头像和水波纹容器的HTML
        const avatarHtml = `
          <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
            <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
            <img src="${newValue.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; transform-origin: center;" />
          </div>
        `;

        // 创建带有HTML内容的标记
        const avatar = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: avatarHtml,
          offset: new AMap.Pixel(-avatarW / 2, -avatarH),
          zIndex: 100,
          extData: {
            id: "avatar",
          },
        });

        const onlineIcon = newIcon(AMap, {
          image:
            "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/9ba8726c-dd1f-4ea0-91fb-465bb74e39b8.png",
          size: new AMap.Size(30, 15),
          imageSize: new AMap.Size(30, 15),
        });

        let dlImg = "";
        if (newValue.electricity > 80) {
          dlImg = dl100;
        } else if (newValue.electricity > 55) {
          dlImg = dl75;
        } else if (newValue.electricity > 30) {
          dlImg = dl50;
        } else {
          dlImg = dl25;
        }

        // 恢复其他标记
        const Dlmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="color:#000;position:relative;top:70px;left:10px">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);line-height:12px;text-align:center">${newValue.electricity}%</div></div>`,
          offset: new AMap.Pixel(-20, -40), // 修正电量显示的位置
          extData: {
            id: "Dlmarker",
          },
        });

        const Fzmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="position:relative;">
						<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/a055781f-bc9b-46e9-b94b-5f7133a43385.png">
						</div>`,
          offset: new AMap.Pixel(25, -33),
          extData: {
            id: "Fzmarker",
          },
        });

        const Sdmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="position:relative;top:20px">
					<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" 
					src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
            <div style="transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(
              0
            ).toFixed(0)}
							<div>km2/h</div>
            </div></div>`,
          offset: new AMap.Pixel(-40, -40), // 修正速度显示的位置
          extData: {
            id: "Sdmarker",
          },
        });

        const online = newMarker(AMap, {
          position: [newValue.longitude, newValue.latitude],
          offset: new AMap.Pixel(-10, -95),
          icon: onlineIcon,
          zIndex: 100,
          extData: {
            id: "online",
          },
        });

        // 添加持续的水波纹动画效果
        const startContinuousRipple = (marker) => {
          // 清除之前的水波纹
          if (this.rippleAnimationInterval) {
            clearInterval(this.rippleAnimationInterval);
            this.rippleAnimationInterval = null;
          }

          // 获取水波纹容器
          setTimeout(() => {
            const rippleContainer = marker
              .getContentDom()
              .querySelector(".ripple-container");
            if (rippleContainer) {
              // 创建持续的水波纹动画
              this.rippleAnimationInterval = setInterval(() => {
                const position = marker.getPosition();
                // 检查位置是否有效
                if (
                  !position ||
                  (position.getLng &&
                    (isNaN(position.getLng()) || isNaN(position.getLat())))
                ) {
                  console.warn("无效的标记位置，跳过水波纹创建:", position);
                  return;
                }
                // 使用蓝色水波纹，更适合表示自己的位置
                this.createRippleEffect(position, this.rippleColors.self, {
                  maxRadius: 60,
                  initialOpacity: 0.9,
                  animationDuration: 1000,
                  rippleCount: 2,
                  strokeWeight: 2,
                  fillOpacity: 0.6,
                  containerElement: rippleContainer,
                });
              }, 2000); // 每2秒创建一组新的水波纹

              // 立即创建第一组水波纹
              const firstPosition = marker.getPosition();
              if (
                firstPosition &&
                (!firstPosition.getLng ||
                  (!isNaN(firstPosition.getLng()) &&
                    !isNaN(firstPosition.getLat())))
              ) {
                this.createRippleEffect(firstPosition, this.rippleColors.self, {
                  maxRadius: 60,
                  initialOpacity: 0.9,
                  animationDuration: 1000,
                  rippleCount: 2,
                  strokeWeight: 2,
                  fillOpacity: 0.6,
                  containerElement: rippleContainer,
                });
              }

              // 存储interval ID以便后续清除
              this.pulseAnimationInterval = this.rippleAnimationInterval;
            }
          }, 50);
        };

        // 启动持续水波纹动画
        startContinuousRipple(avatar);

        // 启动头像脉动动画
        this.startAvatarPulseAnimation(avatar);

        // 添加点击事件
        avatar.on("click", () => {
          this.showMeDesc = !this.showMeDesc;
          this.overlay_me.eachOverlay((overlay, index) => {
            const extData = overlay.getExtData();
            const { id } = extData;
            if (this.showMeDesc) {
              if (id !== "avatar") {
                overlay.show();
              }
            } else {
              if (id !== "avatar") {
                overlay.hide();
              }
            }
          });
        });

        // 添加标记到地图
        this.overlay_me.addOverlay(avatar);
        this.overlay_me.addOverlay(Dlmarker);
        // this.overlay_me.addOverlay(Fzmarker)
        this.overlay_me.addOverlay(Sdmarker);
        // this.overlay_me.addOverlay(online)
        Dlmarker.hide();
        Fzmarker.hide();
        Sdmarker.hide();
        online.hide();
        myMap.setCenter([newValue.longitude, newValue.latitude]);
      } else {
        // 清除之前的脉动动画
        if (this.pulseAnimationInterval) {
          clearInterval(this.pulseAnimationInterval);
          this.pulseAnimationInterval = null;
        }

        if (this.avatarPulseAnimation) {
          cancelAnimationFrame(this.avatarPulseAnimation);
          this.avatarPulseAnimation = null;
        }

        const lastPosition = this.overlay_me.getOverlays()[0].getPosition();
        const endLnglat = new AMap.LngLat(this.me.longitude, this.me.latitude);
        const from = turf.point([lastPosition.lng, lastPosition.lat]);
        const to = turf.point([endLnglat.lng, endLnglat.lat]);
        const options = {
          units: "kilometers",
        };
        const distance = turf.distance(from, to, options);
        const speed = distance * ((3600 * 1000) / 2000);
        // this.meMarket.moveTo(endLnglat, speed)

        this.overlay_me.eachOverlay((overlay, index) => {
          const extData = overlay.getExtData();
          const { id } = extData;
          if (id === "avatar") {
            // 检查头像是否需要更新
            const contentDom = overlay.getContentDom();
            if (contentDom) {
              const avatarImg = contentDom.querySelector("img");
              if (avatarImg && avatarImg.src !== newValue.avatar) {
                // 更新头像图片
                avatarImg.src = newValue.avatar;
              }
            } else {
              // 如果没有内容DOM，可能是旧的标记方式，重新创建HTML内容
              const avatarHtml = `
                <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
                  <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                  <img src="${newValue.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                </div>
              `;
              overlay.setContent(avatarHtml);
            }

            // 重新启动脉动动画
            const startContinuousRipple = (marker) => {
              // 清除之前的水波纹
              if (this.rippleAnimationInterval) {
                clearInterval(this.rippleAnimationInterval);
                this.rippleAnimationInterval = null;
              }

              // 获取水波纹容器
              setTimeout(() => {
                const rippleContainer = marker
                  .getContentDom()
                  .querySelector(".ripple-container");
                if (rippleContainer) {
                  // 创建持续的水波纹动画
                  this.rippleAnimationInterval = setInterval(() => {
                    const position = marker.getPosition();
                    // 检查位置是否有效
                    if (
                      !position ||
                      (position.getLng &&
                        (isNaN(position.getLng()) || isNaN(position.getLat())))
                    ) {
                      console.warn("无效的标记位置，跳过水波纹创建:", position);
                      return;
                    }
                    // 使用蓝色水波纹，更适合表示自己的位置
                    this.createRippleEffect(position, this.rippleColors.self, {
                      maxRadius: 60,
                      initialOpacity: 0.9,
                      animationDuration: 1000,
                      rippleCount: 2,
                      strokeWeight: 2,
                      fillOpacity: 0.6,
                      containerElement: rippleContainer,
                    });
                  }, 2000); // 每2秒创建一组新的水波纹

                  // 立即创建第一组水波纹
                  const firstPosition = marker.getPosition();
                  if (
                    firstPosition &&
                    (!firstPosition.getLng ||
                      (!isNaN(firstPosition.getLng()) &&
                        !isNaN(firstPosition.getLat())))
                  ) {
                    this.createRippleEffect(
                      firstPosition,
                      this.rippleColors.self,
                      {
                        maxRadius: 60,
                        initialOpacity: 0.9,
                        animationDuration: 1000,
                        rippleCount: 2,
                        strokeWeight: 2,
                        fillOpacity: 0.6,
                        containerElement: rippleContainer,
                      }
                    );
                  }
                }
              }, 50);
            };

            // 启动水波纹动画
            startContinuousRipple(overlay);
          }
          if (id === "Sdmarker") {
            overlay.setContent(
              `<div style="position:relative;top:33px;">
							<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)"
							src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
							<div style="transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(
                speed
              ).toFixed(0)}<div>km/h</div>
							</div>`
            );
            overlay.setOffset(new AMap.Pixel(-40, -40)); // 确保更新位置时也修正偏移量
          }
          if (id === "Dlmarker") {
            let dlImg = "";
            if (newValue.electricity > 80) {
              dlImg = dl100;
            } else if (newValue.electricity > 55) {
              dlImg = dl75;
            } else if (newValue.electricity > 30) {
              dlImg = dl50;
            } else {
              dlImg = dl25;
            }
            //电量图片生成
            overlay.setContent(
              `<div style="color:#000;position:relative;top:70px;left:10px">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);fonst-size:12px;line-height:12px;text-align:center">${newValue.electricity}%</div></div>`
            );
            overlay.setOffset(new AMap.Pixel(-20, -40)); // 确保更新位置时也修正偏移量
          }
          // this.overlay_me.removeOverlay(overlay)
          overlay.moveTo(endLnglat, speed);
          // overlay.setPosition(new AMap.LngLat(newValue.longitude, newValue.latitude))
          // this.overlay_me.addOverlay(overlay)
        });

        // 更新头像位置后重新启动动画
        setTimeout(() => {
          // 获取更新后的头像标记
          this.overlay_me.eachOverlay((overlay) => {
            const extData = overlay.getExtData();
            if (extData && extData.id === "avatar") {
              // 重新启动头像脉动动画
              this.startAvatarPulseAnimation(overlay);
            }
          });
        }, 100);
      }
      return;
      const meIcon = newIcon(AMap, {
        image: this.showMeDesc ? newValue.map_user_mark : newValue.avatar,
        size: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
        imageSize: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
      });
      if (!this.meMarket) {
        this.meMarket = newMarker(AMap, {
          position: [newValue.longitude, newValue.latitude],
          offset: this.showMeDesc
            ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
            : new AMap.Pixel(-avatarW / 2, -avatarH),
          icon: meIcon,
          zIndex: 100,
        });
        // this.meMarket.setSize(new AMap.Size(150, 63))
        this.meMarket.setMap(myMap);
        myMap.setCenter([newValue.longitude, newValue.latitude]);
        this.meMarket.on("click", () => {
          this.triggerMeInfo();
        });
      } else {
        this.meMarket.setIcon(meIcon);
        const lastPosition = this.meMarket.getPosition();
        console.log(lastPosition, "lastPosition");
        const endLnglat = new AMap.LngLat(this.me.longitude, this.me.latitude);
        const from = turf.point([lastPosition.lng, lastPosition.lat]);
        const to = turf.point([endLnglat.lng, endLnglat.lat]);
        const options = {
          units: "kilometers",
        };
        const distance = turf.distance(from, to, options);
        const speed = distance * ((3600 * 1000) / 2000);
        this.meMarket.moveTo(endLnglat, speed);
        // this.meMarket.setPosition()
      }
    },
    // 切换用户展示
    triggerMeInfo() {
      this.showMeDesc = !this.showMeDesc;
      const icon = this.meMarket.getIcon();
      const setIcon = this.showMeDesc ? this.me.map_user_mark : this.me.avatar;

      const changeIcon = newIcon(AMap, {
        image: setIcon,
        size: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
        imageSize: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
      });
      this.meMarket.setIcon(changeIcon);
      this.meMarket.setOffset(
        this.showMeDesc
          ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
          : new AMap.Pixel(-avatarW / 2, -avatarH)
      );
    },
    /**
     *  view 层 弹窗回调
     * 	moon 变动回调
     */
    receive_toolEvent(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      const { act, params } = newValue;
      switch (act) {
        case "moonPopupClose":
          this.moon_modal && this.moon_modal.hide();
          break;
        case "visualAngle":
          myMap.setCenter([params.lng, params.lat]);
          break;
        case "housePopupClose":
          this.house_modal && this.house_modal.hide();
          break;
        case "move-view-center":
          if (params) {
            myMap && myMap.setCenter([params.lnogitude, params.latitude]);
          }
          break;
        case "poiMapAddMarker":
          if (myMap) {
            this.poiMarket = newMarker(AMap, {
              position: [params.lnglat.lng, params.lnglat.lat],
              offset: new window.AMap.Pixel(-25, -63),
              zIndex: 100,
            });
            // this.meMarket.setSize(new AMap.Size(150, 63))
            this.poiMarket.setMap(myMap);
            myMap.setCenter([params.lnglat.lng, params.lnglat.lat]);
          }
          break;
        case "person_guanzhu":
          if (myMap) {
            const markers = this.cluster_person.getMarkers();
            markers.forEach((item) => {
              if (params.uid == item.De.extData.uid) {
                item.setExtData({
                  ...params,
                });
              }
            });
          }
          break;
        case "mapStyleChange":
          if (myMap) {
            myMap.setMapStyle(params);
          }
          break;
        case "person_GhostSwitch":
          if (myMap) {
            console.log("[[[[[params]]]]]", params);
            const markers = this.cluster_person.getMarkers();
            markers.forEach((item) => {
              if (params.uid == item.De.extData.uid) {
                item.setExtData({
                  ...params,
                });
              }
            });
          }
          break;
        case "clearPersonSelection":
          // 清空选择的用户数组
          if (
            this.person_showDetail_arr &&
            this.person_showDetail_arr.length > 0
          ) {
            // 获取选中的用户标记
            const markers = this.cluster_person.getMarkers();
            markers.forEach((marker) => {
              // 查找当前选中的标记
              if (
                marker &&
                marker.De &&
                marker.De.extData &&
                this.person_showDetail_arr.includes(marker.De.extData.uid)
              ) {
                try {
                  // 获取标记的内容DOM
                  const contentDom = marker.getContentDom();
                  if (contentDom) {
                    // 清除水波纹容器中的所有元素
                    const rippleContainer =
                      contentDom.querySelector(".ripple-container");
                    if (rippleContainer) {
                      rippleContainer.innerHTML = "";
                    }

                    // 重置头像的变换样式
                    const avatarImg = contentDom.querySelector("img");
                    if (avatarImg) {
                      avatarImg.style.transform = "matrix(1, 0, 0, 1, 0, 0)";
                    }
                  }

                  // 恢复正常大小的头像HTML
                  const normalAvatarHtml = `
                    <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
                      <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                      <img src="${marker.De.extData.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
                    </div>
                  `;

                  // 设置正常大小头像
                  marker.setContent(normalAvatarHtml);
                  marker.setOffset(new AMap.Pixel(-avatarW / 2, -avatarH));
                } catch (e) {
                  console.error("清理标记动画时出错:", e);
                }
              }
            });

            // 清除所有水波纹动画
            if (this.rippleIntervals && this.rippleIntervals.length > 0) {
              this.rippleIntervals.forEach((interval) => {
                clearInterval(interval);
              });
              this.rippleIntervals = [];
            }

            // 清除所有水波纹圆圈
            if (this.rippleCircles && this.rippleCircles.length > 0) {
              this.rippleCircles.forEach((circle) => {
                if (circle) {
                  myMap && myMap.remove(circle);
                }
              });
              this.rippleCircles = [];
            }

            // 清除头像脉动动画
            if (this.avatarPulseAnimation) {
              cancelAnimationFrame(this.avatarPulseAnimation);
              this.avatarPulseAnimation = null;
            }

            // 清除头像脉动计时器
            if (this.avatarPulseTimer) {
              clearTimeout(this.avatarPulseTimer);
              this.avatarPulseTimer = null;
            }
          }

          // 清空选择数组
          this.person_showDetail_arr = [];
          break;
        default:
          break;
      }
    },
    /**
     *  重置地图月亮
     * 	moon 变动回调
     */
    receive_Moon(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;

      const markers = newValue.map((item) => {
        const moonIcon = newIcon(AMap, {
          image: config.ossBaseUrl + "config/map/moon.png",
          size: new AMap.Size(30, 30),
          imageSize: new AMap.Size(30, 30),
        });
        const moonMarket = newMarker(AMap, {
          position: [item.longitude, item.latitude],
          icon: moonIcon,
          extData: item,
          offset: new AMap.Pixel(-15, -30),
          zIndex: 30,
        });

        moonMarket.on("click", () => {
          this.moon_modal && this.moon_modal.hide();
          myMap.setCenter([item.longitude, item.latitude]);
          this.moon_modal = new AMap.Marker({
            position: new AMap.LngLat(item.longitude, item.latitude),
            offset: new AMap.Pixel(-71, -120), //设置文本标注偏移量
            zIndex: 200,
            content: `<div style='width:122px;height:50px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
									<div style="
												font-size: 12px;
												font-family: Source Han Sans-Bold, Source Han Sans;
												font-weight: 700;
												color: #FFFFFF;
												line-height: 46rpx;">
												留宿地
									</div>
									<div style="position:absolute;
												right:10px;
												top:10px; 
									"onclick="openSettingMoon('${item.id},${item.location},${item.remark}')">
										<img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
									</div>
									<div style="
												font-size: 10px;
												font-family: Source Han Sans-Regular, Source Han Sans;
												font-weight: 400;
												color: rgba(255,255,255,0.64);
												line-height: 50rpx;
												margin-top:5px;
											">
												你在那里待了${item.stay_num}晚
												<div>${this.timeFmt(item.last_day, "yyyy年M月D号")}</div>
									</div>
									<div style="width: 0;
												height: 0;
												border: 10px solid;
												border-color: #000 transparent transparent transparent;
												position:absolute;
												left:50%;
												bottom:-20px;
												transform:translateX(-50%);
												">
									</div>
										
								
								</div>`,
          });
          myMap.add(this.moon_modal);
          // 显示月亮弹窗
        });

        return moonMarket;
      });
      this.overlay_moon && this.overlay_moon.clearOverlays();
      this.moon_modal && this.moon_modal.hide();
      this.overlay_moon && this.overlay_moon.addOverlays(markers);
    },
    timeFmt(date = "", fmt) {
      try {
        date = new Date(date || "");

        var a = ["日", "一", "二", "三", "四", "五", "六"];
        var o = {
          "M+": date.getMonth() + 1, // 月份
          "D+": date.getDate(), // 日
          "h+": date.getHours(), // 小时
          "m+": date.getMinutes(), // 分
          "s+": date.getSeconds(), // 秒
          "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
          S: date.getMilliseconds(), // 毫秒
          w: date.getDay(), // 周
          W: a[date.getDay()], // 大写周
          T: "T",
        };
        if (/(y+)/.test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        }
        for (var k in o) {
          if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length === 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
          }
        }
        return fmt;
      } catch (err) {}
    },
    // 点击商家模型
    click3dgltf(ev) {
      const pixel = ev.pixel;
      const px = new AMap.Pixel(pixel.x, pixel.y);
      const obj =
        myMap.getObject3DByContainerPos(px, [this.object3DlayerStore], false) ||
        {};
      if (obj && obj.object) {
        const meshId = obj.object.id;

        this.storeMeshes.forEach(async (item) => {
          if (item && item.layerMesh) {
            for (let i = 0; i < item.layerMesh.length; i++) {
              if (meshId === item.layerMesh[i].id) {
                const extData = item.gltf.option.extData;
                console.log("extData", extData);
                const lng = extData.Longitude;
                const lat = extData.Latitude;

                const startLocation = [this.me.longitude, this.me.latitude];
                const endLocation = [extData.Longitude, extData.Latitude];
                const result = await this.getDriver(startLocation, endLocation);
                if (result.info == "OK") {
                  const distance = result.routes[0].distance;
                  extData.distance =
                    distance && distance > 1000
                      ? (distance / 1000).toFixed(2) + " 千米"
                      : distance + " 米" || 0 + " 米";
                  // this.$refs.storePopup.open(extData)
                  this.callMethod("to_merchant_store", {
                    ...extData,
                  });
                }
              }
            }
          }
        });
      }
    },
    // 商家回调
    receive_MerchantStore(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      map_merchantstore = newValue;

      this.overlay_merchant.clearOverlays();
      // this.overlay_merchant && this.overlay_merchant.clearMarkers()

      const markerIcons = [
        {
          id: "20",
          url: "http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png",
          // url:"http://img.lluuxiu.com/photo/20241212/69a74480-2d3d-435b-84a7-3650dd216b60.png",
        },
        {
          id: "13",
          url: "http://img.lluuxiu.com/photo/20241205/b69c5d91-d2f8-4dc4-86a8-99d58f465fd8.png",
          // url:"http://img.lluuxiu.com/photo/20241212/0acc9f43-0551-404f-9b18-af3d242070a1.png",
        },
        {
          id: "10",
          url: "http://img.lluuxiu.com/photo/20241205/9b2c6359-4713-4662-91fd-15e73bcd81a1.png",
          // url:"http://img.lluuxiu.com/photo/20241212/30b82e73-59e0-46f0-9855-e0e41dffa798.png"
        },
        {
          id: "3",
          url: "http://img.lluuxiu.com/photo/20241205/b19ad54a-ec0c-462c-b0d5-8ebd8a686d0e.png",
          // url:"http://img.lluuxiu.com/photo/20241212/c1564f9a-61cd-4ab6-af92-08e28b2e4682.png",
        },
      ];
      map_merchantstore.forEach((item) => {
        var level = item.Name.split(":")[1] - 1;
        // var level = 3;
        const iconUrl = markerIcons[level].url || markerIcons[0].url; // 如果没有匹配的level，使用默认图标
        const icon_per = new AMap.Icon({
          // image: "http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png",
          image: iconUrl,
          //          size: new AMap.Size(100, 220), // 设置图标大小，例如 50x50 像素
          //          imageSize: new AMap.Size(100, 220), // 设置图标实际大小，例如 50x50 像素
          // anchor: new AMap.Pixel(50, 110),
          size: new AMap.Size(100, 100), // 设置图标大小，例如 50x50 像素
          imageSize: new AMap.Size(100, 100), // 设置图标实际大小，例如 50x50 像素
          anchor: new AMap.Pixel(50, 50),
        });
        const div = document.createElement("div");
        const img = document.createElement("img");
        img.style.width = "100px";
        img.style.height = "100px";
        img.src = markerIcons[level].url || markerIcons[0].url;
        div.appendChild(img);

        const marker_per = new AMap.Marker({
          position: [item.Longitude, item.Latitude],
          // offset: new AMap.Pixel(-15, -15),
          offset: new AMap.Pixel(-50, -50),
          icon: icon_per,
          // content:div,
          extData: {
            item: item,
            id: markerIcons[level].id,
          },
          zIndex: 90,
        });
        marker_per.on("click", (ev) => {
          this.callMethod("to_merchant_store", ev.target.De.extData.item);
          myMap.setCenter([item.Longitude, item.Latitude]);
          myMap.setZoom(15);
        });
        this.storeMarkers.push(marker_per);
      });
      this.overlay_merchant &&
        this.overlay_merchant.addOverlays(this.storeMarkers);
    },
    // 创建水波纹效果
    createRippleEffect(
      position,
      color = this.rippleColors.reset,
      options = {}
    ) {
      // 验证position参数是否有效
      if (
        !position ||
        typeof position !== "object" ||
        (Array.isArray(position) &&
          (position.length < 2 || isNaN(position[0]) || isNaN(position[1])))
      ) {
        console.warn("无效的位置坐标", position);
        return;
      }

      // 确保position是数组或AMap.LngLat对象
      let lng, lat;
      if (Array.isArray(position)) {
        [lng, lat] = position;
      } else if (position.getLng && position.getLat) {
        // 处理AMap.LngLat对象
        lng = position.getLng();
        lat = position.getLat();
        position = [lng, lat]; // 转换为数组格式，以便后续处理
      } else {
        console.warn("无法解析坐标对象:", position);
        return;
      }

      // 再次验证坐标值
      if (isNaN(lng) || isNaN(lat)) {
        console.warn("坐标值无效:", lng, lat);
        return;
      }

      // 合并默认参数和传入的参数
      const {
        maxRadius = this.rippleEffectParams.maxRadius,
        initialRadius = this.rippleEffectParams.initialRadius,
        initialOpacity = this.rippleEffectParams.initialOpacity,
        animationDuration = this.rippleEffectParams.animationDuration,
        rippleCount = this.rippleEffectParams.rippleCount,
        strokeWeight = this.rippleEffectParams.strokeWeight,
        fillOpacity = this.rippleEffectParams.fillOpacity,
        yOffset = this.rippleEffectParams.yOffset,
        xOffset = this.rippleEffectParams.xOffset,
        gradientColors = this.rippleEffectParams.gradientColors,
        rotationAngle = this.rippleEffectParams.rotationAngle,
        shape = this.rippleEffectParams.shape,
        animationType = this.rippleEffectParams.animationType,
        zIndex = this.rippleEffectParams.zIndex,
        containerElement = null,
        strokeColor = this.rippleEffectParams.strokeColor,
        boxShadow = this.rippleEffectParams.boxShadow,
      } = options;

      // 清除之前的水波纹
      for (let i = 0; i < this.rippleIntervals.length; i++) {
        clearInterval(this.rippleIntervals[i]);
      }
      this.rippleIntervals = [];

      // 如果要在DOM元素中创建水波纹效果
      if (containerElement) {
        // 清除之前的圆圈
        containerElement.innerHTML = "";

        // 为每个水波纹层创建定时器
        for (let i = 0; i < rippleCount; i++) {
          setTimeout(() => {
            // 创建圆圈元素
            const circle = document.createElement("div");
            circle.style.position = "absolute";
            // 根据shape参数设置形状
            circle.style.borderRadius = shape === "circle" ? "50%" : "30%";
            // 应用旋转角度
            if (rotationAngle !== 0) {
              circle.style.transform = `rotate(${rotationAngle}deg)`;
            }
            // 设置z-index
            circle.style.zIndex = zIndex;

            // 使用渐变背景
            if (gradientColors && gradientColors.length >= 2) {
              circle.style.background = `linear-gradient(to right, ${gradientColors[0]} 10%, ${gradientColors[1]} 70%)`;
            } else {
              circle.style.backgroundColor = color;
            }

            // 只在strokeWeight > 0时添加边框
            if (strokeWeight > 0) {
              circle.style.border = `${strokeWeight}px solid ${color}`;
            }

            // 设置填充颜色
            circle.style.opacity = initialOpacity * fillOpacity;

            // 初始样式
            circle.style.width = `${initialRadius * 2}px`;
            circle.style.height = `${initialRadius * 2}px`;
            circle.style.left = `${avatarW / 2 - initialRadius + xOffset}px`;
            circle.style.top = `${avatarH / 2 - initialRadius + yOffset}px`;

            containerElement.appendChild(circle);

            // 设置动画
            const startTime = Date.now();
            const interval = setInterval(() => {
              const elapsedTime = Date.now() - startTime;
              const progress = Math.min(elapsedTime / animationDuration, 1);

              // 计算当前半径
              let currentRadius;
              // 简单的线性进展，不添加波动效果
              currentRadius =
                initialRadius + (maxRadius - initialRadius) * progress;

              // 根据动画类型设置不透明度
              let currentOpacity, currentFillOpacity;
              if (animationType === "pulse") {
                // 脉冲效果：先增加再减少
                currentOpacity = initialOpacity * Math.sin(progress * Math.PI);
                currentFillOpacity = fillOpacity * Math.sin(progress * Math.PI);
              } else {
                // 更平滑的淡出效果
                // 使用指数函数实现更自然的淡出效果
                const fadeOutFactor = Math.pow(1 - progress, 2);
                currentOpacity = initialOpacity * fadeOutFactor;
                currentFillOpacity = fillOpacity * fadeOutFactor;
              }

              // 更新样式
              circle.style.width = `${currentRadius * 2}px`;
              circle.style.height = `${currentRadius * 2}px`;
              circle.style.left = `${avatarW / 2 - currentRadius + xOffset}px`;
              circle.style.top = `${avatarH / 2 - currentRadius + yOffset}px`;
              circle.style.opacity = currentOpacity * fillOpacity;
              circle.style.boxShadow =
                boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)";

              // 动画完成后立即清除该层水波纹
              if (progress >= 1) {
                clearInterval(interval);
                circle.remove();

                // 从水波纹定时器数组中移除此定时器
                const index = this.rippleIntervals.indexOf(interval);
                if (index > -1) {
                  this.rippleIntervals.splice(index, 1);
                }
              }
            }, 16); // 约60fps

            this.rippleIntervals.push(interval);
          }, i * (animationDuration / 2)); // 第一个动画执行到一半时第二个动画开始
        }
      } else {
        // 在地图上创建水波纹效果
        this.rippleCircles = [];

        // 为每个水波纹层创建定时器
        let previousAnimationStartTime = Date.now();

        const createNextRipple = (index) => {
          if (index >= rippleCount) return;

          // 创建地图覆盖物，根据shape参数决定形状
          const side = initialRadius * 2;
          const center = position;
          let mapOverlay;

          try {
            // 再次检查坐标的有效性
            if (isNaN(center[0]) || isNaN(center[1])) {
              console.warn("创建覆盖物时检测到无效的坐标", center);
              return;
            }

            if (shape === "circle") {
              // 创建圆形覆盖物
              mapOverlay = new AMap.Circle({
                center: new AMap.LngLat(
                  parseFloat(center[0]),
                  parseFloat(center[1])
                ),
                radius: initialRadius,
                strokeColor: strokeColor || "rgba(255, 255, 255, 0.8)",
                strokeWeight: strokeWeight,
                strokeOpacity: strokeWeight > 0 ? initialOpacity : 0,
                fillColor:
                  gradientColors && gradientColors.length >= 2
                    ? gradientColors[1]
                    : color,
                fillOpacity: fillOpacity,
                zIndex: zIndex,
                extData: {
                  customStyle: {
                    boxShadow:
                      options.boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)",
                  },
                },
              });
            } else {
              // 创建正方形覆盖物
              let bounds = [
                parseFloat(center[0]) - initialRadius / 10000, // 左下角经度
                parseFloat(center[1]) - initialRadius / 10000, // 左下角纬度
                parseFloat(center[0]) + initialRadius / 10000, // 右上角经度
                parseFloat(center[1]) + initialRadius / 10000, // 右上角纬度
              ];

              // 检查bounds中的值是否都有效
              if (bounds.some((coord) => isNaN(coord))) {
                console.warn("计算矩形边界时出现无效坐标", bounds);
                return;
              }

              mapOverlay = new AMap.Rectangle({
                bounds: new AMap.Bounds(
                  [bounds[0], bounds[1]],
                  [bounds[2], bounds[3]]
                ),
                strokeColor: strokeColor || "rgba(255, 255, 255, 0.8)",
                strokeWeight: strokeWeight,
                strokeOpacity: strokeWeight > 0 ? initialOpacity : 0,
                fillColor:
                  gradientColors && gradientColors.length >= 2
                    ? gradientColors[1]
                    : color,
                fillOpacity: fillOpacity,
                zIndex: zIndex,
                // 尝试添加样式以实现渐变效果
                extData: {
                  customStyle: {
                    gradient:
                      gradientColors && gradientColors.length >= 2
                        ? true
                        : false,
                    gradientColors: gradientColors,
                    boxShadow:
                      options.boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)",
                  },
                },
              });
            }

            // 如果支持自定义样式，可以尝试通过CSS添加旋转效果
            if (mapOverlay.setOptions && rotationAngle !== 0) {
              try {
                mapOverlay.setOptions({
                  extData: {
                    rotationAngle: rotationAngle,
                  },
                });
              } catch (e) {
                console.log("地图元素不支持旋转参数", e);
              }
            }

            // 添加到地图
            mapOverlay.setMap(myMap);
            this.rippleCircles.push(mapOverlay);

            // 设置动画
            const startTime = Date.now();
            previousAnimationStartTime = startTime;

            const interval = setInterval(() => {
              const elapsedTime = Date.now() - startTime;
              const progress = Math.min(elapsedTime / animationDuration, 1);

              // 计算当前尺寸
              let currentSize;
              // 简单的线性进展，不添加波动效果
              currentSize =
                initialRadius + (maxRadius - initialRadius) * progress;

              // 根据动画类型设置不透明度
              let currentOpacity, currentFillOpacity;
              if (animationType === "pulse") {
                // 脉冲效果：先增加再减少
                currentOpacity = initialOpacity * Math.sin(progress * Math.PI);
                currentFillOpacity = fillOpacity * Math.sin(progress * Math.PI);
              } else {
                // 更平滑的淡出效果
                // 使用指数函数实现更自然的淡出效果
                const fadeOutFactor = Math.pow(1 - progress, 2);
                currentOpacity = initialOpacity * fadeOutFactor;
                currentFillOpacity = fillOpacity * fadeOutFactor;
              }

              if (shape === "circle") {
                // 更新圆形的半径
                mapOverlay.setRadius(currentSize);
              } else {
                // 更新正方形的边界
                const newBounds = [
                  center[0] - currentSize / 10000,
                  center[1] - currentSize / 10000,
                  center[0] + currentSize / 10000,
                  center[1] + currentSize / 10000,
                ];

                mapOverlay.setBounds(
                  new AMap.Bounds(
                    [newBounds[0], newBounds[1]],
                    [newBounds[2], newBounds[3]]
                  )
                );
              }

              mapOverlay.setOptions({
                strokeOpacity: strokeWeight > 0 ? currentOpacity : 0,
                fillOpacity: currentFillOpacity,
              });

              // 当动画进行到一半时，开始下一个水波纹动画
              if (
                progress >= 0.5 &&
                index < rippleCount - 1 &&
                Date.now() - previousAnimationStartTime >=
                  animationDuration * 0.5
              ) {
                createNextRipple(index + 1);
              }

              if (progress >= 1) {
                // 动画完成，移除覆盖物
                myMap.remove(mapOverlay);
                const index = this.rippleCircles.indexOf(mapOverlay);
                if (index > -1) {
                  this.rippleCircles.splice(index, 1);
                }
                clearInterval(interval);

                // 从水波纹定时器数组中移除此定时器
                const intervalIndex = this.rippleIntervals.indexOf(interval);
                if (intervalIndex > -1) {
                  this.rippleIntervals.splice(intervalIndex, 1);
                }
              }
            }, 16); // 约60fps

            this.rippleIntervals.push(interval);
          } catch (e) {
            console.log("创建水波纹效果时出错:", e);
            clearInterval(interval);
          }
        };

        // 开始第一个水波纹动画
        createNextRipple(0);
      }
    },
  },
};
