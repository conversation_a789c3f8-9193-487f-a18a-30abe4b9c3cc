<template>
	<uni-popup class="inputDialog" ref="popup" type="bottom" :mask-click="true" :safe-area="false">
		<view class="from">
			<view class="title">添加</view>
			<input type="text" class="input" v-model="text" placeholder="请输入...">
			<view class="foot1">
				<view class="content">
					<view class="btn1" @click="close">取消</view>
				</view>

				<view class="btn2" @click="submit">确定</view>
			</view>
			<view class="img24" />
			<u-toast ref='notify' />
		</view>
	</uni-popup>
</template>

<script>
	export default {
		data() {
			return {
				text: '',
				option: {}
			}
		},
		mounted() {},
		methods: {
			open(option) {
				this.text = ''
				this.option = option
				console.log(this.option);
				this.$refs.popup.open('bottom')
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			submit() {
				if (!this.text.trim()) {
					this.toast('请输入名称');
					return
				}
				console.log(this.option);
				this.$emit('InputDialogSubmit', {
					text: this.text,
					...this.option
				})
			},
			close() {
				this.$refs.popup.close('center')
			}
		}
	}
</script>

<style scoped lang="scss">
	.inputDialog {
		width: 100vw;
		height: 100vh;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		padding-bottom: 100rpx;

		.from {
			width: 100vw;
			height: 553rpx;
			background-color: #191C26;
			border-radius: 20rpx;
			padding: 32rpx 40rpx;
			box-sizing: border-box;
			border-radius: 24rpx 24rpx 0rpx 0rpx;

			.title {
				font-weight: 700;
				font-size: 38rpx;
				color: #FFFFFF;
				line-height: 55rpx;
			}

			.input {
				margin-top: 80rpx;
				margin-bottom: 123rpx;
				background-color: #2F3341;
				padding: 32rpx;
				border-radius: 10rpx;
				color: #fff;
			}

			.foot1 {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.content {
					width: 317rpx;
					height: 92rpx;
					box-sizing: border-box;
					padding: 4rpx;
					border-radius: 16rpx;
					background-image: -webkit-linear-gradient(#4BC6ED, #BC93F2);
				}

				.btn1 {
					text-align: center;
					width: 100%;
					height: 100%;
					line-height: 82rpx;
					font-size: 32rpx;
					border-radius: 16rpx;
					background-color: #191C26;
				}

				.btn2 {
					width: 317rpx;
					height: 92rpx;
					border: 2rpx solid #fff;
					color: #fff;
					font-weight: 500;
					font-size: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
					border-radius: 16rpx;
				}

				.btn2 {
					border-color: #3fa0f8;
					background-color: #3fa0f8;
				}
			}

		}



	}
</style>