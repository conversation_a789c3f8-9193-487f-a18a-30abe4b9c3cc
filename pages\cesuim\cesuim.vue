<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view id="container"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script module="Cesium" lang="renderjs">
	export default {
		data() {
			return {
				domlist: null, // 动态引入dom标签合集
				viewer: null, // 三维视窗
				tilesetData: null, // 三维数据
				moduleHeight: 0 // 模型距离地表高度
			}
		},
		mounted() {
			this.initResources()
		},
		methods: {
			// 初始化资源，加载后例化cesium
			initResources() {
				if (this.domlist) {
					this.removeResource()
				} else {
					// 动态引入css文件
					const linkDom = document.createElement('link')
					linkDom.rel = "stylesheet"
					linkDom.href = "@/static/Cesium/Widgets/widgets.css"
					// linkDom.href = "https://cesium.com/downloads/cesiumjs/releases/1.84/Build/Cesium/Widgets/widgets.css"
					document.head.appendChild(linkDom)

					// var datS = this.requireResources('script', 'static/dat.gui.js')
					var cesiumS = this.requireResources('script', '@/static/Cesium/Cesium.js', this.initCesium)
					// var cesiumS = this.requireResources('script',
					// 	'https://cesium.com/downloads/cesiumjs/releases/1.84/Build/Cesium/Cesium.js', this.initCesium)
					this.domlist = [linkDom, cesiumS]
				}
			},
			// 动态创建script引用第三方类库
			requireResources(dom, src, callback) {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement(dom)
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = src;
				document.head.appendChild(script);
				callback && (callback instanceof Function) && (script.onload = callback.bind(this));
				return script
			},
			// 移除动态添加的标签，避免重复添加标签
			removeResource() {
				if (this.domlist)
					for (let i = 0; i < this.domlist.length; i++) {
						document.head.removeChild(this.domlist[i])
					}
			},
			// 初始化
			initCesium() {
				// console.log(window.Cesium.VERSION) // APP可运行
				Cesium.Ion.defaultAccessToken =
					'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.tIVkkfA6ym-5u6XdizffpF5o1jrY5rOHk6qL3bLqtZw';

				this.viewer = new Cesium.Viewer('container', {
					terrainProvider: Cesium.createWorldTerrain(), // 创建世界地形
					geocoder: false, //右上角 搜索
					// homeButton: false, //右上角 Home
					// sceneModePicker: false, //右上角 2D/3D切换
					// baseLayerPicker: false, //右上角 地形
					navigationHelpButton: false, //右上角 Help
					animation: false, // 左下角 圆盘动画控件
					timeline: false, //时间轴
					fullscreenButton: false, //右下角 全屏控件
					vrButton: false, // 如果设置为true，将创建VRButton小部件。
					scene3DOnly: true // 每个几何实例仅以3D渲染以节省GPU内存
					// infoBox: false //隐藏点击要素后的提示信息
				})
				this.viewer._cesiumWidget._creditContainer.style.display = 'none'; //隐藏版本信息
				this.viewer.scene.debugShowFramesPerSecond = true; // 显示帧数


				// 请求3dtiles数据
				// this.tilesetData = new Cesium.Cesium3DTileset({
				// 	url: 'http://localhost:9099/wuhan_baimo/tileset.json'
				// });

				// // 数据请求完成后加载
				// this.tilesetData.readyPromise
				// 	.then((tileset) => {
				// 		this.viewer && this.viewer.scene.primitives.add(tileset);
				// 		this.viewer.zoomTo(tileset);
				// 	})
				// 	.otherwise(function(error) {
				// 		console.log(error);
				// 	});
			},
		},
		beforeDestroy() {
			this.removeResource()
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	#container {
		width: 100%;
		height: 500px;
		/* background: #0ff; */
	}
</style>