<template>
	<view>
		<uni-popup ref="sharePopup" type="bottom" :safe-area="false" :z-index="10010" :mask-click="true"
			@maskClick="close">
			<view class="cPopup" :style="{ marginBottom: securityBottom + 'rpx' }" v-if="arr.length">
				<view class="greyCard"></view>
				<view class="text">
					分享至
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view id="demo1" class="scroll-view-item_H" v-for="(item, index) in arr" :key="index">
						<view class="" style="position: relative;">
							<image class="img102" style="border-radius: 50%;" :src="item.avatar" mode="aspectFill"
								@click="setChoose(index)">
							</image>
							<image class="choose img36" src="../../static/images/xuanze.png" mode="" v-if="item.choose">
							</image>
						</view>
						<view class="name">
							{{ item.nickname }}
						</view>
					</view>
				</scroll-view>
				<view class="line"></view>
				<view class="t_display" v-if="flag" style="padding: 0 32rpx;">
					<u-input style="flex: 1;width: 500rpx;" v-model="value" :type="type" :border="false" :height="300"
						:auto-height="autoHeight" :clearable="false" />
					<image class="cover" :src="post.images[0]" mode="aspectFill" v-if="post.images.length">
					</image>
					<view class="rightInfo" v-else>
						{{ post.content }}
					</view>
				</view>

				<view class="cancal" @click="send">
					发送
				</view>
				<view class="" style="height: 66rpx;" />
			</view>
			<view class="noPeople" v-else>
				暂无互关关注好友
			</view>
		</uni-popup>
		<u-toast ref="notify"></u-toast>
	</view>
</template>

<script>
export default {
	name: "share",
	props: {
		post: {
			default: {}
		},
		//底部安全距离
		securityBottom: {
			type: Number,
			default: 0,
		}
	},
	data() {
		return {
			value: "",
			type: 'textarea',
			border: true,
			height: 100,
			autoHeight: true,
			flag: false,
			chooseIds: [],
			arr: []
		};
	},
	methods: {
		toast(title) {
			this.$refs.notify.show({
				title,
				position: "top"
			})
		},
		setChoose(index) {

			console.log(index, 'flag', !this.arr[index].choose);
			this.$set(this.arr, index, {
				...this.arr[index],
				choose: !this.arr[index].choose
			})
			this.chooseIds = this.arr.filter(item => item.choose)
			this.flag = true
		},
		async getData() {
			await this.$http.get('/api/user/friend/get', {}).then(res => {
				this.arr = res.message.list
			})
		},
		async send() {
			this.chooseIds.map(async (item) => {
				if (!item.choose) return

				let img = [];
				if (this.post.images.length >= 1) {
					img = this.post.images.splice(0, 1)
				} else {
					// img = this.post
					img = ''
				}
				const result = await this.$Yxim.msg.sendCustomMsg({
					attach: '占位',
					scene: "p2p", //消息的场景  "p2p" | "team" | "superTeam"
					to: item.im_id, //接收人
					// to: "7r43w6fv1art7g", //接收人
					ext: JSON.stringify({
						url: "/pages/mainText/mainText?momentId=" + this.post
							.moment_id,
						avatar: this.post.user_info.avatar,
						name: this.post.user_info.nickname,
						content: this.post.content,
						img,
						location: this.post.location,
						type: 'dongtai',
						appAvatar: item.avatar
					}),
				});
				this.toast('分享成功')

				await this.$http.post('/api/moment/share', {
					momentId: this.post.moment_id
				})

				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
				const result2 = await this.$Yxim.msg.sendTextMsg({
					scene: "p2p", //消息的场景  "p2p" | "team" | "superTeam"
					to: item.im_id, //接收人
					body: this.value, //发送得文本消息
					ext: JSON.stringify({
						appAvatar: uni.getStorageSync('avatar'),
					})
				});
				this.$store.commit('SET_onMsg', result2)
			})
			this.close()

		},
		close() {
			this.$refs.sharePopup.close()
			this.$emit('close')
		},
		open() {
			this.arr = []
			this.chooseIds = []
			this.flag = false
			this.value = ""
			this.getData()
			this.$refs.sharePopup.open()
		}
	}
}
</script>

<style lang="scss" scoped>
.uni-popup {
	z-index: 10010 !important;
}

/* 确保分享弹窗显示在输入框之上 */
/deep/ .uni-popup {
	z-index: 10010 !important;
}

/deep/ .uni-popup__wrapper {
	z-index: 10010 !important;
}

/deep/ .uni-popup.uni-popup-bottom {
	z-index: 10010 !important;
}

.noPeople {
	padding: 20rpx 32rpx;
	background: #FFFFFF;
	border-top-left-radius: 30rpx;
	border-top-right-radius: 14rpx;
	display: flex;
	align-items: center;
	flex-direction: column;
	height: 350rpx;
	line-height: 350rpx;
	color: #000;

}

.rightInfo {
	padding: 14rpx;
	width: 170rpx;
	height: 200rpx;
	border-radius: 16rpx;
	font-size: 24rpx;
	background: #F7F7F7;
	color: #888888;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 4;
	overflow: hidden;
	word-break: break-all;
}

.cover {
	width: 170rpx;
	height: 200rpx;
	border-radius: 16rpx;
}

.cPopup {
	padding: 20rpx 32rpx;
	background: #FFFFFF;
	border-top-left-radius: 30rpx;
	border-top-right-radius: 14rpx;
	display: flex;
	align-items: center;
	flex-direction: column;

	.icons {
		width: 100%;
		display: flex;
		justify-content: space-around;
		margin-bottom: 128rpx;

		.tit {
			text-align: center;
			font-size: 24rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #626262;
		}
	}

	.line {
		width: 750rpx;
		height: 1rpx;
		background: #EDEDED;
		margin: 40rpx 0;
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
		display: flex;
		align-items: center;
	}

	.scroll-view-item_H {
		display: inline-block;
		text-align: center;
		padding: 22rpx;

		.choose {
			position: absolute;
			bottom: 0;
			right: 0;
		}

		.name {
			transform: translateX(-12rpx);
			text-align: center;
			font-size: 24rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #3D3D3D;
		}
	}

	.text {
		font-size: 34rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #3D3D3D;

	}

	.greyCard {
		width: 102rpx;
		height: 8rpx;
		background: #D5D5D5;
		border-radius: 50rpx;
		margin: 28rpx 0;
	}

	.cancal {
		width: 622rpx;
		margin-top: 24rpx;

		height: 86rpx;
		line-height: 86rpx;
		text-align: center;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 14rpx 14rpx 14rpx 14rpx;
		font-size: 26rpx;
		color: #fff;
	}

	.item {
		margin-top: 27rpx;
		color: rgba(61, 61, 61, 0.82);
		justify-content: flex-start;

		.rightInfo {
			margin-left: 35rpx;
		}

		.disable {

			width: 42rpx;
			height: 42rpx;
		}
	}

	.avatar {
		width: 42rpx;
		height: 42rpx;
		border-radius: 50%;
	}

	.name {
		margin-left: 35rpx;
		font-size: 26rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #232323;
	}
}
</style>