<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">展品{{isEdit?'修改':'添加'}}</view>
		</view>
		<!-- <view v-if="isEdit" class="audit-status">
			<view class="audit-status-title">
				<view>活动审核状态</view>
				<view class="audit-status-title-label1" v-if="status == 1">已通过</view>
				<view class="audit-status-title-label2" v-else-if="status == 2">未通过</view>
			</view>
			<view v-if="status == 2" class="audit-status-content">
				<view class="audit-status-content-title">
					未通过原因：
				</view>
				<view>
					这是一段文字描述不一定几行，底部的告诉就是正常可以撑开的3行文字4行文字都可以
				</view>
			</view>
		</view> -->
		<view class="content-container">
			<view class="title">
				<textarea class="title-input" placeholder="请输入展品名称" v-model="title"></textarea>
				<hr class="title-hr" />
				<view class="title-images">
					<view class="title-big-image-container">
						<image class="title-big-image" :src="cover" mode="widthFix"></image>
					</view>
					<view class="images-add">
						<view class="images-tips">注：禁止发布违法内容</view>
						<view class="images-small">
							<image @click="upload" class="title-small-image" src="../../static/images/vip/image-add.png"
								mode=""></image>
						</view>
					</view>
				</view>

			</view>
			<view class="active-time">
				<view class="title-time">
					<view class="time-left">
						<image class="icon-time" src="../../static/images/vip/time-icon.png" mode=""></image>
						<view class="time-text">指导价格</view>
					</view>
					<view class="time-right">
						<input type='number' style="text-align: right;" v-model="price"
							placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入展品价格" :maxlength="maxlength"
							@input="handleInput"></input>
						<view class="time-text">元</view>
					</view>
				</view>
			</view>
			<view class="active-content">
				<view class="content-title">
					<image class="content-icon" src="../../static/images/vip/iocn-image.png" mode=""></image>
					<view class="content-title-text">展品介绍</view>
				</view>
				<hr class="content-hr" />
				<view class="content-content" v-if="content" v-html="content" @click="goContent"></view>
				<view class="content-content no-content" v-else @click="goContent">描述展品的要求和展品内容等</view>
				<!-- <view class="content-images">
					<view class="content-images-view">
						<image class="content-image-add" src="../../static/images/vip/content-add-image.png" mode=""></image>
						<image class="content-image-item" src="../../static/images/title-image.png" mode=""></image>
					</view>
				</view> -->
			</view>
			<!-- <view class="active-time"></view> -->
			<view class="active-button">
				<button class="button" type="default" @click="createActive">提交</button>
			</view>
		</view>
		<!-- <uv-datetime-picker ref="datetimePicker" v-model="value" mode="date" @confirm="confirm"></uv-datetime-picker>
		<uv-datetime-picker ref="datetimePicker1" v-model="value" mode="date" @confirm="confirm"></uv-datetime-picker> -->
		<!-- 提交成功弹框 -->
		<liu-popup bgColor="#ffffff" type="center" ref="submitSuccess" width="295px" height="140px" radius="20px"
			@open="popOpen">
			<view class="pop">
				<view class="pop-title">商户活动提交成功</view>
				<button class="pop-button" @click="back">返回上一页</button>
			</view>
		</liu-popup>
	</view>
</template>

<script>
	import color from '../../uview-ui/libs/function/color';
	import {
		config
	} from '@/config.js';
	export default {
		data() {
			return {
				title: '',
				cover: '',
				content: '',
				contentPlaceholder: "描述活动的要求和活动内容等",
				begin_time: '',
				end_time: '',
				business_id: '',
				value: Number(new Date()),
				times: 0,
				isTimeLimit: true,
				isEdit: false,
				status: 2,
				price: null, //价格
				inputAmount: 0,
				maxlength: 9
			};
		},
		onShow() {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 前景色值，包括按钮、标题、状态栏的颜色
				backgroundColor: '#000000', // 背景颜色值，包括背景图
				animation: {
					duration: 400,
					timingFunc: 'easeIn'
				}
			});
			if (uni.getStorageSync('activeInfo') != '') {
				let activeInfo = uni.getStorageSync('activeInfo');
				this.title = activeInfo.title;
				this.cover = activeInfo.cover;
				this.begin_time = activeInfo.begin_time;
				this.end_time = activeInfo.end_time;
			}
			if (uni.getStorageSync('content') != '') {
				this.content = uni.getStorageSync('content');
				console.log(this.content);
			}
		},
		onLoad(options) {
			if (options.shopId) {
				this.business_id = options.shopId;
			}
			if (options.activeId) {
				this.isEdit = true;
			}
		},
		computed: {
			// 计算属性用于显示格式化后的价格
			// displayPrice() {
			//   return this.price.toFixed(2);
			// }
		},
		watch: {

		},
		methods: {
			handleInput(e) {
				let value = e.target.value; // input
				let dot = value.indexOf('.'); //包含小数点
				let reg = /^[0-9]+$/; //正整数
				if (dot > -1) {
					this.maxlength = dot + 3; //长度是小数点后两位
				}
				if (reg.test(value)) { //如果是正整数不包含小数点
					this.maxlength = 9;
				}
			},
			replaceInput(event) {
				// 必须在nextTick中
				this.$nextTick(() => {
					var currentValue = event.target.value;
					var matchedValue = currentValue.match(/^(\d*\.?\d{0,2})/g);
					// this.inputAmount = event.target.value.match(/^\d*(\.?\d{0,2})/g)[0]
					if (matchedValue) {
						// 如果匹配到的值与当前值不同，则更新输入框的值
						if (matchedValue[0] !== currentValue) {
							event.target.value = matchedValue[0];
						}
					} else {
						// 如果没有匹配到值，则说明输入了非法字符，清除非法输入
						event.target.value = event.target.value.substring(0, event.target.value.length - 1);
						event.preventDefault(); // 阻止默认行为
					}
				})
			},
			checkAmount(e) {
				// 部分情况 , 事件参数e会有一定区别, 这里只需要 e.detail.value 是输入的那个字符串即可
				// let num = e.detail.value.match(/^\d*(\.?\d{0,2})/g)[0] || null;
				let that = this;
				let num = e.detail.value
				if (num.indexOf(".") == 0) {
					//'首位小数点情况'
					num = num.replace(/[^$#$]/g, "0.");
					num = num.replace(/\.{2,}/g, ".");
				}
				num = num.match(/^\d*(\.?\d{0,2})/g)[0] || null;
				console.log(num.match(/^\d*(\.?\d{0,2})/g)[0])
				console.log(num)
				//重新赋值给input

				// 检查数字是否超过最大值999999999
				if (num !== null) {
					num = parseFloat(num);
					if (num > 999999999) {
						// num = 999999999; // 如果超过最大值，则设置为最大值
						uni.showToast({
							title: '最大只能输入999999999',
							icon: 'none'
						});
						return;
					}
				}

				this.$nextTick(() => {
					this.price = num;
				});
			},

			back() {
				uni.setStorageSync('activeInfo', '');
				uni.setStorageSync('content', '');
				uni.navigateBack({
					delta: 1
				});
				// uni.navigateTo({
				// 	url: '/pages/shop/shop'
				// });
			},
			// 创建展品
			createActive() {
				this.$http
					.post('/api/user/business/exhibits/post', {
						title: this.title,
						cover: this.cover,
						content: this.content,
						business_id: this.business_id,
						price: this.price,
					})
					.then((res) => {
						console.log(res);
						if (res.code == 200) {
							uni.setStorageSync('activeInfo', '');
							uni.setStorageSync('content', '');
							uni.navigateBack()
							// uni.navigateTo({
							// 	url: '/pages/exhibit/exhibit'
							// });
							// 显示提交成功弹框
							// this.openPopup("submitSuccess");
						}
					});
			},
			openPopup(e) {
				console.log(e)
				this.$refs[e].open();
			},
			popOpen() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0 // 动画时长，默认300ms
				});
			},
			// 上传图片
			upload() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let arr = [];
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4
								});
								this.cover = img;
							})
						);
					}
				});
			},
			// 展品介绍
			goContent() {
				uni.setStorageSync('activeInfo', {
					title: this.title,
					cover: this.cover,
					begin_time: this.begin_time,
					end_time: this.end_time,
					business_id: this.business_id
				});
				uni.navigateTo({
					url: '/pages/exhibitAdd/exhibitContent'
				});
			},
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #f5f7fb;
	}

	.border-top {
		border-top: 1px solid #eaeaea;
	}

	.content {

		width: 100%;
		height: 100%;
		overflow: scroll;
		padding-top: 100px;
		background-color: #f5f7fb;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 100px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: fixed;
			z-index: 1000;
			top: 0;
			left: 0;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				z-index: 1;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
			}
		}

		.navigation-zhezhao {
			width: 100%;
			height: 100px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position: fixed;
			z-index: 1000;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			// padding-bottom: 10%;
			padding-top: 3%;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				z-index: 1;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
			}
		}

		.content-container {
			width: 92%;
			margin: 0 auto;
		}

		.audit-status {
			position: relative;
			z-index: 1;
			width: 95%;
			height: calc(100vh - 170px);
			overflow-y: auto;
			margin-left: 2.5%;
			margin-top: 5%;
			padding: 17px 20px;
			box-sizing: border-box;
			border-radius: 10px;
			background: #ffffff;

			.audit-status-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #212121;
				font-family: HarmonyOS Sans;
				font-size: 16px;
				font-weight: 600;
				line-height: 20px;
				letter-spacing: 0px;
				text-align: left;

				.audit-status-title-label1 {
					border-radius: 4px;
					border: 1px solid #E3B772;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #E3B772;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}

				.audit-status-title-label2 {
					border-radius: 4px;
					border: 1px solid #F35323;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #F35323;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}
			}

			.audit-status-content {
				display: flex;
				align-items: flex-start;
				justify-content: space-evenly;
				padding-top: 13px;
				box-sizing: border-box;
				color: #666666;
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 400;
				line-height: 14px;
				border-top: 1px solid #EAEAEA;
				margin-top: 13px;

				.audit-status-content-title {
					width: 45%;
					max-width: 150px;
				}
			}
		}

		.title {
			width: 100%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;
			z-index: 1;

			.title-input {
				width: 90%;
				height: 100%;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin: 5%;
				color: #000;
				border: 0;
			}

			.title-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			.title-images {
				width: 100%;
				height: 190px;
				display: flex;
				justify-content: space-around;
				align-items: center;
			}

			.title-big-image-container {
				width: 140px;
				height: 140px;
				border-radius: 12px;
				overflow: hidden;
			}

			.title-big-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 12px;
			}

			.images-add {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				height: 150px;

				.images-tips {
					width: 100%;
					// height: 100%;
					font-size: 24rpx;
					font-weight: 400;
					color: #666666;
					margin-top: 10%;
				}

				.images-small {
					width: 100%;

					.title-small-image {
						width: 75px;
						height: 75px;
					}
				}
			}

			.title-time {
				display: flex;
				align-items: center;
				justify-content: center;
				padding-top: 5%;
				padding-bottom: 5%;
				width: 94%;

				.time-left {
					margin: 3% auto;
					width: 50%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-time {
						width: 16px;
						height: 16px;
						margin-left: 5%;
					}

					.time-text {
						font-size: 16px;
						font-weight: 400;
						line-height: 22px;
						color: #212121;
						margin-left: 2%;
					}
				}

				.time-right {
					width: 50%;
					display: flex;
					justify-content: flex-end;
					align-items: center;

					.right-icon {
						width: 16px;
						height: 16px;
						margin-right: 5%;
					}

					.time-time {
						// width: 100px;
						font-size: 12px;
						font-weight: 400;
						line-height: 16px;
						color: #989898;
						margin-right: 2%;
						text-align: right;
					}
				}
			}
		}

		.active-content {
			width: 100%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;
			margin-bottom: 25%;

			.content-title {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin: 3% auto;

				.content-icon {
					width: 16px;
					height: 16px;
					margin-left: 5%;
				}

				.content-title-text {
					font-size: 16px;
					font-weight: 400;
					line-height: 22px;
					color: #212121;
					margin-left: 2%;
				}
			}

			.content-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			// .content-content {
			// 	width: 90%;
			// 	font-size: 16px;
			// 	font-weight: 400;
			// 	line-height: 22px;
			// 	color: #989898;
			// 	margin: 3% auto;
			// 	img{
			// 		width: 100%;
			// 		height: 100%;
			// 	}
			// }

			.content-content::v-deep {
				width: 95%;
				color: #212121;
				font-family: 'HarmonyOS Sans';
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				letter-spacing: 0px;
				text-align: left;
				// margin-top: 10px;
				border-radius: 10px;
				// background: #F5F7FB;
				// border: 1px solid #B2ABDA;
				padding: 3%;
				// margin-bottom: 10px;
				// min-height: 200px;
				overflow: hidden;

				p {
					margin-bottom: 16px;
					color: rgb(33, 33, 33);
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 20px;
					letter-spacing: 0px;
					text-align: left;
				}

				img {
					width: 100%;
					height: auto;
					border-radius: 10px;
					margin: 16px 0;
				}
			}

			.no-content {
				color: rgb(152, 152, 152);
				padding: 3% 3% 10% 3%;
				box-sizing: border-box;
			}

			.content-images {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: space-around;
				align-items: center;

				.content-images-view {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;

					.content-image-add {
						width: 75px;
						height: 75px;
						margin: 3%;
					}

					.content-image-item {
						width: 75px;
						height: 75px;
						margin-right: 3%;
					}
				}
			}

		}

		.active-time {
			width: 100%;
			// height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;

			.title-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			.title-time {
				display: flex;
				align-items: center;
				justify-content: center;
				padding-top: 5%;
				padding-bottom: 5%;
				width: 94%;

				.time-left {
					width: 50%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-time {
						width: 16px;
						height: 16px;
						margin-left: 5%;
					}

					.time-text {
						font-size: 16px;
						font-weight: 400;
						line-height: 22px;
						color: #212121;
						margin-left: 2%;
					}
				}

				.time-right {
					width: 50%;
					display: flex;
					justify-content: flex-end;
					align-items: center;

					.time-text {
						font-size: 16px;
						color: rgb(33, 33, 33) !important;
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
						margin-left: 2%;
					}

					/deep/.uni-input-input {
						color: rgb(33, 33, 33) !important;
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
						text-align: right;
					}
				}
			}

		}

		.active-button {
			// width: 100%;
			background-color: #f5f7fb;
			height: 80px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: fixed;
			bottom: 10px;
			left: 0;
			right: 0;
			padding-bottom: 1%;
			padding: 0 20px;
			box-sizing: border-box;

			.button {
				width: 100%;
				height: 52px;
				background-image: url('../../static/images/vip/active-add-button.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border-radius: 10px;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				color: #ffffff;
			}
		}

		// 弹框
		.pop {
			width: 100%;
			height: 100%;
			// background-color: rgba(0, 0, 0, 0.5);
			z-index: 999;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 32px 0 20px 0;
			box-sizing: border-box;

			.pop-title {
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 18px;
				font-weight: 400;
				line-height: 21px;
				letter-spacing: 0px;
				text-align: center;
				margin-bottom: 32px;
				width: 90%;
			}

			.pop-button {
				width: 136px;
				height: 36px;
				box-sizing: border-box;
				border: 1px solid rgb(120, 120, 120);
				border-radius: 60px;
				color: rgb(120, 120, 120);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 400;
				line-height: 36px;
				background-color: transparent;
			}
		}

	}
</style>