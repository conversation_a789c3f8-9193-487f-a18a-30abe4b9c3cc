<template>
  <view class="simple-map-container">
    <!-- 简单地图组件 -->
    <map id="simpleMap" class="map-view" :style="{ width: width, height: height }" :latitude="39.9042"
      :longitude="116.4074" :scale="16" :markers="markers" :show-location="false"></map>
  </view>
</template>

<script>
export default {
  name: 'SimpleMap',
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300rpx'
    },
    location: {
      type: Array,
      default: () => []
    },
    scale: {
      type: Number,
      default: 16
    }
  },
  data() {
    return {
      mapId: 'simpleMap' + Date.now(),
      latitude: 39.9042,
      longitude: 116.4074,
      markers: []
    }
  },
  watch: {
    location: {
      handler(newLocation) {
        console.log('SimpleMap location changed:', newLocation);
        if (newLocation && newLocation.length >= 2) {
          this.longitude = parseFloat(newLocation[0]);
          this.latitude = parseFloat(newLocation[1]);
          this.updateMarkers();
          this.initMap();
        }
      },
      immediate: true
    }
  },
  mounted() {
    console.log('SimpleMap mounted, location:', this.location);
    this.$nextTick(() => {
      this.updateMarkers();
    });
  },
  methods: {

    updateMarkers() {
      console.log('更新地图标记:', this.latitude, this.longitude);
      this.markers = [{
        id: 1,
        latitude: this.latitude,
        longitude: this.longitude,
        iconPath: '/static/images/localtions.png',
        width: 30,
        height: 30,
        callout: {
          content: '当前位置',
          color: '#000',
          fontSize: 12,
          borderRadius: 5,
          bgColor: '#fff',
          padding: 5,
          display: 'ALWAYS'
        }
      }];
    },

    onMapTap(e) {
      console.log('地图点击事件:', e);
      this.$emit('mapTap', e);
    }
  }
}
</script>

<style scoped>
.simple-map-container {
  width: 100%;
  height: 100%;
}

.map-view {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
</style>
