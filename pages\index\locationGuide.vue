<template>
	<view class="guide-bg">
		<view class="guide-content" v-if="step === 1">
			<view class="guide-title">第一步</view>
			<view class="guide-desc">
				在系统弹窗中，选择<text class="highlight">“使用时允许”</text>按钮，启用LightingBall完整功能
			</view>
			<view class="mock-popup" v-show="hasRequest && isIOS">
				<view class="popup-title">允许“LightingBall”使用你的位置？</view>
				<view class="popup-desc">你可以将位置私密地分享给朋友，也可以随时停止分享</view>
				<image class="popup-map" src="@/static/images/index/map.png" mode="widthFix" />
				<view class="popup-btn allow-once">允许一次</view>
				<view class="popup-btn allow-app selected">
					使用App时允许
					<image class="icon-hand" src="@/static/map/hand.png" />
				</view>
				<view class="popup-btn deny">不允许</view>
			</view>
		</view>
		<view class="guide-content" v-if="step === 2">
			<view class="guide-title">第二步</view>
			<view class="guide-desc">
				在权限选项中，选择<text class="highlight">“始终允许”</text>
			</view>
			<view class="mock-popup" v-show="hasRequest && isIOS">
				<view class="popup-title">允许“LightingBall”在你并未使用该App时使用你的位置吗？？</view>
				<view class="popup-desc">你可以将位置私密地分享给朋友，也可以随时停止分享</view>
				<view class="popup-btn allow-once">保持仅使用期间</view>
				<view class="popup-btn allow-app selected">
					更改为始终允许
					<image class="icon-hand" src="@/static/map/hand.png" />
				</view>
			</view>
		</view>
		<view class="guide-content" v-if="step === 3">
			<view class="guide-title">第三步</view>
			<view class="guide-desc">
				在系统弹窗中，选择<text class="highlight">“允许”</text>启用“运动与健身”权限，提升定位精度并记录运动状态
			</view>
			<view class="mock-popup" v-show="hasRequest && isIOS">
				<view class="popup-title">“LightingBall”想访问你的活动与体能训练记录</view>
				<view class="popup-desc">LinhtingBall使用运动传感器来提高定位精度，并在你移动时进行检测</view>
				<view class="popup-btn">不允许</view>
				<view class="popup-btn allow-app selected">
					允许
					<image class="icon-hand" src="@/static/map/hand.png" />
				</view>
			</view>
		</view>
		<button class="start-btn" @tap="openSetting">开始设置</button>
	</view>
</template>

<script>
	import {
		checkHealthPermission,
		requestHealthPermission,
	} from "@/utils/health.js";

	// import {
	// 	isHealthDataAvailable,
	// 	requestAuthorization
	// } from "@/utils/HeathKit.js";

	export default {
		data() {
			return {
				step: 1,
				hasRequest: true,
				useHealthConnect: false,
				hasPermission: false,
				isIOS: false,
				systemInfo: {},
			};
		},
		computed: {
			title() {
				if (this.step === 1) return "第一步";
				if (this.step === 2) return "第二步";
				return "第三步";
			},
		},
		async onLoad(options) {
			this.systemInfo = uni.getSystemInfoSync() || {};
			this.isIOS = this.systemInfo.platform === "ios";
			this.step = options.type - 0 || 1;
		},
		methods: {
			getLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: "wgs84",
						isHighAccuracy: true,
						success: async (res) => {
							this.hasPermissions = true;
							resolve();
						},
						fail: (err) => {
							reject();
						},
					});
				});
			},
			open() {
				this.step = 1;
				this.show = true;
				uni.setStorageSync("locationGuide", true);
			},
			openSetting() {
				this.hasRequest = false;
				if (this.step === 1) {
					// 请求定位权限
					this.getLocation()
						.then(() => {
							this.step++;
							this.hasRequest = true;
						})
						.catch(() => {
							this.step++;
							this.hasRequest = true;
						});
				} else if (this.step === 2) {
					uni.openAppAuthorizeSetting();
					setTimeout(() => {
						this.step++;
						this.hasRequest = true;
					}, 500);
				} else {
					if (this.systemInfo.platform === "android") {
						console.log("请求健康权限");
						requestHealthPermission().then((res) => {
							console.log("自动点击获取体感、健康权限", res);
							if (res) {
								this.close();
							} else {
								this.openAppAuthorizeSetting()
							}
						});
					} else if (this.systemInfo.platform === "ios") {
						// isHealthDataAvailable().then(res => {
						// 	requestAuthorization().then((res) => {
						// 		if (res) {
						// 			this.close()
						// 		}
						// 	}).catch(err => {
						// 		this.openAppAuthorizeSetting()
						// 	})
						// }).catch(err => {
						// 	this.openAppAuthorizeSetting()
						// })
						this.openAppAuthorizeSetting()
						this.close();
					}
					// else {
					// 	// #ifdef HARMONY
					// 	const abilityContext = plus.harmony.abilityContext;
					// 	const permissions = ["ohos.permission.HEALTH_DATA"];
					// 	abilityContext.requestPermissions(permissions, (result) => {
					// 		this.close();
					// 	});
					// 	// #endif
					// }
				}
				// uni.openAppAuthorizeSetting()
			},
			close() {
				uni.navigateBack({
					delta: 1
				})
			},
			openAppAuthorizeSetting() {
				uni.showModal({
					title: "权限被拒绝",
					content: "请在设置中手动开启运动权限来提高定位精度，并在你移动时进行检测",
					confirmText: "去设置",
					success: (res) => {
						// 跳转应用详情页
						if (res.confirm) {
							uni.openAppAuthorizeSetting();
						}
						this.close();
					},
				});
			}
		},
	};
</script>

<style scoped lang="scss">
	.guide-bg {
		z-index: 1000;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: #19191d;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		padding: 100rpx 57rpx 80rpx;
	}

	.guide-content {
		width: 90vw;
		max-width: 600rpx;
		margin: 0 auto;
		padding-top: 80rpx;
		text-align: center;
	}

	.guide-title {
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 18rpx;
	}

	.guide-desc {
		font-weight: 500;
		font-size: 27rpx;
		color: rgba(255, 255, 255, 0.7);
		line-height: 45rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.highlight {
		color: #fff;
		font-weight: bold;
		font-size: 29rpx;
	}

	.mock-popup {
		background: #ededed;
		border-radius: 28rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		margin: 0 auto 40rpx auto;
		width: 100%;
		max-width: 516rpx;
		position: relative;
		margin-top: 42rpx;
	}

	.popup-title {
		font-weight: 800;
		font-size: 29rpx;
		color: #000000;
		line-height: 42rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		padding: 30rpx 30rpx 0;
	}

	.popup-desc {
		font-weight: 500;
		font-size: 25rpx;
		color: #000000;
		line-height: 34rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		padding: 0 30rpx 32rpx;
	}

	.popup-map {
		width: 90%;
		border-radius: 12rpx;
		margin-bottom: 12rpx;
	}

	.popup-btn {
		background: #f2f2f2;
		padding: 20rpx 0;
		text-align: center;
		color: #007aff;
	}

	.popup-btn:last-of-type {
		border-radius: 0 0 28rpx 28rpx;
	}

	.allow-once {
		color: #007aff;
	}

	.allow-app {
		color: #007aff;
		font-weight: bold;
		background: #fff;
	}

	.selected {
		background: #fff;
		color: #007aff;
		position: relative;
		text-align: center;
	}

	.deny {
		color: #222;
		background: #f2f2f2;
	}

	.icon-hand {
		width: 72rpx;
		height: 72rpx;
		position: absolute;
		right: -90rpx;
		top: 50%;
		transform: translateY(-50%);
		z-index: 1001;
	}

	.start-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 16rpx;
		background: linear-gradient(90deg, #ad60ff 0%, #57c7ff 100%);
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		margin: 0 auto;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
		margin-top: 40rpx;
	}
</style>