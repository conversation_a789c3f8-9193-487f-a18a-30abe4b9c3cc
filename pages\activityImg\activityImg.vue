<template>
	<view class="">
		<cover-image class="bg" src="@/static/images/index/bg.png" mode=""></cover-image>
		<!-- <view class="fixed">
			v1.0.0.1
		</view> -->
		<u-toast ref="notify" />
	</view>
</template>

<script>
	import {
		config
	} from "@/config.js";
	import {
		request
	} from "@/utils/request.js";
	const BASE_URL = config.BASE_URL_App;
	export default {
		data() {
			return {};
		},
		onBackPress() {
			return false;
		},
		onHide() {},
		methods: {
			open() {
				const token = uni.getStorageSync("token");
				const that = this;
				if (token) {
					uni.request({
						url: BASE_URL + "/api/user/info",
						method: "get",
						timeout: 5000,
						header: {
							// 'version_type': uni.getSystemInfoSync().platform, //android或者ios
							// 'edition_number': uni.getStorageSync('versionCode'), // 打包时manifest设置的版本号
							"content-type": "application/json",
							Authorization: "Bearer " + token,
						},
						success(res) {
							const req = res.data;
							if (parseInt(req.code) == 401) {
								uni.request({
									url: BASE_URL + "/auth/refresh-token",
									method: "POST",
									timeout: 10000,
									header: {
										"content-type": "application/json",
										Authorization: "Bearer " + uni.getStorageSync("token"),
									},
									data: {
										refresh_token: uni.getStorageSync("RefreshToken"),
									},
									success(res) {
										const req = res.data;
										if (req.code == 200) {
											uni.setStorageSync("token", req.message.AccessToken);
											uni.setStorageSync(
												"RefreshToken",
												req.message.RefreshToken
											);
											uni.reLaunch({
												url: "/pages/index/index",
											});
										} else {
											setTimeout(() => {
												uni.setStorageSync("token", "");
												uni.setStorageSync("RefreshToken", "");
												that.goLogin();
											}, 2000);
										}
									},
									fail(err) {
										if (err.errMsg == "request:fail timeout") {
											console.log("网络超时");
											toast("网络超时");
										}
										console.log("网络超时");
									},
								});
							} else if (req.code == 200) {
								setTimeout(() => {
									that.$emit("goIndex");
									// uni.reLaunch({
									// 	url: '/pages/index/index'
									// })
								}, 3000);
							} else {
								that.$refs.notify.show({
									title: "网络错误",
									position: "top",
								});
								console.log("=======网络错误====================");
								that.goLogin();
							}
						},
						fail(err) {
							if (err.errMsg) {
								that.$refs.notify.show({
									title: "网络超时",
									position: "top",
								});
							}
							console.log("=======fail跳登录===========", JSON.stringify(err));
							that.goLogin();
						},
					});
				} else {
					console.log("=======没有token跳转登录=============");
					that.goLogin();
				}
			},

			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},
			goLogin() {
				// 获取当前页面信息
				const pages = getCurrentPages();
				// 当前页面是pages数组的最后一个
				const currentPage = pages[pages.length - 1];
				// 获取当前页面的路径，不包含参数
				const currentPath = currentPage.route;
				if (currentPath == "pages/login/login") return;
				this.$store.dispatch("SET_YXIM_DISCONNECT");
				this.$emit('goLogin')
				setTimeout(() => {
					uni.navigateTo({
						url: "/pages/login/login",
					});
				}, 300);
			},
		},
	};
</script>

<style scoped>
	.activity-img-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: 999999;
		background-color: #fff;
		transition: opacity 0.3s ease-out;
	}

	.bg {
		width: 100vw;
		height: 100vh;
		z-index: 99999999999;
	}

	.fixed {
		position: fixed;
		bottom: 80rpx;
		left: 48%;
		color: #fff;
		z-index: 9999;
	}
</style>