<template>
	<view>
		<uni-popup ref="popup" type="center" :animation="true">
			<view class="main-text-fullscreen"
				:class="{ 'popup-enter': isEntering, 'popup-enter-active': isEnterActive }" @touchstart="onTouchStart"
				@touchmove="onTouchMove" @touchend="onTouchEnd">
				<u-navbar :customBack="close" back-text="" title="" :background="{ backgroundColor: '#fff' }"
					:border-bottom="false" title-color="#000" back-icon-color="#000">
					<view class="navContent" @click="goNav('/pages/otherPage/otherPage?uuid=' + info.user_info.uuid)">
						<image class="avatarimg" :src="info.user_info.avatar" mode="">
						</image>

						<view class="nickname">{{ info.user_info.nickname }}</view>
						<view class="auth-border" v-if="info.user_info.relation == 2">
							朋友
						</view>
					</view>
					<view slot="right" style="margin-right: 34rpx" @click="rightTap">
						<image src="../../static/images/moreIcon.png" style="width: 13rpx; height: 38rpx" mode="">
						</image>
					</view>
				</u-navbar>
				<Post :showTop="false" :list="[info]" :moreFlag="false" @follow="follow" :followFlag="false"
					:footerFlag="false" timeFormat="yyyy/M/DD hh:mm:ss" :imgHeight="swiperHeights"></Post>
				<!-- 地图组件 -->
				<view class="map-container">

					<!-- 地图组件 -->
					<view class="map-wrapper">
						<FixedMap :width="'100%'" :height="'100%'" :latitude="mapLocation[1] || 39.9042"
							:longitude="mapLocation[0] || 116.4074" :markers="mapMarkers"
							:userAvatar="info.user_info.avatar" :bgImage="avatarBgImage" />
					</view>
				</view>
				<view class="line"></view>
				<!-- <view class="issue"> 共{{ info.comment_total }}条评论 </view> -->
				<Comment :dataArr="reviewArr" @getMore="getMore" @reply="reply" @setCommentLike="setCommentLike"
					@setReplyLike="setReplyLike">
				</Comment>
				<view class="" style="height: 180rpx"></view>

				<!--   <t-loading
      text="加载中.."
      :mask="true"
      :click="true"
      :show="show"
      ref="loading"
    ></t-loading> -->
				<uni-popup ref="replyPopup" type="bottom" :mask-click="true" :z-index="10002" :safe-area="false">
					<view class="heads t_display">
						<view class="inputBg">
							<!-- <image
                class="img24"
                src="../../static/images/editInput.png"
                style="margin-right: 8rpx"
                mode=""
              /> -->
							<!-- :placeholder="placeholder" -->
							<u-input placeholder="说点什么..." v-model="replyVal" :focus="true" type="text" :border="false"
								confirm-type="send" @confirm="confirmReply" style="color: #000 !important"
								:input-style="{ color: '#000' }" />
						</view>
					</view>
				</uni-popup>
				<MyMore ref="MyMore" @first="first" @del="delMoment"></MyMore>
				<MorePopup ref="morePopup" :popupInfo="info"></MorePopup>
				<sharePopup ref="share" :post="info" @close="onShareClose"></sharePopup>
				<SeePopup ref="seePopup" @choose="seePopChoose"></SeePopup>
				<u-toast ref="notify" />
			</view>
		</uni-popup>
		<!-- 固定在底部的输入框，移到popup外部 -->
		<view class="head t_display" v-if="showPopup && !sharePopupVisible">
			<view class="inputBg">
				<u-input style="color: #000 !important" v-model="username" type="text" :border="false"
					confirm-type="send" @confirm="confirm" :input-style="{ color: '#000' }" />
			</view>
			<view class="t_display flootRight" style="">
				<view class="t_display" @click="
					setLike({ momentId: info.moment_id, isLike: info.is_like })
					">
					<image class="img42" src="../../static/images/topic/like.png" mode="" v-if="!info.is_like">
					</image>
					<image class="img42" src="@/static/images/topic/like2.png" mode="widthFix" v-else></image>
					<view class="nums">
						{{ info.like }}
					</view>
				</view>
				<view class="t_display">
					<image class="img42" src="../../static/images/topic/pinglun.png" mode=""></image>
					<view class="nums">
						{{ info.comment_total }}
					</view>
				</view>

				<view class="t_display" @click="share">
					<image class="img42" src="../../static/images/topic/fenxiang.png" mode=""></image>
					<view class="nums">
						{{ info.share_total }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Post from "@/components/post/content.vue";
import Comment from "./components/comment.vue";
import MyMore from "@/pages/my/components/morePopup.vue";
import SeePopup from "@/pages/my/components/seeLook.vue";
import MorePopup from "./components/more.vue";
import sharePopup from "@/components/share/share.vue";
import MapComponent from "@/components/map/index.vue";
import BasicMap from "@/components/simpleMap/basicMap.vue";
import FixedMap from "@/components/fixedMap/fixedMap.vue";
export default {
	components: {
		SeePopup,
		MyMore,
		MorePopup,
		sharePopup,
		Comment,
		Post,
		MapComponent,
		BasicMap,
		FixedMap,
	},
	data() {
		return {
			swiperHeights: 0,
			showPopup: false,
			show: true,
			replyVal: "",
			replyShow: false,
			replyFlag: false,
			reviewArr: [],
			info: {},
			momentId: "",
			placeholder: "",
			username: "",
			// avatarBgImage: require("../../static/images/avatarBg.png"),
			avatarBgImage: '',
			// 动画相关
			isEntering: false,
			isEnterActive: false,
			// 分享弹窗状态
			sharePopupVisible: false,
			// 左滑关闭相关
			touchStartX: 0,
			touchStartY: 0,
			touchStartTime: 0,
			swipeProgress: 0, // 滑动进度 0-1
		};
	},
	computed: {
		// 计算地图位置坐标
		mapLocation() {
			if (this.info.coordinate) {
				console.log('动态位置信息:', this.info.location, this.info.coordinate);
				// coordinate 格式可能是 "longitude,latitude" 字符串
				const coords = this.info.coordinate.split(',');
				if (coords.length === 2) {
					const location = [parseFloat(coords[0]), parseFloat(coords[1])];
					console.log('解析后的坐标:', location);
					return location;
				}
			}
			return [];
		},
		// 计算地图标记
		mapMarkers() {
			const location = this.mapLocation;
			const userAvatar = this.info.user_info && this.info.user_info.avatar;

			if (location.length === 2) {
				return [{
					id: 1,
					latitude: location[1],
					longitude: location[0],
					iconPath: userAvatar || '/static/images/localtions.png',
					avatar: userAvatar,
					width: 40,
					height: 40,
					title: this.info.location || '当前位置',
					isAvatar: !!userAvatar, // 标记这是一个头像
					type: 'avatar'
				}];
			}
			return [{
				id: 1,
				latitude: 39.9042,
				longitude: 116.4074,
				iconPath: userAvatar || '/static/images/localtions.png',
				avatar: userAvatar,
				width: 40,
				height: 40,
				title: '默认位置',
				isAvatar: !!userAvatar,
				type: 'avatar'
			}];
		},
		// 计算用户头像
		userAvatar() {
			const avatar = this.info.user_info && this.info.user_info.avatar;
			console.log('mainText计算用户头像:', avatar);
			return avatar;
		}
	},
	methods: {
		open(data) {
			this.info = data;
			const src = data.images[0];
			console.log("动态详情图片：", src);
			uni.getImageInfo({
				// src: 'https://img.shetu66.com/2023/06/21/1687308186011156.png',
				src,
				success: (res) => {
					// 获取屏幕宽度
					const screenWidth = uni.getSystemInfoSync().windowWidth;
					// 转换为rpx单位的比例
					const rpxRatio = 750 / screenWidth;

					// 获取图片宽高
					const imgWidth = res.width;
					const imgHeight = res.height;

					// 计算宽高比
					const ratio = imgHeight / imgWidth;

					// 计算在当前屏幕宽度下的高度（转换为rpx单位）
					// 减去左右padding的64rpx
					const imageWidth = screenWidth - 64 / rpxRatio;
					let height = imageWidth * ratio * rpxRatio;

					// 限制最大高度为1000rpx
					if (height > 1000) {
						height = 1000;
					}
					console.log("轮播图片高度：", Math.round(height));
					// 只更新当前轮播图的所有图片使用相同高度
					this.$set(this, "swiperHeights", Math.round(height));
				},
				fail: (err) => {
					console.error("获取图片信息失败", err);
				},
			});
			this.momentId = data.moment_id;
			this.getReview();
			// 开始入场动画
			this.startEnterAnimation();
			// 添加对popup引用的检查
			if (this.$refs && this.$refs.popup) {
				this.$refs.popup.open();
				this.showPopup = true;
				// 通知父组件弹窗已打开
				this.$emit("open");
			} else {
				// 如果popup还未加载，使用nextTick确保在DOM更新后再尝试打开
				this.$nextTick(() => {
					if (this.$refs && this.$refs.popup) {
						this.$refs.popup.open();
						this.showPopup = true;
						this.$emit("open");
					} else {
						console.error("popup组件引用不存在");
					}
				});
			}
		},
		goNav(url) {
			uni.navigateTo({
				url
			})
		},
		startEnterAnimation() {
			// 设置初始状态
			this.isEntering = true;
			this.isEnterActive = false;

			// 延迟一帧后开始动画
			this.$nextTick(() => {
				setTimeout(() => {
					this.isEnterActive = true;
				}, 50);
			});
		},

		close() {
			// 重置动画状态
			this.isEntering = false;
			this.isEnterActive = false;

			this.showPopup = false;
			this.$refs.popup.close();
			// 通知父组件弹窗已关闭
			this.$emit("close");
		},
		seePopChoose(role) {
			this.$http
				.post("/api/moment/update-visible", {
					momentId: this.momentId,
					role,
				})
				.then((res) => {
					this.toast("设置成功");
					this.$refs.seePopup.close();
				});
		},
		first() {
			this.$refs.MyMore.close();
			this.$refs.seePopup.open();
		},
		toast(title) {
			this.$refs.notify.show({
				title,
				position: "top",
			});
		},
		delMoment() {
			this.$http
				.post("/api/moment/del", {
					momentId: this.momentId,
				})
				.then((res) => {
					this.toast("删除成功");
					uni.$emit("filterMain", {
						momentId: this.momentId,
					});
					this.close();
					uni.navigateBack();
				});
		},
		follow(item) {
			//"关注","已关注","互相关注"
			console.log("item:", item);
			let url = "";
			const {
				relation,
				uuid
			} = item;
			if (relation == 0) {
				url = "/api/user/follow/add";
			} else if (relation == 1) {
				url = "/api/user/follow/del";
			}
			this.$http
				.post(url, {
					uuid: item.user_info.uuid,
				})
				.then((res) => {
					this.getInfo();
				});
		},
		confirmReply(val) {
			const index = this.replyItem.index;
			this.$http
				.post("/api/moment/reply", {
					commentId: this.replyItem.commentId,
					momentId: this.replyItem.momentId,
					content: val,
					to_uid: this.replyItem.uid,
				})
				.then((res) => {
					this.$set(this.reviewArr, index, {
						...this.reviewArr[index],
						little_reply: [res.message, ...this.reviewArr[index].little_reply],
					});
					this.getInfo();
					this.replyVal = "";
					this.$refs.replyPopup.close();
				});
		},
		reply(item) {
			this.placeholder = "回复" + item.nickname;
			this.replyItem = item;
			this.$refs.replyPopup.open("bottom");
			// 确保弹窗打开后输入框获得焦点
			setTimeout(() => {
				this.replyVal = ""; // 清空之前的输入
			}, 200);
		},

		confirm(val) {
			this.$http
				.post("/api/moment/comment", {
					momentId: this.momentId,
					content: val,
				})
				.then((res) => {
					this.reviewArr.unshift(res.message);
					this.username = "";
					this.getInfo();
				});
		},
		setCommentLike(item) {
			this.$http
				.post("/api/comment/like", {
					commentId: item.comment_id,
					like: item.isLike ? 2 : 1,
				})
				.then((res) => {
					console.log(item);
					this.$set(this.reviewArr, item.index, {
						...this.reviewArr[item.index],
						is_like: !item.isLike,
						like: this.reviewArr[item.index].like + (!item.isLike ? 1 : -1),
					});
				})
				.catch((err) => {
					console.log(err);
				});
		},
		setReplyLike(item) {
			this.$http
				.post("/api/reply/like", {
					replyId: item.reply_id,
					like: item.isLike ? 2 : 1,
				})
				.then((res) => {
					// 更新子评论的点赞状态和数量
					const parentComment = this.reviewArr[item.parentIndex];
					const replyItem = parentComment.little_reply[item.replyIndex];

					// 创建新的子评论对象，更新点赞状态和数量
					const updatedReply = {
						...replyItem,
						is_like: !item.isLike,
						like: replyItem.like + (!item.isLike ? 1 : -1),
					};

					// 更新子评论数组
					const updatedReplies = [...parentComment.little_reply];
					updatedReplies[item.replyIndex] = updatedReply;

					// 更新父评论中的子评论数组
					this.$set(
						this.reviewArr[item.parentIndex],
						"little_reply",
						updatedReplies
					);
				})
				.catch((err) => {
					console.log(err);
				});
		},
		setLike(item) {
			this.$http
				.post("/api/moment/like", {
					momentId: item.momentId,
					like: item.isLike ? 2 : 1,
				})
				.then((res) => {
					this.info.is_like = !item.isLike;
					this.info.like += !item.isLike ? 1 : -1;
				});
		},
		getMore(item) {
			const index = item.index;
			this.$http
				.get("/api/reply/list", {
					commentId: item.commentId,
					maxId: item.maxId || "",
				})
				.then((res) => {
					console.log("展开的全部消息", res.message);

					this.$set(this.reviewArr, index, {
						...this.reviewArr[index],
						little_reply: [
							...this.reviewArr[index].little_reply,
							...res.message,
						],
					});
					// this.info = res.message
				});
		},
		getInfo() {
			this.$http
				.get("/api/moment/detail", {
					momentId: this.momentId,
				})
				.then((res) => {
					this.info = res.message;
				});
		},
		getReview() {
			this.$http
				.get("/api/comment/list", {
					momentId: this.momentId,
					page: 1,
					size: 10,
				})
				.then((res) => {
					// 修改返回的数据，只保留每个评论的第一条回复
					const modifiedData = res.message.map((item) => {
						if (item.little_reply && item.little_reply.length > 1) {
							// 只保留第一条回复
							return {
								...item,
								little_reply: item.little_reply.slice(0, 1),
							};
						}
						return item;
					});

					this.reviewArr = modifiedData;
				});
		},
		share() {
			this.sharePopupVisible = true;
			this.$refs.share.open(this.info);
		},
		onShareClose() {
			this.sharePopupVisible = false;
		},
		// 触摸开始
		onTouchStart(e) {
			const touch = e.touches[0];
			this.touchStartX = touch.clientX;
			this.touchStartY = touch.clientY;
			this.touchStartTime = Date.now();
		},
		// 触摸移动
		onTouchMove(e) {
			const touch = e.touches[0];
			const currentX = touch.clientX;
			const deltaX = currentX - this.touchStartX;

			// 只有从左边缘开始的滑动才计算进度
			if (this.touchStartX <= 50 && deltaX > 0) {
				// 计算滑动进度 (0-1)，滑动150px为100%
				this.swipeProgress = Math.min(deltaX / 150, 1);
			} else {
				this.swipeProgress = 0;
			}
		},
		// 触摸结束
		onTouchEnd(e) {
			const touch = e.changedTouches[0];
			const endX = touch.clientX;
			const endY = touch.clientY;
			const endTime = Date.now();

			// 计算滑动距离和时间
			const deltaX = endX - this.touchStartX;
			const deltaY = endY - this.touchStartY;
			const deltaTime = endTime - this.touchStartTime;

			// 判断是否为左滑手势
			// 条件：1. 从屏幕左边缘开始 2. 向右滑动距离足够 3. 垂直距离不太大 4. 时间不太长
			const isFromLeftEdge = this.touchStartX <= 50; // 从左边缘50px内开始
			const isRightSwipe = deltaX > 100; // 向右滑动超过100px
			const isHorizontal = Math.abs(deltaY) < 100; // 垂直偏移小于100px
			const isFastEnough = deltaTime < 500; // 500ms内完成

			if (isFromLeftEdge && isRightSwipe && isHorizontal && isFastEnough) {
				console.log('检测到左滑关闭手势');
				this.close();
			}

			// 重置滑动进度
			this.swipeProgress = 0;
		},
		rightTap() {
			const userInfo = uni.getStorageSync("userInfo");
			if (this.info.user_info.uuid == userInfo.uuid) {
				this.$refs.MyMore.open();
			} else {
				this.$refs.morePopup.open();
			}
		},
		// 地图点击事件处理
		handleMapLocation(option) {
			console.log('地图点击位置:', option);
			// 可以在这里处理地图点击事件，比如显示详细地址信息
		},
		// 地图移动结束事件处理
		handleMapLocationMoveend(option) {
			console.log('地图移动结束:', option);
			// 可以在这里处理地图移动结束事件
		},
		// 简单地图点击事件处理
		handleMapTap(e) {
			console.log('简单地图点击:', e);
			// 可以在这里处理地图点击事件
		},
	},
};
</script>

<style lang="scss" scoped>
/* 地图容器样式 */
.map-container {
	width: 689rpx;
	height: 200rpx;
	margin: 0 auto;
	margin-bottom: 23rpx;
	border-radius: 23rpx;
	overflow: hidden;
	background: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.map-container /deep/ .uni-map {
	width: 100% !important;
	height: 100% !important;
	position: relative !important;
}

.map-container /deep/ #basicMap {
	width: 100% !important;
	height: 100% !important;
	position: relative !important;
}

.map-wrapper {
	width: 100%;
	height: 300rpx;
	position: relative;
	overflow: hidden;
}

/* 强制地图组件固定在容器内 */
.map-container /deep/ .uni-map,
.map-wrapper /deep/ .uni-map {
	position: relative !important;
	top: 0 !important;
	left: 0 !important;
	transform: none !important;
}

.map-container /deep/ map,
.map-wrapper /deep/ map {
	position: relative !important;
	top: 0 !important;
	left: 0 !important;
	transform: none !important;
}

.location-info {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f8f8;
	border-bottom: 1rpx solid #eee;
}

.location-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.location-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

/* 保留原来的map样式作为备用 */
.map {
	width: 689rpx;
	height: 141rpx;
	background: #d9d9d9;
	border-radius: 23rpx 23rpx 23rpx 23rpx;
	margin: 0 auto;
	margin-bottom: 23rpx;
}

.line {
	width: 96%;
	margin: 0 auto;
	margin-bottom: 20rpx;
	border: 1rpx solid rgba(51, 53, 59, 0.2);
	padding: 0 10rpx;
}

.flootRight {
	justify-content: space-around;
	width: 215px;
	transform: translateX(18rpx);
}

.item-content {
	font-weight: 500;
	font-size: 27rpx;
	color: #33353b;
	line-height: 40rpx;
}

.navContent {
	display: flex;
	align-items: center;

	.avatarimg {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		margin-right: 15rpx;
	}

	.nickname {
		font-weight: 800;
		font-size: 27rpx;
		color: rgba(51, 53, 59, 0.9);
		line-height: 27rpx;
	}

	.auth-border {
		margin-left: 20rpx;
		padding: 5px 8px;
		cursor: pointer;
		position: relative;
		font-size: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(44.21517382411032deg, #66cdff 0%, #c6b7fd 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		color: transparent;

		// padding: 2rpx 12rpx;
		&::before {
			/* 1 */
			display: block;
			content: "";
			border-radius: 6px;
			border: 1px solid transparent;
			background: linear-gradient(90deg, #4bc6ed, #bc93f2) border-box;
			/* 2 */
			-webkit-mask: linear-gradient(#fff 0 0) padding-box,
				linear-gradient(#fff 0 0);
			/* 3 */
			-webkit-mask-composite: xor;
			/* 4 */
			mask-composite: exclude;
			position: absolute;
			width: 72rpx;
			height: 32rpx;
			z-index: -1;
		}
	}
}

/* 全局样式，确保能够覆盖页面 */
.main-text-fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100vh;
	background-color: #fafafa;
	z-index: 9999;
	overflow-y: auto;
}

.img24 {
	width: 24rpx;
	height: 24rpx;
}

.img42 {
	width: 42rpx;
	height: 42rpx;
}

.t_display {
	display: flex;
	align-items: center;
}

.heads {
	width: 100%;
	height: 110rpx;
	background: #ffffff;
	display: flex;
	padding: 15rpx 32rpx;
	align-items: center;
	position: relative;
	z-index: 10001;

	.nums {
		font-size: 24rpx;
		font-family: DINPro-Medium, DINPro;
		font-weight: 500;
		color: #000;
		margin-left: 8rpx;
	}

	.search {
		font-size: 26rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #000;
	}

	.inputBg {
		width: 100%;
		min-width: 420rpx;
		height: 72rpx;
		color: #000 !important;
		background: #f5f5f5;
		padding: 0 24rpx;
		display: flex;
		align-items: center;
		border-radius: 36rpx;
	}
}

.head {
	width: 100%;
	background: #fff;
	position: fixed !important;
	bottom: 0 !important;
	left: 0 !important;
	right: 0 !important;
	display: flex;
	padding: 30rpx 32rpx;
	padding-bottom: 65rpx;
	z-index: 10002 !important;

	.nums {
		font-size: 24rpx;
		font-family: DINPro-Medium, DINPro;
		font-weight: 500;
		color: #000;
		margin-left: 8rpx;
	}

	.search {
		font-size: 26rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #000;
	}

	.inputBg {
		width: 162px;
		// min-width: 420rpx;
		height: 37px;
		color: #fff;
		background: #f5f5f5;
		padding: 0 24rpx;
		display: flex;
		align-items: center;
		border-radius: 100rpx;
	}
}

.issue {
	padding: 19rpx 32rpx;
	font-size: 24rpx;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: rgba(255, 255, 255, 0.6);
}

/* 添加uni-popup样式覆盖 */
/deep/ .uni-popup {
	z-index: 10002 !important;
}

/deep/ .uni-popup__wrapper {
	z-index: 10002 !important;
}

/deep/ .uni-popup.uni-popup-bottom {
	z-index: 10002 !important;
}

/* 修改u-input组件的文本颜色 */
/deep/ .u-input__input {
	color: #000 !important;
}

/deep/ .u-input {
	color: #000 !important;
}

/deep/ input {
	color: #000 !important;
}

/deep/ textarea {
	color: #000 !important;
}

/deep/ .uni-input-input {
	color: #000 !important;
}

/deep/ .uni-input-wrapper {
	color: #000 !important;
}

/* 弹窗动画效果 */
.main-text-fullscreen {
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.popup-enter {
	transform: scale(0.95);
	opacity: 0;
}

.popup-enter-active {
	transform: scale(1);
	opacity: 1;
}

/* 左滑指示器样式 */
.swipe-indicator {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 20rpx;
	z-index: 10020;
	pointer-events: none;
	transition: opacity 0.2s ease;
}

.swipe-arrow {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.swipe-text {
	font-size: 24rpx;
	white-space: nowrap;
}
</style>