<template>
  <view class="map-container">
    <!-- 地图组件 -->
    <map id="basicMap" class="map-view" :style="mapStyle" :latitude="39.9042" :longitude="116.4074" :scale="16"
      :markers="markers" :show-location="false" :enable-3D="false" :show-compass="false" :enable-overlooking="false"
      :enable-zoom="true" :enable-scroll="false" :enable-rotate="false" @error="onMapError" @loaded="onMapLoaded"
      @tap="onMapTap"></map>
  </view>
</template>

<script>
export default {
  name: 'BasicMap',
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300rpx'
    }
  },
  data() {
    return {
      markers: [{
        id: 1,
        latitude: 39.9042,
        longitude: 116.4074,
        iconPath: '/static/images/localtions.png',
        width: 30,
        height: 30,
        callout: {
          content: '北京天安门',
          color: '#000',
          fontSize: 12,
          borderRadius: 5,
          bgColor: '#fff',
          padding: 5,
          display: 'ALWAYS'
        }
      }]
    }
  },
  computed: {
    mapStyle() {
      return {
        width: this.width,
        height: this.height,
        position: 'static',
        display: 'block'
      }
    }
  },
  mounted() {
    console.log('BasicMap 组件已挂载');
  },
  methods: {
    onMapError(e) {
      console.error('地图加载错误:', e);
    },
    onMapLoaded(e) {
      console.log('地图加载成功:', e);
    },
    onMapTap(e) {
      console.log('地图点击:', e);
    }
  }
}
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 10rpx;
}

.map-view {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
</style>
