<template>
	<uni-popup ref="popup" type="bottom" @maskClick="close" :safe-area="false">
		<view class="hb">
			<view class="title">发布红包</view>
			<view class="types">
				<view class="types-title" @click="changeType">{{type|typeFilter}}</view>
				<image src="../../../static/map/bottom.png" class="bottomIcon"></image>
			</view>
			<uni-forms ref="formsRef" :rules="rules" :modelValue="formData" label-width="0px">
				<uni-forms-item label="" name="total_amount">
					<view class="label">
						<image v-if="type == 'pin'" class="pinImg" src="../../../static/map/pin.png"></image>
						{{type == 'pin'?'总金额':'单个金额'}}
					</view>
					<input type="digit" :value="formData.total_amount" placeholder="0.0" @input="validateNumber"
						maxlength="3" />
					<text>|</text>元
				</uni-forms-item>
				<uni-forms-item label="" name="quantity">
					<view class="label">数量</view>
					<input type="number" v-model="formData.quantity"
						@blur="e=>formData.quantity= parseInt(e.detail.value)" placeholder="0" />
					<text>|</text>个
				</uni-forms-item>
				<!--  -->
				<uni-forms-item label="" name="locationName">
					<!-- v-if="!isStore" -->
					<view
						style="width: 100%;display: flex;align-items: center;justify-content: flex-end;flex-wrap: wrap;">
						<view class="label">红包位置</view>
						<view class="value " @click="bindPosition"><text class="text-overflow"
								style="margin: 0;width: 80%;text-align: right;">{{formData.locationName||"选择位置" }}</text><text
								style="color: #fff;">|</text>
							<image src="../../../static/map/weizhi.png" class="weizhi"></image>
						</view>
					</view>
					<!-- <view v-if="formData.locationName" class="maphb">
						<image class="hbicon" v-if="isStore" src="../../../static/map/hb-store.png" mode="">
						</image>
						<image class="hbicon" v-if="!isStore" src="../../../static/map/hb-user.png" mode="">
						</image>
					</view> -->
				</uni-forms-item>
				<uni-forms-item label="" name="radius" v-if="type == 'position'||type == 'pin'||type == 'normal'">
					<view class="label">范围</view>
					<picker class="value" @change="bindRadiusChange" :value="radius_index" :range="radius_array">
						<view class="t_display">{{formData.radius?(formData.radius+'米')||"选择范围":'不限' }}<text
								style="color: #fff;">|</text>
							<image class="img32" src="../../../static/images/right.png"></image>
						</view>
					</picker>
				</uni-forms-item>
				<uni-forms-item label="" name="duration" v-if="type == 'position'||type == 'pin'||type == 'normal'">
					<view class="label">时间</view>
					<picker class="value" @change="bindTimeChange" :value="duration_index" :range="duration_array">
						<view class="t_display">{{(formData.duration)||"选择时间" }}<text style="color: #fff;">|</text>
							<image class="img32" src="../../../static/images/right.png"></image>
						</view>
					</picker>
				</uni-forms-item>
				<uni-forms-item label="" name="title" v-if="type == 'pin'||type == 'normal'">
					<input type="text" v-model="formData.title" placeholder="大吉大利,恭喜发财" style="text-align: left;" />
				</uni-forms-item>
			</uni-forms>
			<view class="btn" :class="{disabled:!canSend}" @click="bindSend">发布</view>
		</view>
		<Postion ref="position" />
		<u-toast ref='notify' />
	</uni-popup>
</template>

<script>
	import Postion from './hbPosition.vue'
	import {
		apiWalletRedpacketGrant
	} from '@/api/common.js'
	export default {
		components: {
			Postion
		},
		// props: {
		// 	center: {
		// 		type: Array,
		// 		default: () => []
		// 	}
		// },
		data() {
			return {
				rules: {
					total_amount: {
						rules: [{
							required: true,
							errorMessage: '请输入金额',
						}]
					},
					quantity: {
						rules: [{
							required: true,
							errorMessage: '请输入数量',
						}]
					},
					locationName: {
						rules: [{
							required: true,
							errorMessage: '请选择红包位置',
						}]
					},
					duration: {
						rules: [{
							required: true,
							errorMessage: '请选择时间',
						}]

					},
				},
				hbCate: "user",
				radius_array: ['不限', '5', '25', '50', '250', '500'],
				duration_array: ['不限', '5分钟', '10分钟', '20分钟', '30分钟', '1小时', '3小时'],
				duration_index: null,
				radius_index: null,
				type: 'normal',
				isStore: false,
				formData: {
					total_amount: '',
					quantity: '',
					position: '',
					radius: '',
					locationName: '',
					coordinate: '',
					title: ''
				},
				hbChooseLocation: uni.getStorageSync('hbChooseLocation') ? JSON.parse(uni.getStorageSync(
					'hbChooseLocation')) : {},

			}
		},
		watch: {
			'$store.state.hbLocation'(nVal) {
				console.log('----nVal------', nVal);
				this.formData.locationName = nVal ? nVal.locationName : ''
				this.formData.address = nVal ? nVal.address : ''
				this.formData.coordinate = nVal ? nVal.location.replace(/\s+/g, '') : ''
				console.log('----this.formData.coordinate', this.formData.coordinate);
			}
		},
		computed: {
			canSend() {
				const {
					total_amount,
					quantity,
					locationName,
					duration
				} = this.formData
				return total_amount && quantity && locationName && duration
			}
		},
		filters: {
			typeFilter(str) {
				if (str == 'position') {
					return '位置红包'
				} else if (str == 'pin') {
					return '拼手气红包'
				} else if (str == 'normal') {
					return '普通红包'
				} else if (str == 'noCondition') {
					return '无条件红包'
				}
			}
		},
		mounted() {

			// this.open()
		},
		methods: {
			validateNumber(event) {
				console.log('event', event);
				const value = event.detail.value;
				let newValue = value.replace(/[^0-9.]/g, ''); // 清除非数字字符

				// 限制小数点的数量
				newValue = newValue.replace(/\./g, (match, offset) => offset === newValue.length - 2 ? match : '');

				// 限制长度
				if (newValue.length > 4) {
					newValue = newValue.substring(0, 4);
				}

				// 限制范围
				if (newValue && newValue > 200) {
					newValue = 200;
				}
				if (newValue == '00' || newValue == '000') newValue = 0
				console.log('newValue', newValue);
				this.$nextTick(() => {
					this.$set(this.formData, 'total_amount', newValue)
				})
				this.$set(this.formData, 'total_amount', newValue)
			},
			handleInput(e) {
				console.log(parseInt(e.detail.value), 'handleInput');
				this.formData.quantity = parseInt(e.detail.value)
			},
			bindSend() {
				this.$refs.formsRef.validate().then(res => {
					let apply = {}
					const {
						total_amount,
						quantity,
						title,
						coordinate,
						locationName,
						address,
						radius,
						duration
					} = this.formData
					console.log(this.formData);
					if (this.type == 'noCondition') {
						apply = {
							every_amount: Number(total_amount),
							quantity: Number(quantity),
							coordinate: coordinate,
							location: address,
							envelope_type: 2
						}
					} else if (this.type == 'pin') {
						apply = {
							total_amount: Number(total_amount),
							quantity: Number(quantity),
							coordinate: coordinate,
							location: address,
							title: !!title ? title : '大吉大利,恭喜发财',
							duration: this.getMinute(duration),
							envelope_type: 1,
							radius
						}
					} else if (this.type == 'position') {
						apply = {
							every_amount: Number(total_amount),
							quantity: Number(quantity),
							coordinate: coordinate,
							location: address,
							duration: this.getMinute(duration),
							envelope_type: 2,
							radius
						}
					} else if (this.type == 'normal') {
						apply = {
							every_amount: Number(total_amount),
							quantity: Number(quantity),
							coordinate: coordinate,
							location: address,
							title,
							duration: this.getMinute(duration),
							envelope_type: 2,
							radius
						}
					}
					if (this.isStore) {
						const userInfo = uni.getStorageSync('userInfo')
						apply.business_id = userInfo.business_id
					}
					apply.radius =
						apply.radius ? apply.radius : '0'
					console.log('----------apiWalletRedpacketGrant-------------', apply);
					apiWalletRedpacketGrant(apply).then(res => {
						console.log('--------res', res);
						if (res.code == 200) {
							// uni.showToast({
							// 	title: '发送成功',
							// 	duration: 2000
							// })
							this.$store.commit('SET_HB_LOCATION', {})
							this.$emit('refresh_hb', coordinate)
							this.close()
						}
					}).catch(err => {
						console.log('---------err', err);
					})

				}).catch(err => {
					console.log('表单错误信息：', err);
				})
			},
			open(isStore) {
				this.isStore = isStore == 'store'
				this.$store.commit('SET_HB_LOCATION', null)
				// this.$http.get('/api/user/business/get').then(res => {
				// 	const {
				// 		location,
				// 		coordinate
				// 	} = res.message
				// 	this.formData.locationName = location
				// 	this.formData.address = location
				// 	this.formData.coordinate = coordinate
				// })
				setTimeout(() => {
					this.$refs.popup.open('bottom')
				}, 500)
			},
			close() {
				this.formData = {
					total_amount: '',
					quantity: '',
					position: '',
					radius: '',
					locationName: '',
					coordinate: '',
					title: ''
				}
				this.$refs.popup.close()
			},
			bindPosition() {
				uni.navigateTo({
					url: '/pages/index/hbPosition',
				})
			},
			bindRadiusChange(e) {
				this.radius_index = e.detail.value
				this.formData.radius = this.radius_array[this.radius_index] == '不限' ? 0 : this.radius_array[this
					.radius_index]

			},
			bindTimeChange(e) {
				this.duration_index = e.detail.value
				console.log('bindTimeChange', e);
				this.$set(this.formData, 'duration', this.duration_array[this.duration_index])

			},
			changeType() {
				uni.showActionSheet({
					itemList: ['普通红包', '拼手气红包'], //, '无条件红包'
					success: (res) => {
						if (res.tapIndex == 0) {
							this.type = 'normal'
						} else if (res.tapIndex == 1) {
							this.type = 'pin'
						} else if (res.tapIndex == 2) {
							this.type = 'position'
						}
					}
				})
			},
			getMinute(str) {
				let num = 0
				switch (str) {
					case '不限':
						num = 0
						break;
					case '5分钟':
						num = 5
						break;
					case '10分钟':
						num = 10
						break;
					case '20分钟':
						num = 20
						break;
					case '30分钟':
						num = 30
						break;
					case '1小时':
						num = 60
						break;
					case '3小时':
						num = 180
						break;
					default:
						break;
				}
				return num
			}
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 750rpx;
		height: 80vh;
		background: #191C26;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		opacity: 1;
		border-image: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
		padding: 63rpx 32rpx;
		position: relative;

		&::after {
			position: absolute;
			left: 0;
			bottom: -120rpx;
			height: 120rpx;
			width: 750rpx;
			content: '';
			z-index: 2;
			background-color: #191C26;
		}

		::v-deep .uni-forms {
			width: 100%;
			height: 50vh;
			overflow-y: auto;
			padding-bottom: 50rpx;
			// box-sizing: border-box;

			.uni-forms-item {
				width: 686rpx;
				padding: 32rpx;
				box-sizing: border-box;
				background: #2F3341;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				opacity: 1;
				border: 1rpx solid;
				border-color: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
				// border-image: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-bottom: 32rpx;
				flex-wrap: wrap;

				.uni-forms-item__label {
					height: 41rpx;
					font-size: 28rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 41rpx;


				}

				.uni-forms-item__content {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					flex-wrap: wrap;

					.maphb {
						position: relative;
						margin-top: 32rpx;
						background-color: red;
						width: 686rpx;
						height: 218rpx;
						border-radius: 20rpx;
						background-image: url(../../../static/map/hbmap.png);
						background-size: 100% 100%;
						background-repeat: no-repeat;
						display: flex;
						align-items: center;
						justify-content: center;

						.hbicon {
							width: 50rpx;
							height: 50rpx;
							margin: 0 20rpx;
						}
					}

					.label {
						display: flex;
						align-items: center;

						.pinImg {
							width: 36rpx;
							height: 36rpx;
							margin-right: 12rpx;
						}
					}

					.value {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						font-size: 28rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						color: rgba(255, 255, 255, 0.42);
						line-height: 41rpx;

						.rightIcon {
							width: 16rpx;
							height: 32rpx;
							margin-left: 24rpx;
						}

						text {
							margin: 0 24rpx;
						}

						.weizhi {
							width: 28rpx;
							height: 28rpx;
						}
					}

					input {
						flex: 1;
						text-align: right;
					}

					text {
						margin: 0 24rpx;
					}
				}
			}
		}

		.btn {
			width: 686rpx;
			height: 94rpx;
			background: #E93E3E;
			border-radius: 140rpx 140rpx 140rpx 140rpx;
			opacity: 1;
			margin: auto;
			text-align: center;
			line-height: 94rpx;
			position: absolute;
			bottom: 80rpx;

		}

		.btn.disabled {
			background-color: #999;
			// color: #E93E3E;
			color: #fff;
		}

		.title {
			height: 75rpx;
			font-size: 52rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			color: #FFFFFF;
			line-height: 75rpx;
			margin-bottom: 32rpx;
		}

		.types {

			margin-bottom: 32rpx;
			display: flex;
			align-items: center;

			.types-title {
				display: inline-block;
				height: 46rpx;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				line-height: 46rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.bottomIcon {
				width: 32rpx;
				height: 16rpx;
				margin-left: 24rpx;
			}
		}
	}
</style>