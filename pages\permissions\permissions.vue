<template>
	<view class="permission-bg">
		<view class="permission-content">
			<view class="content">
				<view class="title">
					<view>设置位置限权</view>
					<view>让朋友看到你的实时位置</view>
				</view>
				<view class="step step1">
					<view class="step-wrap">
						<view class="step-num">1</view>
						<view class="step-desc">进入设置，选择<span class="highlight">“位置”</span></view>
					</view>
					<view class="mock-row">
						<view class="location-wrap">
							<image class="icon-location" src="@/static/images/location.png" />
						</view>
						<view class="mock-label">位置</view>
						<uni-icons type="right"></uni-icons>
					</view>
				</view>
				<view class="step step2">
					<view class="step-wrap">
						<view class="step-num">2</view>
						<view class="step-desc">在系统弹窗中，选择<span class="highlight">“始终允许”</span>确保APP在后台也可以持续记录位置并丰富你的地图
						</view>
					</view>
					<view class="mock-popup">
						<view class="popup-option">永不</view>
						<view class="popup-option">下次询问或在我共享时</view>
						<view class="popup-option">使用App期间</view>
						<view class="popup-option selected">始终
							<image class="icon-hand" src="@/static/map/hand.png" />
						</view>
					</view>
				</view>
				<view class="step step3">
					<view class="step-wrap">
						<view class="step-num">3</view>
						<view class="step-desc">打开<span class="highlight">“精准位置”</span>开关</view>
					</view>
					<view class="mock-row">
						<view class="mock-label">精准位置</view>
						<view class="mock-switch on"></view>
					</view>
				</view>
			</view>
			<button class="start-btn" @tap="openSetting">开始设置</button>
		</view>
	</view>
</template>

<script>
	import {
		checkPermission,
		openGPSSettings
	} from '../../utils/checkLocation';

	export default {
		onShow() {
			const platform = uni.getSystemInfoSync().platform
			// #ifdef APP-PLUS
			checkPermission().then((code) => {
				// 0-引导页 1-允许 2-设置页
				console.log("设置页", code)
				if (code === 0) {} else if (code === 1) {
					console.log("code === 1", code === 1)
					uni.navigateBack({
						delta: 1
					})
				} else {}
			}).catch((err) => {
				uni.showModal({
					content: '检测权限失败,请确认是否打开GPS',
					showCancel: false,
					confirmText: "去开启",
					success(res) {
						uni.openAppAuthorizeSetting()
					}
				})
			});
			// #endif
		},
		methods: {
			openSetting() {
				uni.setStorageSync('goSettingPermission', true)
				uni.openAppAuthorizeSetting()
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					})
				}, 1000)
			}
		}
	}
</script>

<style scoped>
	.permission-bg {
		height: 100vh;
		width: 100vw;
		background-image: url(../../static/images/index/location_bg.png);
		background-size: cover;
		padding-top: 52rpx;
		padding-bottom: 84rpx;
		box-sizing: border-box;
	}

	.permission-content {
		margin: 0 auto;
		display: flex;
		padding-bottom: 40rpx;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		padding: 0 58rpx;
	}

	.content {
		width: 100%;
		flex: 1;
		padding-top: 100rpx;

	}

	.title {
		color: #fff;
		font-weight: 800;
		font-size: 31rpx;
		color: #FFFFFF;
		line-height: 46rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 64rpx;
	}

	.subtitle {
		color: #fff;
		font-size: 26rpx;
		text-align: center;
		margin-bottom: 40rpx;
		opacity: 0.9;
	}

	.step {
		margin-bottom: 82rpx;
		position: relative;
	}

	.step:last-of-type {
		margin-bottom: 0;
	}

	.location-wrap {
		background: #1D79D6;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 46rpx;
		height: 46rpx;
		margin-right: 16rpx;

	}

	.step-num {
		width: 36rpx;
		height: 36rpx;
		background: #4f8cff;
		color: #fff;
		border-radius: 50%;
		text-align: center;
		line-height: 36rpx;
		font-size: 22rpx;
		font-weight: bold;
		z-index: 2;
		margin-right: 24rpx;
	}

	.step-desc {
		font-weight: 500;
		font-size: 27rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.highlight {
		font-size: 29rpx;
		font-weight: bold;
	}

	.mock-row {
		display: flex;
		align-items: center;
		padding: 16rpx 24rpx;
		margin-top: 16rpx;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 23rpx;
		position: relative;
	}

	.icon-location {
		width: 25rpx;
		height: 25rpx;
	}

	.mock-label {
		color: #fff;
		font-size: 26rpx;
		flex: 1;
	}

	.icon-arrow {
		width: 24rpx;
		height: 24rpx;
	}

	.mock-popup {
		padding: 12rpx 0;
		margin-top: 16rpx;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 23rpx;
	}

	.step-wrap {
		display: flex;
	}

	.popup-option {
		color: #fff;
		font-size: 26rpx;
		padding: 18rpx 32rpx;
		border-bottom: 1px solid rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;
	}

	.popup-option:last-child {
		border-bottom: none;
	}

	.selected {
		/* background: linear-gradient(90deg, #AD60FF 0%, #57C7FF 100%);
	color: #fff;
	font-weight: bold;
	border-radius: 0 0 18rpx 18rpx; */
		display: flex;
		justify-content: space-between;
	}

	.icon-hand {
		width: 36rpx;
		height: 36rpx;
		margin-left: 12rpx;
	}

	.mock-switch {
		width: 64rpx;
		height: 36rpx;
		border-radius: 18rpx;
		background: #ccc;
		position: relative;
		margin-left: 16rpx;
	}

	.mock-switch.on {
		background: #4f8cff;
	}

	.mock-switch.on::after {
		content: '';
		display: block;
		width: 32rpx;
		height: 32rpx;
		background: #fff;
		border-radius: 50%;
		position: absolute;
		right: 2rpx;
		top: 2rpx;
	}

	.start-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 16rpx;
		background: linear-gradient(90deg, #AD60FF 0%, #57C7FF 100%);
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		margin-top: 94rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
	}

	.tabbar {
		width: 100vw;
		height: 100rpx;
		background: rgba(0, 0, 0, 0.18);
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-around;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 10;
	}

	.tab {
		color: #fff;
		font-size: 26rpx;
		flex: 1;
		text-align: center;
		opacity: 0.7;
	}

	.tab.active {
		opacity: 1;
		font-weight: bold;
	}

	.tab.plus {
		font-size: 40rpx;
		color: #fff;
		opacity: 1;
	}
</style>