<template>
	<view class="appPage">
		<barHeight></barHeight>
		<view class="head">
			<uni-icons type="left" color="#fff" style="margin-left: 30rpx;" @click="goBack"></uni-icons>
			<view class="inputBg">
				<uni-icons type="search" color="#fff" style="margin-right: 8rpx;"></uni-icons>
				<u-input v-model="username" type="text" placeholder="请输入关键字" @confirm="getData" :border="false"
					clearable />
			</view>
			<span class="search" @click="getData">
				搜索
			</span>
		</view>
		<view class="card" v-if="userArr.length">
			<view class="t_betweent">
				<view class="">
					用户
				</view>
				<uni-icons type="right" color="#fff"></uni-icons>
			</view>
			<scroll-view class="scroll-view_H" scroll-x="true" @scrolltolower="userBottom">
				<view id="demo1" class="scroll-view-item_H" v-for="(item,index) in userArr" :key="index"
					@click="setChoose(item.uuid)">
					<view class="" style="position: relative;">
						<image class="img102" style="border-radius: 50%;" :src="item.avatar" mode="aspectFill">
						</image>
					</view>
					<view class="name">
						{{item.nickname}}
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="card" style="margin-top: 24rpx;" v-if="firendArr && firendArr.length">
			<view class="t_betweent">
				<view class="">
					好友
				</view>
				<uni-icons type="right" color="#fff"></uni-icons>
			</view>
			<scroll-view class="scroll-view_H" scroll-x="true" @scrolltolower="firendBottom">
				<view id="demo1" class="scroll-view-item_H" v-for="(item,index) in firendArr" :key="index"
					@click="setChoose(item.uuid)">
					<view class="" style="position: relative;">
						<image class="img102" style="border-radius: 50%;" :src="item.avatar" mode="aspectFill">
						</image>
					</view>
					<view class="name">
						{{item.nickname}}
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="card" style="margin-top: 24rpx;" v-if="addressArr && addressArr.length">
			<view class="t_betweent">
				<view class="">
					地点
				</view>
				<uni-icons type="right" color="#fff"></uni-icons>
			</view>
			<scroll-view scroll-y="true" style="max-height: 510rpx;" @scrolltolower="addressBottom">
				<view class="item" v-for="(item,index) in addressArr" :key="index" @click="goNavLoaction(item)">
					<image class="img24" style="padding-top: 10rpx;margin-right: 16rpx;"
						src="../../static/images/weizhi.png" mode=""></image>
					<view class="">
						<view class="">
							<view class="title">
								{{item.name}}
							</view>
							<view class="location" style="margin-top: 12rpx;">
								{{item.cityname}} - {{item.adname}} - {{item.address}}
							</view>
						</view>
						<u-line style="margin-top: 26rpx;" color="rgba(255,255,255,0.13)"></u-line>
					</view>
				</view>
			</scroll-view>
			<u-toast ref='notify' />
		</view>
	</view>
</template>

<script>
	import Post from "@/components/post/post.vue"
	export default {
		components: {
			Post
		},
		data() {
			return {
				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: '',
					location: "",
					output: 'JSON',
					radius: 50000
					// sortrule: 'distance',
				},
				total: 0,
				popupInfo: "",
				username: "",
				statusBarHeight: "",
				userArr: [],
				firendArr: [],
				addressArr: [],
				firentTotal: 0,
				userTotal: 0,
				userPage: 1,
				firendPage: 1,
				addressPage: 1,
				addressTotal: 0
			}
		},
		watch: {},

		onLoad() {
			const system = uni.getStorageSync('system')
			this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
			this.MapSearchForm.location = uni.getStorageSync('location')
		},
		methods: {
			async getMyAddress() {
				const locat = this.MapSearchForm.location.split(",")
				this.$http.get('/location/search/around', {
					keyword: this.username,
					page: this.MapSearchForm.page_num,
					longitude: locat[0],
					latitude: locat[1]
				}).then((res) => {
					this.total = res.message.count
					if (this.MapSearchForm.page_num == 1) {
						this.addressArr = res.message.pois;
						return
					}
					this.addressArr = [...this.addressArr, ...res.message.pois]
				})
			},
			async getAddress() {
				// this.MapSearchForm.keywords = this.username
				// await uni.request({
				// 	method: 'GET',
				// 	url: 'https://restapi.amap.com/v5/place/around?parameters',
				// 	data: {
				// 		this.MapSearchForm.keywords,
				// 		key: "26d3a980c0c4b411f9c13929bbc6559f"
				// 	},
				// 	success: (res) => {
				// 		if (res.statusCode == 200) {
				// 			this.total = res.data.count
				// 			if (this.MapSearchForm.page_num == 1) {
				// 				this.addressArr = res.data.pois;
				// 				return
				// 			}
				// 			this.addressArr = [...this.addressArr, ...res.data.pois]
				// 		} else {
				// 			this.toast('搜索地址信息出错');
				// 		}
				// 	},
				// 	fail(err) {
				// 		this.toast(err.errMsg);
				// 	}
				// })
			},
			toJSON() {},

			goNavLoaction(item) {
				let pages = getCurrentPages();
				let currPage = pages[pages.length - 2]; //当前页面
				console.log(currPage);
				const locat = item.location.split(",")
				// #ifdef APP
				// currPage.$vm.myUpdate({
				currPage.$vm.poiMapAddMarker({
					lnglat: {
						lng: locat[0],
						lat: locat[1]
					},
					location: item.name,
					addressComponent: {
						city: item.cityname,
						district: item.address,
						street: "",
						streetNumber: ""
					}
				});
				// #endif
				uni.navigateBack()
			},
			setChoose(uuid) {
				uni.navigateTo({
					url: "/pages/otherPage/otherPage?uuid=" + uuid
				})
			},
			firendBottom() {
				if (this.firentTotal > this.firendArr.length) {
					this.firendPage++;
					this.getFirend()
				}
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			userBottom() {
				if (this.userTotal > this.userArr.length) {
					this.userPage++;
					this.getUser()
				}
			},
			addressBottom() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
					this.getMyAddress()
				}
			},

			goBack() {
				uni.navigateBack()
			},
			cancelBlack(uid) {
				this.$http.post('/api/user/black-set', {
					uid,
					"opt": 2
				}).then(res => {
					this.getData()
					this.$refs.popup.close()
				})
			},
			getData() {
				this.getUser()
				this.getFirend()
				this.getMyAddress()
			},
			getUser() {
				this.$http.get('/api/user/search-user', {
					keyword: this.username,
					page: this.userPage,
				}).then(res => {
					this.userArr = res.message.list ? res.message.list : []
				})
			},
			getFirend() {
				this.$http.get('/api/user/friend/get', {
					keyword: this.username,
					page: this.firendPage,
				}).then(res => {
					this.firentTotal = res.message.count
					this.firendArr = res.message.list
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.card {
		padding: 14rpx 32rpx;
		font-size: 26rpx;
		font-family: Source Han Sans-Medium, Source Han Sans;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.74);
		line-height: 38rpx;
		background: #22252F;

		.item {
			margin-top: 24rpx;
			// padding: 34rpx;
			display: flex;
			align-items: flex-start;
			justify-content: flex-start;
		}

		.scroll-view_H {
			white-space: nowrap;
			width: 100%;
			display: flex;
			align-items: center;
		}
	}

	.scroll-view-item_H {
		display: inline-block;
		padding: 22rpx;

		.choose {
			position: absolute;
			bottom: 0;
			right: 0;
		}

		.name {
			transform: translateX(-12rpx);
			text-align: center;
			font-size: 24rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #fff;
		}
	}

	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;



		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		// .item {
		// 	margin-top: 27rpx;
		// 	color: rgba(61, 61, 61, 0.82);
		// 	justify-content: flex-start;

		// 	.rightInfo {
		// 		margin-left: 35rpx;
		// 	}

		// 	.disable {

		// 		width: 42rpx;
		// 		height: 42rpx;
		// 	}
		// }

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.titleCard {
		margin-bottom: 24rpx;
		width: 100%;
		height: 70rpx;
		background: #22252F;
		text-align: center;
		color: rgba(255, 255, 255, 0.64);

		line-height: 70rpx;
	}

	.head {
		display: flex;
		align-items: center;

		.search {
			width: 100rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			// width: 600rpx;
			width: 100%;
			margin: 32rpx;
			color: #fff;
			background: #22252F;
			height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
		}
	}
</style>