<template>
	<view>
		<u-navbar backIconName="search" back-text="" title="发布" :custom-back="customBack"
			:background="{backgroundColor: '#191C26'}" :border-bottom="false" title-color="#fff" back-icon-color="#fff">
			<view slot="content" style="width: 240rpx;">
				<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" bg-color="#191C26"
					active-color="#fff" inactive-color="rgba(255,255,255,0.72)" :show-bar="false"></u-tabs>
			</view>
			<view class="navbar-right t_display" slot="right" style="margin-right: 34rpx;" v-if="false">
				<view class="" style="color: rgba(255,255,255,0.72);">
					时间
				</view>
				<image src="../../static/images/time.png" class="img24" mode="" style="margin-left: 16rpx;"></image>
			</view>
		</u-navbar>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			customBack() {
				uni.navigateTo({
					url: "/pages/searchPost/searchPost"
				})
			},
		}
	}
</script>

<style>

</style>