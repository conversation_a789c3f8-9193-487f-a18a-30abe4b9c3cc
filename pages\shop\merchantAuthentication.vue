<template>
	<view class="authenticationContainer">
		<view class="navigation-bar-container" :style="{height: headerHeight + 'px' }">
			<view class="navigation-bar" :style="{paddingTop:statusBarHeight+'px'}" >
				<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
				<view class="nav-title">商户认证</view>
			</view>
		</view>
		
		<view class="authenticationContent" :style="{paddingTop: headerHeight + 'px' }">
			<view class="merchant-authentication-page">
					<view class="merchant-authentication-form">
						<!-- 上传营业执照 -->
						<view class="merchant-authentication-form-item upload-container">
							<view class="form-title"><text>*</text>营业执照</view>
							<view class="gridBox">
								<view class="box-img-container" v-for="(src,index) in imgArr" :key="index">
									<image class="box-img" @click.stop="preview([src])" :src="src" mode="heightFix">
									</image>
									<image class="close-img" @click.stop="delImg(src)" src="../../static/images/close-icon.png" mode="heightFix">
									</image>
								</view>
								<view class="no-data-box" v-if="imgArr && imgArr.length<1" @click="upload">
									<image src="../../static/images/photo-icon.png" class="box-icon">
									</image>
								</view>
							</view>
							<view v-if="imgArr.length!==1" class="tips">仅支持jpg、png、jpeg格式图片，图片大小不能大于10M，请将资质上传至正确的资质类型内，否则可能会导致审核不通过
							</view>
						</view>
						<view class="merchant-authentication-form-item">
							<view class="form-style">
								<view class="form-title"><text>*</text>营业执照类型</view>
								<view v-if="isEdit" class="form-style-tab">
									<view class="form-style-tab-item" :class="{ 'form-style-tab-item-active': index === typeIndex }"
										v-for="(item,index) in typeItems" @click="chooseType(index)">{{item}}</view>
								</view>
								<view v-else class="form-style-tab">
									<view class="form-style-tab-item form-style-tab-item-active">{{typeItems[typeIndex]}}</view>
								</view>
							</view>
							<uni-forms ref="baseForm" :modelValue="formData" label-position="top">
								<uni-forms-item label="执照编码" required :class="{ 'disabled-input': !isEdit }">
									<input class="form-item-input" :disabled="!isEdit" v-model="formData.licenseCode" maxlength="18"
										placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入执照编码" @input="checkFormHasValue" />
								</uni-forms-item>
								<uni-forms-item label="企业名称" required :class="{ 'disabled-input': !isEdit }">
									<input class="form-item-input" :disabled="!isEdit" v-model="formData.enterpriseName"
										placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入企业名称" @input="checkFormHasValue" />
								</uni-forms-item>
								<uni-forms-item label="法人姓名" required :class="{ 'disabled-input': !isEdit }">
									<input class="form-item-input" :disabled="!isEdit" v-model="formData.legalPersonName"
										placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入法人姓名" @input="checkFormHasValue" />
								</uni-forms-item>
								<uni-forms-item label="联系人姓名" required :class="{ 'disabled-input': !isEdit }">
									<input class="form-item-input" :disabled="!isEdit" v-model="formData.name"
										placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入联系人姓名" @input="checkFormHasValue" />
								</uni-forms-item>
								<uni-forms-item label="联系电话" required :class="{ 'disabled-input': !isEdit }">
									<input class="form-item-input" :disabled="!isEdit" v-model="formData.phone" maxlength="11" type="number"
										placeholder="请输入联系电话" placeholder-style="fontSize:14px;color:#9BA0AE" @input="checkFormHasValue" />
								</uni-forms-item>
								<!-- 1申请中 2正常 3申请驳回 4 注销 -->
								<view v-if="shopInfo.status == 1 || shopInfo.status == 2">
									<button v-if="!isEdit" class="merchant-authentication-form-button btn-edit"
										@click="clickEdit">修改</button>
									<button v-else :class="{ 'highlight': isFormValid }" class="merchant-authentication-form-button" @click="submit">确认修改并提交</button>
								</view>
								<view v-else>
									<button :class="{ 'highlight': isFormValid }" class="merchant-authentication-form-button" @click="submit">确认并提交</button>
								</view>
			
							</uni-forms>
						</view>
					</view>
			
					<u-toast ref='notify' />
					<!-- 提交成功弹框 -->
					<liu-popup bgColor="#ffffff" type="center" ref="submitSuccess" width="295px" height="140px" radius="20px"
						@open="popOpen" @close="close">
						<view class="pop">
							<view class="pop-title">商户认证审核中</view>
							<button class="pop-button" @click="goBack">返回上一页</button>
						</view>
					</liu-popup>
					
				</view>
			
		</view>
	</view>
</template>

<script>
	import {
		apiWalletRedpacketGrant
	} from '@/api/common.js'
	export default {
		data() {
			return {
				headerHeight: uni.getSystemInfoSync().statusBarHeight + 44,
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight,
				old_business: "",
				deleteFlag: true,
				imgArr: [],
				is_business: "",
				duration_index: null,
				radius_index: null,
				type: 'noCondition',
				btnTxt: '',
				btnStatus: ["认证", "返回", "返回", "修改信息"],
				isStore: false,
				formData: {
					licenseCode: "",
					enterpriseName: '',
					legalPersonName: "",
					name: "",
					phone: '',
				},
				current: 0,
				typeItems: ['我是企业', '我是个体工商'],
				typeIndex: 0,
				shopInfo: {},
				isEdit: false,
				isFormValid:false,
				shopType:"",//first 首次认证
			}
		},
		watch: {
			// '$store.state.hbLocation'(nVal) {
			// 	this.formData.locationName = nVal ? nVal.locationName : ''
			// 	this.formData.coordinate = nVal ? nVal.location : ''
			// }
		},
		mounted() {
			this.shopInfo = uni.getStorageSync('shopInfo');
			console.log(this.shopInfo)
			if (this.shopInfo) {
				this.formData.phone = this.shopInfo.phone;
				this.imgArr[0] = this.shopInfo.license;
				this.formData.enterpriseName = this.shopInfo.title;
				this.formData.licenseCode = this.shopInfo.license_no;
				this.formData.legalPersonName = this.shopInfo.name_boos;
				this.formData.name = this.shopInfo.name_contact;
				this.typeIndex = this.shopInfo.personal ? 1 : 0;
			}

		},
		onLoad(option) {
			console.log("打印商户")
			console.log(option.type)
			this.shopType = option.type;
			if(this.shopType=='first'){
				this.isEdit = true
			}
		},
		methods: {
			goBack() {
				uni.navigateTo({
					url: '/pages/index/index?current=' + 4
				});
			},
			goNavLoaction(item) {
				console.log("----------------------", item);
				let pages = getCurrentPages();
				let currPage = pages[pages.length - 2]; //当前页面
				const locat = item.coordinate.split(",")
				// #ifdef APP
				// currPage.$vm.myUpdate({
				currPage.$vm.current = 0
				currPage.$vm.poiMapAddMarker({
					lnglat: {
						lng: locat[0],
						lat: locat[1]
					},
					location: item.locationName,
					addressComponent: {
						city: "商家位置",
						district: item.locationName,
						street: "",
						streetNumber: ""
					}
				});
				// #endif
				uni.navigateBack()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			preview(urls) {
				uni.previewImage({
					urls,
				});
			},
			upload() {
				if (this.is_business == 2) return
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							this.imgArr = [img]
						}, ))
					}
				});
			},
			delImg(idx) {
				if (this.is_business == 2) return
				this.imgArr.splice(idx, 1)
			},
			// 点击修改
			clickEdit(){
				this.isEdit = true;
				this.checkFormHasValue()
			},
			// 检查是否已经填写值
			checkFormHasValue() {
			    this.isFormValid = Object.values(this.formData).some(field => field.trim() !== '');
			},
			// 提交认证
			submit() {
				let flag = Object.values(this.formData).every(item => {
					console.log(item)
					return !!item
				})
				if (!flag) return this.toast('请填写完整商家信息')
				if (this.imgArr.length !== 1) return this.toast('请上传营业执照照片')
				var params = {
					"phone": this.formData.phone,
					"license": this.imgArr[0],
					"title": this.formData.enterpriseName,
					"license_no": this.formData.licenseCode,
					"boss_name": this.formData.legalPersonName,
					"contact_name": this.formData.name,
					"personal": this.typeIndex * 1 == 0 ? false : true
				}
				this.$http.post('/api/user/business/apply', params).then(res => {
					if (res.code == 200) {
						this.openPopup("submitSuccess");
					}
				})

			},
			bindPosition(formData) {
				if (this.is_business == 2) {
					this.goNavLoaction(formData)
					return
				}
				uni.navigateTo({
					url: '/pages/index/hbPosition',
				})
			},
			// 选择类型
			chooseType(index) {
				this.typeIndex = index;
			},
			openPopup(e) {
				console.log(e)
				this.$refs[e].open();
			},
			popOpen() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0 // 动画时长，默认300ms
				});
			},
			close() {
				console.log('关闭!');
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
		}
	}
</script>


<style scoped lang="scss">
	// 商户认证
	.authenticationContainer {
		width:100%;
		height:100vh;
		overflow:hidden;
		.navigation-bar-container{
			width: 100%;
			position: absolute;
			z-index: 100;
			top: 0;
			left: 0;
			background-color: #ffffff;
			.navigation-bar {
				width:100%;
				display: flex;
				align-items: center;
				.back {
					width: 31px;
					height: 31px;
					margin-left: 2%;
					z-index: 1;
				}
				
				.nav-title {
					width: 82%;
					height: 30px;
					color: #000000;
					font-family: 阿里巴巴普惠体;
					font-size: 18px;
					font-weight: 500;
					line-height: 30px;
					letter-spacing: 0px;
					text-align: center;
					z-index: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}

	
		.authenticationContent {
			height: 100vh;
			overflow-y: auto;
			.merchant-authentication-page {
				background-color: #FFFFFF;
			
				.merchant-authentication-form {
					width: 95%;
					margin-left: 2.5%;
					padding: 24px 0;
					box-sizing: border-box;
			
					.form-title {
						font-family: HarmonyOS Sans;
						font-size: 12px;
						font-weight: 500;
						line-height: 14px;
						color: #646B7C;
						margin-bottom: 16px;
			
						text {
							color: #F35323;
						}
					}
			
					.merchant-authentication-form-item {
						margin-bottom: 24px;
					}
			
					.upload-container {
						.gridBox {
							margin-bottom: 16px;
							.box-img-container{
								position: relative;
							}
							.box-img {
								height: 80px;
								width: auto;
								max-width: 100%;
							}
							.close-img{
								width:12px;
								height:12px;
								position: absolute;
								top:4px;
								left:4px;
							}
							.no-data-box {
								width: 80px;
								height: 80px;
								border-radius: 12px;
								background: rgb(248, 248, 248);
								display: flex;
								align-items: center;
								justify-content: center;
			
								.box-icon {
									width: 30px;
									height: 30px;
								}
							}
						}
			
						.tips {
							color: rgb(155, 160, 174);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 18px;
						}
			
					}
			
					.form-style {
						width: 100%;
						margin-bottom: 22px;
			
						.form-style-tab {
							display: flex;
							align-items: center;
							justify-content: space-between;
			
						}
			
						.form-style-tab-item {
							width: 100%;
							/* 或者具体的宽度 */
							height: auto;
							/* 或者具体的高度 */
							width: 48%;
							box-sizing: border-box;
							border: 1px solid rgb(155, 160, 174);
							border-radius: 4px;
							padding: 12px 0;
							box-sizing: border-box;
							display: flex;
							align-items: center;
							justify-content: center;
							color: rgb(155, 160, 174);
							font-family: 阿里巴巴普惠体;
							font-size: 14px;
							font-weight: 400;
							line-height: 19px;
						}
			
						.form-style-tab-item-active {
							border: 1px solid rgb(93, 150, 255);
							background: rgba(93, 150, 255, 0.07);
							color: #5D96FF;
						}
					}
			
					/deep/.uni-forms-item {
						.uni-forms-item__label {
							width: 100% !important;
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 500;
							line-height: 14px;
						}
			
						.uni-forms-item__content {
							box-sizing: border-box;
							border: 1px solid rgb(240, 242, 247);
							border-radius: 4px;
							padding: 13px 16px;
							box-sizing: border-box;
			
							.uni-input-input {
								color: rgb(33, 33, 33) !important;
								font-family: HarmonyOS Sans;
								font-size: 14px;
								font-weight: 400;
								line-height: 16px;
							}
			
						}
						
					}
					/deep/.disabled-input {
						.uni-forms-item__content{
							border: none; /* 移除边框 */
							padding-left:0;
						}
					}
					.merchant-authentication-form-button {
						border-radius: 30px;
						background-color: #C1C1C1;
						color: rgb(255, 255, 255);
						font-family: 阿里巴巴普惠体;
						font-size: 16px;
						font-weight: 400;
						line-height: 22px;
						padding: 15px 0;
						box-sizing: border-box;
					}
					.highlight{
						background-color: #5D96FF;
					}
					.btn-edit{
						background-color: transparent;
						border:1px solid #666666;
						color:#666666;
					}
				}
			
			}
			
		}
	
	}
	
	// 弹框
	.pop {
		width: 100%;
		height: 100%;
		// background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 32px 0 20px 0;
		box-sizing: border-box;

		.pop-title {
			color: rgb(33, 33, 33);
			font-family: HarmonyOS Sans;
			font-size: 18px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: center;
			margin-bottom: 32px;
			width: 90%;
		}

		.pop-button {
			width: 136px;
			height: 36px;
			box-sizing: border-box;
			border: 1px solid rgb(120, 120, 120);
			border-radius: 60px;
			color: rgb(120, 120, 120);
			font-family: HarmonyOS Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 36px;
			background-color: transparent;
		}
	}
</style>