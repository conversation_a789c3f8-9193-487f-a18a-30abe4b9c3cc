<template>
	<!-- <view> -->
	<!-- 我的商户信息 -->
	<view class="shop-content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">我的商户</view>
		</view>
		<view class="content-container">
			<!-- 1申请中 2正常 3申请驳回 4 注销 -->
			<view class="title" @click="goShopInfoDetails">
				<view class="title-text">商户信息</view>
				<view class="shop-static1" v-if="shopInfo.status == 1">审核中</view>
				<view class="shop-static2" v-else-if="shopInfo.status == 2">已通过</view>
				<view class="shop-static3" v-else-if="shopInfo.status == 3">未通过</view>
				<!-- <view class="shop-static4" v-else-if="shopInfo.status == 4">待编辑</view> -->
			</view>
			<view class="shop-info">
				<view class="item">
					<view class="key">商户名称</view>
					<view class="value">{{ shopInfo.title }}</view>
				</view>
				<view class="item">
					<view class="key">负责人电话</view>
					<view class="value">{{ shopInfo.phone }}</view>
				</view>
				<view class="item">
					<view class="key">营业执照编码</view>
					<view class="value">{{ shopInfo.license_no }}</view>
				</view>
				<view class="item">
					<view class="key">实际地点</view>
					<view class="value">{{ shopInfo.location }}</view>
				</view>
			</view>
			<view class="shop-info">
				<view class="shop-introduce item" @click="goShopIntroduce">
					<view class="shop-introduce-left">
						<image class="icon-list" src="../../static/images/vip/icon-list.png" mode=""></image>
						<view class="shop-introduce-title">商户简介</view>
					</view>
					<view class="shop-introduce-right">
						<image class="icon-go" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view>

				<view class="active-list item" @click="goActive">
					<view class="active-left">
						<image class="icon-list" src="../../static/images/vip/icon-list.png" mode=""></image>
						<view class="active-title">商户活动列表</view>
					</view>
					<view class="active-right">
						<image v-if="shopInfo.vip_level == 0" class="icon-lock"
							src="../../static/images/vip/icon-lock.png" mode=""></image>
						<image class="icon-go" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view>
				<view class="active-list item" @click="goExhibit">
					<view class="active-left">
						<image class="icon-list" src="../../static/images/vip/icon-list.png" mode=""></image>
						<view class="active-title">展品列表</view>
					</view>
					<view class="active-right">
						<image v-if="shopInfo.vip_level == 0" class="icon-lock"
							src="../../static/images/vip/icon-lock.png" mode=""></image>
						<image class="icon-go" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view>
				<view class="go-map item" @click="goMap">
					<view class="map-left">
						<image class="icon-list" src="../../static/images/vip/icon-list.png" mode=""></image>
						<view class="map-title">模型展示地点</view>
					</view>
					<view class="map-right">
						<!-- <image class="icon-lock" src="../../static/images/vip/icon-lock.png" mode=""></image> -->
						<view class="map-set-up" v-if="shopInfo.location_set">已设置</view>
						<view class="map-not-set" v-else>未设置</view>
						<image class="icon-go" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view>
			</view>
			<view v-if="isShowPay" class="show-map">
				<image class="map-bg" src="../../static/images/vip/map.png" mode="widthFix"></image>
			</view>
			<view v-else class="show-map">
				<!-- <image class="map" src="../../static/images/vip/map-middle.png" mode=""></image> -->
				<!-- 	<liu-easy-map style="width: 100%; height: 400px;" :centerLat="lat" :centerLng="lon" :scale="17" :markerData="markerData" :polygons="polygons"
						@clickMarker="markerClick"></liu-easy-map> -->
				<!-- <map id="amap" style="width: 750rpx; height: 300px;" :latitude="lat" :longitude="lon" scale="17" :markers="markerData">
						</map> -->
				<map id="map" class="map" zIndex="1" ref="map" :style="{width: '100%',height:'500rpx'}" :latitude="lat"
					:longitude="lon" scale="12" :markers="markerData" :show-compass='true' show-location>
					<cover-view class="button-box">
						<cover-view class="button" @click="buy">会员购买</cover-view>
					</cover-view>
				</map>
				<!-- <map id="amap" :longitude="longitude" :latitude="latitude" scale="16" show-location @tap="onMapClick"></map> -->
				<!-- <u-toast ref='notify' /> -->
			</view>
		</view>

		<liu-popup bgColor="#ffffff" type="center" ref="center" width="295px" height="318px" radius="20px"
			@open="popOpen" @close="close">
			<view class="pop">
				<view class="pop-title">非会员需要先购买会员才可使用此功能</view>
				<image class="pop-icon" src="../../static/images/vip/vip-icon.png" mode=""></image>
				<button class="pop-button" @click.stop="buy">会员购买</button>
			</view>
		</liu-popup>

	</view>
	<!-- </view> -->
</template>

<script>
	import MerchantAuthentication from './merchantAuthentication.vue';
	import {
		config
	} from '@/config.js';
	export default {
		components: {
			MerchantAuthentication,
		},
		data() {
			return {
				isAuthentication: false, //是否已经认证、
				headerHeight: "44px",
				shopId: 0,
				status: 1,
				info: {},
				shopInfo: {},
				lon: '123.599861',
				lat: '43.758234',
				isMapSet: 2,
				markerData: [{
					id: 1,
					name: '', //标记点展示名字
					address: '',
					latitude: '', //标记点纬度
					longitude: '', //标记点经度
					iconPath: '', //标记点图标地址
					iconWidth: 32, //标记点图标宽度
					iconHeight: 32, //标记点图标高度
					calloutColor: '#ffffff', //气泡窗口 文本颜色
					calloutFontSize: 14, //气泡窗口 文本大小
					calloutBorderRadius: 6, //气泡窗口 边框圆角
					calloutPadding: 8, //气泡窗口 文本边缘留白
					calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
					calloutDisplay: 'ALWAYS' //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
				}],
				//展示区域点位信息
				polygons: [{
					points: [], //经纬度数组
					strokeWidth: 2, //描边的宽度
					strokeColor: '#FF000060', //描边的颜色
					fillColor: '#FF000090' //填充颜色
				}],
				isShowPay: false,
			};
		},
		created() {},
		onLoad(options) {
			// if (options.shopId) {
			// 	this.shopId = options.shopId;
			// }
			// this.getData();
			// uni.$once('vipclick', function (data) {
			// 	console.log('监听到事件来自 update ，携带参数 msg 为：' + data);
			// });
			// const subNVue = uni.getSubNVueById('concat');
			// subNVue.setStyle({
			// 	left: '20px',
			// 	width: '100%'
			// });
			// subNVue.show('slide-in-left', 3000, () => {
			// 	console.log('subNVue 原生子窗体显示成功');
			// 	uni.$on('vipclick', function (data) {
			// 		console.log('监听到事件来自 update ，携带参数 msg 为：' + data);
			// 	});
			// });
		},
		onReady() {
			this.setNavigationBarHeight();
		},
		onShow() {
			this.getData();
			// uni.setNavigationBarColor({
			// 	frontColor: '#ffffff', // 前景色值，包括按钮、标题、状态栏的颜色
			// 	backgroundColor: '#000000', // 背景颜色值，包括背景图
			// 	animation: {
			// 		duration: 400,
			// 		timingFunc: 'easeIn'
			// 	}
			// });
		},
		methods: {
			// 设置导航栏高度
			setNavigationBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				// 假设你的导航栏固定高度是44px，根据实际情况调整
				const navigationBarHeight = 44;
				// 计算总高度，包括状态栏和导航栏
				const totalHeight = navigationBarHeight + systemInfo.statusBarHeight + 'px';
				console.log(totalHeight)
				this.headerHeight = totalHeight;
			},
			clickBuy(e) {
				console.log(e);
			},
			markerClick(e) {
				console.log('点击标记点信息：', e);
			},
			getData() {
				this.$http.get('/api/user/info').then((res) => {
					this.info = res.message;
					this.getShopInfo();
					console.log(this.info);
				});
			},
			getShopInfo() {
				this.$http.get('/api/user/business/get').then((res) => {
					if (res.code == 400) {
						this.isAuthentication = false;
					}
					if (res.code == 200) {

						this.shopInfo = res.message;
						// status 1申请中 2正常 3申请驳回 4 注销
						this.isAuthentication = this.shopInfo.status == 1 ? true : false
						uni.setStorageSync('shopInfo', this.shopInfo);
						console.log(this.shopInfo);
						var phoneLon, phoneLat;
						// 获取定位信息
						uni.getLocation({
							type: 'wgs84',
							success: function(res) {
								phoneLon = res.longitude;
								phoneLat = res.latitude;
								console.log('经度：' + res.longitude);
								console.log('纬度：' + res.latitude);
								console.log('速度：' + res.speed);
								console.log('位置精度：' + res.accuracy);
							},
							fail: function(err) {
								console.log('获取定位失败：' + err.message);
							}
						});

						this.lon = this.shopInfo.coordinate ? this.shopInfo.coordinate.split(',')[0] : phoneLon;
						this.lat = this.shopInfo.coordinate ? this.shopInfo.coordinate.split(',')[1] : phoneLat;
						switch (this.shopInfo.status) {
							case 1:
								this.status = 2;
								break;
							case 2:
								this.status = 1;
								break;
							case 3:
								this.status = 3;
								break;
							default:
								this.status = 4;
								break;
						}
						this.markerData = [{
							id: this.shopInfo.id,
							name: this.shopInfo.title, //标记点展示名字
							address: this.shopInfo.title,
							latitude: this.lat, //标记点纬度
							longitude: this.lon, //标记点经度
							iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
							// iconPath: 'https://img0.baidu.com/it/u=550544800,2229099292&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', //标记点图标地址
							iconWidth: 32, //标记点图标宽度
							iconHeight: 32, //标记点图标高度
							calloutColor: '#ffffff', //气泡窗口 文本颜色
							calloutFontSize: 14, //气泡窗口 文本大小
							calloutBorderRadius: 6, //气泡窗口 边框圆角
							calloutPadding: 8, //气泡窗口 文本边缘留白
							calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
							calloutDisplay: 'ALWAYS' //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
						}];
					}
				});
			},
			changeStatus() {
				// this.status = Math.floor(Math.random() * 4) + 1;
			},
			openPopup(e) {
				console.log(e)
				this.isShowPay = true;
				this.$refs[e].open();
			},
			popOpen() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0 // 动画时长，默认300ms
				});

				console.log('打开了');
			},
			close() {
				this.isShowPay = false;
				console.log('关闭!');
			},
			buy() {
				console.log('点击购买了');
				uni.navigateTo({
					url: '/pages/Vip/Vip'
				});
			},
			// 模型展示地点
			goMap() {
				// console.log("地图")
				// uni.navigateTo({
				// 	url: '/pages/map/new_file?shopId=' + this.shopInfo.id
				// });
				if (this.shopInfo.vip_level > 0) {
					uni.navigateTo({
						url: '/pages/map/new_file?shopId=' + this.shopInfo.id
					});
				} else {
					this.openPopup("center");
				}
			},
			// 商户简介
			goShopIntroduce() {
				uni.navigateTo({
					url: '/pages/shopIntroduce/shopIntroduce?shopId=' + this.shopInfo.id
				});
			},
			// 活动列表
			goActive() {
				// uni.navigateTo({
				// 	url: '/pages/active/active?shopId=' + this.shopInfo.id
				// });
				if (this.shopInfo.vip_level > 0) {
					uni.navigateTo({
						url: '/pages/active/active?shopId=' + this.shopInfo.id
					});
				} else {
					this.openPopup("center");
				}
			},
			// 展品列表
			goExhibit() {
				// uni.navigateTo({
				// 	url: '/pages/exhibit/exhibit?shopId=' + this.shopInfo.id
				// });
				if (this.shopInfo.vip_level > 0) {
					uni.navigateTo({
						url: '/pages/exhibit/exhibit?shopId=' + this.shopInfo.id
					});
				} else {
					this.openPopup("center");
					// uni.showToast({
					// 	title: '请先购买会员',
					// 	icon: 'none'
					// });
				}
			},
			goBack() {
				uni.navigateBack();
				// uni.navigateTo({
				// 	url: '/pages/index/index?current=' + 4
				// });
			},
			vipclick() {},
			// 商户信息详情
			goShopInfoDetails() {
				uni.navigateTo({
					url: '/pages/shop/merchantAuthentication'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	@font-face {
		font-family: 'ali';
		/* 你可以给字体起一个别名 */
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi-3-65-Medium.eot');
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi-3-65-Medium.woff2') format('woff2');
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi-3-65-Medium.woff') format('woff');
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi-3-65-Medium.eot?#iefix') format('embedded-opentype');
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi.ttf') format('truetype');
		/* 还可以加上 ttf 格式作为备选 */
		font-weight: normal;
		font-style: normal;
	}

	@font-face {
		font-family: 'HarmonyOS Sans';
		/* 你可以给字体起一个别名 */
		src: url('~@/static/images/vip/font/harmonyos_sans-webfont.eot');
		src: url('~@/static/images/vip/font/harmonyos_sans-webfont.woff2') format('woff2');
		src: url('~@/static/images/vip/font/harmonyos_sans-webfont.woff') format('woff');
		src: url('~@/static/images/vip/font/harmonyos_sans-webfont.eot?#iefix') format('embedded-opentype');
		src: url('~@/static/images/vip/font/HarmonyOS_Sans.ttf') format('truetype');
		/* 还可以加上 ttf 格式作为备选 */
		font-weight: normal;
		font-style: normal;
	}

	page {
		background-color: #f5f7fb;
	}

	.shop-content {
		width: 100%;
		height: 100%;
		overflow: scroll;
		padding-top: 100px;
		background-color: #f5f7fb;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 100px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: fixed;
			z-index: 1000;
			top: 0;
			left: 0;
		}

		.navigation-zhezhao {
			width: 100%;
			height: 100px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position: fixed;
			z-index: 1000;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			// padding-bottom: 10%;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				z-index: 1;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.content-container {

			// height: calc(100vh - 100px);
			// position: relative;
			.title {
				width: 92%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 3% auto;
				position: relative;
				z-index: 1;
				margin-top: 5%;

				.title-text {
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 16px;
					line-height: 19px;
					color: #212121;
					letter-spacing: 0px;
					text-align: left;
				}

				.shop-static1 {
					border-radius: 4px;
					border: 1px solid #e4b975;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #e4b975;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}

				.shop-static2 {
					border-radius: 4px;
					border: 1px solid #4fc5ee;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #4fc5ee;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}

				.shop-static3 {
					border-radius: 4px;
					border: 1px solid #f35323;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #f35323;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}

				.shop-static4 {
					border-radius: 4px;
					border: 1px solid #989898;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #989898;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}
			}

			.shop-info {
				background-color: #ffffff;
				border-radius: 10px;
				width: 92%;
				// height: 100%;
				padding: 2%;
				margin: 3% auto;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-direction: column;
				position: relative;
				z-index: 1;

				.item {
					width: 90%;
					display: flex;
					flex-direction: row;
					flex-wrap: nowrap;
					align-items: center;
					justify-content: flex-start;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 14px;
					line-height: 16px;
					color: #212121;
					letter-spacing: 0px;
					text-align: left;
					margin: 5% 0;

					.key {
						width: 35%;
					}

					.value {
						width: 65%;
					}
				}
			}

			.shop-introduce {
				width: 100%;
				// background-color: #ffffff;
				// border-radius: 10px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				// padding: 5% 16px;
				// margin: 3% auto;
				// margin-top: 5%;
				// box-sizing: border-box;

				.shop-introduce-left {
					width: 70%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-list {
						width: 22px;
						height: 22px;
					}

					.shop-introduce-title {
						font-family: 'ali';
						font-weight: 400;
						font-size: 16px;
						line-height: 22px;
						color: #212121;
						letter-spacing: 0px;
						text-align: left;
						margin-left: 2%;
						width: 100%;
						padding-left: 2%;
					}
				}

				.shop-introduce-right {
					width: 30%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-end;

					.icon-lock {
						width: 16px;
						height: 16px;
					}

					.icon-go {
						width: 16px;
						height: 16px;
						margin-left: 10px;
					}
				}
			}

			.active-list {
				width: 100%;
				// background-color: #ffffff;
				// border-radius: 10px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				// padding: 5% 16px;
				// margin: 3% auto;
				// margin-top: 5%;
				// box-sizing: border-box;

				.active-left {
					width: 70%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-list {
						width: 22px;
						height: 22px;
					}

					.active-title {
						font-family: 'ali';
						font-weight: 400;
						font-size: 16px;
						line-height: 22px;
						color: #212121;
						letter-spacing: 0px;
						text-align: left;
						margin-left: 2%;
						width: 100%;
						padding-left: 2%;
					}
				}

				.active-right {
					width: 30%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-end;

					.icon-lock {
						width: 16px;
						height: 16px;
					}

					.icon-go {
						width: 16px;
						height: 16px;
						margin-left: 10px;
					}
				}
			}

			.go-map {
				width: 100%;
				// background-color: #ffffff;
				// border-radius: 10px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				// padding: 5% 16px;
				// margin: 3% auto;
				// margin-top: 5%;
				// box-sizing: border-box;

				.map-left {
					width: 70%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-list {
						width: 22px;
						height: 22px;
					}

					.map-title {
						font-family: 'ali';
						font-weight: 400;
						font-size: 16px;
						line-height: 22px;
						color: #212121;
						letter-spacing: 0px;
						text-align: left;
						margin-left: 2%;
						width: 100%;
						padding-left: 2%;
					}
				}

				.map-right {
					width: 30%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-end;

					.icon-lock {
						width: 16px;
						height: 16px;
					}

					.map-set-up {
						border-radius: 4px;
						border: 1px solid #e4b975;
						font-family: 'HarmonyOS Sans';
						font-weight: 400;
						font-size: 12px;
						line-height: 14px;
						color: #e4b975;
						letter-spacing: 0px;
						text-align: left;
						padding: 6px 12px;
					}

					.map-not-set {
						border-radius: 4px;
						border: 1px solid #4fc5ee;
						font-family: 'HarmonyOS Sans';
						font-weight: 400;
						font-size: 12px;
						line-height: 14px;
						color: #4fc5ee;
						letter-spacing: 0px;
						text-align: left;
						padding: 6px 12px;
					}

					.icon-go {
						width: 16px;
						height: 16px;
						margin-left: 10px;
					}
				}
			}

			.show-map {
				width: 92%;
				// height: 254px;
				max-height: 300px;
				margin: 3% auto;
				// background-color: #ffffff;
				border-radius: 10px;
				overflow: hidden;

				// padding: 5%;
				.map-bg {
					width: 100%;
					height: auto;
					border-radius: 10px !important;
				}

				.map {
					width: 100%;
					height: 100%;
					border-radius: 10px !important;
				}
			}

			.button-box {
				width: 92%;
				display: flex;
				align-items: center;
				justify-content: center;
				position: fixed;
				bottom: 2%;
				left: 4%;
				padding: 2% 0;
				z-index: 100;

				.button {
					// background-image: url('../../static/images/vip/button.png');
					// background-size: 100% 100%;
					// background-repeat: no-repeat;
					// background: linear-gradient(90.00deg, rgb(249, 225, 177), rgb(227, 182, 113) 100%);
					background-color: #F9E1B1;
					width: 100%;
					height: 50px;
					display: flex;
					// flex-direction: row;
					align-items: center;
					justify-content: center;
					font-family: 'ali';
					font-weight: 400;
					font-size: 16px;
					line-height: 50px;
					color: #4d3512;
					letter-spacing: 0px;
					text-align: center;
					border-radius: 10px;
					border: none;
					outline: none;
				}
			}
		}

		.pop {
			width: 100%;
			height: 100%;
			// background-color: rgba(0, 0, 0, 0.5);
			z-index: 999;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.pop-title {
				font-family: 'ali';
				font-weight: 400;
				font-size: 20px;
				line-height: 27px;
				color: #212121;
				letter-spacing: 0px;
				text-align: center;
				margin-top: 5%;
				width: 90%;
			}

			.pop-icon {
				width: 160px;
				height: 160px;
			}

			.pop-button {
				width: 90%;
				background-image: url('../../static/images/vip/buy-button.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: 'ali';
				font-weight: 400;
				font-size: 16px;
				line-height: 22px;
				color: #4d3512;
				letter-spacing: 0px;
				text-align: center;
				border-radius: 10px;
				// margin-top: 5%;
			}
		}
	}
</style>