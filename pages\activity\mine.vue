<template>
	<view class="appPage">
		<view class="header">
			<span :class="chooseJ ? 'choose' : 'default'" @click="choose('1')">
				我加入的（{{join}}）
			</span>
			<span style="margin-left: 40rpx; margin-right: 40rpx; color: rgb(189, 189, 192); ">|</span>
			<span :class="chooseC? 'choose' : 'default'" @click="choose('2')">
				我创建的（{{myCreate}}）
			</span>
		</view>
		<scroll-view v-if="chooseJ" @scrolltolower="scrolltolowerJ" :refresher-threshold="150" scroll-y="true"
			refresher-background="grey41" :scroll-x="false" style="width: 100%; height: calc(100vh - 244rpx);">
			<!-- <view v-if="!recommendTotal" style="color: #fff;margin-top: 20rpx;text-align: center;">到底了~
			</view> -->
			<view class="list" v-for="(item, index) in joinArr">
				<view class="card">
					<!-- <image :src="item.cover" mode="aspectFill"
						style="width: 244rpx; height: 244rpx; margin-right: 20rpx; border-radius: 10px;"
						@click="toDetails(item.uuid)">
					</image> -->
					<view class="mask-container">
						<image :src="item.cover" mode="aspectFill" @click="toDetails(item.uuid)">
						</image>
						<view class="mask-layer" v-if="item.is_closed" @click="toDetails(item.uuid)">
							<text class="mask-text">- 活动结束 -</text>
						</view>
					</view>
					<view class="active">
						<view class="title" @click="toDetails(item.uuid)">
							{{item.title}}
						</view>
						<view class="t-distance" style="justify-content: space-between;margin-bottom: 40rpx;">
							<view class="avatar" v-if="item.member_avatar.length < 5" @click="toDetails(item.uuid)">
								<image v-for="(src, index2) in item.member_avatar" :src="src" mode="aspectFill"
									:style="{left: index2*20 + 'rpx'}"></image>
								<span
									:style="{marginLeft: item.member_avatar.length*20+40 + 'rpx', color: 'rgb(224, 224, 224)', fontSize: '18rpx', lineHeight: '40rpx' }">共{{item.people_cnt}}人</span>
							</view>
							<view class="avatar" v-else @click="toDetails(item.uuid)">
								<image v-for="(src, index2) in item.member_avatar.slice(0,3)" :src="src"
									mode="aspectFill" :style="{left: index2*20 + 'rpx'}"></image>
								<span
									style="width: 40rpx; height: 40rpx; text-align: center; line-height: 40rpx; position: absolute; top: 0; left: 80rpx; background-color: rgb(180, 180, 180); border-radius: 50%;">
									...
								</span>
								<span
									:style="{marginLeft: item.member_avatar.length*20+40 + 'rpx', color: 'rgb(224, 224, 224)', fontSize: '18rpx' }">共{{item.people_cnt}}人</span>
							</view>
							<view v-show="item.need_ticket" class="ticket_tag">
								门票活动
							</view>
						</view>
						<view class="t-distance">
							<span class="t-distance">
								<image src="../../static/active/time.png" mode="aspectFill"
									style="width: 22rpx; height: 22rpx; margin-right: 8rpx;">
								</image>
								{{item.timeline}}
							</span>
							<span class="t-distance" style="margin-left: 20rpx;">
								<image src="../../static/active/distance.png" mode="aspectFill"
									style="width: 22rpx; height: 22rpx; margin-right: 8rpx;">
								</image>
								{{item.distance}}
							</span>
						</view>
					</view>
				</view>
				<view class="content">
					{{item.content}}
				</view>
			</view>
			<view class="clear">

			</view>
		</scroll-view>
		<scroll-view v-if="chooseC" @scrolltolower="scrolltolowerM" :refresher-threshold="150" scroll-y="true"
			refresher-background="grey41" :scroll-x="false" style="width: 100%; height: calc(100vh - 244rpx);">
			<!-- <view v-if="!recommendTotal" style="color: #fff;margin-top: 20rpx;text-align: center;">到底了~
			</view> -->
			<view class="list" v-for="(item, index) in myCreateArr">
				<view class="card">
					<view class="mask-container">
						<image :src="item.cover" mode="aspectFill" @click="toDetails(item.uuid)">
						</image>
						<view class="mask-layer" v-if="item.is_closed" @click="toDetails(item.uuid)">
							<text class="mask-text">- 活动结束 -</text>
						</view>
					</view>
					<view class="active">
						<view class="title" @click="toDetails(item.uuid)">
							{{item.title}}
						</view>
						<view class="t-distance" style="justify-content: space-between;margin-bottom: 40rpx;">
							<view class="avatar" v-if="item.member_avatar.length < 5" @click="toDetails(item.uuid)">
								<image v-for="(src, index2) in item.member_avatar" :src="src" mode="aspectFill"
									:style="{left: index2*20 + 'rpx'}"></image>
								<span
									:style="{marginLeft: item.member_avatar.length*20+40 + 'rpx', color: 'rgb(224, 224, 224)', fontSize: '18rpx', lineHeight: '40rpx' }">共{{item.people_cnt}}人</span>
							</view>
							<view class="avatar" v-else @click="toDetails(item.uuid)">
								<image v-for="(src, index2) in item.member_avatar.slice(0,3)" :src="src"
									mode="aspectFill" :style="{left: index2*20 + 'rpx'}"></image>
								<span
									style="width: 40rpx; height: 40rpx; text-align: center; line-height: 40rpx; position: absolute; top: 0; left: 80rpx; background-color: rgb(180, 180, 180); border-radius: 50%;">
									...
								</span>
								<span
									:style="{marginLeft: item.member_avatar.length*20+40 + 'rpx', color: 'rgb(224, 224, 224)', fontSize: '18rpx', lineHeight: '40rpx' }">共{{item.people_cnt}}人</span>
							</view>
							<view v-show="item.need_ticket" class="ticket_tag">
								门票活动
							</view>
						</view>
						<view class="t-distance">
							<span class="t-distance">
								<image src="../../static/active/time.png" mode="aspectFill"
									style="width: 22rpx; height: 22rpx; margin-right: 8rpx;">
								</image>
								{{item.timeline}}
							</span>
							<span class="t-distance" style="margin-left: 20rpx;">
								<image src="../../static/active/distance.png" mode="aspectFill"
									style="width: 22rpx; height: 22rpx; margin-right: 8rpx;">
								</image>
								{{item.distance}}
							</span>
						</view>
					</view>
				</view>
				<view class="content">
					{{item.content}}
				</view>
			</view>
			<view class="clear">

			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				join: 0,
				myCreate: 0,
				chooseJ: true,
				chooseC: false,
				joinArr: [],
				myCreateArr: [],
				page: 1,
				recommendTotal: true
			}
		},

		onShow() {
			this.page = 1
			this.joinArr = []
			this.myCreateArr = []
			this.myCreate = 0
			this.join = 0
			console.log("===this.page===", this.page);
			this.getJoin()
			this.getAdd()
			this.getCount()
		},
		methods: {
			choose(e) {
				if (e === '1') {
					this.chooseJ = true,
						this.chooseC = false,
						this.page = 1
				} else {
					this.chooseJ = false,
						this.chooseC = true,
						this.page = 1
				}
			},
			toDetails(e) {

				uni.navigateTo({
					url: '/pages/activity/activeMembers?uuid=' + e
				})
			},
			toDetails(e) {
				console.log('------toDetails-----------');
				uni.navigateTo({
					url: '/pages/activity/details?id=' + e
				})
			},
			getJoin() {
				this.$http.get('/activity/join-list', {
					page: this.page
				}).then(res => {
					this.recommendTotal = res.message.length
					this.joinArr.push(...res.message)
				})
			},
			getAdd() {
				this.$http.get('/activity/my-list', {
					page: this.page
				}).then(res => {
					this.recommendTotal = res.message.length
					this.myCreateArr.push(...res.message)
				})
			},
			getCount() {
				this.$http.get('/activity/cnt').then(res => {
					this.join = res.message.join
					this.myCreate = res.message.my
				})
			},
			scrolltolowerJ(s) {
				if (this.recommendTotal) {
					this.page++;
					this.getJoin()
				}
			},
			scrolltolowerM(s) {
				if (this.recommendTotal) {
					this.page++;
					this.getJoin()
				}
			},
		}

	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 20px;
		height: calc();

		.header {
			width: 100%;
			height: 88rpx;
			text-align: center;

			.default {
				color: rgb(189, 189, 192);
				font-size: 28rpx;
				font-weight: 400;
				line-height: 38rpx;
			}

			.choose {
				color: rgb(255, 255, 255);
				font-size: 28rpx;
				font-weight: 400;
				line-height: 38rpx;
			}
		}

		.list {
			width: 100%;
			height: 186px;
			border-radius: 20rpx;
			background: rgb(56, 58, 68);
			margin-top: 40rpx;
			padding: 20rpx;

			.card {
				display: flex;
				justify-content: space-between;
			}

			.active {
				width: 338rpx;
				height: 204rpx;
				margin-top: 20rpx;

				.title {
					font-size: 34rpx;
					width: 100%;
					height: 68rpx;
					margin-bottom: 32rpx;
					overflow-wrap: break-word;
					word-break: break-all;
					white-space: pre-wrap;
				}

				.avatar {
					position: relative;

					image {
						position: absolute;
						border-radius: 50%;
						display: inline-block;
						top: 0;
						left: 0;
						width: 40rpx;
						height: 40rpx;
					}
				}

				.t-distance {
					font-size: 18rpx;
					line-height: 10rpx;
					display: flex;
					align-items: center;

					.ticket_tag {
						background-image: url('../../static/active/ticket_tag.png');
						color: rgb(100, 61, 27);
						font-size: 20rpx;
						font-weight: 400;
						line-height: 28rpx;
						text-align: center;
						padding-left: 32rpx;
						padding-right: 8rpx;
						margin-right: 8rpx;
					}
				}
			}

			.content {
				width: 100%;
				height: 52rpx;
				border-radius: 12rpx;
				background: rgb(70, 78, 91);
				font-size: 20rpx;
				color: rgb(197, 209, 230);
				line-height: 14px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-top: 10px;
				padding: 6px 10px 6px 10px;
			}
		}

		.clear {
			width: 100%;
			height: 75px;
		}
	}

	.mask-layer {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(64, 72, 85, 0.79);
		/* 半透明黑色背景 */
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.mask-text {
		color: rgb(196, 196, 196);
		font-size: 14px;
	}

	.mask-container {
		position: relative;
		width: 244rpx;
		height: 244rpx;
		margin-right: 20rpx;
		border-radius: 20rpx;
		overflow: hidden;

		image {
			width: 100%;
			height: 100%;
		}
	}
</style>