<template>
	<view>
		<view class="content" @touchstart="hideDrawer">
			<scroll-view class="msg-list" scroll-y="true" :scroll-with-animation="scrollAnimation"
				:scroll-top="scrollTop" :scroll-into-view="scrollToView" @scrolltoupper="loadHistory"
				upper-threshold="50">
				<!-- 加载历史数据waitingUI -->
				<view class="loading" v-if="loading">
					<view class="spinner">
						<view class="rect1"></view>
						<view class="rect2"></view>
						<view class="rect3"></view>
						<view class="rect4"></view>
						<view class="rect5"></view>
					</view>
				</view>
				<view class="row" v-for="(row,index) in chatRoomData" :key="index" :id="'msg'+row.userUpdateTime">
					<view class="msgtime" v-if="index==0&&row.scene =='p2p'&& row.msg.time"
						style="margin-bottom: 20rpx;">{{row.msg.time}}
					</view>
					<view class="msgtime"
						v-if="index>0&&row.scene =='p2p'&& row.msg.time&&row.msg.time!=chatRoomData[index-1].msg.time"
						style="margin-bottom: 20rpx;">{{row.msg.time}}</view>
					<!-- 系统消息 -->
					<block v-if="row&&row.type=='system'">
						<view class="system">
							<!-- 文字消息 -->
							<view v-if="row.msg.type=='text'" class="text">
								{{row.msg.content.text}}
							</view>
							<!-- 领取红包消息 -->
							<view v-if="row.msg.type=='redEnvelope'" class="red-envelope">
								<image src="/static/img/red-envelope-chat.png"></image>
								{{row.msg.content.text}}
							</view>
						</view>
					</block>

					<!-- 用户消息 -->
					<block v-if="row&&row.type=='user'">
						<!-- 自己发出的消息 -->
						<view class="my" v-if="row.msg.userinfo.uid==myuid">
							<!-- 左-消息 -->
							<view class="left">
								<view
									style="width: 100%;display: flex;align-items: center;position: relative;justify-content: flex-end;">
									<image class="img32" src="../../static/images/vip/jinggao.png" mode="aspectFill"
										v-if="row.msg.noSend" style="margin-right: 16rpx;"></image>
									<!-- 文字消息 -->
									<view v-if="row.msg.type=='text'" class="bubble"
										style="border-top-right-radius: 8rpx;">
										<rich-text :nodes="row.msg.content.text"></rich-text>
										<view class="status">
											<view class="is_read t_zt" style="font-size: 24rpx;"
												v-if="roomType == 'p2p' && level>1 && !(row.msg.states=='receipt'||msgReceiptTime>=row.msg.readTime)">
												未读
												<!-- {{row.msg.states=='receipt'||msgReceiptTime>=row.msg.readTime?'已读':'未读'}} -->
											</view>
											<view class="is_read" style="font-size: 24rpx;color: #999;"
												v-if="roomType == 'p2p' && level>1 && (row.msg.states=='receipt'||msgReceiptTime>=row.msg.readTime)">
												已读
											</view>
											<view class="is_read  t_display" style="font-size: 24rpx;"
												v-if="level==1 && index==chatRoomData.length-1" @click="goVip(7)">
												<image class="img24" src="../../static/images/vip/yisongda.png"
													style="margin-right: 10rpx;" mode="">
												</image>
												<text class="tz">已送达</text>
											</view>
										</view>
									</view>
									<!-- 自定义消息 -->
									<view v-if="row.msg.type=='custom' &&row.msg.content.text.type == 'dongtai'"
										class="bubble custom" @click="goNav(row.msg.content.text.url)">
										分享
										<view class="cus-avatar">
											<view class="cus-l">
												<image :src="row.msg.content.text.avatar" style="border-radius: 50%;"
													mode="aspectFill">
												</image>
												{{row.msg.content.text.name}}
											</view>
											<view>
												{{row.msg.content.text.tip}}
											</view>
										</view>
										<div style="width: 100%;margin-top: 5px;font-size: 22rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
											v-if="row.msg.content.text.content">
											{{row.msg.content.text.content}}
										</div>
										<view class="cus-content">
											<view class="cus-content-imgs">
												<image v-for="(col,col_index) in row.msg.content.text.img"
													:key="col_index"
													@tap="showPicCustom(row.msg.content.text.img,col_index)" :src="col"
													mode="aspectFill"
													:style="{'width': `${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`,'height':`${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`}">
												</image>

												<image v-if="row.msg.content.text.video"
													:src="row.msg.content.text.video" mode="aspectFill">
												</image>
												<image v-if="row.msg.content.text.audio"
													:src="row.msg.content.text.audio" mode="aspectFill">
												</image>
											</view>
											<text v-if="row.msg.content.text.text">{{row.msg.content.text.text}}</text>
										</view>
										<view class="cus-location" v-if="row.msg.content.text.location">
											<image src="@/static/images/address.png" class="addressimg"></image>
											{{row.msg.content.text.location}}
										</view>
									</view>
									<!-- 自定义位置消息 -->
									<view v-if="row.msg.type=='custom'&&row.msg.content.text.type == 'location'"
										@click="openAmap(row)" class="bubble custom-location">

										<view class="custom-location-content">
											<view class="mapimg">
												<image class="mapavatar" :src="row.msg.content.text.img"
													mode="aspectFill">
												</image>
											</view>
											<view class="info">
												<view class="name">
													{{row.msg.content.text.name}}
												</view>
												<view class="location">
													{{row.msg.content.text.location}}
												</view>
											</view>
										</view>
									</view>
									<!-- 语言消息 -->
									<view v-if="row.msg.type=='voice'" class="bubble voice" @tap="playVoice(row.msg)">
										<image src="../../static/map/voice.gif" v-if="playMsgid == row.msg.idServer"
											style="width: 50rpx; height: 45rpx;"></image>
										<view class="icon other-voice" v-else></view>
										<view class="length">{{row.msg.content.length}}</view>
									</view>
									<!-- 图片消息 -->
									<view v-if="row.msg.type=='image'" class="bubble img"
										@tap="showPic(row.msg.content.url)">
										<image :src="row.msg.content.url" :style="{'width': row.msg.content.w+'px'}"
											mode="aspectFill" @click="showPicCustom(row.msg.content.url)">
										</image>
									</view>

								</view>
							</view>
							<!-- 右-头像 -->
							<view class="right" @click="goNav('/pages/otherPage/otherPage?imId=p2p-'+myImAccount)">
								<image :src="myAvatar" mode="aspectFill" style="border-radius: 50%;"></image>
							</view>
						</view>
						<view class="noSend" v-if="row.msg.userinfo.uid==myuid && row.msg.noSend">
							<view>已到达消息发送限制，开通会员可以给陌生人留言5次。</view>
							<view @click="goVip(4)">去开通</view>
						</view>
						<!-- 别人发出的消息 -->
						<view class="other" v-if="row.msg.userinfo.uid!=myuid ">
							<!-- 左-头像 -->
							<view class="left">
								<image :src="p2p_avatar||row.msg.userinfo.face" mode="aspectFill"
									style="border-radius: 50%;"
									@click="goNav('/pages/otherPage/otherPage?imId='+row.msg.id)"></image>
							</view>
							<!-- 右-用户名称-时间-消息 -->
							<view class="right">
								<view class="username">
									<view class="name">
										{{row.msg.userinfo.username}}
									</view>
								</view>
								<view style="width: 100%;display: flex;align-items: center;position: relative;">
									<!-- 文字消息 -->
									<view v-if="row.msg.type=='text'" class="bubble"
										style="border-top-left-radius: 8rpx;">
										<rich-text :nodes="row.msg.content.text"></rich-text>
									</view>
									<!-- 自定义消息 -->
									<!-- 自定义消息 -->
									<view v-if="row.msg.type=='custom' &&row.msg.content.text.type == 'dongtai'"
										class="bubble custom" @click="goNav(row.msg.content.text.url)">
										分享
										<view class="cus-avatar">
											<view class="cus-l">
												<image :src="row.msg.content.text.avatar" style="border-radius: 50%;"
													mode="aspectFill">
												</image>
												{{row.msg.content.text.name}}
											</view>
											<view>{{row.msg.content.text.tip}}</view>
										</view>
										<div style="width: 100%;margin-top: 5px;font-size: 22rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
											v-if="row.msg.content.text.content">
											{{row.msg.content.text.content}}
										</div>
										<view class="cus-content">
											<view class="cus-content-imgs">
												<image v-for="(col,col_index) in row.msg.content.text.img"
													:key="col_index"
													@tap="showPicCustom(row.msg.content.text.img,col_index)" :src="col"
													mode="aspectFill"
													:style="{'width': `${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`,'height':`${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`}">
												</image>
												<image v-if="row.msg.content.text.video"
													:src="row.msg.content.text.video" mode="aspectFill">
												</image>
												<image v-if="row.msg.content.text.audio"
													:src="row.msg.content.text.audio" mode="aspectFill">
												</image>
											</view>
											<text v-if="row.msg.content.text.text">{{row.msg.content.text.text}}</text>
										</view>
										<view class="cus-location" v-if="row.msg.content.text.location">
											<image src="@/static/images/address.png" class="addressimg"></image>
											{{row.msg.content.text.location}}
										</view>
									</view>
									<!-- 自定义位置消息 -->
									<view v-if="row.msg.type=='custom'&&row.msg.content.text.type == 'location'"
										class="bubble custom-location" @click="openAmap(row)">
										<view class="custom-location-content">
											<view class="mapimg">
												<image class="mapavatar" :src="row.msg.content.text.img"
													mode="aspectFill">
												</image>
											</view>
											<view class="info">
												<view class="name">
													{{row.msg.content.text.name}}
												</view>
												<view class="location">
													{{row.msg.content.text.location}}
												</view>
											</view>
										</view>
									</view>
									<!-- 语音消息 -->
									<view v-if="row.msg.type=='voice'" class="bubble voice" @tap="playVoice(row.msg)">
										<!-- :class="playMsgid == row.msg.id?'play':''" -->
										<image src="../../static/map/voice.gif" v-if="playMsgid == row.msg.idServer"
											style="width: 50rpx; height: 45rpx;" mode="aspectFill"> </image>
										<view class="icon other-voice" v-else></view>
										<view class="length">{{row.msg.content.length}}</view>
									</view>
									<!-- 图片消息 -->
									<view v-if="row.msg.type=='image'" class="bubble img"
										@tap="showPic(row.msg.content.url)">
										<image :src="row.msg.content.url" :style="{'width': row.msg.content.w+'px',}"
											@click="showPicCustom(row.msg.content.url)" mode="aspectFill">
										</image>
									</view>
									<!-- 红包 -->
									<view v-if="row.msg.type=='redEnvelope'" class="bubble red-envelope"
										@tap="openRedEnvelope(row.msg,index)">
										<image src="/static/img/red-envelope.png"></image>
										<view class="tis">
											<!-- 点击开红包 -->
										</view>
										<view class="blessing">
											{{row.msg.content.blessing}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
				<view style="height: 100rpx" id="sscroll-bottom"></view>
			</scroll-view>
		</view>
		<!-- 抽屉栏 -->
		<view class="popup-layer" :class="popupLayerClass" @touchmove.stop.prevent="discard" style="">
			<!-- 表情 -->
			<swiper class="emoji-swiper" :class="{hidden:hideEmoji}" :indicator-dots="false" duration="150">
				<swiper-item v-for="(page,pid) in emojiList" :key="pid">
					<view v-for="(em,eid) in page" :key="eid" @tap="addEmoji(em)">
						<image mode="widthFix" :src="'/static/img/emoji/'+em.url"></image>
					</view>
				</swiper-item>
			</swiper>
			<!-- 更多功能 相册-拍照-红包 -->
			<view class="more-layer" :class="{hidden:hideMore}">
				<view class="list">
					<view class="box" @tap="chooseImage">
						<view class="icon tupian2"></view>
					</view>
					<view class="box" @tap="camera">
						<view class="icon paizhao"></view>
					</view>
					<view class="box" @tap="handRedEnvelopes">
						<view class="icon hongbao"></view>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部输入栏 -->
		<view class="input-box" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<view class="t_display">
				<view class="textbox">
					<view class="voice-mode" :class="[isVoice?'':'hidden',recording?'recording':'']"
						@touchstart="voiceBegin" @touchmove.stop.prevent="voiceIng" @touchend="voiceEnd"
						@touchcancel="voiceCancel">{{voiceTis}}</view>
					<view class="text-mode" :class="isVoice?'hidden':''">
						<view class="box">
							<textarea :auto-height="true" confirm-hold confirm-type="send" @confirm="sendText(send,'text')"
								placeholder="请输入~" v-model="textMsg" @focus="textareaFocus" cursor-spacing="20" />
						</view>
						<!-- <view class="em" @tap="chooseEmoji">
							<view class="icon biaoqing"></view>
						</view> -->
					</view>

				</view>
			</view>
			<view class="t_display btnItem" style="margin-top: 15rpx;">
				<!-- H5下不能录音，输入栏布局改动一下 -->
				<!-- #ifndef H5 -->
				<!-- <view class="voice" v-if="false">
					<image v-if="!isVoice" style="width: 56rpx;height: 56rpx;" src="../../static/images/voice.png"
						mode="" @tap="switchVoice" />
					<image v-else style="width: 56rpx;height: 56rpx;" src="../../static/images/keword.png" mode=""
						@tap="switchVoice" />
				</view> -->
				<!-- #endif -->
				<image src="../../static/images/emjoy.png" @tap="chooseEmoji" style="width: 56rpx;height: 56rpx;"
					mode="" />
				<image src="../../static/images/photo.png" @tap="chooseImage" style="width: 56rpx;height: 56rpx;"
					mode="" />
				<image v-if="roomType === 'team'" src="../../static/images/at.png" @tap="chooseAt" style="width: 56rpx;height: 56rpx;"
					mode="" />
				<image src="../../static/images/location.png" style="width: 56rpx;height: 56rpx;" mode=""
					@click="sendGeoLocationMsg" />
			</view>
			<!-- #ifndef H5 -->
			<!-- <view class="more" @tap="showMore">
				<view class="icon add"></view>
			</view> -->
			<!-- #endif -->
		</view>
		<!-- 录音UI效果 -->
		<view class="record" :class="recording?'':'hidden'">
			<view class="ing" :class="willStop?'hidden':''">
				<view class="icon luyin2"></view>
			</view>
			<view class="cancel" :class="willStop?'':'hidden'">
				<view class="icon chehui"></view>
			</view>
			<view class="tis" :class="willStop?'change':''">{{recordTis}}</view>
		</view>
		<!-- 红包弹窗 -->
		<!-- 	<view class="windows" :class="windowsState">
		
			<view class="mask" @touchmove.stop.prevent="discard" @tap="closeRedEnvelope"></view>
			<view class="layer" @touchmove.stop.prevent="discard">
				<view class="open-redenvelope">
					<view class="top">
						<view class="close-btn">
							<view class="icon close" @tap="closeRedEnvelope"></view>
						</view>
						<image src="/static/img/im/face/face_1.jpg"></image>
					</view>
					<view class="from">来自{{redenvelopeData.from}}</view>
					<view class="blessing">{{redenvelopeData.blessing}}</view>
					<view class="money">{{redenvelopeData.money}}</view>
					<view class="showDetails" @tap="toDetails(redenvelopeData.rid)">
						查看领取详情 <view class="icon to"></view>
					</view>
				</view>
			</view>
		</view> -->
		<u-toast ref='notify' />
		<MapC @sendCustomLocaltion='sendCustomLocaltion' ref="mapc" />
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirmVip" />
	</view>
</template>
<script>
	import emojiData from './emojiData.js'
	import onlineEmojiData from './onlineEmojiData.js'
	import MapC from '@/components/map/popup.vue'
	import {
		apiLocationMe,
		apiUserInfo
	} from '@/api/common.js'
	import moment from 'moment'
	export default {
		components: {
			MapC
		},

		data() {
			return {
				otherInfo: "",
				imgCurrent: 0,
				myAvatar: uni.getStorageSync('avatar'),
				//文字消息
				textMsg: '',
				level: uni.getStorageSync('userInfo').vip_level,
				//消息列表
				isHistoryLoading: false,
				scrollAnimation: false,
				scrollTop: 0,
				scrollToView: '',
				msgList: [],
				msgImgList: [],
				myuid: uni.getStorageSync('im').account,
				//录音相关参数
				// #ifndef H5
				//H5不能录音
				RECORDER: uni.getRecorderManager(),
				// #endif
				isVoice: false,
				voiceTis: '按住 说话',
				recordTis: "手指上滑 取消发送",
				recording: false,
				willStop: false,
				initPoint: {
					identifier: 0,
					Y: 0
				},
				recordTimer: null,
				recordLength: 0,

				//播放语音相关参数
				AUDIO: uni.createInnerAudioContext(),
				playMsgid: null,
				VoiceTimer: null,
				isSend: true,
				// 抽屉参数
				popupLayerClass: '',
				// more参数
				hideMore: true,
				//表情定义
				hideEmoji: true,
				emojiList: emojiData, //表情图片图床名称 ，由于我上传的第三方图床名称会有改变，所以有此数据来做对应，您实际应用中应该不需要
				onlineEmoji: onlineEmojiData,
				//红包相关参数
				windowsState: '',
				redenvelopeData: {
					rid: null, //红包ID
					from: null,
					face: null,
					blessing: null,
					money: null
				},
				sendImageFile: "",
				userItem: {},
				loading: false,
				limit: 100,
				roomType: 'p2p',
				nickname: uni.getStorageSync('nickname'),
				p2p_avatar: '',
				my_avatar: '',
				p2p_username: '',
				myImAccount: ''
			};
		},
		computed: {
			msgReceiptTime() {
				if (this.$store.state.Yxim_info.msgReceiptSessionId === this.userItem.uid) {
					return this.$store.state.Yxim_info.msgReceiptTime
				} else {
					return 0
				}
			},

			chatRoomData() {
				console.log('this.isSend', this.isSend);
				const now = new Date().getTime()
				const nowDay = moment(now).format('yyyy-MM-DD')
				const roomData = this.$store.state.Yxim_info.curChatRoom
				let isSend = ""
				this.$http.get('/api/stranger-msg-check', {
					to_uid: this.otherInfo.uuid
				}).then(res => {
					isSend = res.message
				})
				const data = roomData.map((item, index) => {
					if (!item || item == undefined) return
					const cur = item
					if (item.type == 'text') {
						const ext1 = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext1.appAvatar
						// if (ext1.noSend && !isSend) {
						// 	this.isSend = false
						// }
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext1.noSend,
								id: item.sessionId,
								type: item.type,
								idServer: item.idServer,
								time: this.$common.diaplayTime2(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									text: item.body
								}
							}
						}
					} else if (item.type == 'image') {
						const ext = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext.appAvatar
						// if (ext.noSend && !isSend) {
						// 	this.isSend = false
						// }
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext.noSend,
								idServer: item.idServer,
								id: item.sessionId,
								type: item.type,
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:SS'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									w: item.attach.w,
									h: item.attach.h
								}
							}
						}
					} else if (item.type == 'geo') {
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								idServer: item.idServer,
								id: item.sessionId,
								type: item.type,
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:SS'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: "/static/img/face.jpg"
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									w: item.attach.w,
									h: item.attach.h
								}
							}
						}
					} else if (item.type == 'audio') {
						const ext2 = item.ext ? JSON.parse(item.ext) : {}
						const {
							length
						} = ext2
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext2.appAvatar
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext2.noSend,
								id: item.sessionId,
								idServer: item.idServer,
								type: 'voice',
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									size: item.attach.size,
									length: length ? length : 10
								}
							}
						}
					} else if (item.type == 'custom') {
						const ext3 = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext3.appAvatar
						// if (ext3.noSend && !isSend) {
						// 	this.isSend = false
						// }
						const {
							length
						} = ext3
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext3.noSend,
								id: item.sessionId,
								type: item.type,
								idServer: item.idServer,
								time: this.$common.diaplayTime2(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								readTime: item.time,
								states: item.status,
								content: {
									text: JSON.parse(item.ext)
								}
							}
						}
					}
					const msgtime = moment(cur.time).format('yyyy-MM-DD')
					const prev_msgtime = index > 0 ? moment(roomData[index - 1].time).format('yyyy-MM-DD') : 0
					if (index > 0) {
						if (prev_msgtime == msgtime) {
							item.msg.time = ''
						} else {
							item.msg.time = moment(cur.time).format('yyyy-MM-DD')
						}
						if (now - new Date(cur.time).getTime() < 60 * 60 * 1000) {
							item.msg.time = this.$common.diaplayTime2(cur.time)
						}
					}

					return item
				}).filter((it, index) => {
					if (it.msg.userinfo.uid != this.myuid) {
						return !it.msg.noSend
					} else if (this.isSend && it.msg.userinfo.uid == this.myuid) {
						return !it.msg.noSend
					} else if (!this.isSend && index == roomData.length - 1) {
						return true
					} else if (!this.isSend) {
						return !it.msg.noSend
					}

					return true
				})

				this.scrollToView = ''
				setTimeout(() => {
					this.scrollToView = 'sscroll-bottom'
				}, 500)
				// 重置未读
				return data
			}
		},
		onLoad(option) {
			this.$store.commit('SET_Clear_CurChatRoom')
			const im = uni.getStorageSync('im')
			this.myImAccount = im.account
			if (im && im.account) {
				apiUserInfo(im.account).then(res => {

					if (res.code == 200) {
						this.myAvatar = res.message.user_info.avatar
					}
				})
			}

			this.userItem = JSON.parse(decodeURIComponent(option.userItem))

			this.roomType = this.userItem.uid.split('-')[0] == 'team' ? 'team' : 'p2p'
			if (this.roomType == 'p2p') {
				apiUserInfo(this.userItem.uid.split('-')[1]).then(res => {
					if (res.code == 200) {
						this.otherInfo = res.message.user_info
						this.p2p_avatar = res.message.user_info.avatar
					}
				})
			}
			if (this.roomType == 'p2p') {
				this.p2p_username = this.$store.state.Yxim_info.remarkList[this.userItem.uid.split('-')[1]] || null
				uni.setNavigationBarTitle({
					title: this.$store.state.Yxim_info.remarkList[this.userItem.uid.split('-')[1]] || this.userItem
						.nuck
				})
				this.$store.commit('SET_SESSION_ResetSessionUnreadCount', this.userItem.uid)
			} else {
				uni.setNavigationBarTitle({
					title: this.userItem.nuck
				})
				this.$store.commit('SET_SESSION_ResetSessionUnreadCount', this.userItem.uid)
			}
			this.$store.commit('SET_p2pSessionId', this.userItem.uid)
			// 获取当前p2p用户得历史会话
			this.$Yxim.msgLog.getHistoryMsgs({
				scene: this.roomType,
				to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account,
				limit: this.limit,
				endTime: 0,
				asc: false
				// lastMsgId:''
			}).then(res => {
				const result = res.reverse()
				result.some(item => {
					if (item.status !== 'send' && item.status !== 'receipt') {
						this.$store.dispatch('sendMsgReceipt', item)
					}
				})
				this.$store.state.Yxim_info.curChatRoom.splice(0, 0, ...result)
				this.$store.commit('SET_SendMsgReceipt', result[result.length - 1])

			})
			this.getMsgList();
			//语音自然播放结束
			this.AUDIO.onEnded((res) => {
				this.playMsgid = null;
			});
			// #ifndef H5
			//录音开始事件
			this.RECORDER.onStart((e) => {
				this.recordBegin(e);
			})
			//录音结束事件
			this.RECORDER.onStop((e) => {
				this.recordEnd(e);
			})
			// #endif
		},
		onUnload() {
			// this.$store.commit('SET_Clear_CurChatRoom')
			this.$store.dispatch('GET_ALL_SESSIONS')
		},
		onShow() {

			//模板借由本地缓存实现发红包效果，实际应用中请不要使用此方法。
			//
			uni.getStorage({
				key: 'redEnvelopeData',
				success: (res) => {
					let nowDate = new Date();
					let lastid = this.msgList[this.msgList.length - 1].msg.id;
					lastid++;
					let row = {
						type: "user",
						msg: {
							id: lastid,
							type: "redEnvelope",
							time: nowDate.getHours() + ":" + nowDate.getMinutes(),
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								blessing: res.data.blessing,
								rid: Math.floor(Math.random() * 1000 + 1),
								isReceived: false
							}
						}
					};
					this.screenMsg(row);
					uni.removeStorage({
						key: 'redEnvelopeData'
					});
				}
			});
		},
		methods: {
			seeLog(msg) {
				console.log(JSON.stringify(msg));
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goVip(imgCurrent) {
				this.imgCurrent = imgCurrent
				this.$refs.vipTips.open()
			},
			confirmVip() {
				uni.redirectTo({
					url: '/pages/vipCenter/vipCenter'
				})
			},

			goNav(url) {
				this.navigateTo({
					url
				})
			},
			async sendAudioMsg(file) {
				const result = await this.$Yxim.msg.sendAudioMsg({
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account, //接收人
					filePath: file.url,
					ext: JSON.stringify({
						length: file.length,
						appAvatar: uni.getStorageSync('avatar')
					})
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
			},
			// 发送自定义定位消息
			async sendCustomLocaltion(option) {
				this.$refs.mapc.close()
				const send = async () => {
					const result = await this.$Yxim.msg.sendCustomMsg({
						attach: '占位',
						scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
						to: this.roomType == 'team' ? this.userItem.teamId : this.userItem
							.account, //接收人
						ext: JSON.stringify({
							name: this.nickname,
							img: uni.getStorageSync('avatar'),
							location: option.name,
							longitude: option.location.split(',')[0],
							latitude: option.location.split(',')[1],
							type: 'location',
							appAvatar: uni.getStorageSync('avatar')
						}),
					});
					if (result) {
						this.$store.commit('SET_onMsg', result)
					}
				}
				this.sendText(send, 'custom', false, option)
			},
			// 发送自定义消息
			async sendCustomMsg() {
				const result = await this.$Yxim.msg.sendCustomMsg({
					attach: '占位',
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account, //接收人
					ext: JSON.stringify({
						avatar: 'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
						name: 'zhangsan',
						tip: '查看详情',
						img: [
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665'
						],
						location: 'haerbon',
						type: 'dongtai',
						w: 40,
						h: 40,
						appAvatar: uni.getStorageSync('avatar')
					}),
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}

			},
			// 发送地理位置
			sendGeoLocationMsg() {
				this.$refs.mapc.open()

			},
			// 发送文字消息
			async send() {
				let content = this.replaceEmoji(this.textMsg);
				let msg = {
					text: content
				}
				const result = await this.$Yxim.msg.sendTextMsg({
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem
						.account, //接收人
					body: content, //发送得文本消息
					ext: JSON.stringify({
						appAvatar: uni.getStorageSync('avatar'),
					})
				});
				this.$store.commit('SET_onMsg', result)
				this.textMsg = ''; //清空输入框
			},
			// 是否可以发送文字消息
			async sendText(send, type, whiteFlag = true, option = {}) {
				this.hideDrawer(); //隐藏抽屉
				// let rows = this.chatRoomData.length ? this.chatRoomData[this.chatRoomData.length - 1] : {
				let rows = ""
				if (this.chatRoomData.length) {
					this.chatRoomData.map(item => {
						if (item.msg.userinfo.uid != this.myuid) {
							rows = item
						}
					})
				}
				rows = rows ? rows : {
					msg: {
						userinfo: {
							uid: this.myuid
						}
					}
				}
				console.log('----------rows', rows);
				// let rows = ? this.chatRoomData.filter(item => item.userinfo.uid != this
				// 		.myuid) :

				if (!this.textMsg && whiteFlag) {
					return;
				}
				console.log(this.myuid, '=========判断陌生人每天能打招呼的次数是否达到上限===========', rows.msg.userinfo.uid);
				//判断陌生人每天能打招呼的次数是否达到上限
				await this.$http.get('/share/user/' + this.otherInfo.uuid).then(res => {
					//relation:1是我关注的 2是互相关注的 3关注我的 4我自己 0陌生人
					console.log('relation好友关系：', res.message.user_info.relation);
					if (res.message.user_info.relation != 2 && rows.msg.userinfo.uid == this.myuid) {
						console.log(rows.msg.userinfo.uid == this.myuid, '最后一条是不是对方法的', !this.isSend);
						// if (!this.isSend && rows.msg.userinfo.uid == this.myuid) return this.toast('已到达消息发送限制')
						if (!this.isSend) return this.toast('已到达消息发送限制')
						this.$http.post('/api/send-stranger-msg', {
							to_uid: this.otherInfo.uuid
						}).then(async (it) => {
							// if (it.message && rows.msg.userinfo.uid == this.myuid) {
							if (it.message) {
								let content = this.replaceEmoji(this.textMsg);
								let result = ""
								this.isSend = !it.message
								switch (type) {
									case 'text':
										result = await this.$Yxim.msg.sendTextMsg({
											setting: {
												needSaveHistory: false,
												needUpdateSession: false
											},
											scene: this
												.roomType, //消息的场景  "p2p" | "team" | "superTeam"
											to: this.roomType == 'team' ? this.userItem
												.teamId : this
												.userItem
												.account, //接收人
											body: content, //发送得文本消息
											ext: JSON.stringify({
												appAvatar: uni.getStorageSync(
													'avatar'),
												noSend: true
											})
										});
										break;
									case 'custom':
										result = await this.$Yxim.msg.sendCustomMsg({
											attach: '占位',
											scene: this
												.roomType, //消息的场景  "p2p" | "team" | "superTeam"
											to: this.roomType == 'team' ? this.userItem
												.teamId : this.userItem
												.account, //接收人
											ext: JSON.stringify({
												noSend: true,
												name: this.nickname,
												img: uni.getStorageSync('avatar'),
												location: option.name,
												longitude: option.location.split(
													',')[0],
												latitude: option.location.split(
													',')[1],
												type: 'location',
												appAvatar: uni.getStorageSync(
													'avatar')
											}),
										});
										break;
									case 'image':
										const param = {
											scene: this
												.roomType, //消息的场景  "p2p" | "team" | "superTeam"
											to: this.roomType == 'team' ? this.userItem
												.teamId : this.userItem
												.account, //接收人
											filePath: this.sendImageFile, //发送得文本消息
											ext: JSON.stringify({
												noSend: true,
											}),
										}
										result = await this.$Yxim.msg.sendImageMsg(param);
										break;
									default:
										break;
								}

								this.$store.commit('SET_onMsg', result)
								this.textMsg = ''; //清空输入框

								return
							}
							send()
						})
					} else {
						send()
					}
				})
			},
			// 发送图片消息
			async sendImageMsg(file) {
				this.sendImageFile = file
				const send = async () => {
					const result = await this.$Yxim.msg.sendImageMsg({
						scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
						to: this.roomType == 'team' ? this.userItem.teamId : this.userItem
							.account, //接收人
						filePath: this.sendImageFile, //发送得文本消息
						ext: JSON.stringify({
							appAvatar: uni.getStorageSync('avatar')
						}),
					});
					if (result) {
						this.$store.commit('SET_onMsg', result)
					}
				}
				this.sendText(send, 'image', false)
			},
			// 接受消息(筛选处理)
			screenMsg(msg) {
				//从长连接处转发给这个方法，进行筛选处理
				if (msg.type == 'system') {
					// 系统消息
					switch (msg.msg.type) {
						case 'text':
							this.addSystemTextMsg(msg);
							break;
						case 'redEnvelope':
							this.addSystemRedEnvelopeMsg(msg);
							break;
					}
				} else if (msg.type == 'user') {
					// 用户消息
					switch (msg.msg.type) {
						case 'text':
							this.addTextMsg(msg);
							break;
						case 'voice':
							this.addVoiceMsg(msg);
							break;
						case 'img':
							this.addImgMsg(msg);
							break;
						case 'redEnvelope':
							this.addRedEnvelopeMsg(msg);
							break;
					}
					//非自己的消息震动
					if (msg.msg.userinfo.uid != this.myuid) {

						uni.vibrateLong();
					}
				}
				this.$nextTick(function() {
					// 滚动到底
					this.scrollToView = 'msg' + msg.msg.id
				});
			},

			//触发滑动到顶部(加载历史信息记录)
			loadHistory(e) {
				// console.log(this.chatRoomData,'chatRoomData');
				// // 获取当前p2p用户得历史会话
				// this.$Yxim.msgLog.getHistoryMsgs({
				// 	scene: this.roomType,
				// 	to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account,
				// 	limit: this.limit,
				// 	endTime: 0,
				// 	asc: false
				// 	// lastMsgId:''
				// }).then(res => {
				// 	console.log(res, ';;;;;;;;;;;;;;;;;;;;');
				// 	const result = res.reverse()
				// 	result.some(item => {
				// 		if (item.status !== 'send' && item.status !== 'receipt') {
				// 			this.$store.dispatch('sendMsgReceipt', item)
				// 		}
				// 	})
				// 	this.$store.state.Yxim_info.curChatRoom.splice(0, 0, ...result)
				// })
				// if (this.isHistoryLoading) {
				// 	return;
				// }
				// this.isHistoryLoading = true; //参数作为进入请求标识，防止重复请求
				// this.scrollAnimation = false; //关闭滑动动画
				// let Viewid = this.chatRoomData[0].msg.id; //记住第一个信息ID
				// //本地模拟请求历史记录效果
				// setTimeout(() => {


				// 	//这段代码很重要，不然每次加载历史数据都会跳到顶部
				// 	this.$nextTick(function() {
				// 		this.scrollToView = 'msg' + Viewid; //跳转上次的第一行信息位置
				// 		this.$nextTick(function() {
				// 			this.scrollAnimation = true; //恢复滚动动画
				// 		});

				// 	});
				// 	this.isHistoryLoading = false;

				// }, 1000)
			},
			// 加载初始页面消息
			getMsgList() {
				// 消息列表
				let list = [{
						type: "system",
						msg: {
							id: 0,
							type: "text",
							content: {
								text: "欢迎进入HM-chat聊天室"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 1,
							type: "text",
							time: "12:56",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								text: "为什么温度会相差那么大？"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 2,
							type: "text",
							time: "12:57",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								text: "这个是有偏差的，两个温度相差十几二十度是很正常的，如果相差五十度，那即是质量问题了。"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 3,
							type: "voice",
							time: "12:59",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								url: "/static/voice/1.mp3",
								length: "00:06"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 4,
							type: "voice",
							time: "13:05",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								url: "/static/voice/2.mp3",
								length: "00:06"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 5,
							type: "img",
							time: "13:05",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								url: "/static/img/p10.jpg",
								w: 200,
								h: 200
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 6,
							type: "img",
							time: "12:59",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								url: "/static/img/q.jpg",
								w: 1920,
								h: 1080
							}
						}
					},
					{
						type: "system",
						msg: {
							id: 7,
							type: "text",
							content: {
								text: "欢迎进入HM-chat聊天室"
							}
						}
					},

					{
						type: "system",
						msg: {
							id: 9,
							type: "redEnvelope",
							content: {
								text: "售后客服008领取了你的红包"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 10,
							type: "redEnvelope",
							time: "12:56",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								blessing: "恭喜发财，大吉大利，万事如意",
								rid: 0,
								isReceived: false
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 11,
							type: "redEnvelope",
							time: "12:56",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								blessing: "恭喜发财",
								rid: 1,
								isReceived: false
							}
						}
					},
				]
				// 获取消息中的图片,并处理显示尺寸
				for (let i = 0; i < list.length; i++) {
					if (list[i].type == 'user' && list[i].msg.type == "img") {
						list[i].msg.content = this.setPicSize(list[i].msg.content);
						this.msgImgList.push(list[i].msg.content.url);
					}
				}
				this.msgList = list;
				// 滚动到底部
				this.$nextTick(function() {
					//进入页面滚动到底部
					this.scrollTop = 9999;
					this.$nextTick(function() {
						this.scrollAnimation = true;
					});

				});
			},
			//处理图片尺寸，如果不处理宽高，新进入页面加载图片时候会闪
			setPicSize(content) {
				// 让图片最长边等于设置的最大长度，短边等比例缩小，图片控件真实改变，区别于aspectFit方式。
				let maxW = uni.upx2px(350); //350是定义消息图片最大宽度
				let maxH = uni.upx2px(350); //350是定义消息图片最大高度
				if (content.w > maxW || content.h > maxH) {
					let scale = content.w / content.h;
					content.w = scale > 1 ? maxW : maxH * scale;
					content.h = scale > 1 ? maxW / scale : maxH;
				}
				return content;
			},

			//更多功能(点击+弹出) 
			showMore() {
				this.isVoice = false;
				this.hideEmoji = true;
				if (this.hideMore) {
					this.hideMore = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},
			// 打开抽屉
			openDrawer() {
				this.popupLayerClass = 'showLayer';
			},
			// 隐藏抽屉
			hideDrawer() {
				this.popupLayerClass = '';
				setTimeout(() => {
					this.hideMore = true;
					this.hideEmoji = true;
				}, 150);
			},
			// 选择图片发送
			chooseImage() {
				this.getImage('album');
			},
			// At其他人
			chooseAt() {
				console.log('ATT')
			},
			//拍照发送
			camera() {
				this.getImage('camera');
			},
			//发红包
			handRedEnvelopes() {
				this.navigateTo({
					url: 'HM-hand/HM-hand'
				});
				this.hideDrawer();
			},
			//选照片 or 拍照
			getImage(type) {
				this.hideDrawer();
				uni.chooseImage({
					sourceType: [type],
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					success: (res) => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							uni.getImageInfo({
								src: res.tempFilePaths[i],
								success: (image) => {
									let msg = {
										url: res.tempFilePaths[i],
										w: image.width,
										h: image.height
									};
									// this.sendMsg(msg, 'img');
									this.sendImageMsg(res.tempFilePaths[i])
								}
							});
						}
					}
				});
			},
			// 选择表情
			chooseEmoji() {
				this.hideMore = true;
				if (this.hideEmoji) {
					this.hideEmoji = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},
			//添加表情
			addEmoji(em) {
				this.textMsg += em.alt;
			},

			//获取焦点，如果不是选表情ing,则关闭抽屉
			textareaFocus() {
				if (this.popupLayerClass == 'showLayer' && this.hideMore == false) {
					this.hideDrawer();
				}
			},
			openAmap(content) {
				this.$common.openMap(content.msg.content.text.longitude + ',' + content.msg.content.text.latitude, content
					.msg.content.text.location)
			},
			//替换表情符号为图片
			replaceEmoji(str) {

				let replacedStr = str.replace(/\[([^(\]|\[)]*)\]/g, (item, index) => {

					for (let i = 0; i < this.emojiList.length; i++) {
						let row = this.emojiList[i];
						for (let j = 0; j < row.length; j++) {
							let EM = row[j];
							if (EM.alt == item) {
								//在线表情路径，图文混排必须使用网络路径，请上传一份表情到你的服务器后再替换此路径 
								//比如你上传服务器后，你的100.gif路径为https://www.xxx.com/emoji/100.gif 则替换onlinePath填写为https://www.xxx.com/emoji/
								let onlinePath = 'https://s2.ax1x.com/2019/04/12/'
								let imgstr = '<img src="' + onlinePath + this.onlineEmoji[EM.url] + '">';
								return imgstr;
							}
						}
					}
				});
				return replacedStr.indexOf('<img src=img') > -1 ?
					'<div style="display: flex;align-items: center;word-wrap:break-word;">' + replacedStr + '</div>' :
					replacedStr;
			},

			// 发送消息
			sendMsg(content, type) {
				//实际应用中，此处应该提交长连接，模板仅做本地处理。
				var nowDate = new Date();
				let lastid = this.msgList[this.msgList.length - 1].msg.id;
				lastid++;
				let msg = {
					type: 'user',
					msg: {
						id: lastid,
						time: nowDate.getHours() + ":" + nowDate.getMinutes(),
						type: type,
						userinfo: {
							uid: 0,
							username: "大黑哥",
							face: "/static/img/face.jpg"
						},
						content: content
					}
				}
				// 发送消息
				this.screenMsg(msg);
				// 定时器模拟对方回复,三秒
				setTimeout(() => {
					lastid = this.msgList[this.msgList.length - 1].msg.id;
					lastid++;
					msg = {
						type: 'user',
						msg: {
							id: lastid,
							time: nowDate.getHours() + ":" + nowDate.getMinutes(),
							type: type,
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: content
						}
					}
					// 本地模拟发送消息
					this.screenMsg(msg);
				}, 3000)
			},

			// 添加文字消息到列表
			addTextMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加语音消息到列表
			addVoiceMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加图片消息到列表
			addImgMsg(msg) {
				msg.msg.content = this.setPicSize(msg.msg.content);
				this.msgImgList.push(msg.msg.content.url);
				this.msgList.push(msg);
			},
			addRedEnvelopeMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加系统文字消息到列表
			addSystemTextMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加系统红包消息到列表
			addSystemRedEnvelopeMsg(msg) {
				this.msgList.push(msg);
			},
			diaplayTime2(date) {
				let str = date;
				let result = ''
				//将字符串转换成时间格式
				let timePublish = new Date(str);
				let timeNow = new Date();
				let minute = 1000 * 60;
				let hour = minute * 60;
				let day = hour * 24;
				let month = day * 30;
				let year = month * 12;
				let diffValue = timeNow - timePublish;
				let diffMonth = diffValue / month;
				let diffWeek = diffValue / (7 * day);
				let diffDay = diffValue / day;
				let diffHour = diffValue / hour;
				let diffMinute = diffValue / minute;
				let diffYear = diffValue / year;
				if (diffValue < 0) {
					result = "刚刚发表";
				} else if (diffYear > 1) {
					result = parseInt(diffYear) + "年前";
				} else if (diffMonth > 1) {
					result = parseInt(diffMonth) + "月前";
				} else if (diffWeek > 1) {
					result = parseInt(diffWeek) + "周前";
				} else if (diffDay > 1) {
					result = parseInt(diffDay) + "天前";
				} else if (diffHour > 1) {
					result = parseInt(diffHour) + "小时前";
				} else if (diffMinute > 1) {
					result = parseInt(diffMinute) + "分钟前";
				} else {
					result = "刚刚发表";
				}
				return result;
			},
			// 打开红包
			openRedEnvelope(msg, index) {
				let rid = msg.content.rid;
				uni.showLoading({
					title: '加载中...'
				});

				//模拟请求服务器效果
				setTimeout(() => {
					//加载数据
					if (rid == 0) {
						this.redenvelopeData = {
							rid: 0, //红包ID
							from: "大黑哥",
							face: "/static/img/im/face/face.jpg",
							blessing: "恭喜发财，大吉大利",
							money: "已领完"
						}
					} else {
						this.redenvelopeData = {
							rid: 1, //红包ID
							from: "售后客服008",
							face: "/static/img/im/face/face_2.jpg",
							blessing: "恭喜发财",
							money: "0.01"
						}
						if (!msg.content.isReceived) {
							// {type:"system",msg:{id:8,type:"redEnvelope",content:{text:"你领取了售后客服008的红包"}}},
							this.sendSystemMsg({
								text: "你领取了" + (msg.userinfo.uid == this.myuid ? "自己" : msg.userinfo
									.username) + "的红包"
							}, 'redEnvelope');

							this.msgList[index].msg.content.isReceived = true;
						}
					}
					uni.hideLoading();
					this.windowsState = 'show';

				}, 200)

			},
			// 关闭红包弹窗
			closeRedEnvelope() {
				this.windowsState = 'hide';
				setTimeout(() => {
					this.windowsState = '';
				}, 200)
			},
			sendSystemMsg(content, type) {
				let lastid = this.msgList[this.msgList.length - 1].msg.id;
				lastid++;
				let row = {
					type: "system",
					msg: {
						id: lastid,
						type: type,
						content: content
					}
				};
				this.screenMsg(row)
			},
			//领取详情
			toDetails(rid) {
				this.navigateTo({
					url: 'HM-details/HM-details?rid=' + rid
				})
			},
			showPicCustom(url, index) {
				uni.previewImage({
					indicator: "none",
					urls: [url]
				});
			},
			// 预览图片
			showPic(msg) {
				uni.previewImage({
					indicator: "none",
					current: msg.content.url,
					urls: this.msgImgList
				});
			},
			// 播放语音
			playVoice(msg) {

				this.playMsgid = msg.idServer;
				this.AUDIO.src = msg.content.url;
				this.$nextTick(function() {
					this.AUDIO.play();
				});
			},
			// 录音开始
			voiceBegin(e) {

				if (e.touches.length > 1) {
					return;
				}
				this.initPoint.Y = e.touches[0].clientY;
				this.initPoint.identifier = e.touches[0].identifier;
				this.RECORDER.start({
					format: "mp3"
				}); //录音开始,
			},
			//录音开始UI效果
			recordBegin(e) {
				this.recording = true;
				this.voiceTis = '松开 结束';
				this.recordLength = 0;
				this.recordTimer = setInterval(() => {
					this.recordLength++;
				}, 1000)
			},
			// 录音被打断
			voiceCancel() {
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				this.willStop = true; //不发送录音
				this.RECORDER.stop(); //录音结束
			},
			// 录音中(判断是否触发上滑取消发送)
			voiceIng(e) {
				if (!this.recording) {
					return;
				}
				let touche = e.touches[0];
				//上滑一个导航栏的高度触发上滑取消发送
				if (this.initPoint.Y - touche.clientY >= uni.upx2px(100)) {
					this.willStop = true;
					this.recordTis = '松开手指 取消发送'
				} else {
					this.willStop = false;
					this.recordTis = '手指上滑 取消发送'
				}
			},
			// 结束录音
			voiceEnd(e) {
				if (!this.recording) {
					return;
				}
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				this.RECORDER.stop(); //录音结束
			},
			//录音结束(回调文件)
			recordEnd(e) {
				clearInterval(this.recordTimer);
				if (!this.willStop) {

					let msg = {
						length: 0,
						url: e.tempFilePath
					}
					let min = parseInt(this.recordLength / 60);
					let sec = this.recordLength % 60;
					min = min < 10 ? '0' + min : min;
					sec = sec < 10 ? '0' + sec : sec;
					msg.length = min + ':' + sec;
					this.sendAudioMsg(msg)
					// this.$store.commit('SET_onMsg', msg)
					// this.sendMsg(msg, 'voice');

				} else {
					console.log('取消发送录音');
				}
				this.willStop = false;
			},
			// 切换语音/文字输入
			switchVoice() {
				this.hideDrawer();
				this.isVoice = this.isVoice ? false : true;
			},
			discard() {
				return;
			}
		}
	}
</script>
<style lang="scss">
	@import "@/static/HM-chat/css/style.scss";
</style>
<style lang="scss" scoped>
	.noSend {
		font-weight: 500;
		font-size: 24rpx;
		color: #8C8C8C;
		text-align: center;
		margin-top: 60rpx;

		view:last-child {
			background-image: linear-gradient(to right, #4BC6ED 10%, 40%, #BC93F2);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.tz {
		background-image: linear-gradient(to right, #4BC6ED 10%, 70%, #BC93F2);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.status {
		position: absolute;
		right: 0rpx;
		bottom: -40rpx;
		text-align: right;
	}

	.msgtime {
		font-size: 24rpx;
		color: #ddd;
		text-align: center;
	}

	.btnItem {
		justify-content: space-between;
		padding: 0 84rpx;
		margin-bottom: 50rpx;
		// margin-bottom: 68rpx;
	}

	::v-deep .custom-location {
		.custom-location-content {
			width: 408rpx;
			display: flex;
			align-items: center;

			.mapimg {
				width: 124rpx;
				height: 124rpx;
				background-image: url(@/static/map/mapicon1.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				position: relative;
				margin-top: 20rpx;

				.mapavatar {
					width: 40rpx;
					height: 40rpx;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-80%, -80%);
					z-index: 22;
					border-radius: 50%;
				}


			}

			.info {
				height: 124rpx;
				margin-left: 10rpx;

				.name {
					font-size: 32rpx;
					margin-top: 10rpx;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}

				.location {
					width: 240rpx;
					font-size: 24rpx;
					margin-top: 10rpx;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}
			}


		}
	}

	::v-deep .custom {
		width: 90vw;
		display: flex;
		flex-direction: column;

		.cus-avatar {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 24rpx;

			.cus-l {
				display: flex;
				align-items: center;
				font-size: 24rpx;

				image {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}


		}

		.cus-content {
			max-width: 100%;

			.cus-content-imgs {
				width: 430rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				image {
					border-radius: 20rpx;
					margin: 20rpx 0;
				}
			}

		}

		.cus-location {
			width: 100%;
			display: flex;
			align-items: center;
			font-size: 24rpx;

			image {
				width: 20rpx;
				height: 20rpx;
				margin-right: 10rpx;
			}
		}
	}

	.right {
		position: relative;

		.is_read {
			margin: 0 0 0 20rpx;
			font-size: 24rpx;
		}
	}
</style>