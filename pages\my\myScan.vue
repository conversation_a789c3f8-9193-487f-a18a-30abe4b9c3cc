<template>
	<view class="container">
		<!-- 此处为容器 -->
		<view class="img323"></view>
		<view class="t_center">
			<view class="content">
				<span style="width: 50rpx;">·</span>{{nickname}}<span style="width: 50rpx;">·</span>
			</view>
			<view class="idStyle">{{id}}</view>
			<image class="img409" :src="img" mode=""></image>
		</view>
		<view class="t_display" style="margin-top: 253rpx;justify-content: space-around;">
			<view class="t_center" @click="goScan">
				<image class="img108" src="../../static/images/myScan/sys.png" mode=""></image>
				<view class="txt">扫一扫</view>
			</view>
			<view class="t_center" @click="saveBaseImgFile">
				<image class="img108" src="../../static/images/myScan/xiazai.png" mode=""></image>
				<view class="txt">下载</view>
			</view>
			<!-- <view class="t_center" @click="share">
				<image class="img108" src="../../static/images/myScan/fx.png" mode=""></image>
				<view class="txt">分享</view>
			</view> -->
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				img: "",
				id: "ID:",
				nickname: uni.getStorageSync('nickname')
			}
		},
		onLoad() {
			const user_info = uni.getStorageSync('userInfo')
			this.id += user_info.uuid
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			share() {
				this.toast('暂未开通')
			},
			saveBaseImgFile() {
				let base64 = this.img;
				const bitmap = new plus.nativeObj.Bitmap("base64");
				bitmap.loadBase64Data(base64, function() {
					const url = "_doc/" + new Date().getTime() + ".png";
					console.log('saveHeadImgFile', url)
					bitmap.save(url, {
						overwrite: true, // 是否覆盖
						// quality: 'quality'  // 图片清晰度
					}, (i) => {
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success: function() {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
								bitmap.clear()
							}
						});
					}, (e) => {
						uni.showToast({
							title: '图片保存失败',
							icon: 'none'
						})
						bitmap.clear()
					});
				}, (e) => {
					uni.showToast({
						title: '图片保存失败',
						icon: 'none'
					})
					bitmap.clear()
				});
			},
			goScan() {
				// 只允许通过相机扫码
				this.$common.scan()
			},
			getData() {
				this.$http.get('/api/user/qrcode').then(res => {
					this.img = 'data:image/png;base64,' + res.message
					console.log("this.img", res);
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 这里的scoped表示只针对当前组件生效 */
	.container {
		background: url('../../static/images/myScan/bg.png')100% 100%;
		/* 根据自己项目结构调整图片路径 */
		width: 100%;
		height: 100vh;
		background-size: cover;

		/* 控制背景图大小与位置 */
		.content {
			font-weight: 700;
			font-size: 42rpx;
			color: #FFFFFF;
			text-align: center;
		}

		.idStyle {
			margin-top: 8rpx;
			margin-bottom: 24rpx;
			font-size: 28rpx;
		}

		.txt {
			font-weight: 500;
			font-size: 28rpx;
			line-height: 41rpx;
			margin-top: 16rpx;
		}
	}
</style>