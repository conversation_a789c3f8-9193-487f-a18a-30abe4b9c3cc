<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->

		</view>
		<view class="navigation-zhezhao">
			<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">商户简介</view>
			<view class="navigation-zhezhao-submit" @click="submit('valiForm')">提交</view>
		</view>
		<view class="shop-introduce-content">
			<uni-forms class="shop-introduce-form" ref="valiForm" :rules="rules" :modelValue="valiFormData"
				label-position="top">
				<!-- 店铺公告 -->
				<uni-forms-item label="店铺公告" name="describe">
					<textarea class="common-white-bg" v-model="valiFormData.describe" maxlength="500"
						placeholder-style="fontSize:28rpx;color: rgb(152, 152, 152);" placeholder="请输入店铺公告内容" />
				</uni-forms-item>
				<!-- 客服电话 -->
				<uni-forms-item label="客服电话" name="srv_phone">
					<input class="common-white-bg" v-model="valiFormData.srv_phone" maxlength="11" type="number"
						placeholder="请输入手机号码" placeholder-style="fontSize:28rpx;color: rgb(152, 152, 152);" />
				</uni-forms-item>
			</uni-forms>
		</view>
		
	</view>
</template>

<script>
	import {
		config
	} from '@/config.js';
	export default {
		data() {
			return {
				shopId: 0,
				valiFormData: {
					describe: "",
					srv_phone: ""
				},
				// 校验规则
				rules: {
					srv_phone: {
						rules: [{
							required: true,
							errorMessage: '手机号不能为空'
						}]
					},
					describe: {
						rules: [{
							required: true,
							errorMessage: '店铺公告不能为空'
						}]
					}
				},
			};
		},
		created() {},
		onLoad(options) {
			if (options.shopId) {
				this.shopId = options.shopId;
			}
			if(uni.getStorageSync('shopInfo') != ''){
				this.valiFormData = uni.getStorageSync('shopInfo').business_intro;
				// this.valiFormData.srv_phone = uni.getStorageSync('shopInfo').business_intro.srv_phone;
				console.log(this.valiFormData)
			}
		},
		onShow() {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 前景色值，包括按钮、标题、状态栏的颜色
				backgroundColor: '#000000', // 背景颜色值，包括背景图
				animation: {
					duration: 400,
					timingFunc: 'easeIn'
				}
			});
		},
		methods: {
			submit(ref) {
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					this.$http.post('/api/user/business/set-intro', {
						"business_id": this.shopId,
						"srv_phone":res.srv_phone,
						"describe":res.describe,
					}).then((res)=>{
						uni.showToast({
							title: `提交成功！`
						})
						this.goBack();
					})
				}).catch(err => {
					console.log('err', err);
				})
			},
			goBack() {
				console.log('点击了返回');
				uni.navigateBack({
					delta: 1
				});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #F5F7FB;
	}

	.content {
		width: 100%;
		height: 100%;
		padding-top: 100px;
		background-color: #F5F7FB;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 140px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: absolute;
			z-index: 0;
			top: 0;
			left: 0;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.navigation-zhezhao {
			width: 100%;
			height: 140px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position: absolute;
			z-index: 0;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			height: 140px;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.navigation-zhezhao-submit{
				width:30px;
				margin-right:2%;
				color: #725EE9;
				font-family: 阿里巴巴普惠体;
				font-size: 14px;
				font-weight: 400;
				line-height: 19px;
				
			}
		}

		.common-white-bg {
			width: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			padding:16px;
			box-sizing: border-box;
			color: rgb(152, 152, 152);
			font-family: HarmonyOS Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 18px;
			min-height: 46px;
		}
		
		.shop-introduce-content{
			width:100%;
			background-color: #F5F7FB;
			position: relative;
			z-index: 10;
			padding-top:20px;
			box-sizing: border-box;
			.shop-introduce-form {
				width: 92%;
				margin: 0 auto;
				/deep/.uni-forms-item{
					.uni-forms-item__content{
						.uni-input-input{
							color: rgb(33, 33, 33) !important;
							font-family: HarmonyOS Sans;
							font-size: 14px;
							font-weight: 400;
							line-height: 18px;
						}
						.uni-textarea-textarea{
							color: rgb(33, 33, 33) !important;
							font-family: HarmonyOS Sans;
							font-size: 14px;
							font-weight: 400;
							line-height: 18px;
						}
						
					}
				}
				.uni-forms-item__label{
					color: #212121 !important;
				}
			}
		}
		
	}
</style>