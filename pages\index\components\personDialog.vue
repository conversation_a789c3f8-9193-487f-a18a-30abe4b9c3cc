<template>
  <uni-popup
    class="personDialog"
    ref="popup"
    type="bottom"
    maskBackgroundColor="transparent"
    :safe-area="false"
    @maskClick="onMaskClick"
  >
    <view class="info-content" v-if="JSON.stringify(info) != '{}'">
      <view class="title"></view>
      <image
        src="../../../static/map/close.png"
        class="close"
        mode=""
        @click="close"
      ></image>
      <view class="desc">
        <image
          class="avatar"
          :src="info.origin_avatar"
          @click="goDetail"
          mode="aspectFill"
        ></image>
        <view class="miaoshu">
          <view class="name">{{ info.nick_name }}</view>
          <view class="address">
            <view class="pos">
              <image
                src="../../../static/map/weizhi.png"
                class="pos-icon"
              ></image>
              {{ info.location_info.district }}
            </view>
            <view class="licheng">
              <image
                src="../../../static/map/zuji.png"
                class="licheng-icon"
              ></image>
              {{ info.dist_from_me | dist_from_meTxt }}
            </view>
          </view>
          <view class="juli" @click="handleGhostSwitch">
            对方看到你的位置是
            <text>{{ info.ghost_mode | ghost_modeTxt }}位置</text>
            <image class="jl-icon" src="../../../static/map/change.png"></image>
          </view>
        </view>
      </view>
      <view class="send" v-if="info.relation == 1 || info.relation == 4">
        <view class="bbbox"></view>
        <view class="cccox">
          <input
            @click="sendMsg"
            type="text"
            :placeholder="`给${info.nick_name}发送消息`"
            :disabled="true"
          />
          <view class="btn" @click="sendMsg">
            <!-- @click="handleNav" -->
            <image src="../../../static/map/noSend.png" mode="widthFix"></image>
          </view>
        </view>
      </view>
      <!-- //动态里的relation字段关系是1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
      <view class="send" v-if="info.relation == 2 || info.relation == 3">
        <view class="bbbox"></view>
        <view class="cccox">
          <input
            @click="sendMsg"
            type="text"
            :placeholder="`给${info.nick_name}发送消息`"
            :disabled="true"
          />
          <view class="btn send-btn" @click="sendMsg">
            <!-- @click="handleNav" -->
            <image src="../../../static/map/msg.png" mode="widthFix"></image>
          </view>
        </view>
      </view>
      <view class="send" v-if="info.relation == 0">
        <view class="guanzhu" @click="handleFollowAdd">关注</view>
        <view class="btn" @click="handleNav">
          <image src="../../../static/map/send.png" mode="widthFix"></image>
        </view>
      </view>
      <view class="img74" />
    </view>
    <vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirm" />
    <u-toast ref="notify" />
  </uni-popup>
</template>

<script>
import {
  apiUserFollowAddMapInfo,
  apiLocationGhostSwitch,
} from "@/api/common.js";
export default {
  data() {
    return {
      imgCurrent: 0,
      active: 1,
      info: {},
    };
  },
  filters: {
    ghost_modeTxt(str) {
      if (str == 0) return "精确";
      if (str == 1) return "模糊";
      if (str == 2) return "冻结";
    },
    dist_from_meTxt(num) {
      let mi = "";
      if (num / 1000 > 10) {
        mi = (num / 1000).toFixed(0) + " km";
      } else if (num / 1000 > 1 && num / 1000 > 10) {
        mi = (num / 1000).toFixed(2) + " km";
      } else if (num / 1000 < 1) {
        parseInt(num) + " m";
      }

      // mi = num / 1000 > 1 ? (num / 1000).toFixed(2) + ' km' : parseInt(num) + ' m'

      return "距离 " + mi;
    },
  },
  mounted() {},
  methods: {
    onMaskClick() {
      // 当点击蒙层时通知父组件清除选中状态
      this.$emit("clearSelection");
    },
    confirm() {
      uni.navigateTo({
        url: "/pages/vipCenter/vipCenter",
      });
    },
    toast(title) {
      this.$refs.notify.show({
        title,
        position: "top",
      });
    },
    goDetail() {
      uni.navigateTo({
        url: "/pages/otherPage/otherPage?uuid=" + this.info.uuid,
      });
    },
    handleGhostSwitch() {
      apiLocationGhostSwitch({
        mode: this.info.ghost_mode == 0 ? 1 : this.info.ghost_mode == 1 ? 2 : 0,
        uid: this.info.uid,
      }).then((res) => {
        if (res.code == 200) {
          if (JSON.stringify(res.message) == "{}") {
            this.imgCurrent = 2;
            this.$emit("vipOpen");
            // this.$refs.vipTips.open('center')
            return;
          }
          this.toast("操作成功");
          this.info = res.message;
          this.$emit("GhostSwitch", res.message);
        }
      });
    },

    sendMsg() {
      let params = {
        account: this.info.im_id,
        chatHeadImg: this.info.avatar,
        chatName: this.info.nick_name,
        uid: "p2p-" + this.info.im_id,
        roomtype: "p2p",
        nuck: this.info.nick_name,
      };
      uni.navigateTo({
        url: `/pages/HM-chat/HM-chat?userItem=${encodeURIComponent(
          JSON.stringify(params)
        )}`,
      });
      this.$refs.popup.close();
    },
    async handleNav() {
      console.log("====this.info======", this.info);
      const { location_info } = this.info;
      //relation:1是我关注的 2是互相关注的 3关注我的 4我自己 0陌生人
      if (this.info.relation == 1 || this.info.relation == 4) {
        this.toast("非互相关注");
        return false;
      }
      if (this.info.relation != 2) {
        this.$common.openMap(
          `${location_info.location.Longitude},${location_info.location.Latitude}`,
          this.info.location_info.formatted_address
        );
        this.$refs.popup.close();
        return false;
      }
    },
    async handleFollowAdd() {
      const res = await apiUserFollowAddMapInfo({
        uuid: this.info.uuid,
      });
      if (res.code == 200) {
        this.toast("关注成功");
        console.log("关注成功", res.message);
        this.info = res.message;
        this.$emit("followAddSuccess", res.message);
        // this.$refs.popup.close();
      }
    },
    open(option) {
      console.log("asdasdasd", option);
      this.info = option;
      this.$refs.popup.open("bottom");
    },
    close() {
      // 关闭弹窗时通知父组件清除选中状态
      this.$emit("clearSelection");
      this.$refs.popup.close();
    },
    changeType(str) {
      this.active = str;
    },
  },
};
</script>

<style lang="scss" scoped>
.info-content {
  width: 100vw;
  background-color: #000;
  border-radius: 50rpx 50rpx 0 0;
  box-sizing: border-box;
  color: #fff;
  padding: 50rpx 40rpx 0rpx;
  position: relative;

  &::after {
    width: 100vw;
    height: 120rpx;
    content: "";
    background-color: #000;
    position: absolute;
    left: 0;
    bottom: -90rpx;
  }

  .close {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    right: 40rpx;
    top: 20rpx;
  }

  .title {
    width: 80rpx;
    height: 10rpx;
    background-color: #fff;
    border-radius: 10rpx;
    position: absolute;
    left: 50%;
    top: 20rpx;
    transform: translateX(-50%);
    // margin: 0 auto 20rpx;
  }

  .desc {
    width: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 5rpx solid #fff;
    }

    .miaoshu {
      flex: 1;

      .name {
        font-weight: bold;
        font-size: 36rpx;
      }

      .address,
      .juli {
        font-size: 28rpx;
        margin-top: 5rpx;
        display: flex;
        align-items: center;

        text {
          color: transparent;
          background-image: linear-gradient(90deg, #4beae8 0%, #c095f7 100%);
          background-image: linear-gradient(90deg, #4beae8 0%, #c095f7 100%);
          -webkit-background-clip: text;
          background-clip: text;
          margin-left: 10rpx;
        }

        .jl-icon {
          width: 40rpx;
          height: 30rpx;
          margin-left: 10rpx;
        }

        .pos,
        .licheng {
          flex: 1;
          display: flex;
          align-items: center;
          // margin-right: 50rpx;
        }

        .pos-icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 20rpx;
          vertical-align: middle;
        }

        .licheng-icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 20rpx;
          vertical-align: middle;
        }
      }
    }
  }

  .send {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 50rpx;
    position: relative;

    .bbbox {
      width: 400rpx;
      height: 74rpx;
      content: "";
      inset: 0;
      background: linear-gradient(135deg, #4beae8 0%, #c095f7 100%);
      -webkit-mask-image: linear-gradient(#fff 0 0), linear-gradient(#fff 0 0);
      -webkit-mask-clip: content-box, border-box;
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      padding: 2rpx;
      border-radius: 20rpx;
      position: relative;
    }

    .cccox {
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    input {
      width: 400rpx;
      height: 74rpx;
      border-radius: 20rpx;
      padding-left: 30rpx;
      position: relative;
      box-sizing: border-box;
      inset: 0;

      z-index: 999;
    }

    .btn {
      width: 200rpx;
      height: 70rpx;
      border: 2rpx solid #fff;
      border-radius: 20rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 50rpx;
      }
    }

    .send-btn {
      background: linear-gradient(90deg, #4beae8 0%, #c095f7 100%);
      border: 0;
      padding: 2rpx;
    }

    .guanzhu {
      width: 420rpx;
      border-radius: 20rpx;
      height: 74rpx;
      background: linear-gradient(90deg, #4beae8 0%, #c095f7 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
