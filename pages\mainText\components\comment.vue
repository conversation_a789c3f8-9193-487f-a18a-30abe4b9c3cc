<template>
	<view>
		<view class="comment" v-for="(res, index) in dataArr" :key="res.id">
			<view class="left">
				<image :src="res.user_info.avatar" mode="aspectFill"
					@click="goNav('/pages/otherPage/otherPage?uuid='+res.user_info.uuid)"></image>
			</view>
			<view class="right">
				<view class="top">
					<view class="name">{{ res.user_info.nickname }}</view>
				</view>
				<view class="content" @click="reply(res.comment_id,res.moment_id,index,res.user_info.nickname)">
					{{ res.content }}
				</view>
				<view class="t_display" style="justify-content: space-between;margin-bottom: 25rpx;">
					<view class="times">
						{{res.timeline}}
					</view>
					<view class="like t_display" :class="{ highlight: res.isLike }"
						@click="setCommentLike(res.comment_id, res.is_like,index)">
						<image class="img24" v-if="!res.is_like" src="../../../static/images/like.png" mode=""></image>
						<image class="img24" v-else src="../../../static/images/like2.png" mode=""></image>
						<view class="like_nums">{{ res.like }}</view>
					</view>
				</view>
				<view class="reply-box" v-if="res.little_reply.length"
					:style="{maxHeight: !res.little_reply.length?'none': itemHeight*res.little_reply.length+'rpx'}">
					<view class="item" v-for="(item, ids) in res.little_reply" :key="ids"
						@click="reply(res.comment_id,res.moment_id,index,res.user_info.nickname, item.user_info.ext.uid)">
						<view class="username t_display">
							<image class="img42" :src="item.user_info.avatar" mode="aspectFill"></image>
							{{ item.user_info.nickname }}
						</view>
						<view class="text" style="margin-left: 56rpx;">{{ item.content }}</view>
						<view class="t_display issus" style="">
							<view class="times">
								{{ item.timeline }}
							</view>
							<!-- <view class="like t_display" :class="{ highlight: item.is_like }">
								<image class="img24" v-if="!item.is_like" src="../../../static/images/like.png"
									@click="getLike(ids)" mode=""></image>
								<image class="img24" v-else src="../../../static/images/like2.png" @click="getLike(ids)"
									mode=""></image>
								<view class="like_nums">{{item.like}}</view>
							</view> -->
						</view>
					</view>
				</view>
				<view class="all-reply t_display" style="float: right;margin-top: 9rpx;"
					@tap="toAllReply(res.comment_id,res.little_reply,index)"
					v-if="res.reply_total>res.little_reply.length">
					<view class="codoColor">
						展开更多回复
					</view>
					<image class="img42" src="../../../static/images/themeDown.png" mode=""></image>
				</view>
			</view>
			<u-toast ref='notify' />
		</view>
	</view>
</template>

<script>
	import {
		nextTick
	} from "vue";
	export default {
		props: {
			dataArr: {
				type: Array,
				default: []
			}
		},
		data() {
			return {
				itemHeight: 184,
				commentList: []
			};
		},
		created() {
			// this.getComment();
		},
		methods: {
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			reply(commentId, momentId, index, nickname, uid = 0) {
				console.log("uid", nickname);
				this.$emit("reply", {
					index,
					commentId,
					uid,
					nickname,
					momentId
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			// 跳转到全部回复
			toAllReply(commentId, little_reply, index) {
				this.$emit('getMore', {
					index,
					maxId: little_reply[little_reply.length - 1].reply_id,
					commentId
				})
				// this.commentList[index].moreFlag = true
				// this.$forceUpdate()
			},
			setCommentLike(comment_id, isLike, index) {
				this.$emit('setCommentLike', {
					comment_id,
					isLike,
					index
				})
			},
			// 点赞
			getLike(index) {
				this.$emit('setCommentContentLike', {
					comment_id,
					isLike,
					index
				})
				// this.commentList[index].isLike = !this.commentList[index].isLike;
				// if (this.commentList[index].isLike == true) {
				// 	this.commentList[index].likeNum++;
				// } else {
				// 	this.commentList[index].likeNum--;
				// }
			},
		}
	};
</script>

<style lang="scss" scoped>
	.times {
		height: 35rpx;
		font-size: 24rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.5);
	}

	.codoColor {
		font-size: 24rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.like_nums {
		margin-left: 8rpx;
		font-size: 24rpx;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.75);
		line-height: 31rpx;
	}

	.comment {
		display: flex;
		padding: 30rpx;

		.left {
			image {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				background-color: #f2f2f2;
			}
		}

		.right {
			flex: 1;
			padding-left: 20rpx;
			font-size: 30rpx;

			.top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;

				.name {
					// color: #5677fc;
					color: rgba(255, 255, 255, 0.5);
				}

				.like {
					display: flex;
					align-items: center;
					color: #9a9a9a;
					font-size: 26rpx;


				}

				.highlight {
					color: rgba(255, 255, 255, 0.5);

					.num {
						color: rgba(255, 255, 255, 0.5);
					}
				}
			}

			.content {
				margin-bottom: 10rpx;
				font-size: 28rpx;
			}

			.reply-box {
				background-color: #323232;
				border-radius: 12rpx;
				font-size: 26rpx;
				color: #C8C8C8;
				padding-bottom: 20rpx;
				overflow: hidden;

				.item {
					padding: 20rpx;
					padding-bottom: 0;

					.issus {
						justify-content: space-between;
						margin-top: 11rpx;
						margin-left: 56rpx;
					}

					.username {
						font-size: 28rpx;
						color: #999999;

						image {
							border-radius: 280rpx;
							margin-right: 16rpx;
						}
					}
				}

				.all-reply {
					padding: 20rpx;
					display: flex;
					align-items: center;
					margin-top: 9rpx;

					.more {
						margin-left: 6rpx;
					}
				}
			}

			.bottom {
				margin-top: 20rpx;
				display: flex;
				font-size: 24rpx;
				color: #9a9a9a;

				.reply {
					color: rgba(255, 255, 255, 0.5);
					margin-left: 10rpx;
				}
			}
		}
	}
</style>