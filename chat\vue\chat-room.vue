 <template>
 	<view class="container">
 		<tianxie-chat-room :chatUser="chatUser" @saveData="saveData" :timeInterval="timeInterval" :memeFile="memeFile"
 			:acceptScokData="acceptScokData"></tianxie-chat-room>
 	</view>
 </template>

 <script>
 	export default {
 		data() {
 			return {
 				chatUser: {}, //从聊天列表接收别人的信息
 				timeInterval: 5, //发送时间间隔，默认为5分钟
 				memeFile: '/chat/static/emoji/', //表情包所在的路径
 				acceptScokData: {}, //sockt接收到的数据
 			}
 		},
 		onLoad(options) {
 			this.chatUser = JSON.parse(options.userItem);
 			this.acceptScokData = this.chatUser;
 			uni.setNavigationBarTitle({
 				title: this.chatUser.chatName,
 			})
 		},
 		methods: {
 			//数据类型返回存储
 			saveData(obj) {
 				//msgType消息类型text为文字，img为图片，voice为语音，voido为视频，meme为表情包，time为时间数据，voidoCall为视频通话，place为地址，timeValue为时间间隔值
 				console.log(obj);
 				//视频通话根据需求有多种情况接入，此处不做处理，但可接收到视频数据传输到聊天窗中
 				if (obj.msgType == "voidoCall") {
 					obj.newMsg = "当前通话01:20:05";
 					this.acceptScokData = obj;
 				}

 			}
 		}
 	}
 </script>

 <style>
 </style>