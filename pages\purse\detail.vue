<template>
	<view class="appPage">
		<view class="card">
			<view class="t_betweent">
				<view class="name">领取位置红包</view>
				<view class="price">+214.00</view>
			</view>
			<view class="time">2023/11/24</view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 0 32rpx;

		.card {
			margin-top: 32rpx;
			width: 686rpx;
			height: 172rpx;
			background: #2F3341;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			opacity: 1;
			padding: 32rpx;

			.time {
				margin-top: 24rpx;
				font-size: 26rpx;
				font-family: Source <PERSON>, Source <PERSON>;
				font-weight: 400;
				color: #D9D9D9;
				line-height: 38rpx;
			}

			.name {
				font-size: 32rpx;
				font-family: Source <PERSON>, Source <PERSON>;
				font-weight: 500;
				color: #ECECEC;
				line-height: 46rpx;
			}

			.price {
				font-size: 32rpx;
				font-family: Source <PERSON>, Source <PERSON>;
				font-weight: 700;
				color: #ECECEC;
				line-height: 46rpx;
			}
		}
	}
</style>