<template>
	<view class="appPage">
		<image class="bg" src="../../static/images/pay/payBg.png" mode=""></image>
		<view class="btn" @click="goAplipay">
			立即绑定
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},

		onShow() {
			// let args = plus.runtime.arguments;
			// //console.log(args); //这里可以看到从后端拿过来的urlscheme
			// if (args) {
			// 	plus.runtime.arguments = null; //进入之后就把urlscheme清空要不然下一次oushow时还会执行
			// 	// 处理args参数，如直达到某新页面等
			// 	//通过code请求获取user_id
			// 	var authCode = args.split("=")[1];
			// 	console.log("---authCode---", authCode);
			// 	if (authCode != undefined && authCode != "" && authCode != null) {
			// 		this.$http.get('/api/user/bind-alipay', {
			// 			auth_code: authCode
			// 		}).then(res => {
			// 			//统一各平台的客户端支付API
			// 			uni.navigateBack()
			// 		})

			// 		// this.$busapi.restAuth.queryAlipayInfo("authCode=" + authCode).then(res => {
			// 		// 		if (res.code == '0000') {
			// 		// 			if (res.data.userId) {
			// 		// 				this.whetherBinding(res.data.userId, "alipay");
			// 		// 			}
			// 		// 		} else {
			// 		// 			uni.$message({
			// 		// 				message: res.msg,
			// 		// 				type: 'error'
			// 		// 			});
			// 		// 		}
			// 		// 	})
			// 		// 	.catch(res => {
			// 		// 		this.$message({
			// 		// 			message: res.errmsg,
			// 		// 			type: 'error'
			// 		// 		});
			// 		// 	});
			// 	}
			// }
		},
		onHide() {
			console.log("====onHide=====");
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goAplipay() {
				const AlipayAuth = uni.requireNativePlugin("DHQ-AlipayAuth")

				//支付宝官方SDK参考链接：https://opendocs.alipay.com/open/218/wy75xo?pathHash=03eeb9c7
				//传入参数后拼接的而成的链接参考：https://authweb.alipay.com/auth?auth_type=PURE_OAUTH_SDK&app_id=2016051801417322&scope=auth_user&state=init

				AlipayAuth.login({
						appId: '2021004131698244', //你在支付宝平台申请的App ID
						scheme: 'lightingball', // 需要传到支付宝SDK的scheme,注意需要在manifest.json-->App其他常用配置-->UrlSchemes中配置Android和iOS的
						scope: 'auth_user', //默认为auth_user
						init: 'init' //默认传入init
					},
					(res) => {
						console.log('原生授权返回res', res)
						//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
						let auth_code = res.auth_code
						this.$http.get('/api/user/bind-alipay', {
							auth_code
						}).then(res => {
							//统一各平台的客户端支付API
							uni.navigateBack()
						})
					})
				//相关示例代码：（该代码会打开支付宝授权，授权之后会在支付宝中打开你所设置的【回调地址】网页）

				//***********************
				//***url授权地址由后端拼接也可以前端写死***
				//***以下是一个拼接示例，仅需修改app_id的值和redirect_uri的值***
				//***app_id是商户的APPID，redirect_uri是页面跳回地址（授权成功之后会在支付宝中打开这个地址）***
				//***********************
				// let urls =
				// 	'https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=2021004131698244&scope=auth_userinfo&redirect_uri=http://www.dev.lluuxiu.com/ali-h5/auth';
				// urls = encodeURIComponent(urls); //将地址编码成浏览器访问的格式
				// // 判断平台
				// if (plus.os.name == 'Android') {
				// 	plus.runtime.openURL(
				// 		'alipays://platformapi/startapp?appId=20000067&url=' + urls,
				// 		res => {
				// 			//这里写打开URL地址失败后的处理
				// 			uni.showModal({
				// 				content: '本机未检测到对应客户端，是否打开浏览器访问页面？',
				// 				success: function(res) {
				// 					if (res.confirm) {
				// 						//plus.runtime.openURL();
				// 					}
				// 				}
				// 			});
				// 		},
				// 		'com.eg.android.AlipayGphone'
				// 	);
				// } else if (plus.os.name == 'iOS') {


				// 	//支付宝官方SDK参考链接：https://opendocs.alipay.com/open/218/wy75xo?pathHash=03eeb9c7
				// 	//传入参数后拼接的而成的链接参考：https://authweb.alipay.com/auth?auth_type=PURE_OAUTH_SDK&app_id=2016051801417322&scope=auth_user&state=init
				// 	plus.runtime.openURL(
				// 		'alipay://platformapi/startapp?appId=20000067&url=' + urls,
				// 		res => {
				// 			console.log(res);
				// 			uni.showModal({
				// 				content: '本机未检测到对应客户端，是否打开浏览器访问页面？',
				// 				success: function(res) {
				// 					if (res.confirm) {
				// 						//plus.runtime.openURL(url);
				// 					}
				// 				}
				// 			});
				// 		},
				// 		'com.eg.android.AlipayGphone'
				// 	);
				// }

			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.bg {
		width: 686rpx;
		height: 637rpx;
		margin-top: 154rpx;
	}

	.btn {
		width: 686rpx;
		height: 94rpx;
		line-height: 94rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 140rpx;
		text-align: center;
		margin-top: 372rpx;
		font-size: 32rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		color: #FFFFFF;
	}
</style>