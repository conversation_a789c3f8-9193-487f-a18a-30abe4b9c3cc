<template>
	<view class="">
		<u-toast ref='notify' />
		<view class="swiper">
			<Map ref="mapRef" :BusinessId="business_id" :BusinessLat="business_lat" :BusinessLng="business_lng" />
		</view>
	</view>
</template>
<script>
	import Map from "@/pages/merchantStore/map.vue"
	export default {
		components: {
			Map
		},
		data() {
			return {
				business_id:"",
				business_lat:"",
				business_lng:"",
			}
		},
		onLoad(options) {
			console.log("商家id")
			console.log(options)
			this.business_id = options.business_id
			this.business_lat = options.business_lat
			this.business_lng = options.business_lng
		},
		onPullDownRefresh() {
			// if ([0].includes(this.current)) {
			// 	uni.stopPullDownRefresh();
			// }
		},
		onReachBottom() {

		},
		onShow() {

			// setTimeout(() => {
			// 	this.$refs.mapRef.handelCenter()
			// 	this.$refs.mapRef.openTimeInterval('go')
			// }, 300)

		},
		onHide() {
			// this.$refs.mapRef.cleatTimeout()
		},

		watch: {
			current: {
				handler() {
					// this.$refs.myRef.getUpdate()
				}
			}
		},
		//		uniapp子组件不支持应用生命周期， 所以只能用vue生命周期
		created() {

		},
		methods: {

			// mapUpdate(data) {
			// 	this.$refs.mapRef.poiMapAddMarker({
			// 		...data
			// 	})
			// },
			// poiMapAddMarker(item) {
			// 	this.$refs.mapRef.poiMapAddMarker({
			// 		...item
			// 	})
			// },
		},
	}
</script>
<style lang="scss" scoped>
	.swiper {
		height: 100vh;
	}

	.swiper-item {
		display: block;
		height: 100vh;
		text-align: center;
	}
</style>