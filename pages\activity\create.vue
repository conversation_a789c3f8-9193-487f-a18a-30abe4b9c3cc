<template>
	<view class="pages">
		<view class="view" v-if="!main_uuid">
			<view class="" style="display: flex; align-items: center; font-size: 32rpx; justify-content: flex-start;">
				系列&nbsp;&nbsp;&nbsp; <textarea :value="formData.series_title" maxlength="20" placeholder="请输入系列名称"
					:show-confirm-bar="true" confirm-type="done" auto-height @input="series"
					style="width: 75%; margin-left: 80rpx;" />
			</view>

			<view style="width: 100%; text-align: right; color:rgba(196, 196, 196, .5); ">{{ seriesTitleLength }}/20
			</view>
		</view>
		<view class="view title">
			<textarea v-model="formData.title" maxlength="20" placeholder="请输入标题" @input="activityName"
				:show-confirm-bar="true" confirm-type="done" auto-height />
			<view style="width: 100%; text-align: right; color:rgba(196, 196, 196, .5); ">{{ titleLength }}/20
			</view>
			<view class="t-pic" style="display: flex; justify-content: space-between">
				<view style="width: 48%" v-if="formData.cover">
					<view class="scroll-view-item-img" style="transform: translateX(-20rpx)">
						<image style="width: 300rpx; height: 300rpx; border-radius: 10px" :src="formData.cover"
							mode="aspectFill" @click="previewC(index)">
						</image>
					</view>
				</view>
				<view class="scroll-view" style="width: 48%">
					<view class=""> 注：禁止发布违法内容 </view>
					<image @click="upload" src="../../static/images/addBig.png" class="scroll-view-item"
						style="transform: translateY(50rpx)" mode="aspectFill">
					</image>
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/notTime.png" mode=""></image>
					<view class="textC f_bold"> 无时间限制 </view>
				</view>
				<view class="t_display" style="position: relative; right: -20px">
					<switch style="transform: scale(0.5)" :checked="activeTime" @change="notTime" />
				</view>
			</view>
			<view style="
			  border: 1rpx solid rgba(196, 196, 196, 0.12);
			  margin-top: 22rpx;
			  margin-bottom: 34rpx;
			" v-if="!activeTime">
			</view>
			<view class="item" style="" @click="activiteTime()" v-if="!activeTime">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/images/watch.png" mode=""></image>
					<view class="textC f_bold"> 活动截止时间 </view>
				</view>
				<view class="t_display" style="max-width: 60%; text-align: right;">
					<view class="textC" style="margin-right: 10rpx">
						{{ activieTime }}
					</view>
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
		</view>
		<view class="view">
			<!-- <view class="item" style="" @click="activiteTime()">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/images/watch.png" mode=""></image>
					<view class="textC f_bold"> 活动时间 </view>
				</view>
				<view class="t_display" style="max-width: 60%; text-align: right;">
					<view class="textC" style="margin-right: 10rpx">
						{{ activieTime }}
					</view>
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
			<view style="
          border: 1rpx solid rgba(196, 196, 196, 0.12);
          margin-top: 22rpx;
          margin-bottom: 34rpx;
        ">
			</view> -->
			<view class="item" style="" @click="goNav('/pages/index/hbPosition')">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/images/localtions.png" mode=""></image>
					<view class="textC f_bold"> 活动地点 </view>
				</view>
				<view class="t_display" style="max-width: 60%; text-align: right;">
					<view class="textC" style="margin-right: 10rpx">
						{{ formData.address }}
					</view>
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode=""></image>
					<view class="textC f_bold"> 活动内容 </view>
				</view>
			</view>
			<view style="
          border: 1rpx solid rgba(196, 196, 196, 0.12);
          margin-top: 22rpx;
          margin-bottom: 34rpx;
        ">
			</view>
			<view class="">
				<textarea class="uni-input" auto-height type="text" v-model="formData.content" @input="content"
					placeholder="请输入" :maxlength="maxLength" />
				<view style="width: 100%; text-align: right; color:rgba(196, 196, 196, .5); ">{{ currentLength }} /
					{{ maxLength }}
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/pic.png" mode=""></image>
					<view class="textC f_bold"> 地点图片 </view>
				</view>
				<view class="t_display">
					<view :class="locationhide ? 'block' : 'hide'" @click="locationHide">
					</view>
				</view>
			</view>
			<view style="
          border: 1rpx solid rgba(196, 196, 196, 0.12);
          margin-top: 22rpx;
          margin-bottom: 34rpx;
        ">
			</view>
			<view class="t-pic" style="display: flex; flex-wrap: wrap; margin-bottom: 10px">
				<view class="scroll-view-item-img" v-for="(src, index) in addressArr" :key="index">
					<image class="img" :src="src" mode="aspectFill" @click="preview(index)"
						:style="(index+1)%3===0 ? 'margin-right: 0rpx;width: 170rpx; height: 170rpx; margin-bottom: 40rpx;' : 'margin-right: 40rpx; width: 170rpx; height: 170rpx; margin-bottom: 34rpx;' ">
					</image>
					<image class="close" @click="delImgAddress(index)" src="../../static/images/close.png"
						:style="(index+1)%3===0 ? 'right: -16rpx;' : '' " mode="aspectFill">
					</image>
				</view>
				<image v-if="addressArr.length < 27 &&  locationhide" @click="uploadAddress"
					src="../../static/images/addBig.png" class="scroll-view-item" mode="aspectFill"
					style="width: 170rpx; height: 170rpx;">
				</image>
			</view>
			注：成员到达地点后才可查看该图片
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/pic.png" mode=""></image>
					<view class="textC f_bold"> 活动展示图片 </view>
				</view>
			</view>
			<view style="
          border: 1rpx solid rgba(196, 196, 196, 0.12);
          margin-top: 22rpx;
          margin-bottom: 34rpx;
        ">
			</view>
			<view class="t-pic" style="display: flex; flex-wrap: wrap">
				<view class="scroll-view-item-img" v-for="(src, index) in imgArr" :key="index">
					<image class="img" :src="src" mode="aspectFill" @click="previewH(index)"
						:style="(index+1)%3===0 ? 'margin-right: 0rpx;width: 170rpx; height: 170rpx; margin-bottom: 40rpx;' : 'margin-right: 40rpx; width: 170rpx; height: 170rpx; margin-bottom: 34rpx;' ">
					</image>
					<image class="close" @click="delImg(src.id)" src="../../static/images/close.png"
						:style="(index+1)%3===0 ? 'right: -16rpx;' : '' " mode="aspectFill">
					</image>
				</view>
				<image v-if="imgArr.length < 27" @click="uploadHd" src="../../static/images/addBig.png"
					class="scroll-view-item" mode="aspectFill" style="width: 170rpx; height: 170rpx;">
				</image>
			</view>
		</view>
		<!-- <view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode=""></image>
					<view class="textC"> 费用设置 </view>
				</view>
				<view class="t_display">
					<switch style="transform: scale(0.5)" :checked="true" @change="" :disabled="true" />
				</view>
			</view>
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode=""></image>
					<view class="textC">
						<span>活动费用</span>
					</view>
				</view>
				<view class="t_display" style="color: rgb(79, 197, 238)">
					<input v-model="formData.price" type="text" placeholder="请输入标题" @input="price"
						style="text-align: right" :disabled="true" />
					元
				</view>
			</view>
		</view> -->
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/ryxz.png" mode=""></image>
					<view class="textC f_bold"> 活动人数限制 </view>
				</view>
				<view class="t_display" style="position: relative; right: -20px">
					<switch style="transform: scale(0.5)" :checked="peopleCount" @change="peopleLimit" />
				</view>
			</view>
			<view class="item" v-if="peopleCount">
				<view class="t_display" style="width: 100%">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/people.png" mode=""></image>
					<view class="textC f_bold">
						<view>活动人数</view>
					</view>
				</view>
				<view class="t_display" style="color: rgb(79, 197, 238)">
					<input v-model="formData.people_limit" type="number" maxlength="3" placeholder="请输入数量"
						@input="peopleLimitInput" style="text-align: right" />
					人
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/need_price.png" mode=""></image>
					<view class="textC f_bold"> 需要门票 </view>
				</view>
				<view class="t_display" style="position: relative; right: -20px">
					<switch style="transform: scale(0.5)" :checked="needPrice" @change="needPriceChange" />
				</view>
			</view>
			<view class="item" v-if="needPrice">
				<view class="t_display" style="width: 100%">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/price.png" mode=""></image>
					<view class="textC f_bold">
						<view>门票定价</view>
					</view>
				</view>
				<view class="t_display" style="color: rgb(79, 197, 238)">
					<input v-model="formData.price" type="digit" placeholder="请输入金额" style="text-align: right" @blur="formatPrice" />
					元
				</view>
			</view>
		</view>
		<view style="padding-left: 40rpx; padding-right: 40rpx">
			<button style="
          background-image: url(../../static/img/fbbutton.png);
          background-size: 100%;
          border-radius: 20rpx;
          color: white;
        " @click="publish">
				发布
			</button>
		</view>
		<view class="clear"> </view>
		<view class="time">
			<uni-popup ref="popup" type="bottom" background-color="rgb(41, 44, 51);">
				<view class="" style="width: 50%">
					<u-tabs :list="list" :is-scroll="false" :current="current" @change="change"
						bg-color="rgb(41, 44, 51)" active-color="#fff" inactive-color="rgba(255,255,255,0.72)"
						:show-bar="true">
					</u-tabs>
				</view>
				<swiper class="swiperC" :current="current" @change="setCurrent" disable-touch style="height: 40vh">
					<swiper-item>
						<view class="time_range">
							<!-- <view class="timebox" @click="()=>{currt = 0, startshow=true}"
								:class="{ borders: currt == 0 }">
								{{ begin }}
							</view>
							<span style="line-height: 40px">-</span> -->
							<view class="timebox" @click="()=>{currt = 0, endshow=true}"
								:class="{ borders: currt == 0 }">
								{{ end }}
							</view>
						</view>
						<picker-view :indicator-style="indicatorStyle" :value="value" @change="bindChange"
							class="picker-view">
							<picker-view-column>
								<view class="item" v-for="(item, index) in years" :key="index"
									style="margin-left: 80rpx">{{ item }}年</view>
							</picker-view-column>
							<picker-view-column>
								<view class="item" v-for="(item, index) in months" :key="index"
									style="margin-left: 100rpx">{{ item }}月</view>
							</picker-view-column>
							<picker-view-column>
								<view class="item" v-for="(item, index) in days" :key="index"
									style="margin-left: 40rpx">
									{{ item }}日
								</view>
							</picker-view-column>
							<!-- <picker-view-column>
								<view class="item" style="justify-content: center; margin-left: 40rpx;"
									v-for="(item, index) in timeArr" :key="index">{{ item }}时
								</view>
							</picker-view-column> -->
						</picker-view>
						<!-- <u-picker :params="paramsDate" mode="time" v-model="startshow" safe-area-inset-bottom
							@confirm="bindStartChange" class="custom-picker" cancelColor="#aaa" confirmColor="#fff">
						</u-picker>
						<u-picker :params="paramsDate" mode="time" v-model="endshow" safe-area-inset-bottom
							@confirm="bindEndChange" class="custom-picker" cancelColor="#aaa" confirmColor="#fff">
						</u-picker> -->
					</swiper-item>
					<!-- <swiper-item>
						<view class="time_range" style="width: 100%">
							<view class="timebox" @click="tiemShow = true" style="margin-left: 73px; width: 200px">
								{{ times }}
							</view>
						</view>
						<picker-view :indicator-style="indicatorStyle" @change="settime" :value="times"
							class="picker-view">
							<picker-view-column>
								<view class="item" style="justify-content: center" v-for="(item, index) in timeArr"
									:key="index">{{ item }}</view>
							</picker-view-column>
						</picker-view>
					</swiper-item> -->
					<!-- <u-picker :params="paramsTime" mode="time" v-model="tiemShow" safe-area-inset-bottom
						@confirm="bindTimeChange" class="custom-picker" cancelColor="#aaa" confirmColor="#fff">
					</u-picker> -->
				</swiper>
				<!-- <view class="time_range">
					<view class="timebox" @click="startshow = true">
						{{begin}}
					</view>
					<span style="line-height: 40px;">-</span>
					<view class="timebox" @click="endshow = true">
						{{end}}
					</view>
				</view>
				<view class="">
					<u-picker :params="paramsDate" mode="time" v-model="startshow" safe-area-inset-bottom
						@confirm="bindStartChange">
					</u-picker>
					<u-picker :params="paramsDate" mode="time" v-model="endshow" safe-area-inset-bottom
						@confirm="bindEndChange">
					</u-picker>
				</view> -->
				<view class="button">
					<view class="btnbox" @click="onCancel" style="border: 1px solid rgb(56, 58, 67)">
						取消
					</view>
					<view class="btnbox" @click="onOk"
						style="background: linear-gradient( 90deg, rgb(79, 197, 238), rgb(191, 147, 250) 100%);">
						确认
					</view>
				</view>
			</uni-popup>
		</view>
		<Postion ref="position" />
	</view>
</template>

<script>
	import Postion from './hbPosition.vue'
	import moment from 'moment'
	const date = new Date()
	const years = []
	const year = date.getFullYear()
	const months = []
	const month = date.getMonth() + 1
	const days = []
	const day = date.getDate()
	export default {
		components: {
			Postion,
		},
		data() {
			return {
				currt: 0,
				title: 'picker-view',
				years,
				year,
				months,
				month,
				days,
				day,
				timeArr: [
					'00',
					'01',
					'02',
					'03',
					'04',
					'05',
					'06',
					'07',
					'08',
					'09',
					'10',
					'11',
					'12',
					'13',
					'14',
					'15',
					'16',
					'17',
					'18',
					'19',
					'20',
					'21',
					'22',
					'23',
				],
				value: [0, month - 1, day - 1, 0],

				visible: true,
				indicatorStyle: `height: 50px;`,
				formData: {
					series_title: '',
					title: '',
					address: '',
					cover: '',
					people_limit: null,
					content: '',
					location_img_hide: 0,
					price: 0,
					coordinate: '',
					main_uuid: '',
					begin_unix: ' ',
					end_unix: '',
					location_img: [],
					activity_img: [],
				},
				activieTime: '',
				imgArr: [],
				main_uuid: '',
				addressArr: [],
				locationhide: false,
				peopleCount: false,
				needPrice: false,
				timeHide: false,
				startshow: true,
				endshow: false,
				paramsDate: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false,
					timestamp: true,
				},
				paramsTime: {
					year: false,
					month: false,
					day: false,
					hour: true,
					minute: false,
					second: false,
					timestamp: true,
				},
				tiemShow: true,
				begin: '',
				end: '',
				times: '00',
				startstamp: '',
				endstamp: '',
				list: [{
						name: '活动终止时间',
					},
					// {
					// 	name: '定制时间',
					// },
				],
				current: 0,
				activeTime: true,
				maxLength: 3000,
				currentLength: 0,
				seriesTitleLength: 0,
				titleLength: 0
			}
		},
		watch: {
			'$store.state.hbLocation'(nVal) {
				console.log('-----nVal.name', nVal.name);
				// this.formData.locationName = nVal ? nVal : ''
				this.formData.address = nVal ? nVal.name : ''
				this.formData.coordinate = nVal ? nVal.location : ''
			},
		},
		onLoad(opthion) {
			if (opthion.id) {
				this.main_uuid = opthion.id
			}
			for (let i = date.getFullYear(); i <= date.getFullYear() + 3; i++) {
				years.push(i)
			}

			for (let i = 1; i <= 12; i++) {
				months.push(i)
			}
			for (let i = 1; i <= 31; i++) {
				days.push(i)
			}
		},
		mounted() {
			// for (let i = 0; i < 24; i++) {
			// 	if (i < 10) {
			// 		this.timeArr.push("0" + i + ":00")
			// 	} else {
			// 		this.timeArr.push(i + ':00')
			// 	}
			// }

			console.log('this.timeArr', this.timeArr)
		},
		onShow() {
			// for (let i = date.getFullYear(); i <= date.getFullYear() + 3; i++) {
			// 	years.push(i)
			// }

			// for (let i = 1; i <= 12; i++) {
			// 	months.push(i)
			// }
			// for (let i = 1; i <= 31; i++) {
			// 	days.push(i)
			// }
			let that = this
			uni.$on('address', function(data) {
				console.log(data)
				that.formData.address = data.name
				that.formData.coordinate = data.location
			})
		},
		methods: {

			settime(e) {
				console.log(e)
				const val = e.detail.value
				console.log(val)
				if (val[0] < 10) {
					this.times = '0' + val[0] + ':00'
				} else {
					this.times = val[0] + ':00'
				}
			},
			bindChange: function(e) {
				const val = e.detail.value
				console.log(val)
				this.year = this.years[val[0]]
				if (this.months[val[1]] < 10) {
					this.month = '0' + this.months[val[1]]
				} else {
					this.month = this.months[val[1]]
				}
				if (this.days[val[2]] < 10) {
					this.day = '0' + this.days[val[2]]
				} else {
					this.day = this.days[val[2]]
				}
				// this.times = val[3] < 10 ? "0" + val[3] + ":00" : val[3] + ":00"
				// if (this.currt == 0) {
				// 	this.begin = this.year + '-' + this.month + '-' + this.day + " " + this.times
				// } else {
				this.end = this.year + '-' + this.month + '-' + this.day //+ " " + this.times
				// }
			},
			activiteTime() {
				this.currt = 0
				this.startshow = true
				this.$refs.popup.open()
			},
			goNav(url) {
				uni.navigateTo({
					url,
				})
			},
			series(e) {
				this.seriesTitleLength = e.detail.value.length;
				if (this.seriesTitleLength > 20) {
					this.formData.content = e.detail.value.slice(0, 20);
					this.seriesTitleLength = 20;
				}
				this.formData.series_title = e.detail.value
			},
			activityName(e) {
				this.titleLength = e.detail.value.length;
				if (this.titleLength > 20) {
					if (e.detail.value.indexOf('\n') != -1) { //敲了回车键了
						e.preventDefault() //阻止默认事件
						let value = e.detail.value.replace(/\n+/, '');
						this.formData.title = value.slice(0, 20);
					}
					this.titleLength = 20;
				}

			},
			content(e) {
				this.currentLength = e.detail.value.length;
				this.formData.content = e.detail.value
				if (this.currentLength > this.maxLength) {
					this.formData.content = e.detail.value.slice(0, this.maxLength);
					this.currentLength = this.maxLength;
				}

			},
			delImg(id) {
				this.$http
					.post('/api/user/del-photo', {
						id,
					})
					.then((res) => {
						this.getPhoto()
					})
			},
			previewC(current) {
				uni.previewImage({
					current,
					urls: [this.formData.cover],
				})
			},
			preview(current) {
				uni.previewImage({
					current,
					urls: this.addressArr,
				})
			},
			previewH(current) {
				uni.previewImage({
					current,
					urls: this.imgArr,
				})
			},
			upload() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								this.formData.cover = img
							})
						)
					},
				})
			},
			uploadAddress() {
				let count = 27 - this.addressArr.length
				uni.chooseImage({
					count,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								arr.push(img)
							})
						)

						this.addressArr.push(...arr)
					},
				})
			},
			delImgAddress(index) {
				this.addressArr.splice(index, 1)
			},
			uploadHd() {
				let count = 27 - this.imgArr.length
				uni.chooseImage({
					count,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								arr.push(img)
							})
						)
						this.imgArr.push(...arr)
					},
				})
			},
			delImg(index) {
				this.imgArr.splice(index, 1)
			},
			locationHide() {
				if (this.formData.location_img_hide === 0) {
					this.formData.location_img_hide = 1
					this.locationhide = true
				} else {
					this.formData.location_img_hide = 0
					this.addressArr = []
					this.locationhide = false
				}
			},
			peopleLimit(e) {
				console.log(e)
				this.peopleCount = e.target.value
			},
			needPriceChange(e) {
				this.needPrice = e.target.value
			},
			notTime(e) {
				this.activeTime = e.target.value
			},
			peopleLimitInput(e) {
				console.log(e.detail.value);
				let that = this
				if (e.detail.value > 200) {
					uni.showToast({
						title: `活动最大人数200`,
						icon: 'none',
					})
					that.formData.people_limit = 200
				} else {
					that.formData.people_limit = e.detail.value
				}
			},
			bindStartChange(e) {
				console.log(e.timestamp)
				this.begin = moment(e.timestamp * 1000).format('yyyy-MM-DD HH:mm')
				// this.date = e.mp.detail.value
			},
			bindEndChange(e) {
				console.log(e.timestamp)
				this.end = moment(e.timestamp * 1000).format('yyyy-MM-DD HH:mm')
				// this.date = e.mp.detail.value
			},
			bindTimeChange(e) {
				console.log(e)
				this.times = e.hour + ': 00'
				// this.endstamp = e.timestamp
				// this.date = e.mp.detail.value
			},
			onOk() {
				// const timeArr = this.times.split(':')
				// this.times = timeArr[0].length == 1 ? '0' + timeArr[0] + ':' + timeArr[1] : this.times
				this.formData.begin_unix = this.begin
				this.formData.end_unix = this.end
				this.activieTime = this.end
				// this.activieTime = this.begin + '~' + this.end
				this.$refs.popup.close()
			},
			onCancel() {
				this.begin = ''
				this.startstamp = ''
				this.end = ''
				this.endstamp = ''
				this.activieTime = ''
				this.$refs.popup.close()
			},
			change(index) {
				this.current = index
			},
			setCurrent(ids) {
				this.current = ids.detail.current
			},
			formatPrice() {
				let { price } = this.formData
				if (price) {
					 price = parseFloat(Number(price).toFixed(2));
					 this.formData.price = price
				}
			},
			publish() {
				this.formData.main_uuid = this.main_uuid
				this.formData.location_img = this.addressArr
				this.formData.activity_img = this.imgArr
				if (!this.formData.end_unix) {
					delete(this.formData.end_unix)
				}
				if (!this.needPrice) {
					this.formData.price = 0
				}
				console.log('...this.formData,', this.formData)
				this.$http
					.post('/activity/post', {
						...this.formData,
					})
					.then((res) => {
						uni.showToast({
							title: `发布成功`,
							icon: 'none',
						})
						uni.redirectTo({
							url: "/pages/index/index?current=3"
						})
					})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.picker-view {
		width: 750rpx;
		height: 500rpx;
		background-color: transparent;
	}

	.f_bold {
		font-weight: bold;
		font-size: 30rpx !important;
	}

	.item {
		text-align: center;
		display: flex;
		align-items: center;
	}

	.view {
		border-radius: 20rpx;
		background: rgb(56, 58, 67);
		width: 670rpx;
		margin: auto 40rpx 40rpx;
		padding: 40rpx;
		color: white;

		.scroll-view-item {
			width: 150rpx;
			height: 150rpx;
		}
	}

	.clear {
		width: 100%;
		height: 100px;
	}

	.title {
		// height: 482rpx;

		.t-pic {
			border-top: 2rpx solid rgba(196, 196, 196, 0.12);
			margin-top: 34rpx;
			padding-top: 34rpx;
			padding-bottom: 34rpx;
		}
	}

	.scroll-view-item-img {
		// overflow: hidden;
		max-width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		position: relative;

		.img {
			width: 150rpx;
			height: 150rpx;
			border-radius: 12rpx;
		}
	}

	.scroll-view-item {
		width: 170rpx;
		height: 170rpx;
	}

	.item {
		height: 68rpx;
		font-size: 32rpx;
		line-height: 1.2em;
		display: flex;
		justify-content: space-between;

		.textC {
			font-size: 24rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			margin-left: 14rpx;
			white-space: normal;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}

	.close {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		right: 25rpx;
		top: -16rpx;
		// transform: translateY(-15rpx);
	}

	.block {
		width: 18px;
		height: 18px;
		border: 1px solid rgb(255, 255, 255);
		border-radius: 5px;
		background-image: url('../../static/active/true.png');
		background-size: 100%;
	}

	.hide {
		width: 18px;
		height: 18px;
		border: 1px solid rgb(255, 255, 255);
		border-radius: 5px;
		background: white;
	}

	.borders {
		border: 1px solid #4fc5ee;
	}

	.time {
		width: 100%;

		.time_range {
			display: flex;
			justify-content: center;
			margin-bottom: 20rpx;
			padding: 60rpx;
			width: 100%;

			.timebox {
				width: 157px;
				height: 44px;
				box-sizing: border-box;

				border-radius: 10px;
				background: rgb(44, 55, 64);
				text-align: center;
				line-height: 44px;
			}
		}

		.button {
			width: 100%;
			display: flex;
			justify-content: space-between;
			padding: 40rpx;
			margin-top: 60rpx;

			.btnbox {
				width: 162px;
				height: 44px;
				border-radius: 10px;
				text-align: center;
				line-height: 44px;
			}
		}
	}

	.timehide {
		display: none;
	}

	::v-deep .u-picker-body {
		background-color: rgba(41, 44, 51, 0.9);
		border: none;
	}

	::v-deep .u-picker-header {
		background: rgba(41, 44, 51, 0.9);
	}

	::v-deep .u-picker-header::after {
		border: none;
	}

	::v-deep .u-column-item {
		color: #fff;
	}

	::v-deep .u-picker-view {
		background: linear-gradient(180deg,
				rgb(41, 44, 51) 24.427%,
				rgba(41, 44, 51, 0) 100%);
	}

	::v-deep .uni-scroll-view {
		background: rgba(41, 44, 51, 0.9);
	}

	::v-deep .u-datetime-picker {
		margin-bottom: 0;
	}

	::v-deep .u-picker__mask {
		background-color: rgba(0, 0, 0, 0);
		/* 设置遮罩层背景为完全透明 */
	}

	::v-deep .uni-picker-view-mask {
		height: 100%;
		background: linear-gradient(180deg,
				hsla(238, 0%, 18%, 0.95),
				hsla(238, 0%, 18%, 0.6)),
			linear-gradient(0deg, hsla(238, 0%, 18%, 0.6), hsla(238, 0%, 18%, 0.95));
		// background: linear-gradient(180deg,
		// 		rgb(41, 44, 51) 50%,
		// 		rgba(41, 44, 51, 0) 100%),
		// 	linear-gradient(0deg, rgba(41, 44, 51, 0) 100%, rgb(41, 44, 51) 50%);
		background-repeat: no-repeat;
		background-position: top, bottom;
		background-position-x: center, center;
		background-position-y: 0%, 100%;
		background-size: 100% 102px;

		.uni-picker-view-indicator {
			width: none;
		}
	}
</style>