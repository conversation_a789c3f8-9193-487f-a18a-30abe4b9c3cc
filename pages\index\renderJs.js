import { config } from "@/config.js";
import ren_juhe from "../../static/map/ren_juhe.png";
import * as turf from "@turf/turf";

// 在模块加载时立即设置WebGL强制启用 - uniapp环境
try {
  // 设置全局变量
  if (typeof globalThis !== "undefined") {
    globalThis.forceWebGL = true;
  }

  // 为AMap预设配置
  if (typeof window !== "undefined") {
    window.forceWebGL = true;
  }

  console.log("模块级WebGL强制启用设置完成");
} catch (e) {
  console.log("模块级WebGL设置失败:", e);
}
const dl25 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/cfbdf532-7e6b-4e0b-b83b-17848cef6c8c.png";
const dl50 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0d6bb079-bb1c-4689-ab81-655c772895f3.png";
const dl75 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/3d82a020-3a80-48ba-b5d1-a6539d9884f8.png";
const dl100 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240122/0dcdc02a-dac1-471e-8bf8-676b75f389e2.png";
const yellow_packet =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240202/2b05f6ef-3f28-4c5f-90b9-45943a3b17e8.png";
const red_packet2 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/appImages/hb1.png";
const red_packet =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/appImages/hb2.png";
const msg_icon2 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/appImages/xx1.png";
const msg_icon =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/appImages/xx2.png";
const house_icon =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/housePng.png";
const house_icon2 =
  "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/housePng2.png";
const { avatarW, avatarH, avatarHoverW, avatarHoverH, packetW, packetH } =
  config;
let myMap;
let layer;
let map_person;
let map_merchantstore;
let avatar = null; // 添加全局变量avatar
import {
  newMarker,
  newIcon,
  newText,
  newPerSon,
  getGeocoder,
  newLabelMarkers,
} from "@/utils/MapTools.js";
import {
  apiLocationReport,
  apiLocationVisibleGet,
  apiLocationMe,
  apiAddressSet,
  apiAddressGet,
  apiLocationSearchPeople,
  apiGetNight,
  apiToken,
} from "@/api/common.js";

export default {
  data() {
    return {
      timer: null,
      map: null,
      amap: null,
      autoComplete: null,
      citySearch: null,
      center: null,
      userView: {},
      location: {},
      showTime: false,
      userCenterFlag: 1,
      people: [],
      house: [],
      moon: [],
      msgArr: [],
      person: [],
      event: null,
      me: null,
      maxDist: 500,
      marker_me: null,
      marker_house: null,
      marker_person: null,
      marker_moon: null,
      marker_msg: null,
      marker_redPacket: null,
      marker_merchantStore: null,
      overlay_person: null,
      overlay_house: null,
      overlay_moon: null,
      overlay_msg: null,
      overlay_redPacket: null,
      overlay_merchant: null,
      pulseMarkers: {},
      contentHTML: {},
      personHash: {},
      personFollowHash: {},
      personGhostHash: {},
      houseHash: {},
      userInfo: {},
      followSuccess: false,
      followName: "",
      lastTime: new Date().getTime(),
      district: "",
      temperature: "",
      weatherType: "",
      weaImage: "",
      weatherVisible: false,
      houseMarkers: [],
      personMarkers: [],
      mMsgMarkers: [],
      redPacketMarkers: [],
      storeMarkers: [],
      isDevMode: false,
      lastClusterZoom: null,
      showDetailUid: null,
      // 2.0版本新增：存储聚合数据
      clusterPersonData: [], // 存储人员聚合数据
      personMarkersMap: new Map(), // 存储人员标记映射
      _3dgltfMeshes: [],
      rippleIntervals: [],
      rippleCircles: [],
      zoomLevel: 16,
      avatarPulseAnimation: null, // 头像脉动动画定时器ID
      avatarFixInterval: null, // 头像旋转修正定时器ID
      personRippleInterval: null, // 其他人头像水波纹动画定时器ID
      personAvatarPulseAnimation: null, // 其他人头像脉动动画定时器ID
      // 水波纹效果的默认参数
      rippleEffectParams: {
        maxRadius: 60, // 最大半径
        initialRadius: 20, // 初始半径
        initialOpacity: 0.9, // 初始不透明度
        animationDuration: 1000, // 动画持续时间(毫秒)
        rippleCount: 2, // 水波纹层数
        strokeWeight: 2, // 描边宽度（从0修改为2，添加边框）
        fillOpacity: 0.8, // 填充不透明度
        yOffset: -7, // 水波纹垂直方向的偏移量（负值表示向上）
        xOffset: 2, // 水波纹水平方向的偏移量（正值表示向右）
        gradientColors: ["#4BC6ED", "#BC93F2"], // 渐变色
        rotationAngle: 0, // 水波纹旋转角度(度数)
        shape: "square", // 水波纹形状：'square'或'circle'
        animationType: "fade", // 动画类型：'fade'或'pulse'
        zIndex: 50, // 水波纹的层级
        strokeColor: "rgba(255, 255, 255, 0.8)", // 添加白色描边
        boxShadow: "0 0 8px rgba(255, 255, 255, 0.6)", // 添加白色阴影
      },

      // 灵活点标记缩放级别配置
      elasticMarkerConfig: {
        // 缩放级别分界点
        zoomThreshold: 17,
        // 缩放级别映射配置
        zoomStyleMapping: {
          3: 0,
          4: 0,
          5: 0,
          6: 0,
          7: 0,
          8: 0,
          9: 0,
          10: 0,
          11: 0,
          12: 0,
          13: 0,
          14: 0,
          15: 0,
          16: 0,
          17: 1,
          18: 1,
          19: 1,
          20: 1,
        },
        // 样式配置
        styleConfig: {
          fitZoom: 14,
          scaleFactor: 1,
          maxScale: 1,
          minScale: 1,
        },
      },
      // 不同场景的水波纹颜色
      rippleColors: {
        self: "rgba(51, 153, 255, 0.8)", // 自己的位置
        person: "rgba(255, 107, 107, 0.9)", // 点击其他用户
        reset: "#1989fa", // 默认颜色
      },
    };
  },
  mounted() {
    /**
     * 	初始化高德Api
     */
    if (typeof window.AMap === "function") {
      this.initMap();
    } else {
      window._AMapSecurityConfig = {
        securityJsCode: "********************************",
      };
      /**
       * 	打开房子弹窗
       */
      window.openSetting = (e, a, b) => {
        const arr = e.split(",");
        this.callMethod("open_housePopup", {
          id: arr[0],
          location: arr[1],
          remark: arr[2],
        });
      };

      /**
       * 	打开月亮弹窗
       */
      window.openSettingMoon = (e, a, b) => {
        const arr = e.split(",");
        this.callMethod("open_moonPopup", {
          id: arr[0],
          location: arr[1],
          remark: arr[2],
        });
      };
      // 动态引入较大类库避免影响页面展示 - 优化加载性能
      const script = document.createElement("script");
      script.src =
        "https://webapi.amap.com/maps?v=2.0&key=26ab172d25bd6002eb28192f569071a3";
      script.onload = this.initMap.bind(this);
      script.onerror = () => {
        console.error("高德地图加载失败，请检查网络连接");
      };
      document.head.appendChild(script);
    }
  },
  onUnload() {
    // 清除脉动动画定时器
    if (this.pulseAnimationInterval) {
      clearInterval(this.pulseAnimationInterval);
      this.pulseAnimationInterval = null;
    }

    // 清除头像脉动动画
    if (this.avatarPulseAnimation) {
      cancelAnimationFrame(this.avatarPulseAnimation);
      this.avatarPulseAnimation = null;
    }

    // 清除其他人头像动画
    this.clearPersonRippleAnimation();

    // 清除新添加的定时器
    if (this.avatarPulseTimer) {
      clearTimeout(this.avatarPulseTimer);
      this.avatarPulseTimer = null;
    }

    // 清除水波纹动画
    if (this.rippleAnimationInterval) {
      clearInterval(this.rippleAnimationInterval);
      this.rippleAnimationInterval = null;
    }

    // 清除头像旋转修正定时器
    if (this.avatarFixInterval) {
      clearInterval(this.avatarFixInterval);
      this.avatarFixInterval = null;
    }

    // 移除水波纹圆圈
    this.rippleCircles.forEach((circle) => {
      if (circle) {
        myMap && myMap.remove(circle);
      }
    });
    this.rippleCircles = [];
  },
  methods: {
    /**
     * 开始头像脉动动画
     * @param {Object} marker - 包含头像的标记
     */
    startAvatarPulseAnimation(marker) {
      // 如果已经有动画在运行，先清除它
      if (this.avatarPulseTimer) {
        clearTimeout(this.avatarPulseTimer);
        this.avatarPulseTimer = null;
      }

      if (this.avatarPulseAnimation) {
        cancelAnimationFrame(this.avatarPulseAnimation);
        this.avatarPulseAnimation = null;
      }

      // 安全检查：确保marker是有效的
      if (!marker) {
        console.log("无效的marker对象");
        return;
      }

      try {
        // 直接获取DOM元素，不使用getContentDom方法
        let avatarImg = null;
        let contentDom = null;

        // 尝试获取内容DOM
        try {
          if (
            marker.getContentDom &&
            typeof marker.getContentDom === "function"
          ) {
            contentDom = marker.getContentDom();
          }
        } catch (e) {
          console.log("获取contentDom失败:", e);
        }

        // 如果获取到了contentDom，尝试从中获取img元素
        if (contentDom) {
          try {
            avatarImg = contentDom.querySelector("img");
          } catch (e) {
            console.log("从contentDom获取img失败:", e);
          }
        }

        // 如果上面的方法失败，尝试从marker的content属性获取
        if (!avatarImg) {
          try {
            if (marker.getContent && typeof marker.getContent === "function") {
              const content = marker.getContent();
              if (typeof content === "string" && content.includes("<img")) {
                const tempDiv = document.createElement("div");
                tempDiv.innerHTML = content;
                avatarImg = tempDiv.querySelector("img");
              } else if (content instanceof HTMLElement) {
                avatarImg = content.querySelector("img");
              }
            }
          } catch (e) {
            console.log("从content获取img失败:", e);
          }
        }

        // 如果还是找不到头像元素，尝试从DOM中查找
        if (!avatarImg) {
          try {
            // 尝试通过标记的ID或其他属性查找
            const extData = marker.getExtData ? marker.getExtData() : null;
            if (extData && extData.id === "avatar") {
              // 尝试在整个文档中查找头像
              const avatarWrappers =
                document.querySelectorAll(".avatar-wrapper");
              if (avatarWrappers.length > 0) {
                avatarImg = avatarWrappers[0].querySelector("img");
              }
            }
          } catch (e) {
            console.log("从DOM查找img失败:", e);
          }
        }

        // 如果还是找不到，放弃动画
        if (!avatarImg) {
          console.log("无法找到头像图片元素");
          return;
        }

        // 设置初始样式
        avatarImg.style.transformOrigin = "center center";

        // 使用贝塞尔曲线实现平滑动画
        let startTime = null;
        const duration = 1000; // 动画总时长从3000ms减为2000ms，加快动画速度
        let animationActive = true; // 标记动画是否应该继续

        // 贝塞尔曲线函数，用于计算动画中间值
        const cubicBezier = (t, p0, p1, p2, p3) => {
          const u = 1 - t;
          const tt = t * t;
          const uu = u * u;
          const uuu = uu * u;
          const ttt = tt * t;

          return uuu * p0 + 3 * uu * t * p1 + 3 * u * tt * p2 + ttt * p3;
        };

        // 定义贝塞尔曲线控制点 - 这些值可以调整来改变动画曲线
        const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放，减小拉伸幅度
        const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放，减小拉伸幅度

        // 动画函数
        const animate = (timestamp) => {
          try {
            // 如果动画已停止，不继续执行
            if (!animationActive) return;

            if (!startTime) startTime = timestamp;
            const elapsed = timestamp - startTime;

            // 计算动画进度 (0-1)
            let progress = elapsed / duration;

            // 如果动画完成一个周期，重置开始时间
            if (progress >= 1) {
              startTime = timestamp;
              progress = 0;
            }

            // 使用贝塞尔曲线计算当前X和Y的缩放值
            const scaleX = cubicBezier(
              progress,
              bezierX[0],
              bezierX[1],
              bezierX[2],
              bezierX[3]
            );

            const scaleY = cubicBezier(
              progress,
              bezierY[0],
              bezierY[1],
              bezierY[2],
              bezierY[3]
            );

            // 应用变换 - 使用scale确保不会有旋转角度
            try {
              avatarImg.style.transform = `scale(${scaleX}, ${scaleY})`;
            } catch (e) {
              console.log("应用变换失败，可能元素已不存在:", e);
              animationActive = false;
              return;
            }

            // 继续下一帧动画
            this.avatarPulseAnimation = requestAnimationFrame(animate);
          } catch (e) {
            console.log("动画帧处理出错:", e);
            animationActive = false;
          }
        };

        // 开始动画
        this.avatarPulseAnimation = requestAnimationFrame(animate);

        // 设置清理函数
        this.cleanupAvatarAnimation = () => {
          animationActive = false;
          if (this.avatarPulseAnimation) {
            cancelAnimationFrame(this.avatarPulseAnimation);
            this.avatarPulseAnimation = null;
          }
        };
      } catch (e) {
        console.log("启动动画时出错:", e);
        if (this.avatarPulseAnimation) {
          cancelAnimationFrame(this.avatarPulseAnimation);
          this.avatarPulseAnimation = null;
        }
      }
    },

    /**
     * 修正头像旋转角度，确保头像始终保持正立
     */
    fixAvatarRotation() {
      try {
        // 获取地图当前旋转角度
        const mapRotation =
          myMap && myMap.getRotation ? myMap.getRotation() : 0;

        // 查找所有头像元素并修正旋转
        const avatarWrappers = document.querySelectorAll(".avatar-wrapper");
        const avatarImages = document.querySelectorAll(
          '#AmapRender img[src*="avatar"], #AmapRender img[src*="http"]'
        );

        // 计算反向旋转角度
        const counterRotation = -mapRotation;

        // 修正头像容器
        avatarWrappers.forEach((wrapper) => {
          wrapper.style.transform = `rotate(${counterRotation}deg) !important`;
        });

        // 修正头像图片
        avatarImages.forEach((img) => {
          // 只修正头像图片，不修正其他图片
          if (
            img.src.includes("avatar") ||
            img.parentElement?.classList.contains("avatar-wrapper")
          ) {
            img.style.transform = `rotate(${counterRotation}deg) !important`;
          }
        });

        console.log(
          `头像旋转修正: 地图旋转${mapRotation}度, 头像反向旋转${counterRotation}度`
        );
      } catch (e) {
        console.log("头像旋转修正失败:", e);
      }
    },

    /**
     * 强制设置头像为正立状态（0度旋转）
     */
    forceAvatarUpright() {
      try {
        // 查找所有头像元素
        const avatarWrappers = document.querySelectorAll(".avatar-wrapper");
        const avatarImages = document.querySelectorAll(".avatar-wrapper img");

        // 强制设置头像容器为0度旋转
        avatarWrappers.forEach((wrapper) => {
          wrapper.style.setProperty("transform", "rotate(0deg)", "important");
          wrapper.style.setProperty(
            "transform-origin",
            "center center",
            "important"
          );
        });

        // 强制设置头像图片为0度旋转
        avatarImages.forEach((img) => {
          img.style.setProperty("transform", "rotate(0deg)", "important");
          img.style.setProperty(
            "transform-origin",
            "center center",
            "important"
          );
        });

        // 也处理地图容器内的所有头像相关图片
        const mapContainer = document.querySelector("#AmapRender");
        if (mapContainer) {
          const allImagesInMap = mapContainer.querySelectorAll("img");
          allImagesInMap.forEach((img) => {
            if (
              img.src.includes("avatar") ||
              img.src.includes("http") ||
              img.parentElement?.classList.contains("avatar-wrapper")
            ) {
              img.style.setProperty("transform", "rotate(0deg)", "important");
              img.style.setProperty(
                "transform-origin",
                "center center",
                "important"
              );
            }
          });
        }
      } catch (e) {
        console.log("强制设置头像正立失败:", e);
      }
    },

    /**
     * 开始持续强制头像正立
     */
    startContinuousAvatarFix() {
      if (this.avatarFixInterval) {
        clearInterval(this.avatarFixInterval);
      }
      // 每50毫秒强制设置一次头像为正立状态
      this.avatarFixInterval = setInterval(() => {
        this.forceAvatarUpright();
      }, 50);
    },

    /**
     * 停止持续强制头像正立
     */
    stopContinuousAvatarFix() {
      if (this.avatarFixInterval) {
        clearInterval(this.avatarFixInterval);
        this.avatarFixInterval = null;
      }
    },

    /**
     * 	初始化地图  AmapRender
     */
    initMap() {
      console.log("======this.center========", this.center);
      console.log("开始初始化地图...");

      // uniapp环境下强制启用WebGL矢量渲染
      try {
        // 在renderjs环境中设置全局变量
        if (typeof globalThis !== "undefined") {
          globalThis.forceWebGL = true;
        }

        // 在AMap加载前设置全局配置
        if (typeof AMap !== "undefined") {
          // 确保Config对象存在
          if (!AMap.Config) {
            AMap.Config = {};
          }
          AMap.Config.forceWebGL = true;

          // 尝试设置其他可能的配置路径
          if (AMap.plugin) {
            AMap.plugin.forceWebGL = true;
          }
        }

        console.log("WebGL强制启用设置完成");
      } catch (e) {
        console.log("WebGL设置失败:", e);
      }

      myMap = new AMap.Map("AmapRender", {
        // 基础配置
        center:
          this.center && this.center[0] !== "undefined"
            ? this.center
            : [116.333926, 39.997245],
        zoom: 15,
        zooms: [4, 20],

        // 3D和交互配置
        viewMode: "3D",
        pitch: 50,
        rotateEnable: true,
        pitchEnable: true,
        draggable: true,

        // 性能优化配置 - 解决网格问题
        resizeEnable: true,
        showIndoorMap: false,

        // 矢量地图配置 - uniapp环境
        vectorMap: true, // 启用矢量地图
        features: ["bg", "road", "building", "point"], // 包含矢量要素

        // 强制启用WebGL渲染 - uniapp环境下的配置
        forceWebGL: true, // 强制使用WebGL渲染

        // 矢量地图渲染优化
        WebGLParams: {
          preserveDrawingBuffer: true, // 保持绘图缓冲区
          antialias: true, // 启用抗锯齿
          alpha: true, // 启用透明度
        },

        // 缩放动画优化 - 减少网格闪烁
        animateEnable: true,
        jogEnable: false, // 关闭惯性拖拽
        scrollWheel: true,
        doubleClickZoom: true,
        keyboardEnable: false, // 关闭键盘控制

        // 渲染优化 - 关键配置解决网格问题
        expandZoomRange: true, // 扩展缩放范围
        isHotspot: false, // 关闭热点
        preloadLevel: 2, // 预加载级别，减少瓦片加载延迟
        cacheSize: 256, // 增加缓存大小，减少重复加载

        // 样式配置 - 直接使用自定义样式
        mapStyle: "amap://styles/957040a4d7e11f8e53f93977962d6779",
      });

      console.log("地图实例创建完成");
      // console.log("审图号：", myMap.getMapApprovalNumber());
      layer = new AMap.LabelsLayer({
        zooms: [3, 20],
        zIndex: 1000,
        // 开启标注避让，默认为开启，v1.4.15 新增属性
        collision: true,
        // 开启标注淡入动画，默认为开启，v1.4.15 新增属性
        animation: true,
      });
      myMap.add(layer);

      // 地图加载完成！
      myMap.on("complete", async () => {
        const center = myMap.getCenter();
        console.log("地图加载完成，当前样式:", myMap.getMapStyle());

        // 验证矢量地图是否启用
        const mapOptions = myMap.getOptions ? myMap.getOptions() : null;
        const mapConfig = myMap.getConfig ? myMap.getConfig() : null;

        console.log("矢量地图状态:", {
          // 地图配置检查
          vectorMap:
            mapOptions?.vectorMap || mapConfig?.vectorMap || "配置中已设置",
          features: mapOptions?.features ||
            mapConfig?.features || ["bg", "road", "building", "point"],
          forceWebGL:
            mapOptions?.forceWebGL || mapConfig?.forceWebGL || "配置中已设置",

          // WebGL支持检查
          WebGL支持: typeof WebGLRenderingContext !== "undefined",

          // 全局设置检查
          globalThis_forceWebGL:
            typeof globalThis !== "undefined"
              ? globalThis.forceWebGL
              : "未设置",
          AMap_Config_forceWebGL:
            typeof AMap !== "undefined" && AMap.Config
              ? AMap.Config.forceWebGL
              : "未设置",

          // 地图实例检查
          地图类型: myMap.getMapType ? myMap.getMapType() : "未知",
          渲染模式: myMap.getRenderMode ? myMap.getRenderMode() : "未知",
        });

        // 尝试检查是否真正启用了矢量渲染
        try {
          const canvas = document.querySelector("#AmapRender canvas");
          if (canvas) {
            const context =
              canvas.getContext("webgl") ||
              canvas.getContext("experimental-webgl");
            console.log("WebGL上下文检查:", {
              WebGL上下文存在: !!context,
              渲染器: context ? context.getParameter(context.RENDERER) : "无",
              供应商: context ? context.getParameter(context.VENDOR) : "无",
            });
          }
        } catch (e) {
          console.log("WebGL上下文检查失败:", e);
        }

        // 注释掉初始化头像旋转修正，让高德地图自动处理
        // setTimeout(() => {
        //   this.fixAvatarRotation();
        // }, 1000);

        // 解决网格问题 - 优化地图渲染
        setTimeout(() => {
          // 强制重新渲染，确保瓦片完全加载
          myMap.getView().setZoom(myMap.getZoom());

          // 设置地图渲染优化参数
          if (myMap.getView && myMap.getView()) {
            const view = myMap.getView();
            // 启用瓦片缓存优化
            if (view.setPreloadLevel) {
              view.setPreloadLevel(2); // 预加载2个级别的瓦片
            }
          }

          console.log("地图渲染优化完成");
        }, 100);

        this.callMethod("map:complete", {
          maxDist: this.getMaxDist(),
          longitude: center.lng,
          latitude: center.lat,
        });
        // 获取地图当前可视区域
        const bounds = myMap.getBounds();
        // 赋值当前可是区域
        this.current_bounds_path = bounds.path;
        // 创建地图中用户图层
        this.overlay_person = new AMap.OverlayGroup(); //初始化附近人群组
        this.overlay_person_jh = new AMap.OverlayGroup(); //初始化附近人群组
        this.overlay_me = new AMap.OverlayGroup();
        myMap.add(this.overlay_me);
        this.overlay_moon = new AMap.OverlayGroup();
        this.overlay_address = new AMap.OverlayGroup();
        this.overlay_redPacket = new AMap.OverlayGroup();
        this.overlay_msg = new AMap.OverlayGroup();
        this.overlay_merchant = new AMap.OverlayGroup();
        myMap.add(this.overlay_moon);
        myMap.add(this.overlay_person);
        myMap.add(this.overlay_person_jh);
        myMap.add(this.overlay_address);
        myMap.add(this.overlay_redPacket);
        myMap.add(this.overlay_msg);
        myMap.add(this.overlay_merchant);
        this.overlay_person_jh.hide();
        // 异步加载路径规划插件
        AMap.plugin("AMap.Driving", () => {
          this.mapDriving = new AMap.Driving({
            policy: AMap.DrivingPolicy.LEAST_TIME,
          });
        });
        // 创建3D模型 - 2.0版本中Object3D暂不支持，使用替代方案
        if (AMap.Object3D) {
          AMap.plugin("AMap.Object3D", () => {
            this.object3Dlayer = new AMap.Object3DLayer();
            this.object3DlayerStore = new AMap.Object3DLayer();
            myMap.add(this.object3DlayerStore);
            myMap.add(this.object3Dlayer);
            myMap.plugin(["AMap.GltfLoader"], () => {
              this.gltfObj = new AMap.GltfLoader();
              this.gltfLoader = new AMap.GltfLoader();
            });
          });
        } else {
          console.warn("Object3D 在当前版本中不支持，使用替代方案");
          // 使用普通标记作为替代
          this.object3Dlayer = {
            clear: () => console.log("3D图层清理"),
            add: () => console.log("3D图层添加"),
          };
          this.object3DlayerStore = {
            clear: () => console.log("3D存储图层清理"),
            add: () => console.log("3D存储图层添加"),
          };
        }
        // 天气插件
        AMap.plugin("AMap.Weather", () => {
          this.weather = new AMap.Weather();
          //查询实时天气信息, 查询的城市到行政级别的城市，如朝阳区、杭州市
        });
        // 加载移动动画插件
        AMap.plugin("AMap.MoveAnimation", () => {
          console.log("MoveAnimation plugin loaded");
        });
        // 初始化聚合器变量，稍后在数据到达时创建
        this.cluster_person = null;
      });
      // 地图点击事件！
      myMap.on(
        "touchstart",
        () => {
          this.startClickTime = new Date().getTime();
        },
        this
      );
      // 触发鼠标在地图上单击抬起时的事件
      myMap.on(
        "touchend",
        (ev) => {
          const now = new Date().getTime();
          // 监听地图长按事件
          if (this.startClickTime && now - this.startClickTime > 300) {
            this.mapLongTap(ev);
          }
        },
        this
      );
      // 注释掉强制头像正立的代码，让高德地图自动处理3D旋转
      // myMap.on(
      //   "rotatestart",
      //   () => {
      //     this.forceAvatarUpright();
      //     // 开始持续强制头像正立
      //     this.startContinuousAvatarFix();
      //   },
      //   this
      // );

      // myMap.on(
      //   "rotating",
      //   () => {
      //     this.forceAvatarUpright();
      //   },
      //   this
      // );

      // myMap.on(
      //   "rotateend",
      //   () => {
      //     // 地图旋转结束后，强制设置所有头像为正立状态
      //     this.forceAvatarUpright();
      //     // 停止持续强制头像正立
      //     this.stopContinuousAvatarFix();
      //   },
      //   this
      // );
      myMap.on(
        "click",
        (ev) => {
          // 删除弹层
          this.moon_modal && this.moon_modal.hide();
          this.house_modal && this.house_modal.hide();
          this.poiMarket && this.poiMarket.hide();
          this.callMethod("close_house_popup");
          this.callMethod("close_dian_popup");
          // this.callMethod('to_merchant_store',ev)
          this.click3dgltf(ev);
          this.onMeshesClick(ev);
        },
        this
      );
      myMap.on(
        "movestart",
        () => {
          // 重置地图点击
          this.startClickTime = null;
        },
        this
      );
      myMap.on(
        "dragging",
        () => {
          // 重置地图点击
          this.startClickTime = null;
        },
        this
      );
      myMap.on(
        "moveend",
        async (ev) => {
          // 获取地图中心点坐标
          const center = myMap.getCenter();
          // 动态设置中心点区域名称
          getGeocoder(
            AMap,
            {
              lnglat: [center.lng, center.lat],
            },
            true
          ).then((geoRes) => {
            if (geoRes.info == "OK") {
              const strr =
                geoRes.regeocode.addressComponent.city ||
                geoRes.regeocode.addressComponent.province;

              // console.log('11geoRes', geoRes, '');
              const district =
                geoRes.regeocode.addressComponent.district ||
                geoRes.regeocode.addressComponent.country;
              this.callMethod("changeCountry", district);
              this.weather.getLive(strr, (err, data) => {
                if (!err) {
                  this.temperature = data.temperature + "℃";
                  this.callMethod("temperature:change", {
                    temperature: this.temperature,
                    weather:
                      data.weather.indexOf("雨") > -1
                        ? "yu"
                        : data.weather.indexOf("雪") > -1
                        ? "xue"
                        : "qing",
                  });
                } else {
                  this.callMethod("temperature:change", "");
                }
              });
            } else {
              this.callMethod("changeCountry", "");
            }
          });
          // 获取当前可是区域的最大半径，加载半径内用户
          this.getPeopleInMaxDist();
        },
        this
      );

      // 缩放开始事件 - 预防网格问题
      myMap.on("zoomstart", () => {
        // 缩放开始时的处理，可以在这里添加loading状态
        console.log("缩放开始");
      });

      myMap.on("zoomend", (ev) => {
        // 缩放结束后的处理 - 解决网格问题
        setTimeout(() => {
          // 强制刷新地图渲染，避免网格残留
          myMap.getView().setCenter(myMap.getCenter());
        }, 50);

        // 获取当前可是区域的最大半径，加载半径内用户
        this.getPeopleInMaxDist();
        const zoom = myMap.getZoom();
        // 修改商家模型
        this.storeMarkers.forEach((marker) => {
          const markerData = marker.getExtData();
          // 检查标点是否在当前地图视图的范围内
          // console.log("地图标点id", markerData.id, zoom);
          if (zoom >= markerData.id) {
            // console.log("显示");
            // 如果在范围内，则显示标点
            marker.show();
          } else {
            // console.log("隐藏");
            // 如果不在范围内，则隐藏标点
            marker.hide();
          }
        });

        if (zoom < 13) {
          this.zoom13 = 1;
          this.overlay_person.hide();
          this.overlay_person_jh.show();
        } else {
          this.zoom13 = 2;
          this.overlay_person.show();
          this.overlay_person_jh.hide();
        }
        if (zoom >= 30) {
          if (this.zoom17 == 2) return false;
          this.zoom17 = 2;
          this.cluster_address.clearMarkers();
          this.houseMessage.forEach((item) => {
            if (item.coordinate.indexOf("undefined") > -1) return false;
            const position = item.coordinate.split(",");
            const paramHouse = {
              position: position, // 必须
              scale: 100, // 非必须，默认1
              height: 100, // 非必须，默认0
              scene: 0, // 非必须，默认0
              zIndex: 18,
              extData: item,
            };
            this.gltfObj.load(
              config.ossBaseUrl + "config/map/1.12.gltf",
              (gltfHouse) => {
                this.houseMeshes.push(gltfHouse);
                gltfHouse.setOption(paramHouse);
                gltfHouse.rotateX(0);
                gltfHouse.rotateZ(0);
                gltfHouse.rotateY(0);
                this.object3Dlayer.add(gltfHouse);
              }
            );
          });
        } else {
          if (this.zoom17 == 1) return false;
          this.zoom17 = 1;
          const markersCount =
            this.cluster_address && this.cluster_address.getClustersCount();
          if (this.cluster_address && markersCount <= 0) {
            this.object3Dlayer.clear();
            this.cluster_address.setMarkers(this.houseMarkets);
          }
        }
      });
    },
    /**
     * 	长按事件-扎点
     */
    async mapLongTap(ev) {
      const lnglat = ev.lnglat;

      const res = await getGeocoder(AMap, {
        lnglat: [lnglat.lng, lnglat.lat],
      });
      this.callMethod("mapLongClick", {
        lnglat: lnglat,
        location: res.regeocode.formattedAddress,
        addressComponent: res.regeocode.addressComponent,
      });
    },
    /**
     * 	调用 view 层的方法
     */
    callMethod(act = "", params) {
      this.$ownerInstance.callMethod("callApp", {
        act,
        option: params,
      });
    },
    /**
     * 	获取当前可是区域的最大半径，加载半径内用户
     */
    getPeopleInMaxDist() {
      const r = this.getMaxDist();
      const center = myMap.getCenter();
      if (!this.downTime) {
        this.downTime = setTimeout(() => {
          clearTimeout(this.downTime);
          this.downTime = null;
          this.callMethod("change:maxDist", {
            maxDist: r,
            longitude: center.lng,
            latitude: center.lat,
          });
        }, 2000);
      }
    },
    /**
     * 房子  模型点击
     */
    onMeshesClick(ev) {
      const pixel = ev.pixel;
      const px = new AMap.Pixel(pixel.x, pixel.y);
      const obj =
        myMap.getObject3DByContainerPos(px, [this.object3Dlayer], false) || {};
      if (obj && obj.object) {
        const meshId = obj.object.id;

        this.houseMeshes.forEach(async (item) => {
          if (item && item.layerMesh) {
            for (let i = 0; i < item.layerMesh.length; i++) {
              if (meshId === item.layerMesh[i].id) {
                const extData = item.gltf.option.extData;

                const lng = extData.coordinate.split(",")[0];
                const lat = extData.coordinate.split(",")[1];

                const startLocation = [this.me.longitude, this.me.latitude];
                const endLocation = extData.coordinate.split(",");
                const result = await this.getDriver(startLocation, endLocation);
                if (result.info == "OK") {
                  const distance = result.routes[0].distance;
                  extData.distance =
                    distance && distance > 1000
                      ? (distance / 1000).toFixed(2) + " 千米"
                      : distance + " 米" || 0 + " 米";
                  // this.$refs.storePopup.open(extData)
                  this.callMethod("open_storePopup", {
                    ...extData,
                  });
                }
              }
            }
          }
        });
      }
    },
    /**
     * 	获取视野的最大半径
     */
    getMaxDist() {
      let r = 0;
      const bounds = myMap.getBounds();
      const center = myMap.getCenter();
      try {
        this.current_bounds_path = bounds.path;
        this.current_bounds_path.forEach((item) => {
          const p1 = new AMap.LngLat(item[0], item[1]);
          const p2 = new AMap.LngLat(center.lng, center.lat);
          const dist = p1.distance(p2);
          if (dist > r) r = dist;
        });
        return parseInt(r);
      } catch (e) {
        //TODO handle the exception
        return 200000000;
      }
    },

    /**
     *  重置地图中心点
     * 	center 变动回调
     */
    receive_Center(newValue, oldValue, ownerVm, vm) {
      this.resetPeople();
      try {
        if (newValue === null) return;

        myMap && myMap.setCenter(this.center);
        myMap && myMap.setZoom(15);
      } catch (e) {
        //TODO handle the exception
      }
    },
    resetPeople() {
      // 2.0版本：重置选中状态
      this.person_showDetail_arr = [];
      // 重新创建聚合器以刷新显示
      if (map_person && map_person.length > 0) {
        this.receive_Person(map_person, null, null, null);
      }
    },

    // 红包标注 - 使用灵活点标记根据缩放级别显示不同图标
    receive_redPacket(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.overlay_redPacket && this.overlay_redPacket.clearOverlays();

      // 先加载ElasticMarker插件
      const self = this; // 保存this上下文
      AMap.plugin(["AMap.ElasticMarker"], function () {
        const markers = [];
        const icon_red = newIcon(AMap, {
          image: red_packet,
          size: new AMap.Size(packetW, packetH),
          imageSize: new AMap.Size(packetW, packetH),
        });
        const icon_yellow = newIcon(AMap, {
          image: yellow_packet,
          size: new AMap.Size(packetW, packetH),
          imageSize: new AMap.Size(packetW, packetH),
        });

        newValue.forEach((item) => {
          if (item.is_business) {
            // 商家红包使用普通标记，保持原有逻辑
            const marker_red = new AMap.Marker({
              position: [item.Longitude, item.Latitude],
              icon: icon_yellow,
              offset: new AMap.Pixel(-packetW / 2, -packetH),
              extData: {
                ...item,
              },
              zIndex: 90,
            });
            markers.push(marker_red);
            marker_red.on("touchstart", (ev) => {
              console.log("商家红包标记点击事件触发", ev.target);
              try {
                const extData = ev.target.De
                  ? ev.target.De.extData
                  : ev.target.getExtData();
                console.log("商家红包扩展数据:", extData);
                self.callMethod("view_redPacket_detail", extData);
                myMap.setCenter([item.Longitude, item.Latitude]);
                myMap.setZoom(18);
              } catch (error) {
                console.error("商家红包点击事件错误:", error);
              }
            });
          } else {
            // 普通红包使用灵活点标记，根据缩放级别显示不同图标
            const styles = [
              {
                // 17级以下显示red_packet图标
                icon: {
                  img: red_packet,
                  size: [packetW, packetH],
                  anchor: [packetW / 2, packetH],
                  fitZoom: 14,
                  scaleFactor: 1,
                  maxScale: 1,
                  minScale: 1,
                },
              },
              {
                // 17级以上显示red_packet2图标
                icon: {
                  img: red_packet2,
                  size: [packetW, packetH],
                  anchor: [packetW / 2, packetH],
                  fitZoom: 17,
                  scaleFactor: 1,
                  maxScale: 1,
                  minScale: 1,
                },
              },
            ];

            const zoomStyleMapping = {
              3: 0,
              4: 0,
              5: 0,
              6: 0,
              7: 0,
              8: 0,
              9: 0,
              10: 0,
              11: 0,
              12: 0,
              13: 0,
              14: 0,
              15: 0,
              16: 0,
              17: 1,
              18: 1,
              19: 1,
              20: 1,
            };

            const marker_red = new AMap.ElasticMarker({
              position: [item.Longitude, item.Latitude],
              styles: styles,
              zoomStyleMapping: zoomStyleMapping,
              extData: {
                ...item,
              },
              zIndex: 90,
              map: myMap,
            });

            markers.push(marker_red);

            // 使用click事件而不是touchstart，并且正确获取extData
            marker_red.on("click", (ev) => {
              console.log("普通红包标记点击事件触发", ev.target);
              try {
                const extData = ev.target.getExtData
                  ? ev.target.getExtData()
                  : ev.target.De.extData;
                console.log("普通红包扩展数据:", extData);
                self.callMethod("view_redPacket_detail", extData);
                myMap.setCenter([item.Longitude, item.Latitude]);
                myMap.setZoom(18);
              } catch (error) {
                console.error("普通红包点击事件错误:", error);
              }
            });
          }
        });

        self.overlay_redPacket.addOverlays(markers);
      });
    },
    // 留言标注 - 使用灵活点标记根据缩放级别显示不同图标
    receive_msg(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.overlay_msg && this.overlay_msg.clearOverlays();

      // 先加载ElasticMarker插件
      const self = this; // 保存this上下文
      AMap.plugin(["AMap.ElasticMarker"], function () {
        const markers = [];

        newValue.forEach((item) => {
          // 消息标记使用灵活点标记，根据缩放级别显示不同图标
          const styles = [
            {
              // 低缩放级别显示msg_icon图标
              icon: {
                img: msg_icon,
                size: [packetW, packetH],
                anchor: [packetW / 2, packetH],
                fitZoom: self.elasticMarkerConfig.styleConfig.fitZoom,
                scaleFactor: self.elasticMarkerConfig.styleConfig.scaleFactor,
                maxScale: self.elasticMarkerConfig.styleConfig.maxScale,
                minScale: self.elasticMarkerConfig.styleConfig.minScale,
              },
            },
            {
              // 高缩放级别显示msg_icon2图标
              icon: {
                img: msg_icon2,
                size: [packetW, packetH],
                anchor: [packetW / 2, packetH],
                fitZoom: self.elasticMarkerConfig.zoomThreshold,
                scaleFactor: self.elasticMarkerConfig.styleConfig.scaleFactor,
                maxScale: self.elasticMarkerConfig.styleConfig.maxScale,
                minScale: self.elasticMarkerConfig.styleConfig.minScale,
              },
            },
          ];

          const marker_msg = new AMap.ElasticMarker({
            position: [item.Longitude, item.Latitude],
            styles: styles,
            zoomStyleMapping: self.elasticMarkerConfig.zoomStyleMapping,
            extData: {
              ...item,
            },
            zIndex: 90,
            map: myMap,
          });

          markers.push(marker_msg);

          // 使用click事件，并且正确获取extData
          marker_msg.on("click", (ev) => {
            console.log("留言标记点击事件触发", ev.target);
            try {
              const extData = ev.target.getExtData
                ? ev.target.getExtData()
                : ev.target.De.extData;
              console.log("留言扩展数据:", extData);
              self.callMethod("view_msg_detail", extData);
              myMap.setCenter([item.Longitude, item.Latitude]);
              myMap.setZoom(18);
            } catch (error) {
              console.error("留言点击事件错误:", error);
            }
          });
        });

        self.overlay_msg.addOverlays(markers);
      });
    },
    /**
     *  重置地图用户W
     * 	person 变动回调
     */
    receive_Person(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      map_person = newValue;

      // 2.0版本：准备聚合数据格式
      this.clusterPersonData = [];
      this.personMarkersMap.clear();

      map_person.forEach((item) => {
        if (!item.i_can_see) return false;

        // 转换为聚合数据格式
        const clusterData = {
          lnglat: [item.longitude, item.latitude], // 2.0版本必需的字段
          weight: 1,
          ...item, // 保留原始数据
        };

        this.clusterPersonData.push(clusterData);
        this.personMarkersMap.set(item.uid, clusterData);
      });

      // 调试信息
      console.log("其他人数据总数:", this.clusterPersonData.length);
      console.log("其他人数据:", this.clusterPersonData);

      // 2.0版本：自定义非聚合点样式
      const _renderMarker = (context) => {
        // 尝试多种方式获取数据
        let data = context.data || context.dataItem || context;

        // 如果data是数组，取第一个元素
        if (Array.isArray(data) && data.length > 0) {
          data = data[0];
        }

        // 如果data仍然没有uid，尝试从其他属性获取
        if (!data.uid && context.dataItem) {
          data = context.dataItem;
          if (Array.isArray(data) && data.length > 0) {
            data = data[0];
          }
        }

        // 调试信息
        // console.log("渲染其他人头像 - context:", context);
        // console.log("渲染其他人头像 - data:", data);
        // console.log("渲染其他人头像:", data.uid, data.avatar);

        const hasIndex = this.person_showDetail_arr.indexOf(data.uid);

        // 创建头像HTML内容 - 增加更多容器空间确保完整显示
        const containerW = hasIndex > -1 ? avatarHoverW + 40 : avatarW + 40;
        const containerH = hasIndex > -1 ? avatarHoverH + 40 : avatarH + 40;
        const actualW = hasIndex > -1 ? avatarHoverW : avatarW;
        const actualH = hasIndex > -1 ? avatarHoverH : avatarH;

        const avatarHtml = `
          <div class="avatar-wrapper" style="
            position: relative;
            width: ${containerW}px;
            height: ${containerH}px;
            z-index: 90;
            overflow: visible;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
          ">
            <div class="ripple-container" style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              z-index: 1;
            "></div>
            <img src="${hasIndex > -1 ? data.map_user_mark : data.avatar}"
                 style="
                   width: ${actualW}px;
                   height: ${actualH}px;
                   object-fit: contain;
                   z-index: 2;
                 " />
            ${
              data.bgImage
                ? `
              <img src="${data.bgImage}"
                   style="
                     position: absolute;
                     top: -5px;
                     left: -5px;
                     width: ${containerW}px;
                     height: ${containerH}px;
                     z-index: -1;
                   " />
            `
                : ""
            }
          </div>
        `;

        // 设置标记内容和偏移 - 调整偏移以适应新的容器尺寸
        context.marker.setContent(avatarHtml);
        context.marker.setOffset(
          new AMap.Pixel(
            -containerW / 2, // 容器水平居中
            -containerH / 2 // 容器垂直居中，确保完全可见
          )
        );
        context.marker.setExtData(data);

        // 设置autoRotation为false，防止头像旋转
        if (context.marker.setOptions) {
          context.marker.setOptions({
            autoRotation: false,
          });
        }
        // 添加点击事件
        context.marker.on("click", (ev) => {
          console.log("点击其他人头像 - data uid:", data.uid);
          console.log("点击其他人头像 - data avatar:", data.avatar);
          console.log("--------点击其他人头像 data 详细内容-----------");

          // 安全地打印对象内容，避免循环引用
          try {
            console.log("data JSON:", JSON.stringify(data, null, 2));
          } catch (e) {
            console.log("data 包含循环引用，逐个打印属性:");
            Object.keys(data).forEach((key) => {
              try {
                if (typeof data[key] === "object" && data[key] !== null) {
                  console.log(
                    `data.${key}:`,
                    typeof data[key],
                    data[key].constructor?.name || "Object"
                  );
                } else {
                  console.log(`data.${key}:`, data[key]);
                }
              } catch (err) {
                console.log(`data.${key}: [无法访问]`);
              }
            });
          }

          this.handlePersonMarkerClick(data, context.marker);

          // 创建一个干净的数据对象，避免循环引用，匹配 personDialog.vue 的字段
          const cleanData = {
            uid: data.uid,
            uuid: data.uuid,
            avatar: data.origin_avatar,
            origin_avatar: data.origin_avatar, // personDialog 使用 origin_avatar
            nick_name: data.nickname || data.nick_name, // personDialog 使用 nick_name
            nickname: data.nickname,
            longitude: data.longitude,
            latitude: data.latitude,
            dist_from_me: data.distance || data.dist_from_me, // 距离字段
            relation: data.relation,
            ghost_mode: data.ghost_mode,
            im_id: data.im_id,
            map_user_mark: data.map_user_mark,
            bgImage: data.bgImage,
            i_can_see: data.i_can_see,
            // 位置信息对象
            location_info: {
              district:
                data.district || data.location_info?.district || "未知地区",
              formatted_address:
                data.formatted_address ||
                data.location_info?.formatted_address ||
                "未知地址",
              location: {
                Longitude: data.longitude,
                Latitude: data.latitude,
              },
            },
          };

          console.log("清理后的数据 uid:", cleanData.uid);

          try {
            this.callMethod("open_personPopup", cleanData);
            console.log("callMethod 调用成功");
          } catch (error) {
            console.error("callMethod 调用失败:", error);
          }
        });

        // 如果是选中状态的头像，启动动画
        if (hasIndex > -1) {
          setTimeout(() => {
            this.startPersonRippleAnimationForMarker(context.marker, data.uid);
          }, 100);
        }
      };

      // 自定义聚合点样式
      const _renderClusterMarker = (context) => {
        const count = context.count; // 聚合中点的总数
        // 自定义点标记样式
        const div = document.createElement("div");
        div.innerHTML = count;
        div.className = "amap-cluster";
        div.style.width = 40 + "px";
        div.style.height = 40 + "px";
        div.style.lineHeight = 40 + "px";
        div.style.backgroundImage = `url(https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20231203/659faea8-89d2-4485-ae82-80f14d7933aa.png)`;
        div.style.backgroundRepeat = "no-repeat";
        div.style.backgroundSize = "100% 100%";
        div.style.color = "#ffffff";
        div.style.textAlign = "center";
        div.style.borderRadius = "50%";
        div.style.display = "flex";
        div.style.alignItems = "center";
        div.style.justifyContent = "center";
        div.style.fontSize = "14px";
        div.style.fontWeight = "bold";
        context.marker.setContent(div);
        context.marker.setOffset(new AMap.Pixel(-20, -20));
        // 2.0版本中聚合标记的zIndex通过CSS设置
        if (context.marker.setZIndex) {
          context.marker.setZIndex(40);
        } else {
          div.style.zIndex = "40";
        }

        // 自定义点击事件
        context.marker.on("click", function (e) {
          const curZoom = myMap.getZoom();
          if (curZoom < 20) {
            myMap.setZoomAndCenter(15, e.lnglat);
          }
        });
      };

      // 重新创建聚合器
      AMap.plugin(["AMap.MarkerCluster"], () => {
        // 如果已存在聚合器，先移除
        if (this.cluster_person) {
          this.cluster_person.setMap(null);
        }

        // 创建新的聚合器
        this.cluster_person = new AMap.MarkerCluster(
          myMap,
          this.clusterPersonData,
          {
            gridSize: 60, // 聚合网格像素大小
            renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
            renderMarker: _renderMarker, // 自定义非聚合点样式
          }
        );
      });
    },

    // 处理人员标记点击事件
    handlePersonMarkerClick(data, marker) {
      let showDetailUid = this.person_showDetail_arr[0] || "";
      const isSameClick = showDetailUid && showDetailUid == data.uid;

      // 先重置所有动画状态，无论是点击新用户还是重复点击
      this.resetAllAnimations();

      // 在点击位置创建水波纹效果
      const clickPosition = marker.getPosition();
      if (clickPosition) {
        this.createRippleEffect(clickPosition, this.rippleColors.person, {
          shape: "circle", // 强制使用圆形
          gradientColors: null, // 禁用渐变色，使用传入的颜色
          maxRadius: 40, // 减小最大半径
          initialRadius: 15, // 减小初始半径
          animationDuration: 800, // 缩短动画时间
          rippleCount: 1, // 减少水波纹层数
          fillOpacity: 0.3, // 降低透明度
        });
      }

      // 镜头移动到其他人位置并设置为15级地图层级
      if (clickPosition) {
        // 直接设置为15级缩放并将其他人置于镜头中间
        myMap.setZoomAndCenter(15, clickPosition, false, 800);
      }

      if (isSameClick) {
        // 如果是再次点击已选中的用户，取消选中状态
        this.person_showDetail_arr = [];
      } else {
        // 设置新选中的用户
        this.person_showDetail_arr = [data.uid];
      }

      // 重新创建聚合器以刷新显示
      this.receive_Person(map_person, null, null, null);

      // 只有在选中新用户时才启动动画
      if (!isSameClick) {
        // 延迟启动水波纹和贝塞尔动画，确保标记已更新
        setTimeout(() => {
          this.startPersonRippleAnimation(data.uid);
        }, 100);
      }
    },

    // 启动其他人头像的持续水波纹和贝塞尔动画
    startPersonRippleAnimation(uid) {
      // 清除之前的动画
      this.clearPersonRippleAnimation();

      // 查找对应的标记
      if (this.cluster_person && this.cluster_person.getMarkers) {
        const markers = this.cluster_person.getMarkers();
        const targetMarker = markers.find((marker) => {
          const extData = marker.getExtData();
          return extData && extData.uid === uid;
        });

        if (targetMarker) {
          this.startPersonRippleAnimationForMarker(targetMarker, uid);
        }
      }
    },

    // 启动其他人头像的持续水波纹和贝塞尔动画（直接使用标记对象）
    startPersonRippleAnimationForMarker(targetMarker, uid) {
      // 验证这个用户确实在选中列表中
      if (!this.person_showDetail_arr.includes(uid)) {
        return;
      }

      // 清除之前的动画
      this.clearPersonRippleAnimation();

      setTimeout(() => {
        const contentDom = targetMarker.getContentDom();

        if (contentDom) {
          const rippleContainer = contentDom.querySelector(".ripple-container");
          const avatarImg = contentDom.querySelector("img");

          // 再次验证用户仍在选中状态
          if (!this.person_showDetail_arr.includes(uid)) {
            return;
          }

          if (rippleContainer) {
            // 创建持续的水波纹动画 - 与自己位置的水波纹相同
            this.personRippleInterval = setInterval(() => {
              const position = targetMarker.getPosition();
              if (
                position &&
                !isNaN(position.getLng()) &&
                !isNaN(position.getLat())
              ) {
                // 使用蓝色水波纹，调整为适合其他人头像的尺寸
                this.createRippleEffect(position, this.rippleColors.self, {
                  maxRadius: 60,
                  initialOpacity: 0.8,
                  initialRadius: 25,
                  animationDuration: 1000,
                  rippleCount: 2,
                  strokeWeight: 0,
                  fillOpacity: 0.5,
                  yOffset: -8,
                  xOffset: 3,
                  containerElement: rippleContainer,
                });
              }
            }, 2000); // 每2秒创建一组新的水波纹

            // 立即创建第一组水波纹
            const position = targetMarker.getPosition();
            if (
              position &&
              !isNaN(position.getLng()) &&
              !isNaN(position.getLat())
            ) {
              this.createRippleEffect(position, this.rippleColors.self, {
                maxRadius: 60,
                initialOpacity: 0.8,
                initialRadius: 25,
                animationDuration: 1000,
                rippleCount: 2,
                strokeWeight: 0,
                fillOpacity: 0.5,
                yOffset: -8,
                xOffset: 3,
                containerElement: rippleContainer,
              });
            }
          }

          // 启动头像脉动动画 - 与自己位置的动画相同
          if (avatarImg) {
            // 简化条件：只要用户在选中列表中就启动动画
            this.startPersonAvatarPulse(avatarImg);
          }
        }
      }, 50);
    },

    // 清除其他人头像的动画
    clearPersonRippleAnimation() {
      if (this.personRippleInterval) {
        clearInterval(this.personRippleInterval);
        this.personRippleInterval = null;
      }
      if (this.personAvatarPulseAnimation) {
        cancelAnimationFrame(this.personAvatarPulseAnimation);
        this.personAvatarPulseAnimation = null;
      }
    },

    // 重置所有动画状态
    resetAllAnimations() {
      // 清除所有水波纹动画
      if (this.rippleIntervals && this.rippleIntervals.length > 0) {
        this.rippleIntervals.forEach((interval) => {
          clearInterval(interval);
        });
        this.rippleIntervals = [];
      }

      // 清除所有水波纹圆圈
      if (this.rippleCircles && this.rippleCircles.length > 0) {
        this.rippleCircles.forEach((circle) => {
          if (circle) {
            myMap && myMap.remove(circle);
          }
        });
        this.rippleCircles = [];
      }

      // 清除自己头像的脉动动画
      if (this.avatarPulseAnimation) {
        cancelAnimationFrame(this.avatarPulseAnimation);
        this.avatarPulseAnimation = null;
      }

      // 清除自己头像的脉动计时器
      if (this.avatarPulseTimer) {
        clearTimeout(this.avatarPulseTimer);
        this.avatarPulseTimer = null;
      }

      // 清除其他人头像的动画
      this.clearPersonRippleAnimation();

      // 清除头像旋转修正定时器
      if (this.avatarFixInterval) {
        clearInterval(this.avatarFixInterval);
        this.avatarFixInterval = null;
      }
    },

    // 启动其他人头像的脉动动画
    startPersonAvatarPulse(avatarImg) {
      // 清除之前的动画
      if (this.personAvatarPulseAnimation) {
        cancelAnimationFrame(this.personAvatarPulseAnimation);
        this.personAvatarPulseAnimation = null;
      }

      // 重置头像的transform，避免之前动画的影响
      avatarImg.style.transform = "scale(1)";

      // 设置初始样式
      avatarImg.style.transformOrigin = "center center";

      // 使用与自己头像相同的贝塞尔曲线动画
      let startTime = null;
      const duration = 1000; // 动画总时长
      let animationActive = true; // 标记动画是否应该继续

      // 贝塞尔曲线函数 - 与自己头像相同的实现
      const cubicBezier = (t, p0, p1, p2, p3) => {
        const u = 1 - t;
        const tt = t * t;
        const uu = u * u;
        const uuu = uu * u;
        const ttt = tt * t;

        return uuu * p0 + 3 * uu * t * p1 + 3 * u * tt * p2 + ttt * p3;
      };

      // 定义贝塞尔曲线控制点 - 与自己位置的动画相同
      const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放
      const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放

      // 动画函数
      const animate = (timestamp) => {
        try {
          // 如果动画已停止，不继续执行
          if (!animationActive) return;

          if (!startTime) startTime = timestamp;
          const elapsed = timestamp - startTime;

          // 计算动画进度 (0-1)
          let progress = elapsed / duration;

          // 如果动画完成一个周期，重置开始时间
          if (progress >= 1) {
            startTime = timestamp;
            progress = 0;
          }

          // 使用贝塞尔曲线计算当前X和Y的缩放值
          const scaleX = cubicBezier(
            progress,
            bezierX[0],
            bezierX[1],
            bezierX[2],
            bezierX[3]
          );

          const scaleY = cubicBezier(
            progress,
            bezierY[0],
            bezierY[1],
            bezierY[2],
            bezierY[3]
          );

          // 应用变换 - 使用scale确保不会有旋转角度
          try {
            avatarImg.style.transform = `scale(${scaleX}, ${scaleY})`;
          } catch (e) {
            console.log("应用变换失败，可能元素已不存在:", e);
            animationActive = false;
            return;
          }

          // 继续下一帧动画
          this.personAvatarPulseAnimation = requestAnimationFrame(animate);
        } catch (e) {
          console.log("动画帧处理出错:", e);
          animationActive = false;
        }
      };

      this.personAvatarPulseAnimation = requestAnimationFrame(animate);
    },

    /**
		 *  重置地图房子

		      // 在点击位置创建水波纹效果
		      const clickPosition = marker_per.getPosition();

		      // 检查位置是否有效
		      if (
		        !clickPosition ||
		        (clickPosition.getLng &&
		          (isNaN(clickPosition.getLng()) || isNaN(clickPosition.getLat())))
		      ) {
		        console.warn("点击时获取到无效的标记位置:", clickPosition);
		      } else {
		        // 创建包含水波纹的HTML元素
		        const rippleDiv = document.createElement("div");
		        rippleDiv.className = "person-click-ripple";
		        rippleDiv.style.position = "absolute";
		        rippleDiv.style.top = "0";
		        rippleDiv.style.left = "0";
		        rippleDiv.style.width = "100%";
		        rippleDiv.style.height = "100%";
		        rippleDiv.style.zIndex = "50";
		        rippleDiv.style.pointerEvents = "none";

		        // 将水波纹元素添加到标记中
		        const contentDom = marker_per.getContentDom();
		        if (contentDom) {
		          contentDom.appendChild(rippleDiv);

		          // 给头像添加水波纹效果
		          this.createRippleEffect(clickPosition, this.rippleColors.reset, {
		            containerElement: rippleDiv,
		          });
		        } else {
		          // 如果没有获取到内容DOM，就直接在地图上创建水波纹
		          this.createRippleEffect(clickPosition, this.rippleColors.person, {
		            shape: "circle",           // 强制使用圆形
		            gradientColors: null,      // 禁用渐变色，使用传入的颜色
		            maxRadius: 40,             // 减小最大半径
		            initialRadius: 15,         // 减小初始半径
		            animationDuration: 800,    // 缩短动画时间
		            rippleCount: 1,            // 减少水波纹层数
		            fillOpacity: 0.3           // 降低透明度
		          });
		        }
		      }

		      if (isSameClick) {
		        // 如果是再次点击已选中的用户，取消选中状态
		        this.person_showDetail_arr = [];
		      } else {
		        // 设置新选中的用户
		        this.person_showDetail_arr = [item.uid];
		      }
		      for (let i = 0; i < markers.length; i++) {
		        const curItem = markers[i].De.extData;
		        const curItemUid = markers[i].De.extData.uid;
		        if (isSameClick) {
		          if (showDetailUid === curItemUid) {
		            // 使用HTML标记代替图标
		            const currentSize = avatarW;

		            // 添加收缩动画效果
		            const animateIconShrink = () => {
		              // 创建大一点的头像HTML
		              const largeAvatarHtml = `
		                <div class="avatar-wrapper" style="position: relative; width: ${
		                  currentSize + 10
		                }px; height: ${
		                currentSize + 10
		              }px; display: flex; justify-content: center; align-items: center;">
		                  <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                  <img src="${
		                    curItem.avatar
		                  }" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
		                </div>
		              `;

		              // 设置大头像
		              markers[i].setContent(largeAvatarHtml);
		              markers[i].setOffset(
		                new AMap.Pixel(-(currentSize + 10) / 2, -(currentSize + 10))
		              );

		              // 稍后恢复正常大小
		              setTimeout(() => {
		                // 创建正常大小的头像HTML
		                const normalAvatarHtml = `
		                  <div class="avatar-wrapper" style="position: relative; width: ${currentSize}px; height: ${currentSize}px; display: flex; justify-content: center; align-items: center; transform: rotate(0deg) !important;">
		                    <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                    <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; transform: rotate(0deg) !important; object-fit: cover; border-radius: 50%;" />
		                  </div>
		                `;

		                // 设置正常大小头像
		                markers[i].setContent(normalAvatarHtml);
		                markers[i].setOffset(
		                  new AMap.Pixel(-currentSize / 2, -currentSize)
		                );
		              }, 200);
		            };

		            animateIconShrink();
		            showDetailUid = "";
		            break;
		          }
		        } else {
		          if (item.uid === curItemUid) {
		            // 使用HTML标记代替图标
		            const avatarHtml = `
		              <div class="avatar-wrapper" style="position: relative; width: ${avatarHoverW}px; height: ${avatarHoverH}px; display: flex; justify-content: center; align-items: center;">
		                <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                <img src="${item.map_user_mark}" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
		              </div>
		            `;

		            markers[i].setContent(avatarHtml);
		            markers[i].setOffset(
		              new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
		            );

		            // 添加长大动画效果
		            const animateIconGrow = () => {
		              // 创建小一点的头像HTML
		              const smallAvatarHtml = `
		                <div class="avatar-wrapper" style="position: relative; width: ${
		                  avatarHoverW - 10
		                }px; height: ${
		                avatarHoverH - 10
		              }px; display: flex; justify-content: center; align-items: center;">
		                  <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                  <img src="${
		                    item.map_user_mark
		                  }" style="width: 100%; height: 100%; position: relative; z-index: 2;" />
		                </div>
		              `;

		              // 设置小头像
		              markers[i].setContent(smallAvatarHtml);
		              markers[i].setOffset(
		                new AMap.Pixel(
		                  -(avatarHoverW - 10) / 2,
		                  -(avatarHoverH - 10)
		                )
		              );

		              // 稍后设置正常大小
		              setTimeout(() => {
		                markers[i].setContent(avatarHtml);
		                markers[i].setOffset(
		                  new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
		                );
		              }, 200);
		            };

		            animateIconGrow();

		            // 添加水波纹效果
		            setTimeout(() => {
		              const contentDom = markers[i].getContentDom();
		              if (contentDom) {
		                const rippleContainer =
		                  contentDom.querySelector(".ripple-container");
		                const avatarImg = contentDom.querySelector("img");

		                if (rippleContainer) {
		                  // 获取位置并确保它是有效的
		                  const position = markers[i].getPosition();
		                  if (
		                    !position ||
		                    (position.getLng &&
		                      (isNaN(position.getLng()) ||
		                        isNaN(position.getLat())))
		                  ) {
		                    console.warn("无效的标记位置:", position);
		                    return;
		                  }

		                  // 创建持续的水波纹动画 - 与自己位置的水波纹相同
		                  const rippleInterval = setInterval(() => {
		                    const pos = position;
		                    // 检查位置是否有效
		                    if (
		                      !pos ||
		                      (pos.getLng &&
		                        (isNaN(pos.getLng()) || isNaN(pos.getLat())))
		                    ) {
		                      console.warn("无效的标记位置，跳过水波纹创建:", pos);
		                      return;
		                    }
		                    // 使用蓝色水波纹，与自己位置一致
		                    this.createRippleEffect(pos, this.rippleColors.self, {
		                      maxRadius: 98,
		                      initialOpacity: 0.9,
		                      initialRadius: 50,

		                      animationDuration: 1000,
		                      rippleCount: 2,
		                      strokeWeight: 0,
		                      fillOpacity: 0.6,
		                      yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
		                      xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
		                      containerElement: rippleContainer,
		                    });
		                  }, 2000); // 每2秒创建一组新的水波纹

		                  // 立即创建第一组水波纹
		                  this.createRippleEffect(
		                    position,
		                    this.rippleColors.self,
		                    {
		                      maxRadius: 98,
		                      initialOpacity: 0.9,
		                      initialRadius: 50,
		                      animationDuration: 1000,
		                      rippleCount: 2,
		                      strokeWeight: 0,
		                      fillOpacity: 0.6,
		                      yOffset: 5, // 水波纹垂直方向的偏移量（负值表示向上）
		                      xOffset: 13, // 水波纹水平方向的偏移量（正值表示向右）
		                      containerElement: rippleContainer,
		                    }
		                  );
		                }

		                // 启动头像脉动动画 - 与自己位置的动画相同
		                if (avatarImg) {
		                  // 设置初始样式
		                  avatarImg.style.transformOrigin = "center center";

		                  // 使用贝塞尔曲线实现平滑动画
		                  let startTime = null;
		                  const duration = 1000; // 动画总时长
		                  let animationActive = true; // 标记动画是否应该继续

		                  // 贝塞尔曲线函数，用于计算动画中间值
		                  const cubicBezier = (t, p0, p1, p2, p3) => {
		                    const u = 1 - t;
		                    const tt = t * t;
		                    const uu = u * u;
		                    const uuu = uu * u;
		                    const ttt = tt * t;

		                    return (
		                      uuu * p0 +
		                      3 * uu * t * p1 +
		                      3 * u * tt * p2 +
		                      ttt * p3
		                    );
		                  };

		                  // 定义贝塞尔曲线控制点 - 与自己位置的动画相同
		                  const bezierX = [1, 1.15, 0.92, 1]; // 控制X轴缩放
		                  const bezierY = [1, 0.92, 1.15, 1]; // 控制Y轴缩放

		                  // 动画函数
		                  const animate = (timestamp) => {
		                    try {
		                      // 如果动画已停止，不继续执行
		                      if (!animationActive) return;

		                      if (!startTime) startTime = timestamp;
		                      const elapsed = timestamp - startTime;

		                      // 计算动画进度 (0-1)
		                      let progress = elapsed / duration;

		                      // 如果动画完成一个周期，重置开始时间
		                      if (progress >= 1) {
		                        startTime = timestamp;
		                        progress = 0;
		                      }

		                      // 使用贝塞尔曲线计算当前X和Y的缩放值
		                      const scaleX = cubicBezier(
		                        progress,
		                        bezierX[0],
		                        bezierX[1],
		                        bezierX[2],
		                        bezierX[3]
		                      );

		                      const scaleY = cubicBezier(
		                        progress,
		                        bezierY[0],
		                        bezierY[1],
		                        bezierY[2],
		                        bezierY[3]
		                      );

		                      // 应用变换 - 使用scale确保不会有旋转角度
		                      try {
		                        avatarImg.style.transform = `scale(${scaleX}, ${scaleY})`;
		                      } catch (e) {
		                        console.log("应用变换失败，可能元素已不存在:", e);
		                        animationActive = false;
		                        return;
		                      }

		                      // 继续下一帧动画
		                      requestAnimationFrame(animate);
		                    } catch (e) {
		                      console.log("动画帧处理出错:", e);
		                      animationActive = false;
		                    }
		                  };

		                  // 开始动画
		                  requestAnimationFrame(animate);
		                }
		              }
		            }, 50);
		          } else if (curItemUid == showDetailUid) {
		            // 使用HTML标记代替图标

		            // 添加收缩动画效果
		            const animateIconShrink = () => {
		              // 创建大一点的头像HTML
		              const largeAvatarHtml = `
		                <div class="avatar-wrapper" style="position: relative; width: ${avatarHoverW}px; height: ${avatarHoverH}px; display: flex; justify-content: center; align-items: center; transform: rotate(0deg) !important;">
		                  <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                  <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; transform: rotate(0deg) !important; object-fit: cover; border-radius: 50%;" />
		                </div>
		              `;

		              // 设置大头像
		              markers[i].setContent(largeAvatarHtml);
		              markers[i].setOffset(
		                new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
		              );

		              // 稍后恢复正常大小
		              setTimeout(() => {
		                // 创建正常大小的头像HTML
		                const normalAvatarHtml = `
		                  <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center; transform: rotate(0deg) !important;">
		                    <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
		                    <img src="${curItem.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; transform: rotate(0deg) !important; object-fit: cover; border-radius: 50%;" />
		                  </div>
		                `;

		                // 设置正常大小头像
		                markers[i].setContent(normalAvatarHtml);
		                markers[i].setOffset(
		                  new AMap.Pixel(-avatarW / 2, -avatarH)
		                );
		              }, 200);
		            };

		            animateIconShrink();
		            showDetailUid = "";
		          }
		        }
		      }
		      // TODO
		      this.labelMarker && this.labelMarker.hide();
		      this.MoonLabelMarker && this.MoonLabelMarker.hide();
		      this.callMethod("closeStoreDialog");
		      myMap.setCenter([item.longitude, item.latitude]);
		      myMap.setZoom(15);

		      this.current_edit_person_marker = newMarker;
		      if (!isSameClick) {
		        this.callMethod("open_personPopup", ev.target.De.extData);
		      }
		    });

		    // 添加触摸事件支持，以便在移动设备上也能触发动画效果
		    marker_per.on("touchstart", (ev) => {
		      // 触发与点击相同的处理逻辑
		      marker_per.emit("click", ev);
		    });

		    markers.push(marker_per);
		  });
		  // 2.0版本：不再使用setMarkers方法
		  // 聚合数据已在receive_Person中处理
		},
		/**
		 *  重置地图房子 - 使用灵活点标记根据缩放级别显示不同图标
		 * 	house 变动回调
		 */
    receive_House(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      this.houseMessage = newValue;
      const address_markers = [];
      this.overlay_address.clearOverlays();
      this.cluster_address && this.cluster_address.clearMarkers();

      // 先加载ElasticMarker插件
      const self = this; // 保存this上下文
      AMap.plugin(["AMap.ElasticMarker"], function () {
        newValue.forEach((item) => {
          try {
            if (item.coordinate.indexOf("undefined") > -1) return false;
            const position = item.coordinate.split(",");

            // 房子标记使用灵活点标记，根据缩放级别显示不同图标
            const styles = [
              {
                // 低缩放级别显示house_icon图标
                icon: {
                  img: house_icon,
                  size: [40, 40],
                  anchor: [20, 20],
                  fitZoom: self.elasticMarkerConfig.styleConfig.fitZoom,
                  scaleFactor: self.elasticMarkerConfig.styleConfig.scaleFactor,
                  maxScale: self.elasticMarkerConfig.styleConfig.maxScale,
                  minScale: self.elasticMarkerConfig.styleConfig.minScale,
                },
              },
              {
                // 高缩放级别显示house_icon2图标
                icon: {
                  img: house_icon2,
                  size: [40, 40],
                  anchor: [20, 20],
                  fitZoom: self.elasticMarkerConfig.zoomThreshold,
                  scaleFactor: self.elasticMarkerConfig.styleConfig.scaleFactor,
                  maxScale: self.elasticMarkerConfig.styleConfig.maxScale,
                  minScale: self.elasticMarkerConfig.styleConfig.minScale,
                },
              },
            ];

            const row = new AMap.ElasticMarker({
              position: position,
              styles: styles,
              zoomStyleMapping: self.elasticMarkerConfig.zoomStyleMapping,
              extData: {
                ...item,
              },
              zIndex: 40,
              map: myMap,
            });

            // 为每个房子标记添加点击事件
            row.on("click", async (ev) => {
              console.log("房子标记点击事件触发", ev.target);
              try {
                const extData = ev.target.getExtData
                  ? ev.target.getExtData()
                  : ev.target.De.extData;
                console.log("房子扩展数据:", extData);

                const position = ev.target.getPosition();
                const lng = position.lng;
                const lat = position.lat;
                myMap.setCenter([lng, lat]);

                if (extData.type == 1) {
                  if (self.house_modal) {
                    myMap.remove(self.house_modal);
                    self.house_modal = null;
                  }
                  self.house_modal = new AMap.Marker({
                    position: new AMap.LngLat(lng, lat),
                    offset: new AMap.Pixel(-94, -120), //设置文本标注偏移量
                    zIndex: 99,
                    content: `<div style='width:172px;height:70px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
                                      <div style="
                                            font-size: 14px;
                                            font-family: Source Han Sans-Bold, Source Han Sans;
                                            font-weight: 700;
                                            color: #FFFFFF;
                                            line-height: 46rpx;">
                                            家（${extData.remark}）
                                      </div>
                                      <div style="position:absolute;
                                            right:10px;
                                            top:10px;
                                      " onclick="openSetting('${extData.id}:${extData.location}:${extData.remark}')">
                                        <img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
                                      </div>
                                      <div style="
                                            font-size: 12px;
                                            font-family: Source Han Sans-Regular, Source Han Sans;
                                            font-weight: 400;
                                            color: rgba(255,255,255,0.64);
                                            line-height: 50rpx;
                                            margin-top:5px;
                                          ">
                                            最近一次(${extData.last_stay})
                                            <div>在这里睡了${extData.stay_num}晚</div>
                                      </div>
                                      <div style="width: 0;
                                            height: 0;
                                            border: 10px solid;
                                            border-color: #000 transparent transparent transparent;
                                            position:absolute;
                                            left:50%;
                                            bottom:-20px;
                                            transform:translateX(-50%);
                                            ">
                                      </div>
                                  </div>`,
                  });
                  setTimeout(() => {
                    myMap.add(self.house_modal);
                  }, 300);
                }

                const startLocation = [self.me.longitude, self.me.latitude];
                const endLocation = extData.coordinate.split(",");
                const result = await self.getDriver(startLocation, endLocation);
                if (result.info == "OK") {
                  const distance = result.routes[0].distance;
                  extData.distance =
                    distance && distance > 1000
                      ? (distance / 1000).toFixed(2) + " 千米"
                      : distance + " 米" || 0 + " 米";
                  self.callMethod("open_storePopup", {
                    ...extData,
                  });
                }
              } catch (error) {
                console.error("房子点击事件错误:", error);
              }
            });

            address_markers.push(row);
          } catch (err) {
            console.error("房子标记创建错误:", err);
          }
        });

        // 将标记添加到overlay中
        self.overlay_address &&
          self.overlay_address.addOverlays(address_markers);

        // 保存房子标记
        self.houseMarkets = address_markers;
      }); // 结束ElasticMarker插件加载

      // 注意：不再需要overlay的点击事件，因为每个ElasticMarker都有自己的点击事件
      AMap.plugin(["AMap.MarkerCluster"], async () => {
        // 自定义聚合点样式
        const _renderClusterMarker = (context) => {
          const clusterData = context.clusterData; // 聚合中包含数据
          const index = context.index; // 聚合的条件
          const count = context.count; // 聚合中点的总数
          const marker = context.marker; // 聚合点标记对象
          const styleObj = {
            bgColor: "rgba(0,0,0,.8)",
            borderColor: "rgba(255,255,255,1)",
            // // text: clusterData[0][index['mainKey']],
            size: Math.round(
              Math.pow(count / (address_markers.length || 1), 1 / 5) * 70
            ),
            color: "#ffffff",
            textAlign: "center",
            boxShadow: "0px 0px 5px rgba(0,0,0,0.8)",
          };
          // 自定义点标记样式
          const div = document.createElement("div");
          div.innerHTML = count;
          div.className = "amap-cluster";
          div.style.width = 40 + "px";
          div.style.height = 40 + "px";
          div.style.lineHeight = styleObj.size + "px";
          if (styleObj.index <= 2) {
            div.style.height = styleObj.size + "px";
            // 自定义点击事件
            context.marker.on("click", function (e) {
              const curZoom = map.getZoom();
              if (curZoom < 20) {
                curZoom += 1;
              }
              map.setZoomAndCenter(curZoom, e.lnglat);
            });
          }
          div.style.backgroundImage = `url( ${config.ossBaseUrl}config/map/housePng.png)`;
          div.style.backgroundRepeat = "no-repeat";
          div.style.backgroundSize = "100% 100%";
          div.style.color = styleObj.color;
          div.style.textAlign = styleObj.textAlign;
          context.marker.setContent(div);
          context.marker.setOffset(new AMap.Pixel(-20, -20)); // 居中锚点
          // 2.0版本中聚合标记的zIndex通过CSS设置
          if (context.marker.setZIndex) {
            context.marker.setZIndex(40);
          } else {
            div.style.zIndex = "40";
          }
        };
        self.cluster_address = new AMap.MarkerCluster(myMap, address_markers, {
          gridSize: 1, // 聚合网格像素大小
          renderClusterMarker: _renderClusterMarker,
        });
      });
    },
    /**
     *  获取路径规划
     */
    getDriver(startLngLat, endLngLat) {
      return new Promise((resolve, reject) => {
        this.mapDriving.search(
          startLngLat,
          endLngLat,
          function (status, result) {
            // 未出错时，result即是对应的路线规划方案
            if (status === "complete") {
              resolve(result);
            } else {
              reject(result);
            }
          }
        );
      });
    },
    /**
     *  重置地图本人位置
     * 	me 变动回调
     */
    receive_Me(newValue, oldValue, ownerVm, vm) {
      if (newValue == null) return;
      // console.log(newValue, 'newValue');

      // 当前用户地图扎点
      if (this.overlay_me.getOverlays().length <= 0) {
        // 创建包含头像和水波纹容器的HTML - 移除强制旋转设置
        const avatarHtml = `
          <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center; background: transparent !important; border: none !important; box-shadow: none !important;">
            <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
            <img src="${newValue.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; object-fit: cover; border-radius: 50%;" />
          </div>
        `;

        // 创建带有HTML内容的标记
        const avatar = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: avatarHtml,
          offset: new AMap.Pixel(-avatarW / 2, -avatarH),
          zIndex: 100,
          autoRotation: false, // 禁用自动旋转
          extData: {
            id: "avatar",
          },
        });

        // 注释掉强制设置头像正立的代码
        // setTimeout(() => {
        //   this.forceAvatarUpright();
        // }, 50);

        const onlineIcon = newIcon(AMap, {
          image:
            "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/9ba8726c-dd1f-4ea0-91fb-465bb74e39b8.png",
          size: new AMap.Size(30, 15),
          imageSize: new AMap.Size(30, 15),
        });

        let dlImg = "";
        if (newValue.electricity > 80) {
          dlImg = dl100;
        } else if (newValue.electricity > 55) {
          dlImg = dl75;
        } else if (newValue.electricity > 30) {
          dlImg = dl50;
        } else {
          dlImg = dl25;
        }

        // 恢复其他标记
        const Dlmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="color:#000;position:relative;top:70px;left:10px">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);line-height:12px;text-align:center">${newValue.electricity}%</div></div>`,
          offset: new AMap.Pixel(-20, -80), // 向上移动30px (60rpx)
          zIndex: 110, // 设置比头像更高的层级
          autoRotation: false, // 禁用自动旋转
          extData: {
            id: "Dlmarker",
          },
        });

        const Fzmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="position:relative;">
						<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/a055781f-bc9b-46e9-b94b-5f7133a43385.png">
						</div>`,
          offset: new AMap.Pixel(25, -33),
          autoRotation: false, // 禁用自动旋转
          extData: {
            id: "Fzmarker",
          },
        });

        const Sdmarker = new AMap.Marker({
          position: [newValue.longitude, newValue.latitude],
          content: `<div style="position:relative;top:20px">
					<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)"
					src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
            <div style="transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(
              0
            ).toFixed(0)}
							<div>km/h</div>
            </div></div>`,
          offset: new AMap.Pixel(-40, -70), // 向上移动30px (60rpx)
          zIndex: 110, // 设置比头像更高的层级
          autoRotation: false, // 禁用自动旋转
          extData: {
            id: "Sdmarker",
          },
        });

        const online = newMarker(AMap, {
          position: [newValue.longitude, newValue.latitude],
          offset: new AMap.Pixel(-10, -95),
          icon: onlineIcon,
          zIndex: 100,
          extData: {
            id: "online",
          },
        });

        // 添加持续的水波纹动画效果
        const startContinuousRipple = (marker) => {
          // 清除之前的水波纹
          if (this.rippleAnimationInterval) {
            clearInterval(this.rippleAnimationInterval);
            this.rippleAnimationInterval = null;
          }

          // 获取水波纹容器
          setTimeout(() => {
            const rippleContainer = marker
              .getContentDom()
              .querySelector(".ripple-container");
            if (rippleContainer) {
              // 创建持续的水波纹动画
              this.rippleAnimationInterval = setInterval(() => {
                const position = marker.getPosition();
                // 检查位置是否有效
                if (
                  !position ||
                  (position.getLng &&
                    (isNaN(position.getLng()) || isNaN(position.getLat())))
                ) {
                  console.warn("无效的标记位置，跳过水波纹创建:", position);
                  return;
                }
                // 使用蓝色水波纹，更适合表示自己的位置
                this.createRippleEffect(position, this.rippleColors.self, {
                  maxRadius: 60,
                  initialOpacity: 0.9,
                  animationDuration: 1000,
                  rippleCount: 2,
                  strokeWeight: 2,
                  fillOpacity: 0.6,
                  containerElement: rippleContainer,
                });
              }, 2000); // 每2秒创建一组新的水波纹

              // 立即创建第一组水波纹
              const firstPosition = marker.getPosition();
              if (
                firstPosition &&
                (!firstPosition.getLng ||
                  (!isNaN(firstPosition.getLng()) &&
                    !isNaN(firstPosition.getLat())))
              ) {
                this.createRippleEffect(firstPosition, this.rippleColors.self, {
                  maxRadius: 60,
                  initialOpacity: 0.9,
                  animationDuration: 1000,
                  rippleCount: 2,
                  strokeWeight: 2,
                  fillOpacity: 0.6,
                  containerElement: rippleContainer,
                });
              }

              // 存储interval ID以便后续清除
              this.pulseAnimationInterval = this.rippleAnimationInterval;
            }
          }, 50);
        };

        // 启动持续水波纹动画
        startContinuousRipple(avatar);

        // 启动头像脉动动画
        this.startAvatarPulseAnimation(avatar);

        // 添加点击事件
        avatar.on("click", (e) => {
          // 阻止事件冒泡
          if (e && e.stopPropagation) {
            e.stopPropagation();
          }

          // 防抖：避免快速连续点击
          if (this.avatarClickTimeout) {
            clearTimeout(this.avatarClickTimeout);
          }

          this.avatarClickTimeout = setTimeout(() => {
            this.showMeDesc = !this.showMeDesc;
            console.log("头像点击，切换显示状态:", this.showMeDesc);

            // 立即执行，不使用延迟
            this.overlay_me.eachOverlay((overlay, index) => {
              const extData = overlay.getExtData();
              const { id } = extData;
              if (this.showMeDesc) {
                if (id !== "avatar") {
                  console.log("显示标记:", id);
                  overlay.show();
                }
              } else {
                if (id !== "avatar") {
                  console.log("隐藏标记:", id);
                  overlay.hide();
                }
              }
            });
          }, 100); // 100ms防抖延迟
        });

        // 添加标记到地图
        this.overlay_me.addOverlay(avatar);
        this.overlay_me.addOverlay(Dlmarker);
        // this.overlay_me.addOverlay(Fzmarker)
        this.overlay_me.addOverlay(Sdmarker);
        // this.overlay_me.addOverlay(online)

        // 初始化显示状态为隐藏（仅在首次创建时）
        if (this.showMeDesc === undefined) {
          this.showMeDesc = false;
          console.log("首次创建，初始化showMeDesc为false");
        } else {
          console.log("标记已存在，保持当前showMeDesc状态:", this.showMeDesc);
        }
        Dlmarker.hide();
        Fzmarker.hide();
        Sdmarker.hide();
        online.hide();
        myMap.setCenter([newValue.longitude, newValue.latitude]);
      } else {
        // console.log(
        //   "位置更新，进入else分支，当前showMeDesc状态:",
        //   this.showMeDesc
        // );

        // 检查位置是否真的有变化，避免无意义的更新
        const lastPosition = this.overlay_me.getOverlays()[0].getPosition();
        const newLng = newValue.longitude;
        const newLat = newValue.latitude;
        const oldLng = lastPosition.lng;
        const oldLat = lastPosition.lat;

        // 如果位置变化很小（小于0.0001度，约10米），跳过更新
        const lngDiff = Math.abs(newLng - oldLng);
        const latDiff = Math.abs(newLat - oldLat);

        if (lngDiff < 0.0001 && latDiff < 0.0001) {
          // console.log("位置变化很小，跳过更新，保持当前状态");
          return;
        }

        console.log("位置有明显变化，执行更新:", {
          oldPos: [oldLng, oldLat],
          newPos: [newLng, newLat],
          diff: [lngDiff, latDiff],
        });

        // 清除之前的脉动动画
        if (this.pulseAnimationInterval) {
          clearInterval(this.pulseAnimationInterval);
          this.pulseAnimationInterval = null;
        }

        if (this.avatarPulseAnimation) {
          cancelAnimationFrame(this.avatarPulseAnimation);
          this.avatarPulseAnimation = null;
        }

        const currentPosition = this.overlay_me.getOverlays()[0].getPosition();
        const endLnglat = new AMap.LngLat(this.me.longitude, this.me.latitude);
        const from = turf.point([currentPosition.lng, currentPosition.lat]);
        const to = turf.point([endLnglat.lng, endLnglat.lat]);
        const options = {
          units: "kilometers",
        };
        const distance = turf.distance(from, to, options);
        const speed = distance * ((3600 * 1000) / 2000);
        // this.meMarket.moveTo(endLnglat, speed)

        // 在位置更新前记录当前显示状态
        // console.log("位置更新前，showMeDesc状态:", this.showMeDesc);
        const markerVisibilityState = {}; // 保存标记的显示状态

        this.overlay_me.eachOverlay((overlay, index) => {
          const extData = overlay.getExtData();
          const { id } = extData;

          // 记录每个标记的当前显示状态
          if (id === "Dlmarker" || id === "Sdmarker") {
            const isVisible = overlay.getVisible();
            markerVisibilityState[id] = isVisible;
            console.log("位置更新前标记状态:", id, "是否可见:", isVisible);
          }
          if (id === "avatar") {
            // 检查头像是否需要更新
            const contentDom = overlay.getContentDom();
            if (contentDom) {
              const avatarImg = contentDom.querySelector("img");
              if (avatarImg && avatarImg.src !== newValue.avatar) {
                // 更新头像图片
                avatarImg.src = newValue.avatar;
              }
            } else {
              // 如果没有内容DOM，可能是旧的标记方式，重新创建HTML内容
              const avatarHtml = `
                <div class="avatar-wrapper" style="position: relative; width: ${avatarW}px; height: ${avatarH}px; display: flex; justify-content: center; align-items: center;">
                  <div class="ripple-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                  <img src="${newValue.avatar}" style="width: 100%; height: 100%; position: relative; z-index: 2; object-fit: cover; border-radius: 50%;" />
                </div>
              `;
              overlay.setContent(avatarHtml);

              // 注释掉强制设置头像正立的代码
              // setTimeout(() => {
              //   this.forceAvatarUpright();
              // }, 50);
            }

            // 重新启动脉动动画
            const startContinuousRipple = (marker) => {
              // 清除之前的水波纹
              if (this.rippleAnimationInterval) {
                clearInterval(this.rippleAnimationInterval);
                this.rippleAnimationInterval = null;
              }

              // 获取水波纹容器
              setTimeout(() => {
                const rippleContainer = marker
                  .getContentDom()
                  .querySelector(".ripple-container");
                if (rippleContainer) {
                  // 创建持续的水波纹动画
                  this.rippleAnimationInterval = setInterval(() => {
                    const position = marker.getPosition();
                    // 检查位置是否有效
                    if (
                      !position ||
                      (position.getLng &&
                        (isNaN(position.getLng()) || isNaN(position.getLat())))
                    ) {
                      console.warn("无效的标记位置，跳过水波纹创建:", position);
                      return;
                    }
                    // 使用蓝色水波纹，更适合表示自己的位置
                    this.createRippleEffect(position, this.rippleColors.self, {
                      maxRadius: 60,
                      initialOpacity: 0.9,
                      animationDuration: 1000,
                      rippleCount: 2,
                      strokeWeight: 2,
                      fillOpacity: 0.6,
                      containerElement: rippleContainer,
                    });
                  }, 2000); // 每2秒创建一组新的水波纹

                  // 立即创建第一组水波纹
                  const firstPosition = marker.getPosition();
                  if (
                    firstPosition &&
                    (!firstPosition.getLng ||
                      (!isNaN(firstPosition.getLng()) &&
                        !isNaN(firstPosition.getLat())))
                  ) {
                    this.createRippleEffect(
                      firstPosition,
                      this.rippleColors.self,
                      {
                        maxRadius: 60,
                        initialOpacity: 0.9,
                        animationDuration: 1000,
                        rippleCount: 2,
                        strokeWeight: 2,
                        fillOpacity: 0.6,
                        containerElement: rippleContainer,
                      }
                    );
                  }
                }
              }, 50);
            };

            // 启动水波纹动画
            startContinuousRipple(overlay);
          }
          if (id === "Sdmarker") {
            overlay.setContent(
              `<div style="position:relative;top:33px;">
							<img style="width:30px;height:30px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)"
							src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/photo/20240121/6cba2eea-cced-44a8-8b13-241023e6d5d7.png">
							<div style="transform:scale(.8);font-size:12px;text-align:center;line-height:12px">${Number(
                speed
              ).toFixed(0)}<div>km/h</div>
							</div>`
            );
            overlay.setOffset(new AMap.Pixel(-40, -70)); // 向上移动30px (60rpx)
          }
          if (id === "Dlmarker") {
            let dlImg = "";
            if (newValue.electricity > 80) {
              dlImg = dl100;
            } else if (newValue.electricity > 55) {
              dlImg = dl75;
            } else if (newValue.electricity > 30) {
              dlImg = dl50;
            } else {
              dlImg = dl25;
            }
            //电量图片生成
            overlay.setContent(
              `<div style="color:#000;position:relative;top:70px;left:10px">
					<img style="width:40px;height:15px;position:absolute;z-index:-1;font-size:8px;left:50%;top:50%;transform:translate(-50%,-50%)" src="${dlImg}">
					<div style="transform:scale(.8);fonst-size:12px;line-height:12px;text-align:center">${newValue.electricity}%</div></div>`
            );
            overlay.setOffset(new AMap.Pixel(-20, -80)); // 向上移动30px (60rpx)
          }
          // 使用 moveTo 方法，但禁用自动旋转
          overlay.moveTo(endLnglat, {
            duration: speed,
            autoRotation: false, // 禁用移动时的自动旋转
          });
          // 备用方案：如果 moveTo 的 autoRotation 参数不生效，可以使用 setPosition
          // overlay.setPosition(new AMap.LngLat(newValue.longitude, newValue.latitude));
          // this.overlay_me.addOverlay(overlay)
        });

        // 立即强制恢复显示状态
        setTimeout(() => {
          console.log(
            "moveTo完成后，立即检查并恢复显示状态，showMeDesc:",
            this.showMeDesc
          );
          this.overlay_me.eachOverlay((overlay) => {
            const extData = overlay.getExtData();
            if (
              extData &&
              (extData.id === "Dlmarker" || extData.id === "Sdmarker")
            ) {
              // 使用保存的状态或当前的showMeDesc状态
              const shouldShow =
                markerVisibilityState[extData.id] !== undefined
                  ? markerVisibilityState[extData.id]
                  : this.showMeDesc;

              if (shouldShow) {
                console.log("强制显示标记:", extData.id);
                overlay.show();
              } else {
                console.log("确保隐藏标记:", extData.id);
                overlay.hide();
              }
            }
          });
        }, 10); // 很短的延迟，确保moveTo完成

        // 更新头像位置后重新启动动画
        setTimeout(() => {
          // 获取更新后的头像标记
          this.overlay_me.eachOverlay((overlay) => {
            const extData = overlay.getExtData();
            if (extData && extData.id === "avatar") {
              // 重新启动头像脉动动画
              this.startAvatarPulseAnimation(overlay);
            }
            // 注意：不在这里处理标记的显示状态，因为前面已经处理过了
            // 避免重复设置导致覆盖用户的选择状态
          });

          // 注释掉强制设置头像为正立状态
          // this.forceAvatarUpright();
        }, 100);
      }
    },
    // 切换用户展示 (已废弃，使用新的头像点击事件)
    triggerMeInfo() {
      // this.showMeDesc = !this.showMeDesc; // 注释掉，避免与新实现冲突
      const icon = this.meMarket.getIcon();
      const setIcon = this.showMeDesc ? this.me.map_user_mark : this.me.avatar;

      const changeIcon = newIcon(AMap, {
        image: setIcon,
        size: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
        imageSize: this.showMeDesc
          ? new AMap.Size(avatarHoverW, avatarHoverH)
          : new AMap.Size(avatarW, avatarH),
      });
      this.meMarket.setIcon(changeIcon);
      this.meMarket.setOffset(
        this.showMeDesc
          ? new AMap.Pixel(-avatarHoverW / 2, -avatarHoverH)
          : new AMap.Pixel(-avatarW / 2, -avatarH)
      );
    },
    /**
     *  view 层 弹窗回调
     * 	moon 变动回调
     */
    receive_toolEvent(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      const { act, params } = newValue;
      switch (act) {
        case "moonPopupClose":
          this.moon_modal && this.moon_modal.hide();
          break;
        case "visualAngle":
          myMap.setCenter([params.lng, params.lat]);
          break;
        case "housePopupClose":
          this.house_modal && this.house_modal.hide();
          break;
        case "move-view-center":
          if (params) {
            myMap && myMap.setCenter([params.lnogitude, params.latitude]);
          }
          break;
        case "poiMapAddMarker":
          if (myMap) {
            this.poiMarket = newMarker(AMap, {
              position: [params.lnglat.lng, params.lnglat.lat],
              offset: new AMap.Pixel(-25, -63),
              zIndex: 100,
            });
            // this.meMarket.setSize(new AMap.Size(150, 63))
            myMap.add(this.poiMarket);
            myMap.setCenter([params.lnglat.lng, params.lnglat.lat]);
          }
          break;
        case "person_guanzhu":
          if (myMap) {
            // 2.0版本：更新数据并重新设置
            this.clusterPersonData.forEach((item) => {
              if (params.uid == item.uid) {
                Object.assign(item, params);
              }
            });
            // 2.0版本：重新创建聚合器而不是更新数据
            if (this.cluster_person) {
              this.cluster_person.setMap(null);
              // 重新触发 receive_Person 来重建聚合器
              this.callMethod("refreshPersonCluster");
            }
          }
          break;
        case "mapStyleChange":
          if (myMap) {
            myMap.setMapStyle(params);
          }
          break;
        case "person_GhostSwitch":
          if (myMap) {
            console.log("[[[[[params]]]]]", params);
            // 2.0版本：更新数据并重新设置
            this.clusterPersonData.forEach((item) => {
              if (params.uid == item.uid) {
                Object.assign(item, params);
              }
            });
            // 2.0版本：重新创建聚合器而不是更新数据
            if (this.cluster_person) {
              this.cluster_person.setMap(null);
              // 重新触发 receive_Person 来重建聚合器
              this.callMethod("refreshPersonCluster");
            }
          }
          break;
        case "clearPersonSelection":
          // 清空选择的用户数组
          if (
            this.person_showDetail_arr &&
            this.person_showDetail_arr.length > 0
          ) {
            // 清除所有水波纹动画
            if (this.rippleIntervals && this.rippleIntervals.length > 0) {
              this.rippleIntervals.forEach((interval) => {
                clearInterval(interval);
              });
              this.rippleIntervals = [];
            }

            // 清除所有水波纹圆圈
            if (this.rippleCircles && this.rippleCircles.length > 0) {
              this.rippleCircles.forEach((circle) => {
                if (circle) {
                  myMap && myMap.remove(circle);
                }
              });
              this.rippleCircles = [];
            }

            // 清除头像脉动动画
            if (this.avatarPulseAnimation) {
              cancelAnimationFrame(this.avatarPulseAnimation);
              this.avatarPulseAnimation = null;
            }

            // 清除头像脉动计时器
            if (this.avatarPulseTimer) {
              clearTimeout(this.avatarPulseTimer);
              this.avatarPulseTimer = null;
            }

            // 清除其他人头像的动画
            this.clearPersonRippleAnimation();
          }

          // 清空选择数组
          this.person_showDetail_arr = [];

          // 重新创建聚合器以刷新显示，移除选中状态的头像样式
          if (map_person && map_person.length > 0) {
            this.receive_Person(map_person, null, null, null);
          }
          break;
        default:
          break;
      }
    },
    /**
     *  重置地图月亮
     * 	moon 变动回调
     */
    receive_Moon(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;

      const markers = newValue.map((item) => {
        const moonIcon = newIcon(AMap, {
          image: config.ossBaseUrl + "config/map/moon.png",
          size: new AMap.Size(30, 30),
          imageSize: new AMap.Size(30, 30),
        });
        const moonMarket = newMarker(AMap, {
          position: [item.longitude, item.latitude],
          icon: moonIcon,
          extData: item,
          offset: new AMap.Pixel(-15, -30),
          zIndex: 30,
        });

        moonMarket.on("click", () => {
          this.moon_modal && this.moon_modal.hide();
          myMap.setCenter([item.longitude, item.latitude]);
          this.moon_modal = new AMap.Marker({
            position: new AMap.LngLat(item.longitude, item.latitude),
            offset: new AMap.Pixel(-71, -120), //设置文本标注偏移量
            zIndex: 200,
            content: `<div style='width:122px;height:50px;padding:10px;color:#fff;background:#000;border-radius:10px;position:relative;'>
									<div style="
												font-size: 12px;
												font-family: Source Han Sans-Bold, Source Han Sans;
												font-weight: 700;
												color: #FFFFFF;
												line-height: 46rpx;">
												留宿地
									</div>
									<div style="position:absolute;
												right:10px;
												top:10px; 
									"onclick="openSettingMoon('${item.id},${item.location},${item.remark}')">
										<img src="https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/map/setingInfo.png" style="width:20px;height:20px">
									</div>
									<div style="
												font-size: 10px;
												font-family: Source Han Sans-Regular, Source Han Sans;
												font-weight: 400;
												color: rgba(255,255,255,0.64);
												line-height: 50rpx;
												margin-top:5px;
											">
												你在那里待了${item.stay_num}晚
												<div>${this.timeFmt(item.last_day, "yyyy年M月D号")}</div>
									</div>
									<div style="width: 0;
												height: 0;
												border: 10px solid;
												border-color: #000 transparent transparent transparent;
												position:absolute;
												left:50%;
												bottom:-20px;
												transform:translateX(-50%);
												">
									</div>
										
								
								</div>`,
          });
          myMap.add(this.moon_modal);
          // 显示月亮弹窗
        });

        return moonMarket;
      });
      this.overlay_moon && this.overlay_moon.clearOverlays();
      this.moon_modal && this.moon_modal.hide();
      this.overlay_moon && this.overlay_moon.addOverlays(markers);
    },
    timeFmt(date = "", fmt) {
      try {
        date = new Date(date || "");

        var a = ["日", "一", "二", "三", "四", "五", "六"];
        var o = {
          "M+": date.getMonth() + 1, // 月份
          "D+": date.getDate(), // 日
          "h+": date.getHours(), // 小时
          "m+": date.getMinutes(), // 分
          "s+": date.getSeconds(), // 秒
          "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
          S: date.getMilliseconds(), // 毫秒
          w: date.getDay(), // 周
          W: a[date.getDay()], // 大写周
          T: "T",
        };
        if (/(y+)/.test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        }
        for (var k in o) {
          if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length === 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
          }
        }
        return fmt;
      } catch (err) {}
    },
    // 点击商家模型
    click3dgltf(ev) {
      const pixel = ev.pixel;
      const px = new AMap.Pixel(pixel.x, pixel.y);
      const obj =
        myMap.getObject3DByContainerPos(px, [this.object3DlayerStore], false) ||
        {};
      if (obj && obj.object) {
        const meshId = obj.object.id;

        this.storeMeshes.forEach(async (item) => {
          if (item && item.layerMesh) {
            for (let i = 0; i < item.layerMesh.length; i++) {
              if (meshId === item.layerMesh[i].id) {
                const extData = item.gltf.option.extData;
                console.log("extData", extData);
                const lng = extData.Longitude;
                const lat = extData.Latitude;

                const startLocation = [this.me.longitude, this.me.latitude];
                const endLocation = [extData.Longitude, extData.Latitude];
                const result = await this.getDriver(startLocation, endLocation);
                if (result.info == "OK") {
                  const distance = result.routes[0].distance;
                  extData.distance =
                    distance && distance > 1000
                      ? (distance / 1000).toFixed(2) + " 千米"
                      : distance + " 米" || 0 + " 米";
                  // this.$refs.storePopup.open(extData)
                  this.callMethod("to_merchant_store", {
                    ...extData,
                  });
                }
              }
            }
          }
        });
      }
    },
    // 商家回调
    receive_MerchantStore(newValue, oldValue, ownerVm, vm) {
      if (newValue === null) return;
      map_merchantstore = newValue;

      this.overlay_merchant.clearOverlays();
      // this.overlay_merchant && this.overlay_merchant.clearMarkers()

      const markerIcons = [
        {
          id: "20",
          url: "http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png",
          // url:"http://img.lluuxiu.com/photo/20241212/69a74480-2d3d-435b-84a7-3650dd216b60.png",
        },
        {
          id: "13",
          url: "http://img.lluuxiu.com/photo/20241205/b69c5d91-d2f8-4dc4-86a8-99d58f465fd8.png",
          // url:"http://img.lluuxiu.com/photo/20241212/0acc9f43-0551-404f-9b18-af3d242070a1.png",
        },
        {
          id: "10",
          url: "http://img.lluuxiu.com/photo/20241205/9b2c6359-4713-4662-91fd-15e73bcd81a1.png",
          // url:"http://img.lluuxiu.com/photo/20241212/30b82e73-59e0-46f0-9855-e0e41dffa798.png"
        },
        {
          id: "3",
          url: "http://img.lluuxiu.com/photo/20241205/b19ad54a-ec0c-462c-b0d5-8ebd8a686d0e.png",
          // url:"http://img.lluuxiu.com/photo/20241212/c1564f9a-61cd-4ab6-af92-08e28b2e4682.png",
        },
      ];
      map_merchantstore.forEach((item) => {
        var level = item.Name.split(":")[1] - 1;
        // var level = 3;
        const iconUrl = markerIcons[level].url || markerIcons[0].url; // 如果没有匹配的level，使用默认图标
        const icon_per = new AMap.Icon({
          // image: "http://img.lluuxiu.com/photo/20241205/7f683c37-3323-4ffc-8221-2483f12cf06a.png",
          image: iconUrl,
          //          size: new AMap.Size(100, 220), // 设置图标大小，例如 50x50 像素
          //          imageSize: new AMap.Size(100, 220), // 设置图标实际大小，例如 50x50 像素
          // anchor: new AMap.Pixel(50, 110),
          size: new AMap.Size(100, 100), // 设置图标大小，例如 50x50 像素
          imageSize: new AMap.Size(100, 100), // 设置图标实际大小，例如 50x50 像素
          anchor: new AMap.Pixel(50, 50),
        });
        const div = document.createElement("div");
        const img = document.createElement("img");
        img.style.width = "100px";
        img.style.height = "100px";
        img.src = markerIcons[level].url || markerIcons[0].url;
        div.appendChild(img);

        const marker_per = new AMap.Marker({
          position: [item.Longitude, item.Latitude],
          // offset: new AMap.Pixel(-15, -15),
          offset: new AMap.Pixel(-50, -50),
          icon: icon_per,
          // content:div,
          extData: {
            item: item,
            id: markerIcons[level].id,
          },
          zIndex: 90,
        });
        marker_per.on("click", (ev) => {
          console.log("商家标记点击事件触发", ev.target);
          try {
            const extData = ev.target.De
              ? ev.target.De.extData
              : ev.target.getExtData();
            console.log("商家扩展数据:", extData);
            this.callMethod("to_merchant_store", extData.item);
            myMap.setCenter([item.Longitude, item.Latitude]);
            myMap.setZoom(15);
          } catch (error) {
            console.error("商家点击事件错误:", error);
          }
        });
        this.storeMarkers.push(marker_per);
      });
      this.overlay_merchant &&
        this.overlay_merchant.addOverlays(this.storeMarkers);
    },
    // 创建水波纹效果
    createRippleEffect(
      position,
      color = this.rippleColors.reset,
      options = {}
    ) {
      // 验证position参数是否有效
      if (
        !position ||
        typeof position !== "object" ||
        (Array.isArray(position) &&
          (position.length < 2 || isNaN(position[0]) || isNaN(position[1])))
      ) {
        console.warn("无效的位置坐标", position);
        return;
      }

      // 确保position是数组或AMap.LngLat对象
      let lng, lat;
      if (Array.isArray(position)) {
        [lng, lat] = position;
      } else if (position.getLng && position.getLat) {
        // 处理AMap.LngLat对象
        lng = position.getLng();
        lat = position.getLat();
        position = [lng, lat]; // 转换为数组格式，以便后续处理
      } else {
        console.warn("无法解析坐标对象:", position);
        return;
      }

      // 再次验证坐标值
      if (isNaN(lng) || isNaN(lat)) {
        console.warn("坐标值无效:", lng, lat);
        return;
      }

      // 合并默认参数和传入的参数
      const {
        maxRadius = this.rippleEffectParams.maxRadius,
        initialRadius = this.rippleEffectParams.initialRadius,
        initialOpacity = this.rippleEffectParams.initialOpacity,
        animationDuration = this.rippleEffectParams.animationDuration,
        rippleCount = this.rippleEffectParams.rippleCount,
        strokeWeight = this.rippleEffectParams.strokeWeight,
        fillOpacity = this.rippleEffectParams.fillOpacity,
        yOffset = this.rippleEffectParams.yOffset,
        xOffset = this.rippleEffectParams.xOffset,
        gradientColors = this.rippleEffectParams.gradientColors,
        rotationAngle = this.rippleEffectParams.rotationAngle,
        shape = this.rippleEffectParams.shape,
        animationType = this.rippleEffectParams.animationType,
        zIndex = this.rippleEffectParams.zIndex,
        containerElement = null,
        strokeColor = this.rippleEffectParams.strokeColor,
        boxShadow = this.rippleEffectParams.boxShadow,
      } = options;

      // 清除之前的水波纹
      for (let i = 0; i < this.rippleIntervals.length; i++) {
        clearInterval(this.rippleIntervals[i]);
      }
      this.rippleIntervals = [];

      // 如果要在DOM元素中创建水波纹效果
      if (containerElement) {
        // 清除之前的圆圈
        containerElement.innerHTML = "";

        // 为每个水波纹层创建定时器
        for (let i = 0; i < rippleCount; i++) {
          setTimeout(() => {
            // 创建圆圈元素
            const circle = document.createElement("div");
            circle.style.position = "absolute";
            // 根据shape参数设置形状
            circle.style.borderRadius = shape === "circle" ? "50%" : "30%";
            // 应用旋转角度
            if (rotationAngle !== 0) {
              circle.style.transform = `rotate(${rotationAngle}deg)`;
            }
            // 设置z-index
            circle.style.zIndex = zIndex;

            // 使用渐变背景
            if (gradientColors && gradientColors.length >= 2) {
              circle.style.background = `linear-gradient(to right, ${gradientColors[0]} 10%, ${gradientColors[1]} 70%)`;
            } else {
              circle.style.backgroundColor = color;
            }

            // 只在strokeWeight > 0时添加边框
            if (strokeWeight > 0) {
              circle.style.border = `${strokeWeight}px solid ${color}`;
            }

            // 设置填充颜色
            circle.style.opacity = initialOpacity * fillOpacity;

            // 初始样式
            circle.style.width = `${initialRadius * 2}px`;
            circle.style.height = `${initialRadius * 2}px`;

            // 动态计算容器中心点
            const containerRect = containerElement.getBoundingClientRect();
            const containerCenterX = containerRect.width / 2;
            const containerCenterY = containerRect.height / 2;

            circle.style.left = `${
              containerCenterX - initialRadius + xOffset
            }px`;
            circle.style.top = `${
              containerCenterY - initialRadius + yOffset
            }px`;

            containerElement.appendChild(circle);

            // 设置动画
            const startTime = Date.now();
            const interval = setInterval(() => {
              const elapsedTime = Date.now() - startTime;
              const progress = Math.min(elapsedTime / animationDuration, 1);

              // 计算当前半径
              let currentRadius;
              // 简单的线性进展，不添加波动效果
              currentRadius =
                initialRadius + (maxRadius - initialRadius) * progress;

              // 根据动画类型设置不透明度
              let currentOpacity;
              if (animationType === "pulse") {
                // 脉冲效果：先增加再减少
                currentOpacity = initialOpacity * Math.sin(progress * Math.PI);
              } else {
                // 更平滑的淡出效果
                // 使用指数函数实现更自然的淡出效果
                const fadeOutFactor = Math.pow(1 - progress, 2);
                currentOpacity = initialOpacity * fadeOutFactor;
              }

              // 更新样式
              circle.style.width = `${currentRadius * 2}px`;
              circle.style.height = `${currentRadius * 2}px`;
              circle.style.left = `${
                containerCenterX - currentRadius + xOffset
              }px`;
              circle.style.top = `${
                containerCenterY - currentRadius + yOffset
              }px`;
              circle.style.opacity = currentOpacity;
              circle.style.boxShadow =
                boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)";

              // 动画完成后立即清除该层水波纹
              if (progress >= 1) {
                clearInterval(interval);
                circle.remove();

                // 从水波纹定时器数组中移除此定时器
                const index = this.rippleIntervals.indexOf(interval);
                if (index > -1) {
                  this.rippleIntervals.splice(index, 1);
                }
              }
            }, 16); // 约60fps

            this.rippleIntervals.push(interval);
          }, i * (animationDuration / 2)); // 第一个动画执行到一半时第二个动画开始
        }
      } else {
        // 在地图上创建水波纹效果
        this.rippleCircles = [];

        // 为每个水波纹层创建定时器
        let previousAnimationStartTime = Date.now();

        const createNextRipple = (index) => {
          if (index >= rippleCount) return;

          // 创建地图覆盖物，根据shape参数决定形状
          const center = position;
          let mapOverlay;

          try {
            // 再次检查坐标的有效性
            if (isNaN(center[0]) || isNaN(center[1])) {
              console.warn("创建覆盖物时检测到无效的坐标", center);
              return;
            }

            if (shape === "circle") {
              // 创建圆形覆盖物
              mapOverlay = new AMap.Circle({
                center: new AMap.LngLat(
                  parseFloat(center[0]),
                  parseFloat(center[1])
                ),
                radius: initialRadius,
                strokeColor: strokeColor || "rgba(255, 255, 255, 0.8)",
                strokeWeight: strokeWeight,
                strokeOpacity: strokeWeight > 0 ? initialOpacity : 0,
                fillColor:
                  gradientColors && gradientColors.length >= 2
                    ? gradientColors[1]
                    : color,
                fillOpacity: fillOpacity,
                zIndex: zIndex,
                extData: {
                  customStyle: {
                    boxShadow:
                      options.boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)",
                  },
                },
              });
            } else {
              // 创建正方形覆盖物
              let bounds = [
                parseFloat(center[0]) - initialRadius / 10000, // 左下角经度
                parseFloat(center[1]) - initialRadius / 10000, // 左下角纬度
                parseFloat(center[0]) + initialRadius / 10000, // 右上角经度
                parseFloat(center[1]) + initialRadius / 10000, // 右上角纬度
              ];

              // 检查bounds中的值是否都有效
              if (bounds.some((coord) => isNaN(coord))) {
                console.warn("计算矩形边界时出现无效坐标", bounds);
                return;
              }

              mapOverlay = new AMap.Rectangle({
                bounds: new AMap.Bounds(
                  [bounds[0], bounds[1]],
                  [bounds[2], bounds[3]]
                ),
                strokeColor: strokeColor || "rgba(255, 255, 255, 0.8)",
                strokeWeight: strokeWeight,
                strokeOpacity: strokeWeight > 0 ? initialOpacity : 0,
                fillColor:
                  gradientColors && gradientColors.length >= 2
                    ? gradientColors[1]
                    : color,
                fillOpacity: fillOpacity,
                zIndex: zIndex,
                // 尝试添加样式以实现渐变效果
                extData: {
                  customStyle: {
                    gradient:
                      gradientColors && gradientColors.length >= 2
                        ? true
                        : false,
                    gradientColors: gradientColors,
                    boxShadow:
                      options.boxShadow || "0 0 8px rgba(255, 255, 255, 0.6)",
                  },
                },
              });
            }

            // 如果支持自定义样式，可以尝试通过CSS添加旋转效果
            if (mapOverlay.setOptions && rotationAngle !== 0) {
              try {
                mapOverlay.setOptions({
                  extData: {
                    rotationAngle: rotationAngle,
                  },
                });
              } catch (e) {
                console.log("地图元素不支持旋转参数", e);
              }
            }

            // 添加到地图
            myMap.add(mapOverlay);
            this.rippleCircles.push(mapOverlay);

            // 设置动画
            const startTime = Date.now();
            previousAnimationStartTime = startTime;

            const interval = setInterval(() => {
              const elapsedTime = Date.now() - startTime;
              const progress = Math.min(elapsedTime / animationDuration, 1);

              // 计算当前尺寸
              let currentSize;
              // 简单的线性进展，不添加波动效果
              currentSize =
                initialRadius + (maxRadius - initialRadius) * progress;

              // 根据动画类型设置不透明度
              let currentOpacity, currentFillOpacity;
              if (animationType === "pulse") {
                // 脉冲效果：先增加再减少
                currentOpacity = initialOpacity * Math.sin(progress * Math.PI);
                currentFillOpacity = fillOpacity * Math.sin(progress * Math.PI);
              } else {
                // 更平滑的淡出效果
                // 使用指数函数实现更自然的淡出效果
                const fadeOutFactor = Math.pow(1 - progress, 2);
                currentOpacity = initialOpacity * fadeOutFactor;
                currentFillOpacity = fillOpacity * fadeOutFactor;
              }

              if (shape === "circle") {
                // 更新圆形的半径
                mapOverlay.setRadius(currentSize);
              } else {
                // 更新正方形的边界
                const newBounds = [
                  center[0] - currentSize / 10000,
                  center[1] - currentSize / 10000,
                  center[0] + currentSize / 10000,
                  center[1] + currentSize / 10000,
                ];

                mapOverlay.setBounds(
                  new AMap.Bounds(
                    [newBounds[0], newBounds[1]],
                    [newBounds[2], newBounds[3]]
                  )
                );
              }

              mapOverlay.setOptions({
                strokeOpacity: strokeWeight > 0 ? currentOpacity : 0,
                fillOpacity: currentFillOpacity,
              });

              // 当动画进行到一半时，开始下一个水波纹动画
              if (
                progress >= 0.5 &&
                index < rippleCount - 1 &&
                Date.now() - previousAnimationStartTime >=
                  animationDuration * 0.5
              ) {
                createNextRipple(index + 1);
              }

              if (progress >= 1) {
                // 动画完成，移除覆盖物
                myMap.remove(mapOverlay);
                const index = this.rippleCircles.indexOf(mapOverlay);
                if (index > -1) {
                  this.rippleCircles.splice(index, 1);
                }
                clearInterval(interval);

                // 从水波纹定时器数组中移除此定时器
                const intervalIndex = this.rippleIntervals.indexOf(interval);
                if (intervalIndex > -1) {
                  this.rippleIntervals.splice(intervalIndex, 1);
                }
              }
            }, 16); // 约60fps

            this.rippleIntervals.push(interval);
          } catch (e) {
            console.log("创建水波纹效果时出错:", e);
            clearInterval(interval);
          }
        };

        // 开始第一个水波纹动画
        createNextRipple(0);
      }
    },

    // 重置自己位置的水波纹动画
    resetMyRippleAnimation() {
      try {
        console.log("开始重置自己位置的水波纹动画");

        // 先重置所有动画状态
        this.resetAllAnimations();

        // 查找自己的标记
        if (this.overlay_me && this.overlay_me.getOverlays) {
          const overlays = this.overlay_me.getOverlays();
          if (overlays && overlays.length > 0) {
            const myMarker = overlays[0]; // 自己的标记通常是第一个

            if (myMarker && myMarker.getContentDom) {
              const contentDom = myMarker.getContentDom();
              if (contentDom) {
                const rippleContainer =
                  contentDom.querySelector(".ripple-container");
                if (rippleContainer) {
                  // 清除所有现有的水波纹元素
                  rippleContainer.innerHTML = "";

                  // 延迟重新启动水波纹动画
                  setTimeout(() => {
                    console.log("重新启动自己位置的水波纹动画");
                    this.createRippleEffect(null, this.rippleColors.self, {
                      containerElement: rippleContainer,
                    });
                  }, 200);
                }
              }
            }
          }
        }
      } catch (error) {
        console.log("重置水波纹动画失败:", error);
      }
    },
  },
};
