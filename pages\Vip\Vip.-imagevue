<template>
	<view class="content">
		<view class="top">
			<view class="top-circular">

			</view>
			<view class="top-title">
				<image @click="back" class="back" src="../../static/images/vip/back.png" mode=""></image>
				<view class="nav-title">
					商家会员
				</view>
			</view>
			<view class="cardList">
					<swiper class="swiperCard" circular :indicator-dots="indicatorDots" :loop="loop" :autoplay="autoplay" :interval="interval"
						:duration="duration" @change="swiperChange">
						<swiper-item class="swiperCardItem" :class="item.goods_prefix" v-for="(item,index) in productList" :key="index" >
							<!-- 图片 -->
							<view class="swiperCardItemCover">
								<!-- 状态 -->
								<view class="swiperCardItemStatus">{{shopInfo.vip_level == (index*1+1) ?'已解锁':'未解锁'}}</view>
								<view class="swiperCardItemTitle">
									<view class="swiperCardItemTitleName">会员</view>
									<view class="swiperCardItemTitleLevelName">{{item.name}}</view>
								</view>
							</view>
							
							<view class="swiperCardItemContent">
								<!-- 边框 -->
								<!-- <view class="swiperCardItemContentCircle"></view> -->
								<!-- 描述 -->
								<view class="swiperCardItemDescribeBg">
									<view class="swiperCardItemDescribe">
										<view class="swiperCardItemDescribeContent">
											<view class="swiperCardItemDescribeContentLevelName">
												{{item.name}}
											</view>
											<view class="swiperCardItemDescribeContentText">
												地图上展示小房子<image class="house" src="../../static/images/vip/house.png" mode="widthFix"></image> 在较小比例尺内显示
											</view>
										</view>
									</view>
								</view>
								
							</view>
						</swiper-item>
					</swiper>
			</view>
			
		</view>

		<view v-if="isShowPay" class="map">
			<image class="map-bg" src="../../static/images/vip/map.png" mode=""></image>
		</view>
		<view v-else class="map">
			<liu-easy-map :centerLat="lat" :centerLng="lon" :scale="17" :markerData="markerData" :polygons="polygons"
				@clickMarker="markerClick"></liu-easy-map>
		</view>
		
		<view class="bottom">
			<!-- <view class="bottom-top">
				To Be Determined To Be Determined To Be Determined To Be Determined To Be Determined《用户服务》《用户隐私政策》
			</view> -->
			<view class="bottom-top">
				To Be Determined To Be Determined To Be Determined To Be Determined To Be Determined
				<navigator class="agreement" to="/pages/index/index">《用户服务》</navigator>
				<navigator class="agreement" to="/pages/index/index">《用户隐私政策》</navigator>
			</view>
			<button class="button" type="default" @click="toPay">购买</button>
		</view>
		<!-- 弹框 -->
		<view v-if="isShowPay" class="box-shadow">
			<view class="box-shadow-content">
				<view class="close-container" @click="closePay">
					<image class="close-icon" src="../../static/images/close-icon.png" mode=""></image>
				</view>
				<view class="price-container">
					<view class="price-icon">￥</view>{{price}}
				</view>
				<view class="order-describe">
					<view class="order-describe-title">订单信息</view>
					<view>商户会员支付</view>
				</view>
				<view class="pay-status">
					<view class="t_betweent pay-status-item" v-for="(item,idx) in payStatus" :key="idx" @click="payIndex=idx">
						<view class="t_display">
							<image class="img42" :src="'../../static/images/vip/'+item.img" mode=""></image>
							<span class="txt">{{item.name}}</span>
						</view>
						<image class="img32"
							:src="payIndex===idx?'../../static/images/pay/radio2.png':'../../static/images/pay/radio.png'"
							mode=""></image>
					</view>
				</view>

				<view class="btn" @click="payment">
					立即支付
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		Iap,
		IapTransactionState
	} from "./iap.js"
	export default {
		data() {
			return {
				list: [{
						src: "../../static/images/vip/qingtong.png"
					},
					{
						src: "../../static/images/vip/baiyin.png"
					},
					{
						src: "../../static/images/vip/huangjin.png"
					},
					{
						src: "../../static/images/vip/baijin.png"
					},
					{
						src: "../../static/images/vip/zuanshi.png"
					}
				],
				loop:false,
				autoplay: false,
				interval: 3000,
				 duration: 500,
				font_color: "level-qingtong",
				border_color: "border-qingtong",
				lon: '123.599861',
				lat: '43.758234',
				markerData: [{
					id: 1,
					name: '', //标记点展示名字
					address: '',
					latitude: '', //标记点纬度
					longitude: '', //标记点经度
					markerUrl: '', //标记点图标地址
					iconWidth: 32, //标记点图标宽度
					iconHeight: 32, //标记点图标高度
					calloutColor: '#ffffff', //气泡窗口 文本颜色
					calloutFontSize: 14, //气泡窗口 文本大小
					calloutBorderRadius: 6, //气泡窗口 边框圆角
					calloutPadding: 8, //气泡窗口 文本边缘留白
					calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
					calloutDisplay: 'ALWAYS' //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
				}],
				//展示区域点位信息
				polygons: [{
					points: [], //经纬度数组
					strokeWidth: 2, //描边的宽度
					strokeColor: '#FF000060', //描边的颜色
					fillColor: '#FF000090' //填充颜色
				}],
				info: {},
				shopInfo: {},
				status: 1,
				level: 0,
				current: 0,
				level_name: '',
				loading: false,
				productId: '',
				productList: [{
						id: 'bvip1',
						alias: 'vip1' //别名
					},
					{
						id: 'bvip2',
						alias: 'vip2' //别名
					},
					{
						id: 'bvip3',
						alias: 'vip3' //别名
					},
					{
						id: 'bvip4',
						alias: 'vip4' //别名
					},
					{
						id: 'bvip5',
						alias: 'vip5' //别名
					},
				],
				payIndex: 0, //支付方式下标
				payStatus: [{
						name: "支付宝",
						img: "zhifubao.png"
					},
					// {
					// 		name: "Apple pay",
					// 		img: "apple-pay.png"
					// },
					// {
					// 		name: "微信支付",
					// 		img: "weixin.png"
					// 	},
				],
				isShowPay:false,
				platform:1,//设备类型
				indicatorDots:false,
				price:0,
			};
		},
		onLoad() {
			// 获取设备类型
			const systemInfo = uni.getSystemInfoSync();
			if (systemInfo.platform === 'android') {
			    console.log('运行在安卓设备上');
				this.platform = 1;
			} else if (systemInfo.platform === 'ios') {
			    this.platform = 2;
				var item = {
					name: "Apple pay",
					img: "apple-pay.png"
				}
				this.payStatus.push(item);
			} 
			this.getData();
			this.getVipList();
			// this._iap = new Iap({
			// 	products: [
			// 		// 苹果开发者中心创建
			// 		{
			// 			id: 'bvip1',
			// 			alias: 'vip1' //别名
			// 		},
			// 		{
			// 			id: 'bvip2',
			// 			alias: 'vip2' //别名
			// 		},
			// 		{
			// 			id: 'bvip3',
			// 			alias: 'vip3' //别名
			// 		},
			// 		{
			// 			id: 'bvip4',
			// 			alias: 'vip4' //别名
			// 		},
			// 		{
			// 			id: 'bvip5',
			// 			alias: 'vip5' //别名
			// 		},
			// 	] // 苹果开发者中心创建
			// })
		},
		methods: {
			 changeIndicatorDots(e) {
			            this.indicatorDots = !this.indicatorDots
			        },
			        changeAutoplay(e) {
			            this.autoplay = !this.autoplay
			        },
			        intervalChange(e) {
			            this.interval = e.target.value
			        },
			        durationChange(e) {
			            this.duration = e.target.value
			        },
			//当前轮播索引
			change(e) {
				//e 0/青铜,1/白银,2/黄金,3/白金,4/钻石;
				console.log('==========', e)
				switch (e) {
					case 0:
						this.font_color = 'level-qingtong';
						this.border_color = 'border-qingtong';
						this.level_name = '青铜会员';
						break;
					case 1:
						this.font_color = 'level-baiyin';
						this.border_color = 'border-baiyin';
						this.level_name = '白银会员';
						break;
					case 2:
						this.font_color = 'level-huangjin';
						this.border_color = 'border-huangjin';
						this.level_name = '黄金会员';
						break;
					case 3:
						this.font_color = 'level-baijin';
						this.border_color = 'border-baijin';
						this.level_name = '白金会员';
						break;
					case 4:
						this.font_color = 'level-zuanshi';
						this.border_color = 'border-zuanshi';
						this.level_name = '钻石会员';
						break;
				}
				this.productId = this.productList[e].id;
				console.log(this.productId);
			},
			swiperChange(e){
				var currentSwiperIndex = e.detail.current;
				
				this.productId = this.productList[currentSwiperIndex].id;
				this.price =  this.productList[currentSwiperIndex].now_amount;
			},
			// 打开支付弹框
			toPay(){
				this.isShowPay = true;
			},
			// 关闭支付弹框
			closePay(){
				this.isShowPay = false;
			},
			// 支付
			async payment() {
				if (this.loading == true) {
					return;
				}
				this.loading = true;

				uni.showLoading({
					title: '支付处理中...'
				});
				let amount = "0.1";
				//支付宝
				if (this.payIndex == 0) {
					console.log(this.productId)
					// http://www.dev.lluuxiu.com/api/pay/goods/vip/buy/ali
					this.$http.post('/api/pay/goods/vip/buy/ali', {
						gid: this.productId
					}).then(res => {
						console.log(res.code)
						console.log(res.reason)
						console.log("支付宝"+res.message)
						this.loading = false;
						this.isShowPay = false;
						uni.hideLoading();
						//统一各平台的客户端支付API
						uni.requestPayment({
							provider: 'alipay', //服务提供商（支付宝）（服务提供商，通过uni.getProvider获取）
							orderInfo: res.message, //后台返回的支付宝订单数据
							success(res) {
								this.getData()
								this.isShowPay = false;
								this.loading = false;
								uni.hideLoading();
							},
							fail(err) {
								console.log('fail:' + JSON.stringify(err));
								this.loading = false;
								this.isShowPay = false;
								uni.hideLoading();
							}
						});
					})
				} else if (this.payIndex == 1) { //苹果支付
					try {
						// 从开发者服务器创建订单
						const orderId = await this.createOrder({
						  productId: this.productId
						});
					
						// 请求苹果支付
						const transaction = await this._iap.requestPayment({
							productid: this.productId,
							manualFinishTransaction: true,
							username: username + orderId //根据业务需求透传参数，关联用户和订单关系
						});
					
						// 在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
						await this.validatePaymentResult({
						  orderId: orderId,
						  username: username,
						  transactionReceipt: transaction.transactionReceipt, // 不可作为订单唯一标识
						  transactionIdentifier: transaction.transactionIdentifier
						});
					
						// 验证成功后关闭订单
						await this._iap.finishTransaction(transaction);
					
						// 支付成功
					} catch (e) {
						uni.showModal({
							content: e.message,
							showCancel: false
						});
					} finally {
						this.loading = false;
						uni.hideLoading();
					}		
				}
				
			},
			//点击轮播
			click(e) {
				console.log('点击轮播', e)
			},
			back() {
				// uni.navigateBack({
				// 	delta: 1
				// })
				uni.navigateTo({
					url: '/pages/shop/shop'
				});
			},
			markerClick(e) {
				console.log('点击标记点信息：', e);
			},
			getData() {
				this.$http.get('/api/user/info').then(async (res) => {
					this.info = res.message;
					this.getShopInfo();
					// console.log('info', this.info);
					// this.current = this.info.user_info.vip_level;
					// switch (this.current) {
					// 	case 0:
					// 		this.font_color = 'level-qingtong';
					// 		this.border_color = 'border-qingtong';
					// 		this.level_name = '青铜会员';
					// 		break;
					// 	case 1:
					// 		this.font_color = 'level-baiyin';
					// 		this.border_color = 'border-baiyin';
					// 		this.level_name = '白银会员';
					// 		break;
					// 	case 2:
					// 		this.font_color = 'level-huangjin';
					// 		this.border_color = 'border-huangjin';
					// 		this.level_name = '黄金会员';
					// 		break;
					// 	case 3:
					// 		this.font_color = 'level-baijin';
					// 		this.border_color = 'border-baijin';
					// 		this.level_name = '白金会员';
					// 		break;
					// 	case 4:
					// 		this.font_color = 'level-zuanshi';
					// 		this.border_color = 'border-zuanshi';
					// 		this.level_name = '钻石会员';
					// 		break;
					// }

					// console.log('current', this.current);
				});
			},
			async init() {
				this.productList = await this._iap.getProduct();
				await this._iap.init();
			},
			getShopInfo() {
				this.$http.get('/api/user/business/get').then((res) => {
					this.shopInfo = res.message;
					console.log('shopinfo', this.shopInfo);
					this.lon = this.shopInfo.coordinate.split(',')[0];
					this.lat = this.shopInfo.coordinate.split(',')[1];
					this.markerData = [{
						id: this.shopInfo.id,
						name: this.shopInfo.title, //标记点展示名字
						address: this.shopInfo.title,
						latitude: this.lat, //标记点纬度
						longitude: this.lon, //标记点经度
						markerUrl:'http://img.lluuxiu.com/photo/20240921/c4494ba7-8f9f-427d-bfb3-64b8535241ae.png',
						// markerUrl: 'https://img0.baidu.com/it/u=550544800,2229099292&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', //标记点图标地址
						iconWidth: 32, //标记点图标宽度
						iconHeight: 32, //标记点图标高度
						calloutColor: '#ffffff', //气泡窗口 文本颜色
						calloutFontSize: 14, //气泡窗口 文本大小
						calloutBorderRadius: 6, //气泡窗口 边框圆角
						calloutPadding: 8, //气泡窗口 文本边缘留白
						calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
						calloutDisplay: 'ALWAYS' //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
					}];
				});
			},
			// 获取会员列表
			getVipList() {
				this.$http.get('/api/pay/vip-goods', {
					goods_type: 3,
					platform: this.platform
				}).then((res) => {
					console.log('vipInfo', res.message);
					this.productList = res.message;
					this.productId = this.productList[0].id;
					this.price =  this.productList[0].now_amount;
					// 创建实例
					this._iap = new Iap({
						products: [this.productId] // 苹果开发者中心创建
					})
					// this._iap = new Iap({
					// 	products: [
					// 		// 苹果开发者中心创建
					// 		{
					// 			id: 'bvip1',
					// 			alias: 'vip1' //别名
					// 		},
					// 		{
					// 			id: 'bvip2',
					// 			alias: 'vip2' //别名
					// 		},
					// 		{
					// 			id: 'bvip3',
					// 			alias: 'vip3' //别名
					// 		},
					// 		{
					// 			id: 'bvip4',
					// 			alias: 'vip4' //别名
					// 		},
					// 		{
					// 			id: 'bvip5',
					// 			alias: 'vip5' //别名
					// 		},
					// 	] // 苹果开发者中心创建
					// })
				});
			}
		}
	};
</script>

<style lang="scss">

		.uni-margin-wrap {
			width: 690rpx;
			width: 100%;
		}
		.swiper {
			height: 300rpx;
		}
		.swiper-item {
			display: block;
			height: 300rpx;
			line-height: 300rpx;
			text-align: center;
		}
		.swiper-list {
			margin-top: 40rpx;
			margin-bottom: 0;
		}
		.uni-common-mt {
			margin-top: 60rpx;
			position: relative;
		}
		.info {
			position: absolute;
			right: 20rpx;
		}
		.uni-padding-wrap {
			width: 550rpx;
			padding: 0 100rpx;
		}
	

	/* 假设字体文件名为 MyFont.woff，并放在 static/font 文件夹下 */
	@font-face {
		font-family: '钉钉进步体';
		/* 你可以给字体起一个别名 */
		src: url('~@/static/images/vip/font/DingTalkJinBuTi.ttf') format('truetype');
		/* 还可以加上 ttf 格式作为备选 */
		font-weight: normal;
		font-style: normal;
	}

	@font-face {
		font-family: '阿里巴巴普惠体';
		/* 你可以给字体起一个别名 */
		src: url('~@/static/images/vip/font/AlibabaPuHuiTi.ttf') format('truetype');
		/* 还可以加上 ttf 格式作为备选 */
		font-weight: normal;
		font-style: normal;
	}

	page {
		background-color: #24262E;
	}

	.content {
		width: 100%;
		height: 100%;
		background-color: #24262E;
		position: absolute;
		top: 0;
		display:flex;
		flex-direction: column;
		.top {
			/* 矩形 239 */
			width: 100%;
			// height: 252px;
			background: rgb(35, 35, 45);
		}

		.top-circular {
			/* 椭圆 13 */
			position: absolute;
			width: 247px;
			height: 241px;
			left: -31.53%;
			right: 46.67%;
			top: -15.68%;
			bottom: 83%;
			filter: blur(107px);
			background-color: rgba(222, 230, 243, 0.41);
		}

		.top-title {
			/* 组合 61 */
			width: 100%;
			height: 30px;
			display: flex;
			margin: 54px 0px 0 2%;

		}

		.back {
			/* 组合 4 */
			width: 31px;
			height: 31px;
		}

		.nav-title {
			/* 商家会员 */
			width: 82%;
			;
			height: 23px;

			color: rgb(255, 255, 255);
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: center;
		}

		.cardList {
			width: 100%;
			margin-top: 12%;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-wrap: nowrap;
			flex-direction: row;
			// width: 200%;
			//     overflow: hidden;
			//     position: relative;
			//     left: -50%;
			//     border-bottom-left-radius: 700px 180px;
			//     border-bottom-right-radius: 700px 180px;
			//     height: 150px;
			//     /deep/ .van-swipe {
			//       width: 50% !important;
			//       position: relative;
			//       left: 25%;
			//     }
			//     img {
			//       width: 100%;
			//       height: 100%;
			//     }
			  
			.swiperCard{
				width: 100%;
				height:254px;
				.swiperCardItem{
					width:100%;
					.swiperCardItemCover{
						width:90%;
						position:relative;
						margin:0 auto;
						box-sizing: border-box;
						border-radius: 16px;
						.swiperCardItemStatus{
							position:absolute;
							top:0;
							left:0;
							border-radius: 60px 0px 60px 0px;
							background: rgba(196, 196, 196,0.1);
							text-align: center;
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 500;
							line-height: 15px;
							letter-spacing: 0px;
							padding:5px 20px;
							box-sizing: border-box;
						}
						.swiperCardItemTitle{
							width:100%;
							height: 100%;
							display:flex;
							justify-content: flex-start;
							padding:52px 20px 70px 20px;
							box-sizing: border-box;
							font-family: 钉钉进步体;
							font-weight: 400;
							.swiperCardItemTitleName{
								font-size: 32px;
								line-height: 38px;
								margin-right:12px;
							}
							.swiperCardItemTitleLevelName{
								font-size: 12px;
								text-align: center;
								height: 24px;
								padding:5px 8px;
								box-sizing: border-box;
								margin-top:7px;
							}
						}
					}				
					.swiperCardItemContent{
						width:100%;
						margin-top:-60px;
						.swiperCardItemDescribeBg{
							background-image: url("../../static/images/vip/mengban.png");
							background-repeat: no-repeat;
							background-size: 100%;
							width: 100%;
							position: relative;
							display: flex;
							flex-direction: row;
							justify-content: center;
							padding:14% 20px 20px 20px;
							box-sizing: border-box;
							.swiperCardItemDescribe{
								width:100%;
								.swiperCardItemDescribeContent{
									width: 100%;
									max-height: 88px;
									box-sizing: border-box;
									border-radius: 12px;
									background: rgba(241, 230, 220, 0.16);
									display: flex;
									flex-direction: column;
									justify-content: center;
									align-items: flex-start;
									padding:15px;
									box-sizing: border-box;
									font-family: 阿里巴巴普惠体;
									font-size: 14px;
									font-weight: 400;
									line-height: 19px;
									.swiperCardItemDescribeContentLevelName{
										font-size: 14px;
									}
									.swiperCardItemDescribeContentText{
										font-size: 12px;
										.house {
											width: 26px;
											height: auto;
											margin:0px 10px -6px 10px;
										}
									}
								}
							}
						}
						
					}
				}
				.BVIP1{
					color: rgb(255, 142, 43);
					.swiperCardItemCover{
						border: 1px solid rgb(228, 209, 182);
						background: linear-gradient(-45.00deg, rgb(222, 186, 147) 0%,rgb(255, 241, 221) 53.135%,rgb(255, 245, 230) 62.964%,rgb(238, 205, 176) 100%);
						.swiperCardItemTitle{
							.swiperCardItemTitleLevelName{
								border:1px solid rgb(255, 142, 43);
							}
						}
					}
					.swiperCardItemContent{
						color: rgb(255, 151, 60);
						.swiperCardItemDescribeBg{
							.swiperCardItemDescribe{
								.swiperCardItemDescribeContent{
									border:1px solid rgba(241, 230, 220, 0.16);
								}
							}
						}
					}
				}
				.BVIP2{
					color: rgb(119, 131, 174);
					.swiperCardItemCover{
						border: 1px solid rgb(119, 131, 174);
						background: linear-gradient(135.00deg, rgb(173, 194, 230),rgb(239, 243, 253) 41.184%,rgb(238, 242, 253) 44.313%,rgb(160, 186, 231) 100%);
					}
					.swiperCardItemContent{
						color: rgb(214, 223, 255);
						.swiperCardItemDescribeBg{
							.swiperCardItemDescribe{
								.swiperCardItemDescribeContent{
									border:1px solid rgba(222, 229, 243, 0.16);
								}
							}
						}
					}
				}
				.BVIP3{
					color: rgb(234, 128, 28);
					.swiperCardItemCover{
						border: 1px solid rgb(255, 194, 136);
						background: linear-gradient(-45.00deg, rgb(255, 226, 162) 3.817%,rgb(255, 249, 238) 57.773%,rgb(254, 248, 233) 64.454%,rgb(255, 222, 147) 100%);
					}
					.swiperCardItemContent{
						color: rgb(255, 164, 10);
						.swiperCardItemDescribeBg{
							.swiperCardItemDescribe{
								.swiperCardItemDescribeContent{
									border:1px solid rgba(241, 230, 220, 0.16);
								}
							}
						}
					}
				}
				.BVIP4{
					color: rgb(93, 150, 255);
					.swiperCardItemCover{
						border: 1px solid rgb(148, 197, 244);
						background: linear-gradient(-45.00deg, rgb(167, 226, 255) 0%,rgb(238, 249, 255) 57.773%,rgb(234, 247, 254) 64.454%,rgb(173, 222, 255) 100%);
					}
					.swiperCardItemContent{
						color: rgb(116, 201, 255);
						.swiperCardItemDescribeBg{
							.swiperCardItemDescribe{
								.swiperCardItemDescribeContent{
									border:1px solid rgba(241, 230, 220, 0.16);
								}
							}
						}
					}
				}
				// .bvip5{
				// 	color: rgb(255, 142, 43);
				// 	.swiperCardItemCover{
				// 		border: 1px solid rgb(228, 209, 182);
				// 		background: linear-gradient(-45.00deg, rgb(222, 186, 147) 0%,rgb(255, 241, 221) 53.135%,rgb(255, 245, 230) 62.964%,rgb(238, 205, 176) 100%);
				// 	}
				// 	.swiperCardItemContent{
				// 		color: rgb(255, 151, 60);
				// 	}
				// }
			}
		}

		.card-item {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			width: 320px;
			height: 160px;
			background-repeat: no-repeat;
			background-size: 100%;
			background-image: url('../../static/images/vip/zuanshi.png');

		}

		.card-left {
			width: 50%;
			display: flex;
			flex-direction: column;
			flex-wrap: nowrap;
		}

		.card-right {
			width: 50%;
			display: flex;
			align-items: center;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: flex-end;
		}

		.card-icon {
			width: 120px;
			height: 110px;
		}

		.left-top {
			width: 90px;
			height: 25px;
		}

		.lock {
			width: 90px;
			height: 25px;
			padding-top: 1px;
		}

		.left-bottom {
			width: 100%;
			height: 65%;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center;
			align-content: center;
		}

		.bottom-left {
			color: rgb(54, 84, 143);
			font-family: 钉钉进步体;
			font-size: 32px;
			font-weight: 400;
			line-height: 38px;
			letter-spacing: 0px;
			text-align: center;
			margin: 7%;
		}

		.bottom-right {
			color: rgb(54, 84, 143);
			font-family: 钉钉进步体;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
			box-sizing: border-box;
			border: 1px solid rgb(54, 84, 143);
			border-radius: 4px;
			padding: 3%;
			margin-top: 5px;
		}



		.map {
			width: 100%;
			flex:1;
			// height: 42%;
			background-image: url("../../static/images/vip/map.png");
			background-repeat: no-repeat;
			background-size: 100%;
		}

		.bottom {
			width: 100%;
			height:135px;
			position: relative;
			bottom: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #24262E;
			padding-bottom: 20px;
			padding-top: 20px;
			box-sizing: border-box;
		}

		.bottom-button {}

		.button {
			background-image: url("../../static/images/vip/button.png");
			background-repeat: no-repeat;
			background-size: 100%;
			box-sizing: border-box;
			border-radius: 10px;
			background: linear-gradient(90.00deg, rgb(249, 225, 177), rgb(227, 182, 113) 100%);
			width: 90%;
		}

		.bottom-top {
			font-family: 阿里巴巴普惠体;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
			width: 90%;
			color: #ffffff;
			margin-bottom: 2%;

			.agreement {
				color: #B1C5F0;
				display: inline-block;
			}
		}

		.level-qingtong {
			color: #FF973C;
		}

		.level-baiyin {
			color: #D6DFFF;
		}

		.level-huangjin {
			color: #FFA40A;
		}

		.level-baijin {
			color: #74C9FF;
		}

		.level-zuanshi {
			color: #C0B4FF;
		}

		.border-qingtong {
			border: 1px solid #FF973C;
		}

		.border-baiyin {
			border: 1px solid #D6DFFF;
		}

		.border-huangjin {
			border: 1px solid #FFA40A;
		}

		.border-baijin {
			border: 1px solid #74C9FF;
		}

		.border-zuanshi {
			border: 1px solid #C0B4FF;
		}



	}

	.box-shadow {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		z-index: 10000;
		display: flex;
		align-items: flex-end;
		justify-content: center;

		.box-shadow-content {
			flex: 1;
			padding: 20px 20px 30px 20px;
			box-sizing: border-box;
			width: 100%;
			background-color: #fff;
			border-radius: 20px 20px 0px 0px;

			.close-container {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.close-icon {
					width: 16px;
					height: 16px;
				}
			}

			.price-container {
				display: flex;
				align-items: baseline;
				justify-content: center;
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 36px;
				font-weight: 600;
				line-height: 42px;
				margin-bottom: 10px;

				.price-icon {
					font-size: 20px;
					font-weight: 500;
				}
			}

			.order-describe {
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 400;
				line-height: 16px;
				border-bottom: 1px solid #F4F6F8;
				padding: 20px 0;
				box-sizing: border-box;
				margin-bottom: 15px;

				.order-describe-title {
					color: rgb(152, 152, 152);
					font-size: 14px;
				}
			}

			.btn {
				width: 100%;
				height: 48px;
				border-radius: 10px;
				background: linear-gradient(90.00deg, rgb(249, 225, 177), rgb(227, 182, 113) 100%);
				color: rgb(77, 53, 18);
				font-family: HarmonyOS Sans;
				font-size: 16px;
				font-weight: 600;
				line-height: 48px;
				letter-spacing: 0px;
				text-align: center;
				margin-top: 35px;
			}

			.pay-status {
				width: 100%;
				.pay-status-item{
					padding: 15px 0;
					box-sizing: border-box;
					.txt {
						margin-left: 16px;
						color: rgb(33, 33, 33);
						font-family: HarmonyOS Sans;
						font-size: 18px;
						font-weight: 400;
						line-height: 21px;
						letter-spacing: 0px;
						text-align: left;
					}
				}
				
			}


		}

	}
</style>