<template>
	<view class="appPage">
		<scroll-view :scroll-y="true" style="height: 90vh;">
			<view class="info t_display">
				<image class="img92" :src="userInfo.avatar" mode="" style="border-radius: 50%;"></image>
				<view class="right">
					<view class="first">{{userInfo.nickname}}</view>
					<view class="second">已逾期 48 天</view>
				</view>
			</view>
			<scroll-view class="scroll-view_H" :scroll-x="true" @scroll="scroll">
				<view class="scroll-view-item_H " v-for="(item,index) in cardArr" :class="{'box':current==index}"
					@click="chooseCard(index)" :style="'border:'+(current==index?'':'6rpx solid #999BA1;')">
					<view class="first">{{item.name}}</view>
					<view id="price">
						<p class="price">￥<span class="number">{{item.now_amount}}</span></p>
					</view>
					<view class="bottom t_betweent">
						<view class="left">￥{{item.avg_month}}/月</view>
						<view class="saveMoney">立省￥{{item.preferential}}</view>
					</view>
				</view>
			</scroll-view>
			<view class="" style="padding: 32rpx;">
				<image src="../../static/images/vip/twoAuthority.png" style="width: 100%;" mode="widthFix"
					v-if="type==0">
				</image>
				<image src="../../static/images/vip/sixAuthority.png" style="width: 100%;" mode="widthFix"
					v-else-if="type==1"></image>
				<image src="../../static/images/vip/nightAuthority.png" style="width: 100%;" mode="widthFix"
					v-else-if="type==2"></image>
			</view>
			<view class="img124">

			</view>
		</scroll-view>
		<view class="bottomBtn">
			<view class="btn" @click="pay()">
				继续-总计￥{{cardArr[current].now_amount}}
			</view>
		</view>
		<uni-popup ref="payStatus" type="bottom" :safe-area="false">
			<view class="pay">
				<view class="title">
					<view></view>
					<view>选择支付方式</view>
					<image @click="close" class="img32" src="../../static/images/vip/rightClose.png" mode="" />
				</view>
				<view id="price">
					<p class="price">￥<span class="number">{{cardArr[current].now_amount}}</span></p>
				</view>
				<view class="tips">到期后自动续费，可随时取消</view>
				<view class="payCard t_betweent">
					<view class="t_display">
						<image class="img42" src="../../static/images/vip/vipLogo.png" mode=""></image>
						<view class="" style="margin-left: 24rpx;">
							Apple pay
						</view>
					</view>
					<image class="img32" src="../../static/images/pay/radio2.png" mode=""></image>
				</view>
				<cusButton :txt="'确定协议并支付'+cardArr[current].now_amount+'元'" :radio="140" @confirm="create"
					style="margin: 32rpx 0;"></cusButton>
				<view class="check">
					<radio :checked="checked" @click="checked = !checked" style="transform:scale(0.7)" />
					<span style="line-height: 45rpx;">支付即同意<text class="t_zt"
							@click="goNav('/pages/privacyAgreement/userAgreement')">《会员协议》</text>及<text class="t_zt"
							@click="goNav('/pages/privacyAgreement/privacyAgreement')">《自动续费》</text></span>
				</view>
				<view class="img42" />
			</view>
		</uni-popup>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	import PayStatus from "./components/payStatus.vue"
	export default {
		components: {
			PayStatus
		},
		data() {
			return {
				checked: false,
				userInfo: "",
				current: 0,
				borderFlag: false,
				type: "",
				cardArr: [],
			}
		},
		onLoad(options) {
			this.type = options.current
			this.userInfo = uni.getStorageSync('userInfo')
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			create() {

			},
			close() {
				this.$refs.payStatus.close()
			},
			pay(number) {
				this.$refs.payStatus.open()
			},
			async getData() {
				this.$http.get('/api/pay/vip-goods', {
					goods_type: this.type
				}).then(res => {
					this.cardArr = res.message
				})

			},
			chooseCard(ids) {
				this.current = ids
			},
			scroll() {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.check {
		font-size: 24rpx;
		font-family: Source Han Sans-Regular, Source Han Sans;
		font-weight: 400;
		color: #888888;
		line-height: 35rpx;
	}

	.pay {
		color: #000;
		padding: 32rpx 32rpx 20rpx 32rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;

		.payCard {
			margin-top: 24rpx;
			background: #F9F9F9;
			border-radius: 16rpx;
			padding: 32rpx;
		}

		.title {
			display: flex;
			justify-content: space-between;
			color: #000;
		}

		.tips {
			font-size: 26rpx;
			color: #979797;
		}

		#price {
			margin: 68rpx 0 40rpx 0;
			background: linear-gradient(to right, #4BC6ED, #BC93F2);
			font-style: normal;
			text-transform: none;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			text-align: center;

			.price .number {
				font-weight: 500;
				font-size: 100rpx;
			}
		}
	}

	.appPage {
		.bottomBtn {
			padding: 42rpx;
			border-top: 1px solid #999;
			display: flex;
			justify-content: center;
			background: #191C26;
			position: fixed;
			bottom: 42rpx;
			left: 0;

			.btn {
				width: 686rpx;
				height: 94rpx;
				line-height: 94rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 140rpx 140rpx 140rpx 140rpx;
				text-align: center;
				font-weight: 500;
				font-size: 32rpx;
			}
		}

		.scroll-view_H {
			background: #30333E;
			white-space: nowrap;
			width: 100%;
			height: 446rpx;
			box-sizing: border-box;
			padding: 32rpx 0;
		}

		.box {
			transform: translateY(-6rpx);

			&::before {
				/* 1 */
				display: block;
				content: '';
				border-radius: 26rpx;
				border: 6rpx solid transparent;
				background: linear-gradient(90deg, #8f41e9, #578aef) border-box;
				/* 2 */
				-webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
				/* 3 */
				-webkit-mask-composite: xor;
				/* 4 */
				mask-composite: exclude;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 97%;
			}

		}

		.scroll-view-item_H {
			display: inline-block;
			width: 525rpx;
			height: 372rpx;
			border-radius: 26rpx;
			padding: 32rpx;
			margin-left: 32rpx;
			margin-top: 10rpx;
			background: #23253C;
			// border: 6rpx solid #999BA1;
			cursor: pointer;
			position: relative;



			.bottom {
				.left {
					font-weight: 400;
					font-size: 32rpx;
				}

				.saveMoney {
					font-weight: 400;
					line-height: 39rpx;
					font-size: 26rpx;
					background: #000000;
					border-radius: 50rpx 50rpx 50rpx 50rpx;
					padding: 4rpx 18rpx;
				}
			}

			.first {
				font-weight: 500;
				font-size: 32rpx;
			}

			#price {
				margin: 30rpx 0 40rpx 0;
				background: linear-gradient(to right, #4BC6ED 0%, #BC93F2 45%);
				font-style: normal;
				text-transform: none;
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;

				.price .number {
					font-weight: 500;
					font-size: 100rpx;
				}
			}

		}


		.info {
			padding: 24rpx 32rpx;

			.right {
				margin-left: 25rpx;
				line-height: 92rpx;

				view:first-child {
					font-weight: 500;
					font-size: 32rpx;
					line-height: 46rpx;
				}

				view:nth-child(2) {
					font-weight: 400;
					font-size: 28rpx;
					line-height: 41rpx;
				}
			}
		}
	}
</style>