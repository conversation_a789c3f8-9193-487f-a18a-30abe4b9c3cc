<template>
	<view class="maskBody" v-if="isShow" @click.stop="isShow = false" :style="{ zIndex: zIndex }">
		<view class="">
			<view class="" style="float: left;"></view>
			<image src="/static/images/share/close.png" mode="" class="close" @click.stop="isShow = false"></image>
		</view>
		<!-- 引入活动卡片组件 -->
		<activity-card ref="activityCard" v-if="type == 'activity'" :cardData="cardData"
			@renderOver="share"></activity-card>
		<sh-card ref="activityCard" v-if="type == 'sh'" :cardData="cardData" @renderOver="share"></sh-card>
		<!-- 使用uqrcode组件 -->
		<uqrcode ref="uqrcode" canvas-id="qrcode" :value="href" :options="{ margin: 10 }"
			style="position: absolute; left: -9999px;"></uqrcode>

		<view class="title">分享至</view>
		<view class="buttonArea" @click.stop="noAct">
			<view class="item" v-if="shareWith.weixin" @click="shareFunc('friend')">
				<image src="@/static/images/share/wx.png" mode="widthFix"></image>
				<text>微信好友</text>
			</view>
			<view class="item" v-if="shareWith.weixin" @click="shareFunc('line')">
				<image src="@/static/images/share/pyq.png" mode="widthFix"></image>
				<text>微信朋友圈</text>
			</view>
			<view class="item" v-if="shareWith.qq" @click="shareFunc('qq')">
				<image src="@/static/images/share/qq.png" mode="widthFix"></image>
				<text>QQ</text>
			</view>
			<view class="item" @click="shareFunc('qqkj')">
				<image src="@/static/images/share/qqkj.png" mode="widthFix"></image>
				<text>QQ空间</text>
			</view>
			<view class="item" v-if="shareWith.withSys" @click="shareFunc('sys')">
				<image src="@/static/images/share/bctp.png" mode="widthFix"></image>
				<text>保存图片</text>
			</view>
		</view>
		<view class="" style="height: 150px;" v-if="type == 'sh'"></view>
	</view>
</template>

<script>
	let shareOption = {};
	// 导入活动卡片组件
	import ActivityCard from './activity-card.vue';
	import ShCard from './sh-card.vue';
	import bgImages from "./enum.js";
	// 导入QRCode库
	import QRCode from 'qrcode';
	// 导入uQRCode组件
	import uqrcode from '@/components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue';

	export default {
		components: {
			ShCard,
			ActivityCard,
			uqrcode
		},
		data() {
			return {
				isShow: false,
				href: "",
				shareWith: {
					sinaweibo: false,
					qq: false,
					weixin: false,
					withSys: false
				},
				// 卡片数据对象
				cardData: {
					cardId: '', // 卡片ID
					cardTitle: '', // 活动标题
					memberCount: '', // 成员数量
					qrCodeImage: '', // 二维码图片
				},
				cardBgImage: '', // 背景图
				way: "",
				bodyOverflow: '', // 保存body原始overflow值
			};
		},
		props: {
			type: {
				type: String,
				default: ""
			},
			zIndex: {
				type: Number,
				default: 1000
			}
		},
		created() {
			let that = this;
			// #ifdef APP-PLUS
			this.shareWith.withSys = true;
			uni.getProvider({
				service: 'share',
				success: function(res) {
					console.log(res);
					if (~res.provider.indexOf('qq')) that.shareWith.qq = true;
					if (~res.provider.indexOf('sinaweibo')) that.shareWith.sinaweibo = true;
					if (~res.provider.indexOf('weixin')) that.shareWith.weixin = true;
				}
			})
			// #endif
		},
		methods: {
			show(cardData) {
				// 随机选择背景图
				const bgIndex = Math.floor(Math.random() * 3) + 1;
				// this.cardBgImage = `/static/images/share/cardBg${bgIndex}.png`;
				this.cardBgImage = bgImages[`cardBgImage${bgIndex}`]
				this.cardData = cardData
				// 初始化分享选项
				shareOption = {
					title: this.cardData.title || '',
					summary: '快来参加活动吧！',
					href: '',
					imageUrl: '',
					miniProgram: {}
				};

				// 生成二维码
				this.href = "https://static.lluuxiu.com/v1/download.html?type=activity&uuid=" + cardData.uuid;

				// 禁止背景滚动
				this.preventBackgroundScroll(true);

				this.isShow = true;
			},
			// 防止背景滚动
			preventBackgroundScroll(isPrevent) {
				// #ifdef H5
				if (isPrevent) {
					// 保存当前body的overflow值
					this.bodyOverflow = document.body.style.overflow;
					// 禁止滚动
					document.body.style.overflow = 'hidden';
				} else {
					// 恢复滚动
					document.body.style.overflow = this.bodyOverflow;
				}
				// #endif

				// #ifdef APP-PLUS
				if (isPrevent) {
					// 禁止页面滚动
					plus.webview.currentWebview().setStyle({
						bounce: 'none'
					});
				} else {
					// 恢复页面滚动
					plus.webview.currentWebview().setStyle({
						bounce: 'vertical'
					});
				}
				// #endif
			},
			noAct(e) {
				// 阻止事件冒泡和默认行为
				e.stopPropagation();
				e.preventDefault();
			},
			close() {
				// 恢复背景滚动
				this.preventBackgroundScroll(false);
				this.isShow = false;
			},
			shareFail(res) {
				if (res.errCode == -8 || res.errCode == -3) {
					uni.showToast({
						icon: 'none',
						title: '未安裝客戶端'
					})
				} else {
					uni.showToast({
						icon: 'none',
						title: JSON.stringify(err)
					})
				}
			},
			shareFunc(way) {
				let that = this;
				this.way = way
				// 使用活动卡片组件的方法直接保存图片
				this.$refs.activityCard.cusRenderDom();
			},
			share(tempFilePath) {
				const way = this.way
				let that = this;
				// 使用活动卡片组件的方法生成图片
				console.log("分享图片已生成:", tempFilePath);
				shareOption.imageUrl = tempFilePath;

				if (way == 'friend') {
					uni.share({
						provider: "weixin",
						scene: "WXSceneSession",
						type: 2,
						imageUrl: shareOption.imageUrl,
						success: function(res) {
							console.log("success:" + JSON.stringify(res));
							that.close();
						},
						fail: function(err) {
							console.log("fail:" + JSON.stringify(err));
						}
					});
				} else if (way == 'line') {
					uni.share({
						provider: "weixin",
						scene: "WXSceneTimeline",
						type: 2,
						imageUrl: shareOption.imageUrl,
						success: function(res) {
							console.log("success:" + JSON.stringify(res));
							that.close();
						},
						fail: function(err) {
							console.log("fail:" + JSON.stringify(err));
						}
					});
				} else if (way == 'mp') {
					uni.share({
						provider: 'weixin',
						scene: 'WXSceneSession',
						title: shareOption.title,
						type: 2,
						imageUrl: shareOption.imageUrl,
						miniProgram: shareOption.miniProgram,
						success: () => {
							that.close();
						},
						fail: this.shareFail
					})
				} else if (way == 'qqkj') {
					uni.shareWithSystem({
						imageUrl: shareOption.imageUrl,
						success() {
							// 分享完成，请注意此时不一定是成功分享
							that.close();
						},
						fail() {
							// 分享失败
						}
					})
				} else if (way == 'qq') {
					uni.share({
						provider: 'qq',
						type: 2,
						imageUrl: shareOption.imageUrl,
						success: function(res) {
							console.log(res);
							that.close();
						},
						fail: function(err) {
							console.log(err);
							uni.shareWithSystem({
								imageUrl: shareOption.imageUrl,
								success() {
									// 分享完成，请注意此时不一定是成功分享
									that.close();
								},
								fail() {
									// 分享失败
								}
							})
						}
					});
				} else if (way == 'sys') {
					// 保存图片到相册
					uni.saveImageToPhotosAlbum({
						filePath: shareOption.imageUrl,
						success: function() {
							console.log('save success');
							uni.showToast({
								icon: 'none',
								title: '保存成功'
							})
						}
					});
				} else {
					uni.shareWithSystem({
						href: shareOption.href,
						type: 'text',
						summary: shareOption.title,
						imageUrl: shareOption.imageUrl,
						success: () => {
							that.close();
						},
						fail: this.shareFail
					})
				}



				this.close();
			},
			// 保存卡片为图片
			saveCardImage() {
				// 调用活动卡片组件的保存方法
				this.$refs.activityCard.saveCardImage();
			},
			generateQRCode(href) {
				// 使用箭头函数保持this上下文
				QRCode.toDataURL(href, {
						errorCorrectionLevel: 'H',
						type: 'image/png',
						margin: 1,
						color: {
							dark: '#000000', // 黑色二维码
							light: '#FFFFFF' // 白色背景
						},
						width: 200
					})
					.then(url => {
						console.log('生成二维码成功');
						this.cardData.qrCodeImage = url;
					})
					.catch(err => {
						console.error('生成二维码失败', err);
						uni.showToast({
							title: '生成二维码失败',
							icon: 'none'
						});
					});
			}
		}
	}
</script>

<style lang="scss">
	.close {
		width: 20px;
		height: 20px;
		float: right;
		margin-right: 26px;
		// position: absolute;
		// top: 116px;
		// right: 26px;
	}

	.title {
		color: #fff;
		text-align: center;
		margin-bottom: 30px;
		font-size: 18px;
	}

	.maskBody {
		color: #000;
		position: fixed;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 100vh;
		z-index: 1000;
		background-color: rgba(59, 59, 59, .8);
		pointer-events: auto;
		touch-action: none;
		/* 禁止所有触摸操作 */
		overflow: hidden;
		/* 禁止内部滚动 */

		.buttonArea {
			color: #fff;
			position: relative;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			flex-wrap: nowrap;
			margin: 0 2.5%;
			margin-bottom: 128rpx;
			width: 95%;
			border-radius: 15rpx;
			padding: 25rpx;
			box-sizing: border-box;
			touch-action: pan-x pan-y;
			/* 允许按钮区域的触摸操作 */

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;

				image {
					width: 64rpx;
					margin-bottom: 10rpx;
				}

				text {
					font-size: 12px;
				}
			}
		}
	}
</style>