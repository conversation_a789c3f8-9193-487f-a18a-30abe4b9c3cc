<template>
	<view class="appPage">
		<view class="title">
			邀请群成员
		</view>
		<view class="card">
			<scroll-view class="scroll-view_H" scroll-x="true" @scroll="scroll">
				<view id="demo1" class="scroll-view-item_H" v-for="item in checkUser" :key="item.ext.id">
					<image style="width: 102rpx;height: 102rpx;border-radius: 50%;" :src="item.avatar"
						mode="aspectFill">
					</image>
				</view>
				<view id="demo3" class="scroll-view-item_H" @click="goNav">
					<image style="width: 102rpx;height: 102rpx;" src="../../static/images/add.png" mode=""></image>
				</view>
			</scroll-view>
		</view>
		<view class="title">
			群组名字
		</view>
		<u-input v-model="groupName" placeholder="群名称" :type="'text'" :border="true"
			border-color="rgba(255,255,255,0.28)" />
		<view class="title">
			群组简介
		</view>
		<u-input v-model="groupIntro" placeholder="群介绍" type="textarea" height="332" :border="true"
			:custom-style="{color:'#fff'}" border-color="rgba(255,255,255,0.28)" style="margin-bottom: 147rpx;" />
		<cusButton txt="创建" @confirm="create"></cusButton>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				groupName: '',
				groupIntro: '',
				checkUser: []
			}
		},
		onShow() {
			uni.$on('infoArr', (data) => {
				this.checkUser = data
				console.log(this.checkUser, 'checkUser');
			})
		},
		onHide() {
			// uni.$off('infoArr')
		},
		methods: {
			goNav(url) {

				uni.navigateTo({
					url: `/pages/checkMember/checkMember?mode=createGroup&checkUserIds=${1}`
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			async create() {
				if (!this.groupName) {
					this.toast('请填写群名称');
					return
				}
				if (!this.groupIntro) {
					this.toast('请填写群介绍');
					return
				}
				// 创建群聊
				const accounts = this.checkUser.map(item => item.im_id)
				console.log(accounts, );
				const res = await this.$Yxim.team.createTeam({
					type: 'advanced',
					accounts, //群成员id
					intro: this.groupIntro, //群介绍
					name: this.groupName, //群名称
					ps: 'pleaseJoinTeam'
				})
				console.log(res, 'sssss');
				if (res) {
					uni.navigateBack({
						delta: 1
					})
				}

			},
			scroll: function(e) {

			},
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 0 32rpx;

		.scroll-view_H {
			white-space: nowrap;
			width: 100%;
			display: flex;
			align-items: center;
		}

		.scroll-view-item_H {
			display: inline-block;
			padding: 24rpx 8rpx;
		}

		.card {
			background: #22252F;
			border-radius: 14rpx;
			border: 1rpx solid rgba(255, 255, 255, 0.28);
		}

		.title {
			padding: 24rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>