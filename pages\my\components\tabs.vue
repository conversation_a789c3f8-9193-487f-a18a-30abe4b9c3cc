<template>
	<view class="content">
		<view class="tab" v-for="(item,index) in tabs" :key="index" :style="{width:'50vw'}" @click="setCurrent(index)">
			<view class="title" :style="{color:current==index?'#fff':'rgba(255,255,255,0.6)'}">
				{{item.name}}
			</view>
			<view class="line" v-if="current==index"></view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			tabs: Array,
			current: Number
		},
		data() {
			return {

			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			setCurrent(index) {
				this.$emit("setCurrent", index)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		width: 750rpx;
		display: flex;
		background-color: #191C26;

		.tab {
			padding-top: 70rpx;
			padding-bottom: 20rpx;
			text-align: center;
		}

		.title {
			font-size: 26rpx;
			font-weight: 400;
		}

		.line {
			margin-top: 14rpx;
			width: 325rpx;
			height: 2rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		}
	}
</style>