<template>
	<view class="appPage">
		<view class="item" @click="goNav('/pages/setting/setting')">
			<view class="txt">
				<view class="topTxt">账户与安全</view>
				<view class="bottomTxt">隐私设置 账号设置</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
		<view class="item">
			<view class="txt">
				<view class="topTxt">关于我们</view>
				<view class="bottomTxt">当前版本{{versionCode}}</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
		<!-- <view class="item">
			<view class="txt">
				<view class="topTxt">清除缓存</view>
				<view class="bottomTxt">可释放1.43M</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view> -->
		<view class="item" @click="goNav('/pages/feedback/feedback')">
			<view class="txt">
				<view class="topTxt">意见反馈</view>
				<view class="bottomTxt">请提交您的意见</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
		<view class="item" @click="goNav('/pages/privacyAgreement/privacyAgreement')">
			<view class="txt">
				<view class="topTxt">隐私政策</view>
				<view class="bottomTxt">隐私 协议说明</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
		<view class="item" @click="goNav('/pages/privacyAgreement/userAgreement')">
			<view class="txt">
				<view class="topTxt">用户协议</view>
				<view class="bottomTxt">用户 协议说明</view>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
		<view class="baStype">
			<div>客服邮箱：<EMAIL></div>
			ICP备案/许可证号:黑ICP备2023013144号-1A
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				versionCode: ""
			}
		},
		onLoad() {
			this.versionCode = uni.getStorageSync('versionCode').split('').join('.')
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			toLogut() {
				uni.redirectTo({
					url: "/pages/login/login"
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
	page {
		background-color: #191C26;
	}

	.baStype {
		margin-top: 500rpx;
		text-align: center;
		color: #666;
	}

	.title {
		padding: 0 32rpx;
		height: 94rpx;
		font-size: 32rpx;
		line-height: 94rpx;
		background: #201F1F;
	}

	.item {
		padding: 41rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-family: Source Han Sans-Medium, Source Han Sans;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 41rpx;

		.txt {
			.topTxt {
				font-size: 26rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}

			.bottomTxt {
				font-size: 24rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: rgba(255, 255, 255, 0.62);
			}
		}
	}
</style>