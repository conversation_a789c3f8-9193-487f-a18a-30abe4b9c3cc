<template>
	<view class="appPage" style="background-color: #191c26;">
		<scroll-view :scroll-with-animation="true" scroll-y="true" style="height: 100vh;" class="scroll-view_back"
			@scrolltolower="scrollBnt">
			<!-- <view class="item_group" @click="goGroup">
				<image style="width: 108rpx;height: 108rpx;" src="../../static/images/group.png" mode=""></image>
				<view class="text">
					群聊
				</view>
			</view> -->
			<view style="border: 1rpx solid #333;"></view>
			<block v-for="(item,index) in itemArr" :key="index">
				<view class="item_group" style="justify-content: space-between;" @click="goSend(item)">
					<view class="t_display">
						<image style="width: 108rpx;height: 108rpx;border-radius: 50%;" :src="item.avatar"
							mode="aspectFill">
						</image>
						<view class="info">
							<view class="name">
								{{item.remark_name||item.nickname}}
							</view>
							<!-- 	<view class="note">
								{{item.remark_name}}
							</view> -->
						</view>
					</view>
					<view style="width: 100rpx;text-align: right;" @click.stop="openAction(item)">
						<image class="right" src="../../static/images/moreIcon.png" mode=""></image>
					</view>
				</view>
				<view style="border: 1rpx solid #333;"></view>
			</block>
		</scroll-view>
		<FriendToolPopup ref="friendToolPopup" @refresh="refresh" />
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import FriendToolPopup from './friendToolPopup.vue'
	export default {
		components: {
			FriendToolPopup
		},
		data() {
			return {
				itemArr: [],
				page: 1,
				getFlag: true,
				title: '标题',
				list: [{
						name: '选项一',
						subname: "选项一描述",
						color: '#ffaa7f',
						fontSize: '20'
					},
					{
						name: '选项二禁用',
						disabled: true
					},
					{
						name: '开启load加载', //开启后文字不显示
						loading: true
					}
				],
				show: false
			}
		},
		computed: {},
		created() {
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			refresh() {
				this.page = 1
				this.itemArr = []
				setTimeout(() => {
					this.getData()
				}, 1000)
			},
			goGroup() {
				console.log(123);
				this.navigateTo({
					url: '/pages/chat-list/group'
				})
			},
			openAction(item) {
				this.$refs.friendToolPopup.open(item)
			},
			goSend(item) {
				this.$emit('send', item)
			},
			scrollBnt(val) {
				if (val && this.getFlag) {
					++this.page;
					this.getData()
				}
			},
			async getData() {
				await this.$http.get('/api/user/friend/get', {
					page: this.page
				}).then(res => {
					this.getFlag = !!res.message.list
					if (res.message.list) {
						this.itemArr = [...this.itemArr, ...res.message.list]
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		height: 100vh;
		background: #191C26;

		.item_group {
			display: flex;
			padding: 24rpx 32rpx;
			align-items: center;


			.right {
				width: 12rpx;
				height: 38rpx;
			}

			.info {
				margin-left: 32rpx;

				.note {
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.64);
				}

				.name {
					font-size: 34rpx;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #FFFFFF;
				}
			}


			.text {
				margin-left: 32rpx;
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}
	}
</style>