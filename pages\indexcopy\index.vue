<template>
	<view class="content">
		<view class="text-area">
			<text class="title">地图页</text>
			<view style="width: 100rpx;height: 100rpx;position: fixed;right: 100rpx;top: 100rpx;background-color: red;"
				@click="goNav('/pages/mapSetting/mapSetting')">
				设置</view>
		</view>

		<drag-button :isDock="true" :existTabBar="true" @btnClick="btnClick">
			<image style="width: 124rpx;height: 100rpx;" src="../../static/images/message.png" mode=""></image>
		</drag-button>
		<Tabbar :page="page"></Tabbar>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import dragButton from "@/components/drag-button/drag-button.vue";

	export default {
		components: {
			dragButton
		},
		data() {
			return {
				title: 'Hello',
				page: "/pages/index/index"
			}
		},
		onLoad() {

		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(url) {
				this.navigateTo({
					url
				})
			},
			btnClick() {
				this.navigateTo({
					url: '/pages/chat-list/chat-list'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>