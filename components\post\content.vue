<template>
  <view class="content">
    <view
      class="topic-con"
      v-for="(item, index) in list"
      :key="index"
      :class="{ topicBg: item.up == 1 && showTop }"
    >
      <!-- 	<view class="line" v-if="index != 0"></view>
			<view class="t_display" v-if="item.up == 1 && showTop">
				<image class="img32" :src="
            '../../static/images/vip/vipLogo' +
            item.user_info.vip_level +
            '.png'
          " mode="" />
				<view class="topText"> 置顶 </view>
			</view> -->

      <!-- 用户头像 -->
      <!-- <view class="con-header">
        <view
          class="avatar"
          @click="
            goNavT(
              '/pages/otherPage/otherPage?uuid=' + item.user_info.uuid,
              item.user_info.uuid
            )
          "
        >
          <image
            class="avatarimg"
            :src="item.user_info ? item.user_info.avatar : ''"
            mode="aspectFill"
          >
          </image>
        </view> 
        <view class="message">
          <view class="nickname t_display" v-if="item.user_info">
            {{ item.user_info.nickname }}
            <image
              class="img32"
              :src="
                '../../static/images/vip/vipLogo' +
                item.user_info.vip_level +
                '.png'
              "
              v-if="showVipLogo"
              mode=""
              style="margin-left: 24rpx"
            />
            <view class="auth" v-if="false" style="margin-left: 20rpx">
              <image
                style="width: 32rpx"
                src="@/static/images/authentication.png"
                mode="widthFix"
              >
              </image>
            </view>
            <view class="auth-border" v-if="item.user_info.relation == 2">
              朋友
            </view>
          </view>
          <view class="label" :style="'width:400px;padding-right:20px'">
            <view
              class=""
              style="
                max-width: 200rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ item.timeline }}
            </view>

            <view
              class="address"
              v-if="item.location && !moreFlag"
              style="width: 350rpx; text-align: right"
            >
              来自于{{ item.location }}
            </view>
            <view class="address" v-if="item.has_edit == 1 && !moreFlag">
              已编辑
            </view>
          </view> 
		</view>
			<view class="operate" v-if="moreFlag && item.user_info.relation == 4">
				<image class="operate-img" @click="goMore(item, index)" src="@/static/images/moreIcon.png"
					mode="widthFix">
				</image>
				<view class="status" style="" v-if="showAuthority">
					{{
              ["", "好友可见", "自己可见", "部分可见", "不给谁看", "公开"][
                item.role
              ]
            }}
				</view>
			</view>
			<view class="follow" v-if="followFlag && ![3, 4].includes(item.user_info.relation)" @click="follow(item)">
				{{ ["关注", "已关注", "互相关注"][item.user_info.relation] }}
			</view>
		</view>-->
      <view class="con-body">
        <view class="itempic" v-if="item.images">
          <!-- 计数器 -->
          <view
            class="image-counter"
            v-if="Array.isArray(item.images) && item.images.length > 1"
          >
            {{ currentSwiperIndex + 1 }}/{{
              Array.isArray(item.images) ? item.images.length : 1
            }}
          </view>
          <!-- height: swiperHeights[index]
		    ? swiperHeights[index] + 'rpx'
		    : '450rpx', -->
          <swiper
            class="swiper-container"
            :indicator-dots="false"
            :autoplay="false"
            :circular="true"
            :duration="500"
            :style="{
              height: `${imgHeight}rpx`,
            }"
            @change="
              (e) => {
                currentSwiperIndex = e.detail.current;
              }
            "
          >
            <swiper-item
              v-for="(img, imgIndex) in Array.isArray(item.images)
                ? item.images
                : [item.images]"
              :key="imgIndex"
              class="swiper-item"
            >
              <!-- image的方法@load：获取图片高度的 -->
              <!-- @load="imgIndex === 0 && onImageLoad($event, index, img)" -->
              <image
                class="swiper-image"
                :src="img"
                mode="aspectFill"
                :style="{ height: `${imgHeight}rpx` }"
                @click="
                  previewFlag
                    ? goNav(
                        '/pages/mainText/mainText?momentId=' + item.moment_id
                      )
                    : preview(
                        Array.isArray(item.images)
                          ? item.images
                          : [item.images],
                        imgIndex
                      )
                "
              />
            </swiper-item>
          </swiper>
          <!-- 自定义指示点 -->
          <view
            class="custom-indicators"
            v-if="Array.isArray(item.images) && item.images.length > 1"
          >
            <view
              v-for="(dot, index) in Array.isArray(item.images)
                ? item.images.length
                : 1"
              :key="index"
              :class="[
                'indicator-dot',
                { active: index === currentSwiperIndex },
              ]"
            ></view>
          </view>
        </view>
        <view class="con">
          {{ item.title + "" || "标题" }}
        </view>
        <view class="item-content">
          {{ item.content + "" || "内容" }}
        </view>
        <view class="item-footer">
          {{ item.timeline
          }}<span style="margin-left: 20rpx">{{ item.location }}</span>
        </view>
        <!-- @click="$common.openMap(item.coordinate,item.location)" -->
        <!-- <view
          class="addresspic"
          @click="
            goAddress({ coordinate: item.coordinate, location: item.location })
          "
          v-if="item.coordinate && item.location"
        >
          <image
            class="map-position"
            src="@/static/images/locationFollow.png"
            mode=""
          >
          </image>
        </view> -->
      </view>
      <!-- //动态里的relation字段关系是1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
      <!-- <view
        class="t_display"
        style="justify-content: space-between"
        v-if="footerFlag"
      >
        <image
          src="../../static/images/disSee.png"
          class="img24"
          mode=""
          v-if="flag"
          @click="setEye"
        >
        </image>
        <view
          class="t_display"
          v-else-if="!flag && item.user_info.relation == 4"
        >
          <image
            src="../../static/images/eye.png"
            class="img24"
            mode=""
            @click="setEye"
          ></image>
          <view class="seeSty"> {{ item.view_total }}次浏览 </view>
        </view>
        <view class="" v-else></view>
        <view class="con-footer">
          <view
            class="t_center"
            @click="setLike(item.moment_id, item.is_like, index)"
          >
            <image
              src="@/static/images/like.png"
              mode="widthFix"
              v-if="!item.is_like"
            ></image>
            <image
              src="@/static/images/like2.png"
              mode="widthFix"
              v-else
            ></image>
            <view class="">
              {{ item.like }}
            </view>
          </view>
          <view
            class="t_center"
            @click="
              goNav('/pages/mainText/mainText?momentId=' + item.moment_id)
            "
          >
            <image src="@/static/images/pingluna.png" mode="widthFix"></image>
            <view class="">
              {{ item.comment_total }}
            </view>
          </view>

          <view class="t_center" @click="share(item)">
            <image src="@/static/images/share.png" mode="widthFix"></image>
            <view class="">
              {{ item.share_total }}
            </view>
          </view>
        </view>
      </view> -->
    </view>
  </view>
</template>

<script>
export default {
  props: {
    imgHeight: {
      type: Number,
      default: 450,
    },
    uuid: {
      type: String,
      default: "",
    },
    goFLag: {
      type: Boolean,
      default: false,
    },
    showAuthority: {
      type: Boolean,
      default: true,
    },
    showTop: {
      type: Boolean,
      default: true,
    },
    showVipLogo: {
      type: Boolean,
      default: true,
    },
    moreFlag: {
      type: Boolean,
      default: true,
    },
    footerFlag: {
      type: Boolean,
      default: true,
    },
    followFlag: {
      type: Boolean,
      default: false,
    },
    previewFlag: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: [],
    },
    timeFormat: {
      type: String,
      default: "yyyy/M/D",
    },
  },
  data() {
    return {
      likeFlag: false,
      flag: false,
      currentSwiperIndex: 0,
      swiperHeights: [],
    };
  },
  methods: {
    goAddress(item) {
      this.$emit("goAddress", item);
    },
    follow(item) {
      this.$emit("follow", {
        ...item,
      });
    },
    preview(urls, current) {
      uni.previewImage({
        current,
        urls,
        // longPressActions: {
        // 	itemList: ['发送给朋友', '保存图片', '收藏'],
        // 	success: function(data) {
        // 		console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
        // 	},
        // 	fail: function(err) {}
        // }
      });
    },
    setLike(momentId, isLike, index) {
      this.$u.debounce(() => {
        this.$emit("setLike", {
          index,
          momentId,
          isLike,
        });
      }, 200);
    },
    share(item) {
      this.$emit("share", item);
    },
    goNav(url) {
      this.navigateTo({
        url,
      });
    },
    goNavT(url, uuid = "") {
      if (this.goFLag || uuid == this.uuid) return;
      this.navigateTo({
        url,
      });
    },
    setEye() {
      this.flag = !this.flag;
    },
    goMore(item, index) {
      this.$emit("more", {
        ...item,
        index,
      });
    },
    onImageLoad(event, index, img) {
      try {
        // 获取图片URL
        const src = img;
        if (!src) {
          console.log("图片src为空，使用默认高度");
          this.$set(this.swiperHeights, index, 450);
          return;
        }

        // 使用uni.getImageInfo获取图片真实尺寸
        uni.getImageInfo({
          src: src,
          success: (res) => {
            // 获取屏幕宽度
            const screenWidth = uni.getSystemInfoSync().windowWidth;
            // 转换为rpx单位的比例
            const rpxRatio = 750 / screenWidth;

            // 获取图片宽高
            const imgWidth = res.width;
            const imgHeight = res.height;

            // 计算宽高比
            const ratio = imgHeight / imgWidth;

            // 计算在当前屏幕宽度下的高度（转换为rpx单位）
            // 减去左右padding的64rpx
            const imageWidth = screenWidth - 64 / rpxRatio;
            let height = imageWidth * ratio * rpxRatio;

            // 限制最大高度为1000rpx
            if (height > 1000) {
              height = 1000;
            }

            console.log(
              `图片尺寸: ${imgWidth}x${imgHeight}, 计算高度: ${height}rpx`
            );

            // 只更新当前轮播图的所有图片使用相同高度
            this.$set(this.swiperHeights, index, Math.round(height));
          },
          fail: (err) => {
            console.error("获取图片信息失败", err);
            // 出错时使用默认高度
            this.$set(this.swiperHeights, index, 450);
          },
        });
      } catch (error) {
        console.error("计算图片高度出错", error);
        // 出错时使用默认高度
        this.$set(this.swiperHeights, index, 450);
      }
    },
  },
  created() {
    // 初始化每个轮播图的默认高度
    if (this.list && this.list.length > 0) {
      this.list.forEach((_, index) => {
        this.$set(this.swiperHeights, index, 450);
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.item-content {
  font-weight: 500;
  font-size: 27rpx;
  color: #33353b;
  line-height: 40rpx;
  margin-top: 15rpx;
}
.item-footer {
  font-weight: 500;
  font-size: 21rpx;
  color: rgba(51, 53, 59, 0.4);
  line-height: 31rpx;
}
.status {
  margin-top: 10rpx;
  width: 100rpx;
  font-size: 24rpx;
}

.topText {
  font-weight: 500;
  font-size: 24rpx;
  line-height: 34rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: linear-gradient(to right, #4bc6ed 50%, #bc93f2 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 30rpx 0 30rpx 12rpx;
}

.topBg {
  width: 750rpx;
  height: 100rpx;
}

.line {
  width: 100%;
  height: 12rpx;
  background: #323232;
  margin-bottom: 14rpx;
}

.follow {
  width: 144rpx;
  height: 152rpx;
  background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
  border-radius: 134rpx 134rpx 134rpx 134rpx;
  text-align: center;
  line-height: 152rpx;
  font-size: 24rpx;
}

.seeSty {
  margin-left: 10rpx;
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

// .swiperC {
// 	height: 100vh;
// }

.topic-con {
  background: #fafafa;
  margin-bottom: 8rpx;
  width: 100%;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
}

.topicBg {
  background: url("../../static/images/my/postBg.png");
  background-size: 100%;
  background-repeat: no-repeat;
}

.topic-con > view {
  padding-left: 32rpx;
  padding-right: 32rpx;
  box-sizing: border-box;
}

.con-header {
  width: 100%;
  height: 96rpx;
  display: flex;

  border-bottom: 1rpx solid #191c26;
}

.avatar {
  width: 75rpx;
  height: 75rpx;
  margin-top: 5rpx;
}

.avatarimg {
  width: 75rpx;
  height: 75rpx;
  border-radius: 50%;
}

.message {
  width: 500rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 12rpx;
  padding-left: 6rpx;
}

.nickname {
  font-size: 34rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
  color: #000;
}

.auth {
  transform: translateY(5rpx);
}

.auth-border {
  padding: 5px 8px;
  cursor: pointer;
  position: relative;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  // padding: 2rpx 12rpx;
  &::before {
    /* 1 */
    display: block;
    content: "";
    border-radius: 6px;
    border: 1px solid transparent;
    background: linear-gradient(90deg, #4bc6ed, #bc93f2) border-box;
    /* 2 */
    -webkit-mask: linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    /* 3 */
    -webkit-mask-composite: xor;
    /* 4 */
    mask-composite: exclude;
    position: absolute;
    width: 72rpx;
    height: 32rpx;
  }
}

.label {
  display: flex;
  font-size: 28rpx;
  color: #000;
  line-height: 40rpx;

  margin-top: 5rpx;
}

.address {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 16rpx;
  font-size: 26rpx;
}

.operate {
  width: 140rpx;
  height: 100%;
  text-align: right;
  transform: translateY(-3rpx);
  display: flex;
  flex-direction: column;
  align-items: end;
}

.operate-img {
  width: 14rpx;

  // margin-top: 23rpx;
}

.con-body {
  /* width: 100%; */
  //   padding-top: 20rpx;
  padding-bottom: 10rpx;
  font-size: 24rpx;
  color: #000;
  line-height: 34rpx;
  position: relative;
  z-index: 1;
}

.con {
  font-family: PingFang SC, PingFang SC;
  font-weight: 800;
  font-size: 34rpx;
  color: #33353b;
  line-height: 48rpx;
  word-wrap: break-word;
  margin-bottom: 16rpx;
}

.item-content {
  font-weight: 500;
  font-size: 27rpx;
  color: #33353b;
  line-height: 40rpx;
}
.itempic {
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
  margin-left: -32rpx;
  margin-right: -32rpx;
  width: calc(100% + 64rpx);
}

.itempic image {
  border-radius: 0;
}

.addresspic {
  width: 100%;
  height: 76rpx;
  margin-top: 8rpx;
  border-radius: 14rpx;
  background-image: url("../../static/images/maps.png");
  background-size: cover;
  text-align: center;
  position: relative;
  z-index: 1;
}

.map-position {
  width: 42rpx;
  height: 42rpx;
  margin-top: 16rpx;
}

.con-footer {
  height: 128rpx;
  padding-top: 30rpx;
  display: flex;
  justify-content: flex-end;
  position: relative;
  z-index: 1;
}

.con-footer > view {
  width: 38rpx;
  margin-left: 52rpx;
  font-size: 24rpx;
}

.con-footer > view > image {
  width: 38rpx;
}

.swiper-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  background-color: #f0f0f0;
}

.swiper-item {
  width: 100%;
  height: auto;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0;
}

.content {
  width: 100%;
  overflow: hidden;
}

.t_display {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.custom-indicators {
  position: relative;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
}

.indicator-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #cccccc;
  margin: 0 8rpx;
  transition: all 0.3s;
}

.indicator-dot.active {
  width: 24rpx;
  border-radius: 6rpx;
  background-color: #000;
}

.image-counter {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
  z-index: 10;
}
</style>
