/* 图标 */
.iconfont{
	font-family:iconfont;
}
.icon-sm {
	height: 32rpx;
	width: 32rpx;
}
.icon-base {
	height: 40rpx;
	width: 40rpx;
}
.close-btn {
	height: 18px;
	width: 18px;
}
.text{
	font-size:28rpx; 
	line-height:1.8; 
	color: #333333;
}
/* #ifndef APP-NVUE */
/* 强制换行 */
.word-wrap {
	word-wrap: break-word;
	word-break: break-all;
	white-space: pre-line;
}
/* #endif */
/* #ifndef APP-PLUS-NVUE */
.border-box {box-sizing: border-box;}
/* #endif */
/* 宽度 */
.w-100{ width: 750rpx; }
.w_100{ width: 100%; }
.h_100{ height: 100%; }
.h-100{ height: 100vh; }
.w-678{ width: 678rpx; }
/* flex 布局 */
.flex{
	/* #ifndef APP-PLUS-NVUE */
	display:flex;
	/* #endif */
	flex-direction:row;
}
.flex-column{ flex-direction:column!important; }
.flex-wrap{ flex-wrap:wrap;}
.flex-nowrap{ flex-wrap:nowrap;}
.justify-start{justify-content:flex-start!important;}
.justify-end{justify-content:flex-end!important;}
.justify-between{justify-content:space-between!important;}
.justify-center{justify-content:center!important;}
.justify-around{justify-content: space-around!important;}
.align-center{ align-items: center!important; }
.align-stretch{ align-items: stretch!important; }
.align-start{ align-items: flex-start!important; }
.align-end{ align-items: flex-end!important; }
.flex-1{ flex: 1!important; }
.flex-2{ flex: 2; }
.flex-3{ flex: 3; }
.flex-4{ flex: 4; }
.flex-5{ flex: 5; }
.flex-6{ flex: 6; }
/* #ifndef APP-PLUS-NVUE */
.flex-shrink{ flex-shrink: 0; }
/* #endif */
/*  -- 内外边距 -- */
.m-0 { margin: 0; }
/* #ifndef APP-PLUS-NVUE */
.m-auto{ margin: auto; }
/* #endif */
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }
.m-5 { margin: 50rpx; }
.mt-0 { margin-top: 0; }
/* #ifndef APP-PLUS-NVUE */
.mt-auto { margin-top: auto; }
/* #endif */
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }
.mt-5 { margin-top: 50rpx; }
.mt-8 { margin-top: 80rpx; }
.mb-0 { margin-bottom: 0; }
/* #ifndef APP-PLUS-NVUE */
.mb-auto { margin-bottom: auto; }
/* #endif */
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }
.mb-32 { margin-bottom: 32rpx; }
.mb-5 { margin-bottom: 50rpx; }
.ml-0 { margin-left: 0; }
/* #ifndef APP-PLUS-NVUE */
.ml-auto { margin-left: auto; }
/* #endif */
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }
.ml-32 { margin-left: 32rpx; }
.ml-4 { margin-left: 40rpx; }
.ml-5 { margin-left: 50rpx; }
.mr-0 { margin-right: 0; }
/* #ifndef APP-PLUS-NVUE */
.mr-auto { margin-right: auto; }
/* #endif */
.mr {margin-right: 4rpx;}
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }
.mr-32 { margin-right: 32rpx; }
.mr-4 { margin-right: 40rpx; }
.mr-5 { margin-right: 50rpx; }
.my-0 { margin-top: 0; margin-bottom: 0; }
/* #ifndef APP-PLUS-NVUE */
.my-auto { margin-top: auto; margin-bottom: auto; }
/* #endif */
.my-1 { margin-top: 10rpx; margin-bottom: 10rpx; }
.my-2 { margin-top: 20rpx; margin-bottom: 20rpx; }
.my-3 { margin-top: 30rpx; margin-bottom: 30rpx; }
.my-4 { margin-top: 40rpx; margin-bottom: 40rpx; }
.my-5 { margin-top: 50rpx; margin-bottom: 50rpx; }
.mx-0 { margin-left: 0; margin-right: 0; }
/* #ifndef APP-PLUS-NVUE */
.mx-auto { margin-left: auto; margin-right: auto; }
/* #endif */
.mx-1 { margin-left: 10rpx; margin-right: 10rpx;}
.mx-2 { margin-left: 20rpx; margin-right: 20rpx;}
.mx-3 { margin-left: 30rpx; margin-right: 30rpx;}
.mx-4 { margin-left: 40rpx; margin-right: 40rpx;}
.mx-5 { margin-left: 50rpx; margin-right: 50rpx;}
.mx-36 { margin-left: 36rpx; margin-right: 36rpx; }

.p-0 { padding: 0; }
.p { padding: 5rpx; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }
.p-5 { padding: 50rpx; }
.pt-0 { padding-top: 0; }
.pt { padding-top: 4rpx; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-32 { padding-top: 32rpx; }
.pt-4 { padding-top: 40rpx; }
.pt-5 { padding-top: 50rpx; }
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb { padding-bottom: 5rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-32 { padding-bottom: 32rpx; }
.pb-4 { padding-bottom: 40rpx; }
.pb-5 { padding-bottom: 50rpx; }
.pl-0 { padding-left: 0; }
.pl { padding-left: 5rpx; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }
.pl-4 { padding-left: 40rpx; }
.pl-5 { padding-left: 50rpx; }
.pl-32 { padding-left: 32rpx; }
.pr-0 { padding-right: 0; }
.pr { padding-right: 5rpx; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }
.pr-4 { padding-right: 40rpx; }
.pr-5 { padding-right: 50rpx; }
.pr-32 { padding-right: 32rpx; }
.py-0 { padding-top: 0; padding-bottom: 0; }
.py { padding-top: 5rpx; padding-bottom: 5rpx; }
.py-1 { padding-top: 10rpx; padding-bottom: 10rpx; }
.py-2 { padding-top: 20rpx; padding-bottom: 20rpx; }
.py-3 { padding-top: 30rpx; padding-bottom: 30rpx; }
.py-32 { padding-top: 32rpx; padding-bottom: 32rpx; }
.py-4 { padding-top: 40rpx; padding-bottom: 40rpx; }
.py-5 { padding-top: 50rpx; padding-bottom: 50rpx; }
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: 10rpx; padding-right: 10rpx;}
.px { padding-left: 5rpx; padding-right: 5rpx;}
.px-2 { padding-left: 20rpx; padding-right: 20rpx;}
.px-3 { padding-left: 30rpx; padding-right: 30rpx;}
.px-36 {padding-left: 36rpx; padding-right: 36rpx;}
.px-32 {padding-left: 32rpx; padding-right: 32rpx;}
.px-4 { padding-left: 40rpx; padding-right: 40rpx;}
.px-5 { padding-left: 50rpx; padding-right: 50rpx;}
.pt-12 { padding-top: 120rpx; }
/* 文字大小 */
.font-small { font-size: 20rpx;}
.font-sm { font-size: 24rpx;}
.font-sm-l { font-size: 26rpx;}
.font-mini{font-size: 22rpx;}
.font-base { font-size: 28rpx;}
.font { font-size: 30rpx;}
.font-md { font-size: 32rpx;}
.font-md-g { font-size: 34rpx;}
.font-md-l { font-size: 36rpx;}
.font-lg { font-size: 40rpx;}
.font-lg-l { font-size: 48rpx;}
/* 文字划线 */
.text-through{text-decoration:line-through;}
/* 文字对齐 */
.text-left { text-align: left;}
.text-right { text-align: right;}
.text-center { text-align: center;}
/* 文字换行溢出处理 */
.text-ellipsis {
	/* #ifndef APP-PLUS-NVUE */
	overflow: hidden;text-overflow: ellipsis;white-space: nowrap;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	lines: 1;
	/* #endif */
}
/* 文字粗细和斜体 */
.font-weight-light {font-weight: 300;}      /*细*/
.font-weight-normal { font-weight: 400;}    /*正常*/
.font-weight-bold { font-weight: 700;}      /*粗*/
.font-weight-bolder { font-weight: bold;} /*更粗*/
/* 文字颜色 */
.text-white {color: #ffffff;}
.text-primary {color: #007bff;}
.text-second {color: #666;}
.text-fourth {color: #999}
.text-success {color: #32C29B;}
.text-success-op {color: rgba(50, 194, 155, 0.1);}
.text-info { color: #17a2b8;}
.text-grey { color: #aeaeae;}
.text-grey-d { color: #666;}
.text-print { color: #535353; }
.text-hover-info {color: #0f6674;}
.text-warning {color: #ffc107;}
.text-hover-warning { color: #ba8b00;}
.text-danger { color: #F65B57;}
.text-err {color: rgba(246, 91, 87, 1);}
.text-hover-danger { color: #a71d2a;}
.text-light { color: #f8f9fa;}
.text-dark { color: #343a40;}
.text-golden {color: #FFC273;}
.text-p-admin {color: rgba(107, 90, 247, 1);}
.text-admin {color: #333;}
.text-hot { color: #FF6B1E; font-family: YouSheBiaoTiHei;}
/* 背景颜色 */
.bg-l-admin {background:linear-gradient(to right, #8757fc, #6d59f9);}
.bg-admin {background: rgba(107, 90, 247, 1)}
.bg-admin-secoed {background-color: rgba(107, 90, 247, 0.2);}
.bg-admin-third {background-color: rgba(107, 90, 247, 0.1);}
.bg-admin-fourth {background-color: rgb(241, 236, 254);}
.bg-light-f {background-color: rgba(153, 153, 153, 0.1);}
.bg-primary { background-color: #007bff;}
.bg-golden {background-color:rgb(79, 192, 141);}
.bg-btn-main {background-color: RGBA(45, 80, 255, 1);}
.bg-secondary { background-color: #6c757d;}
.bg-page {background-color: #f5f5f5;}
.bg-page-o {background-color: rgba(243, 243, 243, 0.1);}
.bg-light { background-color: rgba(153, 153, 153, 0.1); }
.bg-success { background-color: #32C29B;}
.bg-success-op { background-color: rgba(50, 194, 155, 0.1);}
.bg-info { background-color: #17a2b8;}
.bg-warning { background-color: #ffc107;}
.bg-hover-warning { background-color: #d39e00;}
.bg-opacity {background-color: rgba(0, 0, 0, 0);}
.bg-danger { background-color: #dc3545;}
.bg-err { background-color: rgba(246, 91, 87, 1); }
.bg-err-op { background-color: rgba(246, 91, 87, 0.1); }
.bg-dark { background-color: #343a40;}
.bg-white { background-color: #ffffff;}
.bg-transparent { background-color: transparent;}
.bg-grey {background-color: #eee;}
.bg-light-l {background-color: rgba(231, 231, 231);}
.bg-hover {opacity: 0.6;}
.bg-vip{background: linear-gradient(-90deg, #C09F69, #E9D5AC );}
.hover-style {transform: translateX(3rpx) translateY(3rpx);}
/* 边框 */
.border { border-width: 1rpx;border-style: solid;border-color: #999999;}
.border-top {
  border-top-width: 1rpx;
  border-top-style: solid;
  border-top-color:rgba(153, 153, 153, 0.3);
}
.border-bottom {
  border-bottom-width: 1rpx;
  border-bottom-style: solid;
  border-bottom-color: rgba(153, 153, 153, 0.3);
}
.border-right {
	border-right-width: 1rpx;
	border-right-style: solid;
	border-right-color: rgba(153, 153, 153, 0.3);
}

.border-dark {
  border-color: #343a40;
}
.border-l {
  border: 1px solid;
}
.border-0 {border: 0 !important;}

/* 圆角 */
.rounded-radius { border-radius: 12rpx;}
.rounded-radius-l {border-radius: 20rpx;}
.rounded-top {
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}
.rounded-top-l {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.rounded-right {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.rounded-bottom {
  border-bottom-right-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.rounded-bottom-l {
  border-bottom-right-radius: 34rpx;
  border-bottom-left-radius: 34rpx;
}
.rounded-circle-right {
	border-top-right-radius: 200rpx;
	border-bottom-right-radius: 200rpx;
}
.rounded-left {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
 }
.rounded-circle { border-radius: 900rpx;}
/* 显示 */


.position-relative { position: relative;}
.position-sticky { position: sticky;}
.position-absolute { position: absolute;}
.position-fixed { position: fixed;}

.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.overflow-hidden {
  overflow: hidden;
}
.font-ss{font-size: 20rpx;}

/* 阴影 */
.shadow { box-shadow: 0 2upx 6upx rgba(0, 0, 0, 0.1);}
.shadow-s { box-shadow: 0 2rpx 8rpx 0 rgba(0, 0, 0, 0.06);}
.shadow-lg { box-shadow: 4rpx 2rpx 12rpx rgba(0, 0, 0, .2);}
.op-ani-show {opacity: 1; transition: opacity 300ms;}
.op-ani-hide {opacity: 0; transition: opacity 300ms;}
