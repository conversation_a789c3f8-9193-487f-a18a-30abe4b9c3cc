{"name": "uni_locations", "id": "uni_locations", "version": "1.0.0", "description": "定位插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"ios": {"plugins": [{"type": "module", "name": "LocationModule", "class": "LocationSDK"}], "integrateType": "framework", "deploymentTarget": "15.6", "frameworks": ["CoreLocation.framework", "CoreMotion.framework", "UIKit.framework"], "linkerFlags": ["-Wl,-no_alignment_warnings", "-ld_classic", "-Wl,-no_warn_duplicate_libraries", "-Wl,-ignore_optimization_hints"], "embedSwift": true, "embedFrameworks": ["LocationModule.framework"]}, "android": {"plugins": [{"type": "module", "name": "kitsLocations", "class": "com.kits.uni.locations.Bridge"}, {"type": "module", "name": "LocationModule", "class": "com.kits.uni.locations.LocationModule"}, {"type": "module", "name": "BatteryModule", "class": "com.kits.uni.locations.BatteryModule"}], "dependencies": ["com.squareup.okhttp3:okhttp:4.12.0", "com.squareup.okhttp3:logging-interceptor:4.12.0"], "integrateType": "aar", "minSdkVersion": 21}}}