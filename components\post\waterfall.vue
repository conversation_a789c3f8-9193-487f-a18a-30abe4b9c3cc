<template>
	<view class="content">
		<view class="waterfall-container">
			<view class="waterfall-column" v-for="(column, columnIndex) in columns" :key="columnIndex">
				<view class="waterfall-item" v-for="(item, index) in column" :key="index" 
					:id="`card-${columnIndex}-${index}`" 
					@click="handleCardClick(item, columnIndex, index)">
					<view class="item-content" :class="{'card-animating': animatingCard === `${columnIndex}-${index}`}">
						<!-- 动画遮罩层 -->
						<view v-if="showAnimationMask" class="animation-mask" 
							:style="animationMaskStyle"></view>
						<!-- 图片区域 -->
						<view class="item-image" v-if="item.images && item.images.length > 0">
							<image :src="item.images[0]" :mode="getImageMode(item.images[0])"
								:style="getImageStyle(item.images[0])" @click.stop="
                  previewFlag ? goMainText(item) : preview(item.images, 0)
                "></image>
						</view>

						<!-- 内容区域 -->
						<view class="item-text">
							<view class="item-title">{{ item.title || "标题" }}</view>

							<!-- 底部信息区域 -->
							<view class="item-footer">
								<view class="user-info">
									<image class="user-avatar" :src="item.user_info.avatar" mode="aspectFill"
										@click.stop="
                      goNavT(
                        '/pages/otherPage/otherPage?uuid=' +
                          item.user_info.uuid,
                        item.user_info.uuid
                      )
                    "></image>
									<text class="user-name">{{ item.user_info.nickname }}</text>
								</view>

								<view class="interaction">
									<view class="like" @click.stop="
                      setLike(item.moment_id, item.is_like, columnIndex, index)
                    ">
										<image src="@/static/images/topic/like2.png" mode="aspectFit"
											v-if="item.is_like"></image>
										<image src="@/static/images/topic/like.png" mode="aspectFit" v-else></image>
										<text style="margin-left: 10rpx">{{ item.like }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			uuid: {
				type: String,
				default: "",
			},
			goFLag: {
				type: Boolean,
				default: false,
			},
			showAuthority: {
				type: Boolean,
				default: true,
			},
			showTop: {
				type: Boolean,
				default: true,
			},
			showVipLogo: {
				type: Boolean,
				default: true,
			},
			moreFlag: {
				type: Boolean,
				default: true,
			},
			footerFlag: {
				type: Boolean,
				default: true,
			},
			followFlag: {
				type: Boolean,
				default: false,
			},
			previewFlag: {
				type: Boolean,
				default: false,
			},
			list: {
				type: Array,
				default: [],
			},
			timeFormat: {
				type: String,
				default: "yyyy/M/D",
			},
		},
		data() {
			return {
				likeFlag: false,
				flag: false,
				columns: [
					[],
					[]
				],
				indexMapping: {},
				imageRatios: {}, // 存储图片比例信息
				uuid: uni.getStorageSync("uuid"),
				goFLag: false,
				// 动画相关
				animatingCard: null, // 当前正在动画的卡片
				showAnimationMask: false, // 是否显示动画遮罩
				animationMaskStyle: {}, // 动画遮罩样式
			};
		},
		watch: {
			list: {
				handler(newVal) {
					console.log("瀑布流数据更新", newVal?.length);
					// 立即处理数据，不等待下一个tick
					this.distributeItems(newVal);

					// 然后在nextTick中确保渲染完成
					this.$nextTick(() => {
						// 再次检查，确保列数据正确
						if (
							newVal &&
							newVal.length > 0 &&
							this.columns[0].length === 0 &&
							this.columns[1].length === 0
						) {
							console.log("重新分配列数据");
							this.distributeItems(newVal);
						}

						this.$forceUpdate();
					});
				},
				immediate: true,
				deep: true,
			},
		},
		mounted() {
			// 组件挂载后，确保数据正确分配
			this.distributeItems(this.list);

			// 使用更短的延时
			setTimeout(() => {
				if (this.list && this.list.length > 0) {
					this.distributeItems(this.list);
					this.$forceUpdate();
				}
			}, 50);
		},
		methods: {
			// 获取图片显示模式
			getImageMode(imageUrl) {
				// 使用aspectFill模式
				return "aspectFill";
			},

			// 获取图片样式，根据比例设置不同的样式
			getImageStyle(imageUrl) {
				const ratio = this.imageRatios[imageUrl];

				if (!ratio) {
					// 如果还没有获取到比例，先加载图片
					this.getImageRatio(imageUrl);
					return "";
				}

				// 根据比例选择合适的样式
				if (ratio >= 0.9 && ratio <= 1.1) {
					// 接近1:1，正方形
					return "height: 375rpx;"; // 假设宽度为375rpx
				} else if (ratio > 1.1 && ratio <= 1.5) {
					// 宽图(宽>高)，接近4:3，横向矩形
					return "height: 281rpx;"; // 宽度的75%
				} else if (ratio >= 0.45 && ratio < 0.9) {
					// 长图(高>宽)，接近3:4，纵向矩形
					return "height: 500rpx;"; // 宽度的133%
				}

				// 其他比例使用默认高度
				return "height: 375rpx;";
			},
			getImageStyleR(imageUrl) {
				const ratio = this.imageRatios[imageUrl];
				return ratio;
			},
			// 获取图片比例
			getImageRatio(imageUrl) {
				// 如果已经有这个图片的比例信息，直接返回
				if (this.imageRatios[imageUrl] !== undefined) {
					return;
				}

				// 使用uni.getImageInfo获取图片信息
				uni.getImageInfo({
					src: imageUrl,
					success: (res) => {
						// 计算宽高比
						const ratio = res.width / res.height;
						// 存储比例信息
						this.$set(this.imageRatios, imageUrl, ratio);
						// 强制更新视图
						this.$forceUpdate();
					},
					fail: () => {
						// 获取失败时设置一个默认比例
						this.$set(this.imageRatios, imageUrl, 1);
					},
				});
			},
			// 将列表项分配到两列中
			distributeItems(items) {
				if (!items || items.length === 0) return;

				// 重置列
				this.columns = [
					[],
					[]
				];
				// 重置索引映射
				this.indexMapping = {};

				// 过滤掉没有图片的项目
				const filteredItems = items.filter(
					(item) => item.images && item.images.length > 0
				);

				// 如果过滤后没有项目，直接返回
				if (filteredItems.length === 0) return;

				// 为每个项目添加原始索引属性
				filteredItems.forEach((item, originalIndex) => {
					// 添加原始索引属性
					item._originalIndex = originalIndex;
				});

				// 最简单的方案：严格交替分配 - 奇数索引项放右列，偶数索引项放左列
				filteredItems.forEach((item, originalIndex) => {
					// 偶数索引放左列，奇数索引放右列
					const columnIndex = originalIndex % 2;
					this.columns[columnIndex].push(item);
				});
			},
			// 检查并重新平衡两列
			checkAndRebalanceColumns() {
				// 如果列数据发生明显不平衡，重新分配
				// 不再使用简单的项目数比较，而是考虑列高度

				// 估算两列当前高度
				let leftColumnHeight = this.estimateColumnHeight(this.columns[0]);
				let rightColumnHeight = this.estimateColumnHeight(this.columns[1]);

				// 如果高度差异超过30%，重新分配
				const totalHeight = leftColumnHeight + rightColumnHeight;
				const heightDiff = Math.abs(leftColumnHeight - rightColumnHeight);
				const diffRatio = heightDiff / totalHeight;

				if (diffRatio > 0.3 && this.list && this.list.length > 0) {
					console.log("列高度不平衡，重新分配...", diffRatio);
					this.distributeItems(this.list);
				}
			},
			// 估算一列的总高度
			estimateColumnHeight(column) {
				if (!column || column.length === 0) return 0;

				let height = 0;
				column.forEach((item) => {
					// 基础高度
					let itemHeight = 100;

					// 图片高度
					if (item.images && item.images.length > 0) {
						itemHeight += item.images.length * 150;
					}

					// 内容高度
					if (item.content) {
						itemHeight += item.content.length * 0.5;
					}

					height += itemHeight;
				});

				return height;
			},
			goAddress(item) {
				this.$emit("goAddress", item);
			},
			follow(item) {
				this.$emit("follow", {
					...item,
				});
			},
			preview(urls, current) {
				console.log("urls" + current + ":", urls);
				uni.previewImage({
					current,
					urls,
				});
			},
			setLike(momentId, isLike, columnIndex, index) {
				this.$u.debounce(() => {
					// 获取当前项
					const item = this.columns[columnIndex][index];
					// 使用原始列表中的索引
					const originalIndex =
						item._originalIndex !== undefined ? item._originalIndex : index;

					this.$emit("setLike", {
						index: originalIndex, // 使用原始索引
						momentId,
						isLike,
					});
				}, 200);
			},
			share(item) {
				this.$emit("share", item);
			},
			goNav(url) {
				this.navigateTo({
					url,
				});
			},
			handleCardClick(data, columnIndex, index) {
				// 获取卡片元素的位置信息
				const cardId = `card-${columnIndex}-${index}`;
				const query = uni.createSelectorQuery().in(this);
				
				query.select(`#${cardId}`).boundingClientRect((rect) => {
					if (rect) {
						// 开始动画
						this.startCardAnimation(rect, data, columnIndex, index);
					} else {
						// 如果获取不到位置信息，直接打开
						this.goMainText(data);
					}
				}).exec();
			},
			
			startCardAnimation(rect, data, columnIndex, index) {
				// 设置当前动画卡片
				this.animatingCard = `${columnIndex}-${index}`;
				
				// 获取屏幕尺寸
				const systemInfo = uni.getSystemInfoSync();
				const screenWidth = systemInfo.windowWidth;
				const screenHeight = systemInfo.windowHeight;
				
				// 初始化遮罩样式（卡片的初始位置和大小）
				this.animationMaskStyle = {
					position: 'fixed',
					top: rect.top + 'px',
					left: rect.left + 'px',
					width: rect.width + 'px',
					height: rect.height + 'px',
					backgroundColor: '#fff',
					borderRadius: '12rpx',
					zIndex: 9999,
					transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
					transform: 'scale(1)',
					opacity: 1
				};
				
				// 显示遮罩
				this.showAnimationMask = true;
				
				// 延迟一帧后开始动画
				this.$nextTick(() => {
					setTimeout(() => {
						// 动画到全屏
						this.animationMaskStyle = {
							...this.animationMaskStyle,
							top: '0px',
							left: '0px',
							width: screenWidth + 'px',
							height: screenHeight + 'px',
							borderRadius: '0px',
							transform: 'scale(1.02)'
						};
						
						// 动画完成后打开详情页
						setTimeout(() => {
							this.goMainText(data);
							// 重置动画状态
							this.resetAnimation();
						}, 300);
					}, 50);
				});
			},
			
			resetAnimation() {
				this.animatingCard = null;
				this.showAnimationMask = false;
				this.animationMaskStyle = {};
			},
			
			goMainText(data) {
				// 将点击事件传递给父组件
				this.$emit("goMainText", data);
			},
			goNavT(url, uuid = "") {
				if (this.goFLag || uuid == this.uuid) return;
				this.navigateTo({
					url,
				});
			},
			setEye() {
				this.flag = !this.flag;
			},
			goMore(item, index) {
				this.$emit("more", {
					...item,
					index,
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.content {
		padding: 20rpx 6rpx;
		min-height: 100%;
		box-sizing: border-box;
	}

	.waterfall-container {
		display: flex;
		width: 100%;
		box-sizing: border-box;
		min-height: 60vh;
		/* 确保容器高度适中 */
		justify-content: space-between;
	}

	.waterfall-column {
		width: 49.5%;
		/* 而不是flex:1，确保两列宽度相等 */
		display: flex;
		flex-direction: column;
		padding: 0 3rpx;
	}

	.waterfall-item {
		margin-bottom: 12rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #fff;
		width: 100%;
		box-sizing: border-box;
	}

	.item-content {
		width: 100%;
	}

	.item-image {
		width: 100%;
		overflow: hidden;
		background-color: #f5f5f5; // 图片加载前的背景色

		image {
			width: 100%;
			border-radius: 12rpx 12rpx 0 0; // 匹配卡片圆角
			display: block; // 防止图片底部有间隙
			object-fit: cover; // 确保图片覆盖整个区域
		}
	}

	.item-text {
		padding: 12rpx; // 减小内边距
	}

	.item-title {
		font-size: 28rpx;
		line-height: 38rpx; // 减小行高
		color: #33353b;
		margin-bottom: 12rpx; // 减小下边距
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		font-weight: 800;
	}

	.item-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 12rpx; // 减小上边距
	}

	.user-info {
		display: flex;
		align-items: center;
	}

	.user-avatar {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
	}

	.user-name {
		font-size: 24rpx;
		color: #9b9aa0;
		margin-left: 10rpx;
		max-width: 120rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.interaction {
		display: flex;
		align-items: center;
	}

	.like {
		display: flex;
		align-items: center;

		image {
			width: 28rpx;
			height: 28rpx;
		}

		text {
			font-size: 24rpx;
			color: #9b9aa0;
			margin-left: 6rpx;
		}
	}

	/* 动画相关样式 */
	.animation-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #fff;
		z-index: 9999;
		pointer-events: none;
		border-radius: 12rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	.card-animating {
		position: relative;
		z-index: 10000;
	}
</style>