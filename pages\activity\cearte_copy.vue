<template>
	<view class="pages">
		<view class="view title">
			<input v-model="formData.title" type="text" placeholder="请输入标题" @input="activityName" />
			<view class="t-pic" style="display: flex; justify-content: space-between">
				<view style="width: 48%" v-if="formData.cover">
					<view class="scroll-view-item-img" style="transform: translateX(-20rpx)">
						<image style="width: 300rpx; height: 300rpx" :src="formData.cover" mode="aspectFill"
							@click="previewC(index)">
						</image>
					</view>
				</view>
				<view class="scroll-view" style="width: 48%">
					<view class=""> 注：禁止发布违法内容 </view>
					<image @click="upload" src="../../static/images/addBig.png" class="scroll-view-item"
						style="transform: translateY(10rpx)" mode="aspectFill">
					</image>
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item" style="" @click="activiteTime()">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/images/watch.png" mode="aspectFill">
					</image>
					<view class="textC"> 活动时间 </view>
				</view>
				<view class="t_display">
					<view class="textC" style="margin-right: 10rpx">
						{{activieTime}}
					</view>
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
			<view style=" border: 1rpx solid rgba(196, 196, 196, 0.12); margin-top: 22rpx; margin-bottom: 34rpx;">
			</view>
			<view class="item" style="" @click="goNav('/pages/searchMap/searchMap')">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/images/localtions.png"
						mode="aspectFill"></image>
					<view class="textC"> 活动地点 </view>
				</view>
				<view class="t_display">
					<view class="textC" style="margin-right: 10rpx">
						{{ formData.address }}
					</view>
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode="aspectFill">
					</image>
					<view class="textC"> 活动内容 </view>
				</view>
			</view>
			<view style="border: 1rpx solid rgba(196, 196, 196, 0.12); margin-top: 22rpx; margin-bottom: 34rpx;">
			</view>
			<view class="">
				<input class="uni-input" type="text" v-model="formData.content" @input="content" placeholder="请输入" />
			</view>
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/pic.png" mode="aspectFill">
					</image>
					<view class="textC"> 地点图片 </view>
				</view>
				<view class="t_display">
					<view :class="locationhide ? 'block' : 'hide'" @click="locationHide">
					</view>
				</view>
			</view>
			<view style=" border: 1rpx solid rgba(196, 196, 196, 0.12); margin-top: 22rpx; margin-bottom: 34rpx; ">
			</view>
			<view class="t-pic" style="display: flex; flex-wrap: wrap; margin-bottom: 10px;">
				<view class="scroll-view-item-img" v-for="(src, index) in addressArr" :key="index">
					<image class="img" :src="src" mode="aspectFill" @click="preview(index)"
						style="margin-right: 20rpx; width: 150rpx; height: 150rpx;">
					</image>
				</view>
				<image @click="uploadAddress" src="../../static/images/addBig.png" class="scroll-view-item"
					mode="aspectFill">
				</image>
			</view>
			注：勾选后，成员到达地点后才可查看该图片
		</view>
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/pic.png" mode="aspectFill">
					</image>
					<view class="textC"> 活动展示图片 </view>
				</view>
			</view>
			<view style="border: 1rpx solid rgba(196, 196, 196, 0.12); margin-top: 22rpx; margin-bottom: 34rpx;">
			</view>
			<view class="t-pic" style="display: flex; flex-wrap: wrap">
				<view class="scroll-view-item-img" style="transform: translateX(-20rpx)" v-for="(src, index) in imgArr"
					:key="index">
					<image class="img" :src="src" mode="aspectFill" @click="previewH(index)"
						style="margin-right: 20rpx; width: 150rpx; height: 150rpx;">
					</image>
				</view>
				<image @click="uploadHd" src="../../static/images/addBig.png" class="scroll-view-item"
					mode="aspectFill">
				</image>
			</view>
		</view>
		<!-- <view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode="aspectFill"></image>
					<view class="textC"> 费用设置 </view>
				</view>
				<view class="t_display">
					<switch style="transform: scale(0.5)" :checked="true" @change="" :disabled="true" />
				</view>
			</view>
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/img/path.png" mode="aspectFill"></image>
					<view class="textC">
						<span>活动费用</span>
					</view>
				</view>
				<view class="t_display" style="color: rgb(79, 197, 238)">
					<input v-model="formData.price" type="text" placeholder="请输入标题" @input="price"
						style="text-align: right" :disabled="true" />
					元
				</view>
			</view>
		</view> -->
		<view class="view">
			<view class="item">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/ryxz.png" mode="aspectFill">
					</image>
					<view class="textC"> 活动人数限制 </view>
				</view>
				<view class="t_display" style="position: relative; right: -20px;">
					<switch style="transform: scale(0.5);" :checked="true" @change="peopleLimit" />
				</view>
			</view>
			<view class="item">
				<view class="t_display" style="width: 100%;">
					<image style="width: 24rpx; height: 24rpx" src="../../static/active/people.png" mode="aspectFill">
					</image>
					<view class="textC">
						<view>活动人数</view>
					</view>
				</view>
				<view class="t_display" style="color: rgb(79, 197, 238)">
					<input v-model="formData.people_limit" type="number" placeholder="请输入数量" @input="peopleLimitInput"
						style="text-align: right" />
					人
				</view>
			</view>
		</view>
		<view style="padding-left: 40rpx; padding-right: 40rpx; ">
			<button
				style=" background-image: url(../../static/img/fbbutton.png); background-size: 100%; border-radius: 20rpx; color: white; "
				@click="publish">
				发布
			</button>
		</view>
		<view class="clear">

		</view>
		<view class="time">
			<uni-popup ref="popup" type="bottom" background-color="rgb(41, 44, 51);">
				<view class="" style="width: 50%;">
					<u-tabs :list=" list" :is-scroll="false" :current="current" @change="change"
						bg-color="rgb(41, 44, 51)" active-color="#fff" inactive-color="rgba(255,255,255,0.72)"
						:show-bar="true">
					</u-tabs>
				</view>
				<swiper class="swiperC" :current="current" @change="setCurrent" disable-touch>
					<swiper-item>
						<view class="time_range">
							<view class="timebox" @click="startshow = true">
								<input type="text" v-model="begin" :focus="true" />
							</view>
							<span style="line-height: 40px;">-</span>
							<view class="timebox" @click="endshow = true">
								{{end}}
							</view>
						</view>
						<view class="" style="width: 100%; height: 90vh; background-color: white;">

						</view>
						<!-- <picker-view :indicator-style="indicatorStyle" :value="value" @change="bindChange"
							:mask-style="maskStyle" class="picker-view">
							<picker-view-column>
								<view class="item" v-for="(item,index) in years" :key="index">{{item}}年</view>
							</picker-view-column>
							<picker-view-column>
								<view class="item" v-for="(item,index) in months" :key="index">{{item}}月</view>
							</picker-view-column>
							<picker-view-column>
								<view class="item" v-for="(item,index) in days" :key="index">{{item}}日</view>
							</picker-view-column>
						</picker-view> -->
					</swiper-item>
					<swiper-item>

					</swiper-item>
				</swiper>

				<view class="button">
					<view class="btnbox" @click="onCancel" style="border: 1px solid rgb(56, 58, 67);">
						取消
					</view>
					<view class="btnbox" @click="onOk"
						style="background: linear-gradient(90.00deg, rgb(79, 197, 238),rgb(191, 147, 250) 100%);">
						确认
					</view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>

<script>
	import moment from 'moment'
	export default {
		data() {
			const date = new Date()
			const years = []
			const year = date.getFullYear()
			const months = []
			const month = date.getMonth() + 1
			const days = []
			const day = date.getDate()
			console.log(year, month, day);
			for (let i = 1990; i <= date.getFullYear() + 3; i++) {
				years.push(i)
			}
			for (let i = 1; i <= 12; i++) {
				months.push(i)
			}
			for (let i = 1; i <= 31; i++) {
				days.push(i)
			}
			return {
				formData: {
					title: '',
					address: '',
					cover: '',
					people_limit: null,
					content: '',
					location_img_hide: 0,
					price: 0,
					coordinate: '',
					main_uuid: '',
					begin_unix: '',
					end_unix: '',
					location_img: [],
					activity_img: [],
				},
				activieTime: '',
				imgArr: [],
				main_uuid: '',
				addressArr: [],
				locationhide: false,
				peopleCount: false,
				timeHide: false,
				startshow: false,
				endshow: false,
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false,
					timestamp: true,
				},
				begin: '',
				end: '',
				startstamp: '',
				endstamp: '',
				list: [{
					name: '日期选择'
				}, {
					name: '定制时间'
				}],
				current: 0,
				indicatorStyle: `height: 50px; backgroundColor: none`,
				maskStyle: 'height: 600px',
				years,
				year,
				months,
				month,
				days,
				day,
				value: [9999, month - 1, day - 1],
			}
		},
		onLoad(opthion) {
			if (opthion.id) {
				this.main_uuid = opthion.id
			}
		},
		onShow() {
			let that = this
			uni.$on('address', function(data) {
				console.log(data);
				that.formData.address = data.name
				that.formData.coordinate = data.location
			})
		},
		methods: {
			activiteTime() {
				this.$refs.popup.open()
			},
			goNav(url) {
				uni.navigateTo({
					url,
				})
			},
			activityName(e) {
				console.log(e.detail.value)
				this.formData.title = e.detail.value
			},
			content(e) {
				this.formData.content = e.detail.value
			},
			delImg(id) {
				this.$http
					.post('/api/user/del-photo', {
						id,
					})
					.then((res) => {
						this.getPhoto()
					})
			},
			previewC(current) {
				uni.previewImage({
					current,
					urls: [this.formData.cover],
				})
			},
			preview(current) {
				uni.previewImage({
					current,
					urls: this.addressArr,
				})
			},
			previewH(current) {
				uni.previewImage({
					current,
					urls: this.imgArr,
				})
			},
			upload() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								this.formData.cover = img
							})
						)
					},
				})
			},
			uploadAddress() {
				// let count = 9 - this.addressArr.length
				uni.chooseImage({
					count: 9,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								arr.push(img)
							})
						)

						this.addressArr.push(...arr)
					},
				})
			},
			uploadHd() {
				// let count = 9 - this.addressArr.length
				uni.chooseImage({
					count: 9,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						let arr = []
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								})
								arr.push(img)
							})
						)
						this.imgArr.push(...arr)
					},
				})
			},
			locationHide() {
				if (this.formData.location_img_hide === 0) {
					this.formData.location_img_hide = 1
					this.locationhide = true
				} else {
					this.formData.location_img_hide = 0
					this.locationhide = false
				}
			},
			peopleLimit(e) {
				console.log(e)
				if (!e.target.value) {
					this.peopleCount = true
				}
			},
			peopleLimitInput(e) {
				this.formData.people_limit = e.detail.value
			},
			bindStartChange(e) {
				console.log(e.timestamp);
				this.begin = moment(e.timestamp * 1000).format('yyyy-MM-DD')
				this.startstamp = e.timestamp
				// this.date = e.mp.detail.value
			},
			bindEndChange(e) {
				console.log(e.timestamp);
				this.end = moment(e.timestamp * 1000).format('yyyy-MM-DD')
				this.endstamp = e.timestamp
				// this.date = e.mp.detail.value
			},
			onOk() {
				this.formData.begin_unix = this.startstamp
				this.formData.end_unix = this.endstamp
				this.activieTime = moment(this.startstamp * 1000).format('yyyy-MM-DD HH:mm') + "~" + moment(this.endstamp *
					1000).format('yyyy-MM-DD HH:mm')
				this.$refs.popup.close()
			},
			onCancel() {
				this.begin = ''
				this.startstamp = ''
				this.end = ''
				this.endstamp = ''
				this.activieTime = ""
				this.$refs.popup.close()
			},
			change(index) {
				this.current = index;
			},
			setCurrent(ids) {
				this.current = ids.detail.current;
			},
			bindChange(e) {
				const val = e.detail.value
				this.year = this.years[val[0]]
				this.month = this.months[val[1]]
				this.day = this.days[val[2]]
			},
			publish() {
				this.formData.main_uuid = this.main_uuid
				this.formData.location_img = this.addressArr
				this.formData.activity_img = this.imgArr
				console.log(this.formData)
				this.$http.post('/activity/post', {
					...this.formData
				}).then(res => {
					uni.showToast({
						title: `发布成功`,
						icon: 'none'
					});
					uni.switchTab({
						url: "/pages/events/events"
					})
				})
			},
		},
	}
</script>

<style lang="scss">
	.view {
		border-radius: 20rpx;
		background: rgb(56, 58, 67);
		width: 670rpx;
		margin: auto 40rpx 40rpx;
		padding: 40rpx;
		color: white;

		.scroll-view-item {
			width: 150rpx;
			height: 150rpx;
		}
	}

	.clear {
		width: 100%;
		height: 100px;
	}

	.title {
		height: 482rpx;

		.t-pic {
			border-top: 2rpx solid rgba(196, 196, 196, 0.12);
			margin-top: 34rpx;
			padding-top: 34rpx;
		}


	}

	.scroll-view-item-img {
		overflow: hidden;
		max-width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.img {
			width: 150rpx;
			height: 150rpx;
			border-radius: 12rpx;
		}
	}

	.scroll-view-item {
		width: 150rpx;
		height: 150rpx;
	}

	.item {
		height: 68rpx;
		font-size: 32rpx;
		line-height: 1.2em;
		display: flex;
		justify-content: space-between;

		.textC {
			font-size: 24rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			margin-left: 14rpx;
		}
	}

	.close {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		right: -25rpx;
		top: -3rpx;
		// transform: translateY(-15rpx);
	}

	.block {
		width: 18px;
		height: 18px;
		border: 1px solid rgb(255, 255, 255);
		border-radius: 5px;
		background: linear-gradient(90deg,
				rgb(79, 197, 238),
				rgb(191, 147, 250) 100%);
	}

	.hide {
		width: 18px;
		height: 18px;
		border: 1px solid rgb(255, 255, 255);
		border-radius: 5px;
		background: white;
	}

	.time {
		width: 100%;

		.time_range {
			display: flex;
			justify-content: space-between;
			padding: 60rpx;

			.timebox {
				width: 157px;
				height: 44px;
				box-sizing: border-box;
				border: 1px solid rgb(79, 197, 238);
				border-radius: 10px;
				background: rgb(44, 55, 64);
				text-align: center;
				line-height: 44px;
			}
		}

		.button {
			width: 100%;
			display: flex;
			justify-content: space-between;
			padding: 40rpx;
			margin-top: 60rpx;

			.btnbox {
				width: 162px;
				height: 44px;
				border-radius: 10px;
				text-align: center;
				line-height: 44px;
			}
		}

	}

	.timehide {
		display: none;
	}

	.u-tabs__wrap .u-tabs__item.u-tabs__item--active::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		bottom: -1px;
		/* 调整这个值来控制下划线的位置 */
		height: 2px;
		/* 下划线的厚度 */
		background-color: #ff0000;
		/* 下划线的颜色 */
	}

	.picker-view {
		width: 100%;
		height: 600rpx;
		margin-top: 20rpx;
		background-color: none;
	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}
</style>