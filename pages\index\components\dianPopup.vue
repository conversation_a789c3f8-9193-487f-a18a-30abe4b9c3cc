<template>
	<view ref="popup2" v-if="show" mode="bottom" :overlay="false" :round="20" z-index="40" bgColor="transparent">
		<view class="info-content" v-if="show">
			<view class="title"></view>
			<view class="desc">
				<view class="city">
					{{info.addressComponent.city}}
				</view>
				<view class="xxx">
					{{info.addressComponent.district}}
					{{info.addressComponent.street}}{{info.addressComponent.streetNumber}}
				</view>
			</view>
			<image @click="add" src="@/static/map/add.png" class="addicon" mode=""></image>
			<view class="tools">

				<view class="tool-li" :class="{active:active=='2'}" @click="changeType('2')">
					<image class="tool-icon" src="../../../static/map/daohang.png"></image>
					<view class="tool-txt"></view>
				</view>
				<view class="tool-li" :class="{active:active=='3'}" @click="changeType('3')">
					<image class="tool-icon" src="../../../static/map/fenxiang.png"></image>
					<view class="tool-txt"></view>
				</view>
			</view>
			<u-toast ref='notify' />
			<!-- <view class="img140"></view> -->
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				active: 1,
				info: {},
				show: false
			}
		},
		mounted() {},
		methods: {
			open(option) {
				this.info = option
				console.log(option, 'option');
				// this.$refs.popup2.open()
				this.show = true
			},
			guanbi() {
				// this.$refs.popup2.close('bottom')
				this.show = false
			},
			add() {
				this.$emit('mapLongClick', this.info)
			},
			close() {
				this.show = false
				// this.$refs.popup2.close('bottom')
			},
			async changeType(str) {
				this.active = str
				if (str == 2) {
					this.show = false
					this.$common.openMap(`${this.info.lnglat.lng},${this.info.lnglat.lat}`, this.info.location)
				} else if (str == 3) {
					// this.show  = false
					this.$emit('share-house-position', {
						remark: uni.getStorageSync('nickname'),
						avatar: uni.getStorageSync('avatar'),
						coordinate: `${this.info.lnglat.lng},${this.info.lnglat.lat}`,
						location: this.info.location

					})
					// const result = await this.$Yxim.msg.sendCustomMsg({
					// 	attach: '占位',
					// 	scene: "p2p", //消息的场景  "p2p" | "team" | "superTeam"
					// 	// to: item.im_id, //接收人
					// 	to: , //接收人
					// 	ext: JSON.stringify({
					// 		url: "/pages/mainText/mainText?momentId=" + this.post
					// 			.moment_id,
					// 		avatar: this.post.avatar,
					// 		name: this.post.content,
					// 		img,
					// 		location: this.post.location,
					// 		type: 'dongtai'
					// 	}),
					// });

				}
			}
		}
	}
</script>
<style lang="scss" scoped>
	// uni-popup样式重置
	/deep/.uni-popup {
		background-color: red !important;
	}
</style>
<style lang="scss" scoped>
	.info-content {
		width: 100vw;
		height: 320rpx;
		background-color: #000;
		border-radius: 50rpx 50rpx 0 0;
		box-sizing: border-box;
		color: #fff;
		padding: 50rpx 40rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 31;

		&::after {
			width: 100vw;
			height: 120rpx;
			content: '';
			background-color: #000;
			position: absolute;
			left: 0;
			bottom: -90rpx;
		}

		.addicon {
			width: 70rpx;
			height: 70rpx;
			position: absolute;
			right: 30rpx;
			top: 30rpx;
		}

		.title {
			width: 80rpx;
			height: 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			position: absolute;
			left: 50%;
			top: 20rpx;
			transform: translateX(-50%);
			// margin: 0 auto 20rpx;


		}

		.desc {
			width: 100%;

			.xxx {
				margin-top: 10rpx;
				color: #999;
				font-size: 24rpx;
			}
		}

		.tools {
			width: 100%;
			padding-left: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 40rpx;

			.tool-li {
				width: 260rpx;
				margin-right: 40rpx;
				background-color: #282828;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10rpx;
				border-radius: 40rpx;

				.tool-icon {
					width: 40rpx;
					height: 40rpx;
				}

				.tool-txt {}
			}

			.active {
				background-color: #3686f8;
			}
		}
	}
</style>