<template>
	<view class="">
		<uni-popup ref="popups" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="item t_display" @click="choose(5)">
					<image class="disable" src="../../../static/images/see1.png" mode=""></image>
					<view class="rightInfo">
						所有人可见
					</view>
				</view>
				<view class="item t_display" style="margin: 20rpx 0;" @click="choose(1)">
					<image class="disable" src="../../../static/images/see2.png" mode=""></image>
					<view class="rightInfo">
						好友可见
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="close">
					取消
				</view>
				<view class="img32" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		components: {

		},
		data() {
			return {

			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			choose(ids) {
				this.$emit('choose', ids)
			},
			close() {
				this.$refs.popups.close()
			},
			open() {
				this.$refs.popups.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		padding-top: 30rpx;
		// height: 304rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;


		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			border-top-left-radius: 40rpx;
			padding-bottom: 18rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.line {
				height: 1rpx;
				background: #E5E5E5;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
				opacity: 1;
			}

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;

		}
	}
</style>