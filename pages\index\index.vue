<template>
	<view class="root-container" :class="{ 'no-scroll': !showFlag }">
		<ActivityImg ref="activityImgRef" @goIndex="handleGoIndex" @goLogin="goLoginFlag = false" v-show="!showFlag" />
		<view class="container" v-if="goLoginFlag">
			<u-toast ref="notify" />
			<!-- 	<view style="position: fixed;z-index: 999999999999999999999;font-size: 20px;margin-top: 100rpx;"
				@click="clickPes">
				测试
			</view> -->
			<view class="swiper">
				<Map v-show="current == 0" ref="mapRef" :unReadCount="unReadCount" />
				<Topic v-show="current == 1" ref="topicRef" @goAddress="goAddress" />
				<Event v-show="current == 3" ref="eventRef" />
				<My v-show="current == 4" ref="myRef" @goAddress="goAddress" />
				<permissions v-if="showPermission && current == 0" />
			</view>
			<view class="tab_bar" :style="{
				backgroundColor:
					current === 1 ? '#ffffff' : style.blackBackgourndColor,
			}" v-show="showMenu">
				<view class="menu-item" v-for="(item, index) in tabBarList" :key="index" :class="{
					active: current === index,
					'add-btn': index === 2 && false,
				}" @click="index === 2 ? $refs.TbSend.open(current) : goPages(index)">
					<image v-if="index === 2" src="@/static/images/topic/add.png" style="width: 100rpx; height: 62rpx">
					</image>
					<text v-else class="menu-text" :style="{ color: getMenuTextColor(index) }">{{ item.text }}</text>
				</view>
			</view>
			<TbSend ref="TbSend" />
		</view>
	</view>
</template>
<script>
	import ActivityImg from "@/pages/activityImg/activityImg.vue";
	import Map from "@/pages/index/map.vue";
	import Topic from "@/pages/topic/topic.vue";
	import Event from "@/pages/events/events.vue";
	import My from "@/pages/my/my.vue";
	import TbSend from "./components/tbSend.vue";
	import {
		config
	} from "../../config.js"
	import {
		style
	} from "@/style.js";
	import {
		nextTick
	} from "vue";
	import {
		checkPermission
	} from "../../utils/checkLocation";
	import permissions from "./permissions.vue";
	export default {
		components: {
			ActivityImg,
			Map,
			Topic,
			Event,
			My,
			TbSend,
			permissions
		},
		data() {
			return {
				LocationModule: "",
				permissionsFlag: true,
				handleLocation: "暂无定位",
				goLoginFlag: true, //是否要去登录页，如果要去登录页就不加载首页了
				showFlag: false,
				showMenu: false,
				mapCurrent: 0,
				inNum: 0,
				current: 0,
				oldCurrent: 0,
				tabBarList: "",
				appInitialized: false,
				style,
				showPermission: true
			};
		},
		computed: {
			//新增加的聊天人员列表，请求完数据后需清空，等待下次数据请求在赋值
			unReadCount() {
				const data = this.$store.state.Yxim_info.allsessions.map((item) => {
					return item;
				});
				const unReadCount = data.reduce((pre, cre) => {
					pre = pre + cre.unreadCount;
					return pre;
				}, 0);
				return unReadCount;
			},
		},
		onLoad(options) {
			this.LocationModule = uni.requireNativePlugin('LocationModule')
			this.$store.dispatch("GET_ALL_SESSIONS");
			if (options.current) {
				this.current = options.current;
			}
			setTimeout(() => {
				this.showMenu = true;
			}, 2000);


		},
		onPullDownRefresh() {
			if ([0].includes(this.current)) {
				uni.stopPullDownRefresh();
			}
		},
		onReachBottom() {
			console.log("下拉触底");
			// if ([4].includes(this.current)) {
			// 	this.$refs.myRef.currentGetData();
			// }
		},
		async onShow() {
			const token = uni.getStorageSync('token')
			// #ifdef APP-PLUS
			const platform = uni.getSystemInfoSync().platform;
			if (token) {
				// 有token才判断权限，避免未登录就请求权限
				const code = await checkPermission()
				console.log("权限code:", code)
				// 0-引导页 1-允许 2-设置页
				if (code === 0) {
					console.log("权限引导")
					this.showPermission = false
					uni.navigateTo({
						url: "/pages/index/locationGuide"
					})
				} else if (code === 1) {
					console.log("获取定位权限")
					this.showPermission = false
				} else if (code === 2) {
					console.log("跳转权限设置")
					// uni.navigateTo({
					// 	url: '/pages/permissions/permissions'
					// })
					this.showPermission = true
				}
			}
			// #endif
			console.log("App show");
			nextTick(() => {
				console.log("this.$refs.activityImgRef", this.$refs.activityImgRef);
				this.$refs.activityImgRef.open();
			});
			if (!this.showPermission) {
				this.startPlugin()
			}
			let that = this;
			uni.$on("filterMain", function(data) {
				that.$refs.topicRef.filterData(data.momentId);
			});
			// if (this.oldCurrent == 2 || this.current == 4) {
			// 	if (this.showFlag) {
			// 		this.$refs.myRef.getUpdate();
			// 	}
			// }
			if (this.current == 0 && this.showFlag && this.appInitialized) {
				setTimeout(() => {
					//第一次进入时定位到自己
					if (this.mapCurrent == 0) {
						this.$refs.mapRef.handelCenter();
					}
					this.mapCurrent += 1;
				}, 300);
			}
			setTimeout(() => {
				const scanDetail = uni.getStorageSync("scanDetail");
				if (
					scanDetail &&
					scanDetail.type == "sh" &&
					this.current == 0 &&
					this.showFlag
				) {
					this.$refs.mapRef.openShareSh(scanDetail);
				}
			}, 300);
		},
		onHide() {
			// this.$refs.mapRef.cleatTimeout()
		},

		watch: {
			showFlag(val) {
				if (val) {
					if (!this.appInitialized) {
						this.appInitialized = true;
						this.initializeApp();
					}
				}
			},
			current: {
				handler(val) {
					if (this.current == 0 && this.showFlag && this.appInitialized) {
						setTimeout(() => {
							//第一次进入时定位到自己
							if (this.mapCurrent == 0) {
								this.$refs.mapRef.handelCenter();
							}
							this.mapCurrent += 1;
						}, 300);
					}
				},
			},
		},
		//		uniapp子组件不支持应用生命周期， 所以只能用vue生命周期
		created() {
			const imgSrc = "@/static/images/";
			//tabbar数据，这儿也支持后台定义通过接口获取数据
			this.tabBarList = [{
					text: "地图",
					iconPath: "/static/images/index.png", //tabbar icon
					selectIconPath: "/static/images/index2.png", //tabbar 选择icon
					pageIndex: "/pages/index/index", //页面路径
				},
				{
					text: "动态",
					iconPath: "/static/images/topic.png", //tabbar icon
					selectIconPath: "/static/images/topic2.png", //tabbar 选择icon
					pageIndex: "/pages/topic/topic", //页面路径
				},
				{
					text: "发布",
					iconPath: "/static/images/send.png", //tabbar icon
					selectIconPath: "/static/images/send2.png", //tabbar 选择icon
					pageIndex: "/pages/send/send", //页面路径
				},
				{
					text: "活动",
					iconPath: "/static/images/event.png", //tabbar icon
					selectIconPath: "/static/images/event3.png", //tabbar 选择icon
					pageIndex: "/pages/events/events", //页面路径
				},
				{
					text: "我",
					iconPath: "/static/images/my.png", //tabbar icon
					selectIconPath: "/static/images/my2.png", //tabbar 选择icon
					pageIndex: "/pages/my/my", //页面路径
				},
			];
		},
		methods: {
			clickPes() {
				console.log("-----------开始调用插件--------------");
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();

				// 判断操作系统
				if (systemInfo.platform === 'android') {
					console.log('运行在Android上');
					const option = {
						interval_filter: 3000,
						distance_filter: 1.0,
						bind_notify: true,
						bind_notify_title: "LightingBall",
						bind_notify_content: "正在与朋友共享位置...",
						activity_step: true
					}
					uni.requireNativePlugin('LocationModule').startLocationService(option)

					plus.globalEvent.addEventListener('locationUpdate', (res) => {
						console.log('定位信息:', {
							latitude: res.latitude, // 纬度
							longitude: res.longitude, // 经度
							accuracy: res.accuracy, // 精度（米）
							altitude: res.altitude, // 海拔（米）
							timestamp: res.timestamp, // 时间戳
							provider: res.provider, // 定位提供者（gps/network）
							background: res.background // 是否是后台
						})
						this.handleLocation = `${res.latitude}.${res.longitude}`
					})
				}
				const token = uni.getStorageSync("token");
				const RefreshToken = uni.getStorageSync("RefreshToken");
				uni.requireNativePlugin('LocationModule').setBaseUrl(config.BASE_URL_App)
				uni.requireNativePlugin('LocationModule').setToken(token, RefreshToken)
				uni.requireNativePlugin('LocationModule').debug(true)
				plus.globalEvent.addEventListener('locationUpdateStart', (res) => {
					console.log("-----------调通了-------------", res);
				})

			},
			startPlugin() {
				this.permissionsFlag = true
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();

				// 判断操作系统
				if (systemInfo.platform === 'android') {
					console.log('运行在Android上');
					this.getPermission()
					// 检查是否开启电池忽略
					uni.requireNativePlugin('kitsLocations').ignoreBatteryOptimizationsEnable(res => {
						console.log('电池忽略状态:', res.status)
						if (!res.status) {
							this.permissionsFlag = false
							// 跳转开启电池忽略
							uni.requireNativePlugin('kitsLocations').ignoreBatteryOptimizations(res => {
								console.log('跳转设置页结果:', res.status)
							})
						} else {
							// this.permissionsFlag:定位权限开关
							if (this.permissionsFlag) {
								// uni.requireNativePlugin('LocationModule').startLocationServiceFilter(1.0, 3000, true, true, true)
								console.log("===============调用插件==========================")
								this.showPermission = false
								this.clickPes()
							} else {
								this.toast('请手动开启授权')
								// 跳转到应用设置页
								this.showPermission = true
								// uni.requireNativePlugin('kitsLocations').toAppSettings(res => {
								// 	console.log('跳转设置页结果:', res.success)
								// })
							}
						}
					})
				} else if (systemInfo.platform === 'ios') {
					console.log('运行在iOS上');
					this.getIosPermission()
					this.clickPes()
				} else {
					console.log('运行在开发者工具上');
				}

				console.log('权限授权状态', this.permissionsFlag);

			},
			getIosPermission() {
				// uni.requireNativePlugin('LocationModule').authWhenInUse()
				// uni.requireNativePlugin('LocationModule').authAlways()
			},
			getPermission() {
				// 申请前台定位权限
				uni.requireNativePlugin('kitsLocations').requestForegroundLocationPermission(res => {
					if (res.success && res.status === 'granted') {
						console.log('前台定位权限已授权')
						this.showPermission = false
					} else if (res.status === 'permanently_denied') {
						console.log('前台定位权限被永久拒绝，需要引导用户去设置页')
						this.permissionsFlag = false
						// 可以调用 toAppSettings 引导用户去设置页
						uni.navigateTo({
							url: "/pages/index/locationGuide"
						})
						// uni.requireNativePlugin('UniLocations').toAppSettings()
					} else if (res.status === 'denied') {
						console.log('前台定位权限被拒绝，可以再次申请')
						this.permissionsFlag = false
						return
					} else {
						console.log('权限申请出错:', res.error)
						this.permissionsFlag = false
					}
				})

				// 申请后台定位权限
				uni.requireNativePlugin('kitsLocations').requestBackgroundLocationPermission(res => {
					if (res.success && res.status === 'granted') {
						console.log('后台定位权限已授权')
						this.showPermission = false
					} else if (res.status === 'permanently_denied') {
						console.log('后台定位权限被永久拒绝')
						this.permissionsFlag = false
						this.showPermission = true
						uni.navigateTo({
							url: "/pages/index/locationGuide"
						})
					} else if (res.status === 'denied') {
						console.log('后台定位权限被拒绝')
						this.permissionsFlag = false
						this.showPermission = true
					} else {
						console.log('权限申请出错:', res.error)
						this.permissionsFlag = false
					}
				})

				// 	// // 申请活动识别权限
				// 	// uni.requireNativePlugin('kitsLocations').requestActivityPermission(res => {
				// 	// 	if (res.success && res.status === 'granted') {
				// 	// 		console.log('活动识别权限已授权')
				// 	// 	} else if (res.status === 'permanently_denied') {
				// 	// 		console.log('活动识别权限被永久拒绝')
				// 	// 		this.permissionsFlag = false
				// 	// 	} else if (res.status === 'denied') {
				// 	// 		console.log('活动识别权限被拒绝')
				// 	// 		this.permissionsFlag = false
				// 	// 	} else {
				// 	// 		console.log('权限申请出错:', res.error)
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })

				// 	// // 申请通知权限
				// 	// uni.requireNativePlugin('kitsLocations').requestPostPermission(res => {
				// 	// 	if (res.success && res.status === 'granted') {
				// 	// 		console.log('通知权限已授权')
				// 	// 	} else if (res.status === 'permanently_denied') {
				// 	// 		console.log('通知权限被永久拒绝')
				// 	// 		this.permissionsFlag = false
				// 	// 	} else if (res.status === 'denied') {
				// 	// 		console.log('通知权限被拒绝')
				// 	// 		this.permissionsFlag = false
				// 	// 	} else {
				// 	// 		console.log('权限申请出错:', res.error)
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })

				// 	// // 查询前台位置权限状态
				// 	// uni.requireNativePlugin('kitsLocations').getForegroundLocationPermissionStatus(res => {
				// 	// 	console.log('前台定位权限状态:', res.status)
				// 	// 	if (!res.status) {
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })

				// 	// // 查询后台位置权限状态
				// 	// uni.requireNativePlugin('kitsLocations').getBackgroundLocationPermissionStatus(res => {
				// 	// 	console.log('前台定位权限状态:', res.status)
				// 	// 	if (!res.status) {
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })

				// 	// // 查询运动权限状态
				// 	// uni.requireNativePlugin('kitsLocations').getActivityPermissionStatus(res => {
				// 	// 	console.log('前台定位权限状态:', res.status)
				// 	// 	if (!res.status) {
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })


				// 	// // 查询通知权限状态
				// 	// uni.requireNativePlugin('kitsLocations').getPostPermissionStatus(res => {
				// 	// 	console.log('前台定位权限状态:', res.status)
				// 	// 	if (res.status != 'granted') {
				// 	// 		this.permissionsFlag = false
				// 	// 	}
				// 	// })
				// },
			},
			getMenuTextColor(index) {
				// 当前菜单为第二个菜单时
				if (this.current === 1) {
					// 如果是选中的菜单项
					if (index === this.current) {
						return "#33353b"; // 黑色字
					}
					return "#cccccc"; // 灰色字
				} else {
					// 其他菜单情况下
					if (index === this.current) {
						return "#ffffff"; // 选中时白色字
					}
					return "#cccccc"; // 未选中时灰色字
				}
			},
			handleGoIndex() {
				// 分开处理，先设置showFlag为true
				this.showFlag = true;
				this.appInitialized = true;
				this.initializeApp();
			},
			initializeApp() {
				// 当showFlag变为true时，初始化地图和其他组件
				if (this.current == 0) {
					setTimeout(() => {
						if (this.mapCurrent == 0) {
							this.$refs.mapRef.handelCenter();
						}
						this.mapCurrent += 1;
					}, 300);
				}
				if (this.oldCurrent == 2 && this.current == 4) {
					this.$refs.myRef.getUpdate();
				}
			},
			// 前往系统设置的功能
			checkVersion() {
				permissions(!this.isNotifications);
			},
			//扎点  定位  给其他页面使用的
			goAddress(item) {
				this.current = 0;
				const locat = item.coordinate.split(",");
				this.poiMapAddMarker({
					lnglat: {
						lng: locat[0],
						lat: locat[1],
					},
					location: item.location,
					addressComponent: {
						city: item.location,
						district: item.location,
						street: "",
						streetNumber: "",
					},
				});
			},
			poiMapAddMarker(item) {
				this.$refs.mapRef.poiMapAddMarker({
					...item,
				});
			},
			tos() {
				this.$refs.notify.show({
					title: "",
					position: "top",
				});
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},
			mapUpdate(data) {
				this.$refs.mapRef.poiMapAddMarker({
					...data,
				});
			},
			setCurrent(number) {
				this.current = number;
			},

			goOldPage(pageIndex) {
				console.log("重新获取当前页面的数据", pageIndex);
				// if ([1, 3, 4].includes(pageIndex) && this.oldCurrent == pageIndex) {
				if ([1, 3, 4].includes(pageIndex)) {
					this.goPages(pageIndex);
				}
			},
			//进入tabble页
			goPages(pageIndex) {
				this.oldCurrent = pageIndex;
				if (pageIndex == 2) {
					this.$refs.TbSend.open(this.current);
					// uni.navigateTo({
					// 	url: "/pages/send/send?pages=" + this.current
					// })
					this.oldCurrent = pageIndex;
					return;
				} else if (pageIndex == 4 && pageIndex == this.current) {
					this.$refs.myRef.getUpdate();
				} else if (pageIndex == 1 && pageIndex == this.current) {
					this.$refs.topicRef.getFirstData();
				} else if (pageIndex == 3 && pageIndex == this.current) {
					this.$refs.eventRef.getFirstData();
				}
				this.current = pageIndex;
			},
		},
	};
</script>
<style lang="scss" scoped>
	.bg {
		width: 100vw;
		height: 99.5vh;
		z-index: 99999999999;
	}

	.root-container {
		position: relative;
		width: 100%;
		height: 100vh;
	}

	.no-scroll {
		overflow: hidden;
		height: 100vh;
		position: fixed;
		width: 100%;
	}

	.container {
		width: 100%;
		height: 100vh;
	}

	.tips {
		position: fixed;
		bottom: 160rpx;
		left: 15rpx;
		color: #535e65;
		font-weight: bold;
		z-index: 9999999999;
	}

	.swiper {
		height: 100vh;
	}

	.swiper-item {
		display: block;
		height: 100vh;
		text-align: center;
	}

	.tab_bar {
		z-index: 101;
		width: 100%;
		// height: 100rpx;
		padding: 20rpx 0 62rpx 0;
		position: fixed;
		bottom: 0;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
	}

	.menu-item {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		padding: 0 20rpx;
	}

	.menu-text {
		font-size: 32rpx;
	}

	.active .menu-text {
		font-weight: 600;
	}

	.add-btn {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border: 2rpx solid #e0e7ff;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.add-icon {
		color: #aabbff;
		font-size: 40rpx;
	}
</style>