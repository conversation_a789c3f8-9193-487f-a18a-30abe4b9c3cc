<template>
  <view class="agreement-modal">
    <view class="agreement-content">
      <scroll-view scroll-y style="max-height: 65vh;">
        <text class="agreement-title">欢迎使用 LightingBall!</text>
        <text class="agreement-text">
          我们高度重视您的个人信息安全与隐私保护。
          在您开始使用 LightingBall 之前，请务必仔细阅读我们的
          <text class="link" @tap="openUserAgreement">《用户协议》</text>
          和
          <text class="link" @tap="openPrivacyPolicy">《隐私政策》</text>
          。主要说明如下：
          1. 用户资料管理：为修改您的用户资料，我们可能需要请求存储权限和摄像头权限。
          2. 定位服务：为提供完整的实时定位服务，我们可能需要请求您的位置权限。
          3. 设备信息与日志：为实现信息推送和保障账号安全，我们会请求系统设备权限以收集必要的设备信息和日志信息。
          4. 信息安全：我们将采取严格的安全技术措施保护您的个人信息。未经您明确同意，我们不会从第三方获取、共享或对外提供您的信息。
          5. 您的权利：您可以访问、更正或删除您的个人信息。我们也提供账号注销和投诉反馈的渠道。
          请点击“同意”按钮，表示您已充分知晓并同意上述内容及完整的
          <text class="link" @tap="openUserAgreement">《用户协议》</text>
          与
          <text class="link" @tap="openPrivacyPolicy">《隐私政策》</text>
          ，即可继续使用 LightingBall。
        </text>
      </scroll-view>
      <view class="agreement-actions">
        <button class="btn-decline" @tap="onDecline">拒绝</button>
        <button class="btn-accept" @tap="onAccept">同意</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AgreementModal',
  data() {
    return {

    }
  },

  onLoad() {
    // #ifdef APP-PLUS || H5
    uni.hideNavigationBarLoading();
    // #endif
  },

  methods: {
    onAccept() {
      uni.setStorageSync('user_agreement', true)
      uni.navigateBack({
        delta: 1
      })
    },
    onDecline() {
      uni.showToast({ title: '您必须同意协议才能继续', icon: 'none' })
    },
    openUserAgreement() {
      // 跳转到用户协议页面
      uni.navigateTo({ url: '/pages/privacyAgreement/userAgreement' })
    },
    openPrivacyPolicy() {
      // 跳转到隐私政策页面
      uni.navigateTo({ url: '/pages/privacyAgreement/privacyPolicy' })
    }
  }
}
</script>

<style scoped>
.agreement-modal {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #FF231B 0%, #1FBCFF 100%);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-content {
  background: #010101;
  color: #fff;
  border-radius: 58rpx;
  padding: 52rpx 46rpx;
  max-width: 600rpx;
  box-sizing: border-box;
}

.agreement-title {
  font-size: 27rpx;
  font-weight: bold;
  display: block;
  line-height: 40rpx;
}

.agreement-text {
  font-size: 27rpx;
  line-height: 40rpx;
  margin-bottom: 32rpx;
  display: block;
}

.link {
  color: #ff4d4f;
  text-decoration: underline;
}

.agreement-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 46rpx;
}

.btn-decline,
.btn-accept {
  flex: 1;
  margin: 0 8rpx;
  border-radius: 16rpx;
  font-size: 27rpx;
  background: #252525;
}

.btn-decline {
  background: #222;
  color: #fff;
}

.btn-accept {
  background: #fff;
  color: #222;
  background: #FFFFFF;
}
</style>