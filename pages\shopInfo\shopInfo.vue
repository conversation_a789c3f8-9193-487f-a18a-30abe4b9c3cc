<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">商户详情</view>
		</view>
		<view class="list">
			<view class="item" v-for="(item, index) in 3" :key="index">
				<image class="activeimage" src="../../static/images/vip/active.png" mode=""></image>
				<view class="item-info">
					<view class="item-title">端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟</view>
					<view class="item-time">
						<image class="time-clock" src="../../static/images/vip/clock.png" mode=""></image>
						<view class="time">2024.10.01 ~ 10.09</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style lang="scss">
page {
	background-color: #f5f7fb;
}
.content {
	width: 100%;
	height: 100%;
	padding-top: 100px;
	background-color: #f5f7fb;
	.navigation-bar {
		width: 100%;
		display: flex;
		align-items: center;
		height: 170px;
		background-image: url('../../static/images/vip/newBackground.png'); /* 背景图路径 */
		background-size: cover;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
	}
	.navigation-zhezhao {
		width: 100%;
		height: 170px;
		background-image: url('../../static/images/vip/nav-zhezhao.png'); /* 背景图路径 */
		background-size: 100%;
		background-repeat: no-repeat;
		background-position: bottom;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		padding-bottom: 10%;
		.back {
			width: 31px;
			height: 31px;
			margin-left: 2%;
			z-index: 1;
		}
		.nav-title {
			width: 82%;
			height: 30px;
			color: #000000;
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: center;
			z-index: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
	.list {
		width: 95%;
		height: 130px;
		margin-left: 2.5%;
		position: relative;
		z-index: 1;
		.item {
			width: 100%;
			height: 130px;
			display: flex;
			justify-content: space-evenly;
			align-items: center;
			align-content: center;

			border-radius: 10px;
			background: #ffffff;
			margin-top: 10px;

			// padding: 10px 3%;
			.activeimage {
				width: 110px;
				height: 110px;
				border-radius: 6px;
			}

			.item-info {
				height: 110px;
				width: 64%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.item-title {
					color: #212121;
					font-family: 阿里巴巴普惠体;
					font-size: 17px;
					font-weight: 400;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: left;
				}

				.item-time {
					line-height: 20px;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					flex-direction: row;

					color: #212121;
					font-family: HarmonyOS Sans;
					font-size: 13px;
					font-weight: 400;
					line-height: 14px;
					letter-spacing: 0px;
					text-align: left;

					.time-clock {
						width: 16px;
						height: 16px;
						margin-right: 5px;
					}
				}
			}
		}
	}
}
</style>
