<template>
	<view>
		<view class="login" @click="confirm" :style="{borderRadius:radio+'rpx'}">{{txt}}
		</view>
	</view>
</template>

<script>
	export default {
		name: "submitBtn",
		props: {
			txt: {
				type: String,
				default: '确定'
			},
			radio: {
				type: Number,
				default: 14
			}
		},
		data() {
			return {

			};
		},
		methods: {
			confirm() {
				this.$emit('confirm')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.login {
		color: #fff;
		height: 94rpx;
		line-height: 94rpx;
		font-size: 32rpx;
		text-align: center;
		background: linear-gradient(#4BC6ED, #BC93F2);
	}
</style>