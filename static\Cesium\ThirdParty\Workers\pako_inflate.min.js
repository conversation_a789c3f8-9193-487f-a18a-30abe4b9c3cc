/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */(function(O,I){typeof exports=="object"&&typeof module<"u"?I(exports):typeof define=="function"&&define.amd?define(["exports"],I):I((O=typeof globalThis<"u"?globalThis:O||self).pako={})})(this,function(O){"use strict";var I=(e,i,t,o)=>{let s=65535&e|0,r=e>>>16&65535|0,h=0;for(;t!==0;){h=t>2e3?2e3:t,t-=h;do s=s+i[o++]|0,r=r+s|0;while(--h);s%=65521,r%=65521}return s|r<<16|0};const Zt=new Uint32Array((()=>{let e,i=[];for(var t=0;t<256;t++){e=t;for(var o=0;o<8;o++)e=1&e?**********^e>>>1:e>>>1;i[t]=e}return i})());var U=(e,i,t,o)=>{const s=Zt,r=o+t;e^=-1;for(let h=o;h<r;h++)e=e>>>8^s[255&(e^i[h])];return-1^e};const j=16209;var St=function(e,i){let t,o,s,r,h,l,p,n,a,A,w,d,Z,v,u,y,k,f,g,E,c,x,m,b;const _=e.state;t=e.next_in,m=e.input,o=t+(e.avail_in-5),s=e.next_out,b=e.output,r=s-(i-e.avail_out),h=s+(e.avail_out-257),l=_.dmax,p=_.wsize,n=_.whave,a=_.wnext,A=_.window,w=_.hold,d=_.bits,Z=_.lencode,v=_.distcode,u=(1<<_.lenbits)-1,y=(1<<_.distbits)-1;t:do{d<15&&(w+=m[t++]<<d,d+=8,w+=m[t++]<<d,d+=8),k=Z[w&u];e:for(;;){if(f=k>>>24,w>>>=f,d-=f,f=k>>>16&255,f===0)b[s++]=65535&k;else{if(!(16&f)){if(!(64&f)){k=Z[(65535&k)+(w&(1<<f)-1)];continue e}if(32&f){_.mode=16191;break t}e.msg="invalid literal/length code",_.mode=j;break t}g=65535&k,f&=15,f&&(d<f&&(w+=m[t++]<<d,d+=8),g+=w&(1<<f)-1,w>>>=f,d-=f),d<15&&(w+=m[t++]<<d,d+=8,w+=m[t++]<<d,d+=8),k=v[w&y];i:for(;;){if(f=k>>>24,w>>>=f,d-=f,f=k>>>16&255,!(16&f)){if(!(64&f)){k=v[(65535&k)+(w&(1<<f)-1)];continue i}e.msg="invalid distance code",_.mode=j;break t}if(E=65535&k,f&=15,d<f&&(w+=m[t++]<<d,d+=8,d<f&&(w+=m[t++]<<d,d+=8)),E+=w&(1<<f)-1,E>l){e.msg="invalid distance too far back",_.mode=j;break t}if(w>>>=f,d-=f,f=s-r,E>f){if(f=E-f,f>n&&_.sane){e.msg="invalid distance too far back",_.mode=j;break t}if(c=0,x=A,a===0){if(c+=p-f,f<g){g-=f;do b[s++]=A[c++];while(--f);c=s-E,x=b}}else if(a<f){if(c+=p+a-f,f-=a,f<g){g-=f;do b[s++]=A[c++];while(--f);if(c=0,a<g){f=a,g-=f;do b[s++]=A[c++];while(--f);c=s-E,x=b}}}else if(c+=a-f,f<g){g-=f;do b[s++]=A[c++];while(--f);c=s-E,x=b}for(;g>2;)b[s++]=x[c++],b[s++]=x[c++],b[s++]=x[c++],g-=3;g&&(b[s++]=x[c++],g>1&&(b[s++]=x[c++]))}else{c=s-E;do b[s++]=b[c++],b[s++]=b[c++],b[s++]=b[c++],g-=3;while(g>2);g&&(b[s++]=b[c++],g>1&&(b[s++]=b[c++]))}break}}break}}while(t<o&&s<h);g=d>>3,t-=g,d-=g<<3,w&=(1<<d)-1,e.next_in=t,e.next_out=s,e.avail_in=t<o?o-t+5:5-(t-o),e.avail_out=s<h?h-s+257:257-(s-h),_.hold=w,_.bits=d};const K=15,Tt=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),Ot=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Ut=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Dt=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var z=(e,i,t,o,s,r,h,l)=>{const p=l.bits;let n,a,A,w,d,Z,v=0,u=0,y=0,k=0,f=0,g=0,E=0,c=0,x=0,m=0,b=null;const _=new Uint16Array(16),S=new Uint16Array(16);let H,q,J,Q=null;for(v=0;v<=K;v++)_[v]=0;for(u=0;u<o;u++)_[i[t+u]]++;for(f=p,k=K;k>=1&&_[k]===0;k--);if(f>k&&(f=k),k===0)return s[r++]=20971520,s[r++]=20971520,l.bits=1,0;for(y=1;y<k&&_[y]===0;y++);for(f<y&&(f=y),c=1,v=1;v<=K;v++)if(c<<=1,c-=_[v],c<0)return-1;if(c>0&&(e===0||k!==1))return-1;for(S[1]=0,v=1;v<K;v++)S[v+1]=S[v]+_[v];for(u=0;u<o;u++)i[t+u]!==0&&(h[S[i[t+u]]++]=u);if(e===0?(b=Q=h,Z=20):e===1?(b=Tt,Q=Ot,Z=257):(b=Ut,Q=Dt,Z=0),m=0,u=0,v=y,d=r,g=f,E=0,A=-1,x=1<<f,w=x-1,e===1&&x>852||e===2&&x>592)return 1;for(;;){H=v-E,h[u]+1<Z?(q=0,J=h[u]):h[u]>=Z?(q=Q[h[u]-Z],J=b[h[u]-Z]):(q=96,J=0),n=1<<v-E,a=1<<g,y=a;do a-=n,s[d+(m>>E)+a]=H<<24|q<<16|J|0;while(a!==0);for(n=1<<v-1;m&n;)n>>=1;if(n!==0?(m&=n-1,m+=n):m=0,u++,--_[v]==0){if(v===k)break;v=i[t+h[u]]}if(v>f&&(m&w)!==A){for(E===0&&(E=f),d+=y,g=v-E,c=1<<g;g+E<k&&(c-=_[g+E],!(c<=0));)g++,c<<=1;if(x+=1<<g,e===1&&x>852||e===2&&x>592)return 1;A=m&w,s[A]=f<<24|g<<16|d-r|0}}return m!==0&&(s[d+m]=v-E<<24|64<<16|0),l.bits=f,0},V={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{Z_FINISH:lt,Z_BLOCK:Bt,Z_TREES:P,Z_OK:C,Z_STREAM_END:It,Z_NEED_DICT:Ct,Z_STREAM_ERROR:T,Z_DATA_ERROR:dt,Z_MEM_ERROR:ft,Z_BUF_ERROR:Nt,Z_DEFLATED:ht}=V,Y=16180,G=16190,D=16191,$=16192,tt=16194,X=16199,W=16200,et=16206,R=16209,ct=e=>(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24);function zt(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const N=e=>{if(!e)return 1;const i=e.state;return!i||i.strm!==e||i.mode<Y||i.mode>16211?1:0},ut=e=>{if(N(e))return T;const i=e.state;return e.total_in=e.total_out=i.total=0,e.msg="",i.wrap&&(e.adler=1&i.wrap),i.mode=Y,i.last=0,i.havedict=0,i.flags=-1,i.dmax=32768,i.head=null,i.hold=0,i.bits=0,i.lencode=i.lendyn=new Int32Array(852),i.distcode=i.distdyn=new Int32Array(592),i.sane=1,i.back=-1,C},wt=e=>{if(N(e))return T;const i=e.state;return i.wsize=0,i.whave=0,i.wnext=0,ut(e)},bt=(e,i)=>{let t;if(N(e))return T;const o=e.state;return i<0?(t=0,i=-i):(t=5+(i>>4),i<48&&(i&=15)),i&&(i<8||i>15)?T:(o.window!==null&&o.wbits!==i&&(o.window=null),o.wrap=t,o.wbits=i,wt(e))},mt=(e,i)=>{if(!e)return T;const t=new zt;e.state=t,t.strm=e,t.window=null,t.mode=Y;const o=bt(e,i);return o!==C&&(e.state=null),o};let it,nt,kt=!0;const Ft=e=>{if(kt){it=new Int32Array(512),nt=new Int32Array(32);let i=0;for(;i<144;)e.lens[i++]=8;for(;i<256;)e.lens[i++]=9;for(;i<280;)e.lens[i++]=7;for(;i<288;)e.lens[i++]=8;for(z(1,e.lens,0,288,it,0,e.work,{bits:9}),i=0;i<32;)e.lens[i++]=5;z(2,e.lens,0,32,nt,0,e.work,{bits:5}),kt=!1}e.lencode=it,e.lenbits=9,e.distcode=nt,e.distbits=5},_t=(e,i,t,o)=>{let s;const r=e.state;return r.window===null&&(r.wsize=1<<r.wbits,r.wnext=0,r.whave=0,r.window=new Uint8Array(r.wsize)),o>=r.wsize?(r.window.set(i.subarray(t-r.wsize,t),0),r.wnext=0,r.whave=r.wsize):(s=r.wsize-r.wnext,s>o&&(s=o),r.window.set(i.subarray(t-o,t-o+s),r.wnext),(o-=s)?(r.window.set(i.subarray(t-o,t),0),r.wnext=o,r.whave=r.wsize):(r.wnext+=s,r.wnext===r.wsize&&(r.wnext=0),r.whave<r.wsize&&(r.whave+=s))),0};var B={inflateReset:wt,inflateReset2:bt,inflateResetKeep:ut,inflateInit:e=>mt(e,15),inflateInit2:mt,inflate:(e,i)=>{let t,o,s,r,h,l,p,n,a,A,w,d,Z,v,u,y,k,f,g,E,c,x,m=0;const b=new Uint8Array(4);let _,S;const H=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(N(e)||!e.output||!e.input&&e.avail_in!==0)return T;t=e.state,t.mode===D&&(t.mode=$),h=e.next_out,s=e.output,p=e.avail_out,r=e.next_in,o=e.input,l=e.avail_in,n=t.hold,a=t.bits,A=l,w=p,x=C;t:for(;;)switch(t.mode){case Y:if(t.wrap===0){t.mode=$;break}for(;a<16;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(2&t.wrap&&n===35615){t.wbits===0&&(t.wbits=15),t.check=0,b[0]=255&n,b[1]=n>>>8&255,t.check=U(t.check,b,2,0),n=0,a=0,t.mode=16181;break}if(t.head&&(t.head.done=!1),!(1&t.wrap)||(((255&n)<<8)+(n>>8))%31){e.msg="incorrect header check",t.mode=R;break}if((15&n)!==ht){e.msg="unknown compression method",t.mode=R;break}if(n>>>=4,a-=4,c=8+(15&n),t.wbits===0&&(t.wbits=c),c>15||c>t.wbits){e.msg="invalid window size",t.mode=R;break}t.dmax=1<<t.wbits,t.flags=0,e.adler=t.check=1,t.mode=512&n?16189:D,n=0,a=0;break;case 16181:for(;a<16;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(t.flags=n,(255&t.flags)!==ht){e.msg="unknown compression method",t.mode=R;break}if(57344&t.flags){e.msg="unknown header flags set",t.mode=R;break}t.head&&(t.head.text=n>>8&1),512&t.flags&&4&t.wrap&&(b[0]=255&n,b[1]=n>>>8&255,t.check=U(t.check,b,2,0)),n=0,a=0,t.mode=16182;case 16182:for(;a<32;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.head&&(t.head.time=n),512&t.flags&&4&t.wrap&&(b[0]=255&n,b[1]=n>>>8&255,b[2]=n>>>16&255,b[3]=n>>>24&255,t.check=U(t.check,b,4,0)),n=0,a=0,t.mode=16183;case 16183:for(;a<16;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.head&&(t.head.xflags=255&n,t.head.os=n>>8),512&t.flags&&4&t.wrap&&(b[0]=255&n,b[1]=n>>>8&255,t.check=U(t.check,b,2,0)),n=0,a=0,t.mode=16184;case 16184:if(1024&t.flags){for(;a<16;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.length=n,t.head&&(t.head.extra_len=n),512&t.flags&&4&t.wrap&&(b[0]=255&n,b[1]=n>>>8&255,t.check=U(t.check,b,2,0)),n=0,a=0}else t.head&&(t.head.extra=null);t.mode=16185;case 16185:if(1024&t.flags&&(d=t.length,d>l&&(d=l),d&&(t.head&&(c=t.head.extra_len-t.length,t.head.extra||(t.head.extra=new Uint8Array(t.head.extra_len)),t.head.extra.set(o.subarray(r,r+d),c)),512&t.flags&&4&t.wrap&&(t.check=U(t.check,o,d,r)),l-=d,r+=d,t.length-=d),t.length))break t;t.length=0,t.mode=16186;case 16186:if(2048&t.flags){if(l===0)break t;d=0;do c=o[r+d++],t.head&&c&&t.length<65536&&(t.head.name+=String.fromCharCode(c));while(c&&d<l);if(512&t.flags&&4&t.wrap&&(t.check=U(t.check,o,d,r)),l-=d,r+=d,c)break t}else t.head&&(t.head.name=null);t.length=0,t.mode=16187;case 16187:if(4096&t.flags){if(l===0)break t;d=0;do c=o[r+d++],t.head&&c&&t.length<65536&&(t.head.comment+=String.fromCharCode(c));while(c&&d<l);if(512&t.flags&&4&t.wrap&&(t.check=U(t.check,o,d,r)),l-=d,r+=d,c)break t}else t.head&&(t.head.comment=null);t.mode=16188;case 16188:if(512&t.flags){for(;a<16;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(4&t.wrap&&n!==(65535&t.check)){e.msg="header crc mismatch",t.mode=R;break}n=0,a=0}t.head&&(t.head.hcrc=t.flags>>9&1,t.head.done=!0),e.adler=t.check=0,t.mode=D;break;case 16189:for(;a<32;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}e.adler=t.check=ct(n),n=0,a=0,t.mode=G;case G:if(t.havedict===0)return e.next_out=h,e.avail_out=p,e.next_in=r,e.avail_in=l,t.hold=n,t.bits=a,Ct;e.adler=t.check=1,t.mode=D;case D:if(i===Bt||i===P)break t;case $:if(t.last){n>>>=7&a,a-=7&a,t.mode=et;break}for(;a<3;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}switch(t.last=1&n,n>>>=1,a-=1,3&n){case 0:t.mode=16193;break;case 1:if(Ft(t),t.mode=X,i===P){n>>>=2,a-=2;break t}break;case 2:t.mode=16196;break;case 3:e.msg="invalid block type",t.mode=R}n>>>=2,a-=2;break;case 16193:for(n>>>=7&a,a-=7&a;a<32;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if((65535&n)!=(n>>>16^65535)){e.msg="invalid stored block lengths",t.mode=R;break}if(t.length=65535&n,n=0,a=0,t.mode=tt,i===P)break t;case tt:t.mode=16195;case 16195:if(d=t.length,d){if(d>l&&(d=l),d>p&&(d=p),d===0)break t;s.set(o.subarray(r,r+d),h),l-=d,r+=d,p-=d,h+=d,t.length-=d;break}t.mode=D;break;case 16196:for(;a<14;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(t.nlen=257+(31&n),n>>>=5,a-=5,t.ndist=1+(31&n),n>>>=5,a-=5,t.ncode=4+(15&n),n>>>=4,a-=4,t.nlen>286||t.ndist>30){e.msg="too many length or distance symbols",t.mode=R;break}t.have=0,t.mode=16197;case 16197:for(;t.have<t.ncode;){for(;a<3;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.lens[H[t.have++]]=7&n,n>>>=3,a-=3}for(;t.have<19;)t.lens[H[t.have++]]=0;if(t.lencode=t.lendyn,t.lenbits=7,_={bits:t.lenbits},x=z(0,t.lens,0,19,t.lencode,0,t.work,_),t.lenbits=_.bits,x){e.msg="invalid code lengths set",t.mode=R;break}t.have=0,t.mode=16198;case 16198:for(;t.have<t.nlen+t.ndist;){for(;m=t.lencode[n&(1<<t.lenbits)-1],u=m>>>24,y=m>>>16&255,k=65535&m,!(u<=a);){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(k<16)n>>>=u,a-=u,t.lens[t.have++]=k;else{if(k===16){for(S=u+2;a<S;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(n>>>=u,a-=u,t.have===0){e.msg="invalid bit length repeat",t.mode=R;break}c=t.lens[t.have-1],d=3+(3&n),n>>>=2,a-=2}else if(k===17){for(S=u+3;a<S;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}n>>>=u,a-=u,c=0,d=3+(7&n),n>>>=3,a-=3}else{for(S=u+7;a<S;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}n>>>=u,a-=u,c=0,d=11+(127&n),n>>>=7,a-=7}if(t.have+d>t.nlen+t.ndist){e.msg="invalid bit length repeat",t.mode=R;break}for(;d--;)t.lens[t.have++]=c}}if(t.mode===R)break;if(t.lens[256]===0){e.msg="invalid code -- missing end-of-block",t.mode=R;break}if(t.lenbits=9,_={bits:t.lenbits},x=z(1,t.lens,0,t.nlen,t.lencode,0,t.work,_),t.lenbits=_.bits,x){e.msg="invalid literal/lengths set",t.mode=R;break}if(t.distbits=6,t.distcode=t.distdyn,_={bits:t.distbits},x=z(2,t.lens,t.nlen,t.ndist,t.distcode,0,t.work,_),t.distbits=_.bits,x){e.msg="invalid distances set",t.mode=R;break}if(t.mode=X,i===P)break t;case X:t.mode=W;case W:if(l>=6&&p>=258){e.next_out=h,e.avail_out=p,e.next_in=r,e.avail_in=l,t.hold=n,t.bits=a,St(e,w),h=e.next_out,s=e.output,p=e.avail_out,r=e.next_in,o=e.input,l=e.avail_in,n=t.hold,a=t.bits,t.mode===D&&(t.back=-1);break}for(t.back=0;m=t.lencode[n&(1<<t.lenbits)-1],u=m>>>24,y=m>>>16&255,k=65535&m,!(u<=a);){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(y&&!(240&y)){for(f=u,g=y,E=k;m=t.lencode[E+((n&(1<<f+g)-1)>>f)],u=m>>>24,y=m>>>16&255,k=65535&m,!(f+u<=a);){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}n>>>=f,a-=f,t.back+=f}if(n>>>=u,a-=u,t.back+=u,t.length=k,y===0){t.mode=16205;break}if(32&y){t.back=-1,t.mode=D;break}if(64&y){e.msg="invalid literal/length code",t.mode=R;break}t.extra=15&y,t.mode=16201;case 16201:if(t.extra){for(S=t.extra;a<S;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.length+=n&(1<<t.extra)-1,n>>>=t.extra,a-=t.extra,t.back+=t.extra}t.was=t.length,t.mode=16202;case 16202:for(;m=t.distcode[n&(1<<t.distbits)-1],u=m>>>24,y=m>>>16&255,k=65535&m,!(u<=a);){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(!(240&y)){for(f=u,g=y,E=k;m=t.distcode[E+((n&(1<<f+g)-1)>>f)],u=m>>>24,y=m>>>16&255,k=65535&m,!(f+u<=a);){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}n>>>=f,a-=f,t.back+=f}if(n>>>=u,a-=u,t.back+=u,64&y){e.msg="invalid distance code",t.mode=R;break}t.offset=k,t.extra=15&y,t.mode=16203;case 16203:if(t.extra){for(S=t.extra;a<S;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}t.offset+=n&(1<<t.extra)-1,n>>>=t.extra,a-=t.extra,t.back+=t.extra}if(t.offset>t.dmax){e.msg="invalid distance too far back",t.mode=R;break}t.mode=16204;case 16204:if(p===0)break t;if(d=w-p,t.offset>d){if(d=t.offset-d,d>t.whave&&t.sane){e.msg="invalid distance too far back",t.mode=R;break}d>t.wnext?(d-=t.wnext,Z=t.wsize-d):Z=t.wnext-d,d>t.length&&(d=t.length),v=t.window}else v=s,Z=h-t.offset,d=t.length;d>p&&(d=p),p-=d,t.length-=d;do s[h++]=v[Z++];while(--d);t.length===0&&(t.mode=W);break;case 16205:if(p===0)break t;s[h++]=t.length,p--,t.mode=W;break;case et:if(t.wrap){for(;a<32;){if(l===0)break t;l--,n|=o[r++]<<a,a+=8}if(w-=p,e.total_out+=w,t.total+=w,4&t.wrap&&w&&(e.adler=t.check=t.flags?U(t.check,s,w,h-w):I(t.check,s,w,h-w)),w=p,4&t.wrap&&(t.flags?n:ct(n))!==t.check){e.msg="incorrect data check",t.mode=R;break}n=0,a=0}t.mode=16207;case 16207:if(t.wrap&&t.flags){for(;a<32;){if(l===0)break t;l--,n+=o[r++]<<a,a+=8}if(4&t.wrap&&n!==(4294967295&t.total)){e.msg="incorrect length check",t.mode=R;break}n=0,a=0}t.mode=16208;case 16208:x=It;break t;case R:x=dt;break t;case 16210:return ft;default:return T}return e.next_out=h,e.avail_out=p,e.next_in=r,e.avail_in=l,t.hold=n,t.bits=a,(t.wsize||w!==e.avail_out&&t.mode<R&&(t.mode<et||i!==lt))&&_t(e,e.output,e.next_out,w-e.avail_out),A-=e.avail_in,w-=e.avail_out,e.total_in+=A,e.total_out+=w,t.total+=w,4&t.wrap&&w&&(e.adler=t.check=t.flags?U(t.check,s,w,e.next_out-w):I(t.check,s,w,e.next_out-w)),e.data_type=t.bits+(t.last?64:0)+(t.mode===D?128:0)+(t.mode===X||t.mode===tt?256:0),(A===0&&w===0||i===lt)&&x===C&&(x=Nt),x},inflateEnd:e=>{if(N(e))return T;let i=e.state;return i.window&&(i.window=null),e.state=null,C},inflateGetHeader:(e,i)=>{if(N(e))return T;const t=e.state;return 2&t.wrap?(t.head=i,i.done=!1,C):T},inflateSetDictionary:(e,i)=>{const t=i.length;let o,s,r;return N(e)?T:(o=e.state,o.wrap!==0&&o.mode!==G?T:o.mode===G&&(s=1,s=I(s,i,t,0),s!==o.check)?dt:(r=_t(e,i,t,t),r?(o.mode=16210,ft):(o.havedict=1,C)))},inflateInfo:"pako inflate (from Nodeca project)"};const Lt=(e,i)=>Object.prototype.hasOwnProperty.call(e,i);var Mt=function(e){const i=Array.prototype.slice.call(arguments,1);for(;i.length;){const t=i.shift();if(t){if(typeof t!="object")throw new TypeError(t+"must be non-object");for(const o in t)Lt(t,o)&&(e[o]=t[o])}}return e},Ht=e=>{let i=0;for(let o=0,s=e.length;o<s;o++)i+=e[o].length;const t=new Uint8Array(i);for(let o=0,s=0,r=e.length;o<r;o++){let h=e[o];t.set(h,s),s+=h.length}return t};let gt=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{gt=!1}const F=new Uint8Array(256);for(let e=0;e<256;e++)F[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;F[254]=F[254]=1;var jt=e=>{if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(e);let i,t,o,s,r,h=e.length,l=0;for(s=0;s<h;s++)t=e.charCodeAt(s),(64512&t)==55296&&s+1<h&&(o=e.charCodeAt(s+1),(64512&o)==56320&&(t=65536+(t-55296<<10)+(o-56320),s++)),l+=t<128?1:t<2048?2:t<65536?3:4;for(i=new Uint8Array(l),r=0,s=0;r<l;s++)t=e.charCodeAt(s),(64512&t)==55296&&s+1<h&&(o=e.charCodeAt(s+1),(64512&o)==56320&&(t=65536+(t-55296<<10)+(o-56320),s++)),t<128?i[r++]=t:t<2048?(i[r++]=192|t>>>6,i[r++]=128|63&t):t<65536?(i[r++]=224|t>>>12,i[r++]=128|t>>>6&63,i[r++]=128|63&t):(i[r++]=240|t>>>18,i[r++]=128|t>>>12&63,i[r++]=128|t>>>6&63,i[r++]=128|63&t);return i},Kt=(e,i)=>{const t=i||e.length;if(typeof TextDecoder=="function"&&TextDecoder.prototype.decode)return new TextDecoder().decode(e.subarray(0,i));let o,s;const r=new Array(2*t);for(s=0,o=0;o<t;){let h=e[o++];if(h<128){r[s++]=h;continue}let l=F[h];if(l>4)r[s++]=65533,o+=l-1;else{for(h&=l===2?31:l===3?15:7;l>1&&o<t;)h=h<<6|63&e[o++],l--;l>1?r[s++]=65533:h<65536?r[s++]=h:(h-=65536,r[s++]=55296|h>>10&1023,r[s++]=56320|1023&h)}}return((h,l)=>{if(l<65534&&h.subarray&&gt)return String.fromCharCode.apply(null,h.length===l?h:h.subarray(0,l));let p="";for(let n=0;n<l;n++)p+=String.fromCharCode(h[n]);return p})(r,s)},Pt=(e,i)=>{(i=i||e.length)>e.length&&(i=e.length);let t=i-1;for(;t>=0&&(192&e[t])==128;)t--;return t<0||t===0?i:t+F[e[t]]>i?t:i},at={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Yt=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Gt=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};const pt=Object.prototype.toString,{Z_NO_FLUSH:Xt,Z_FINISH:Wt,Z_OK:L,Z_STREAM_END:rt,Z_NEED_DICT:ot,Z_STREAM_ERROR:qt,Z_DATA_ERROR:vt,Z_MEM_ERROR:Jt}=V;function M(e){this.options=Mt({chunkSize:65536,windowBits:15,to:""},e||{});const i=this.options;i.raw&&i.windowBits>=0&&i.windowBits<16&&(i.windowBits=-i.windowBits,i.windowBits===0&&(i.windowBits=-15)),!(i.windowBits>=0&&i.windowBits<16)||e&&e.windowBits||(i.windowBits+=32),i.windowBits>15&&i.windowBits<48&&!(15&i.windowBits)&&(i.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Yt,this.strm.avail_out=0;let t=B.inflateInit2(this.strm,i.windowBits);if(t!==L)throw new Error(at[t]);if(this.header=new Gt,B.inflateGetHeader(this.strm,this.header),i.dictionary&&(typeof i.dictionary=="string"?i.dictionary=jt(i.dictionary):pt.call(i.dictionary)==="[object ArrayBuffer]"&&(i.dictionary=new Uint8Array(i.dictionary)),i.raw&&(t=B.inflateSetDictionary(this.strm,i.dictionary),t!==L)))throw new Error(at[t])}function st(e,i){const t=new M(i);if(t.push(e),t.err)throw t.msg||at[t.err];return t.result}M.prototype.push=function(e,i){const t=this.strm,o=this.options.chunkSize,s=this.options.dictionary;let r,h,l;if(this.ended)return!1;for(h=i===~~i?i:i===!0?Wt:Xt,pt.call(e)==="[object ArrayBuffer]"?t.input=new Uint8Array(e):t.input=e,t.next_in=0,t.avail_in=t.input.length;;){for(t.avail_out===0&&(t.output=new Uint8Array(o),t.next_out=0,t.avail_out=o),r=B.inflate(t,h),r===ot&&s&&(r=B.inflateSetDictionary(t,s),r===L?r=B.inflate(t,h):r===vt&&(r=ot));t.avail_in>0&&r===rt&&t.state.wrap>0&&e[t.next_in]!==0;)B.inflateReset(t),r=B.inflate(t,h);switch(r){case qt:case vt:case ot:case Jt:return this.onEnd(r),this.ended=!0,!1}if(l=t.avail_out,t.next_out&&(t.avail_out===0||r===rt))if(this.options.to==="string"){let p=Pt(t.output,t.next_out),n=t.next_out-p,a=Kt(t.output,p);t.next_out=n,t.avail_out=o-n,n&&t.output.set(t.output.subarray(p,p+n),0),this.onData(a)}else this.onData(t.output.length===t.next_out?t.output:t.output.subarray(0,t.next_out));if(r!==L||l!==0){if(r===rt)return r=B.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,!0;if(t.avail_in===0)break}}return!0},M.prototype.onData=function(e){this.chunks.push(e)},M.prototype.onEnd=function(e){e===L&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=Ht(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var xt=M,yt=st,Et=function(e,i){return(i=i||{}).raw=!0,st(e,i)},Rt=st,At=V,Qt={Inflate:xt,inflate:yt,inflateRaw:Et,ungzip:Rt,constants:At};O.Inflate=xt,O.constants=At,O.default=Qt,O.inflate=yt,O.inflateRaw=Et,O.ungzip=Rt,Object.defineProperty(O,"__esModule",{value:!0})});
