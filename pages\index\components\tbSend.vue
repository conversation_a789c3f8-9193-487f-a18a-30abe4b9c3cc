<template>
	<view class="">
		<uni-popup ref="popup" :safe-area="true" type="bottom">
			<view class="hb">
				<!-- 上边间距 -->
				<view class="title"></view>
				<view class="list">
					<!-- <view class="li" @click="send('store')" v-if="userInfo.is_business == 2">
					<view class="imagebox">
						<image src="@/static/map/hb-store.png" mode=""></image>
					</view>
					<text>商家红包</text>
				</view> -->
					<view class="li" @click="joinStore()">
						<view class="imagebox">
							<image src="@/static/images/index/dongtai.png" mode="aspectFill"></image>
						</view>
						<text>发动态</text>
					</view>
					<view class="li" @click="sendHD()">
						<view class="imagebox">
							<image src="@/static/images/index/huodong.png" mode="aspectFill"></image>
						</view>
						<text>发活动</text>
					</view>
					<view class="li" @click="handleMsg()">
						<view class="imagebox">
							<image src="@/static/images/index/liuyan.png" mode="aspectFill"></image>
						</view>
						<text>去留言</text>
					</view>
					<view class="li" @click="goNav('/pages/leaveAMessage/leaveAMessage')">
						<view class="imagebox">
							<image src="@/static/images/index/liebiao.png" mode="aspectFill"></image>
						</view>
						<text>留言列表</text>
					</view>
				</view>
				<view class="" style="margin: 50px auto; width: 33px; height: 33px; padding: 9px" @click="close()">
					<image src="../../../static/active/close.png" mode="" style="width: 15px; height: 15px"></image>
				</view>
				<view class="img24" />
				<!-- <u-toast ref='notify' /> -->
			</view>
		</uni-popup>

		<MsgPopup ref="msgPopup" @msg="getMsg" />
		<SendMsg ref="SendMsgPopup" :type="type" @send="send" />
	</view>
</template>

<script>
	import {
		apiGetRedpacketDetail
	} from "@/api/common.js";
	import MsgPopup from "../components/msg.vue";
	import SendMsg from "../components/sendMsg.vue";
	export default {
		components: {
			MsgPopup,
			SendMsg,
		},
		data() {
			return {
				redPacketInfo: {},
				userInfo: uni.getStorageSync("userInfo"),
				current: null,
				type: "",
				leaveAMessage: {},
			};
		},
		filters: {},
		mounted() {},
		methods: {
			joinStore() {
				const imgArr = []
				uni.chooseImage({
					count: 9,
					sourceType: ["album"],
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							imgArr.push(img)
						}, )).then(() => {
							this.close();
							uni.navigateTo({
								url: "/pages/send/send?pages=" + this.current + '&imgArr=' +
									encodeURIComponent(JSON.stringify(imgArr)),
							});

						})
					}
				});

			},
			sendHD() {
				uni.navigateTo({
					// url: '/pages/business/business?mode=noRegister'
					url: "/pages/activity/create",
				});
				this.close();
			},
			open(option) {
				this.current = option;
				this.$http.get("/api/user/info").then((res) => {
					this.userInfo = res.message.user_info;
					uni.setStorageSync("userInfo", res.message.user_info);
					this.$refs.popup.open("center");
				});
			},
			close() {
				this.$refs.popup.close();
			},
			handleMsg() {
				console.log(this.$refs.msgPopup);
				this.$refs.popup.close();
				this.$nextTick(() => {
					this.$refs.msgPopup.open();
				});
			},
			goNav(url) {
				this.$refs.popup.close();
				this.$common.navigateTo({
					url,
				});
			},
			getMsg(info) {
				this.type = "1";
				this.leaveAMessage = info;
				this.$refs.msgPopup.close();
				this.$refs.SendMsgPopup.openSend(info);
			},
			send(txt) {
				this.$http
					.post("/api/location-msg/add", {
						...this.leaveAMessage,
						content: txt,
					})
					.then((res) => {
						this.$refs.SendMsgPopup.close();
						this.toast("留言成功");
					});
			},
		},
	};
</script>

<style scoped lang="scss">
	.uni-popup {
		z-index: 999;
	}

	.hb {
		width: 750rpx;
		height: 100vh;
		padding: 50vh 32rpx 0;
		box-sizing: border-box;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 20rpx;
		position: relative;

		&::after {
			position: absolute;
			left: 0;
			bottom: -120rpx;
			height: 120rpx;
			width: 750rpx;
			content: "";
			z-index: 2;
			background-color: rgba(0, 0, 0, 0.5);
		}

		.title {
			width: 228rpx;
			height: 300rpx;
			font-size: 38rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			color: #ffffff;
			line-height: 55rpx;

			margin-bottom: 0x;
		}

		.list {
			display: flex;
			align-items: center;
			justify-content: space-around;

			.li {
				display: flex;
				flex-direction: column;
				align-items: center;

				.imagebox {
					width: 132rpx;
					height: 132rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgb(240, 240, 240);
					border-radius: 20rpx;
					opacity: 1;
					margin-bottom: 16rpx;

					image {
						width: 64rpx;
						height: 64rpx;
					}
				}

				text {
					width: 128rpx;
					height: 46rpx;
					font-size: 32rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: rgba(255, 255, 255, 0.72);
					line-height: 46rpx;
					text-align: center;
				}
			}
		}
	}
</style>