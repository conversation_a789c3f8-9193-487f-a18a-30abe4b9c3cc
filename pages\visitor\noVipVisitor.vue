<template>
	<view>
		<view class="t_betweent_center" style="padding: 32rpx;">
			<view class="">
				<view class="t_zt">隐身访问</view>
				<view class="t_font_grey" style="margin-top: 16rpx;">打开后访问他人主页不留足迹</view>
			</view>
			<u-switch v-model="checked" inactive-color="#eee" size="45" @input="invisibleAccess"></u-switch>
		</view>
		<scroll-view scroll-y="true" style="height: 88vh;" :lower-threshold="200" crolltoupper="upper"
			@scrolltolower="lower">
			<view class="item" v-for="(item,index) in arr" :key="index">
				<view class="t_display">

					<image class="avatar" :src="item.userinfo.avatar" mode="aspectFill"
						@click="goNav('/pages/otherPage/otherPage?uuid='+item.userinfo.uuid,item.hide)">
					</image>
					<!-- <view class="masking"></view> -->
					<!-- <image class="masking" src="../../static/images/vip/touxiangzhezhao.png" mode=""></image> -->
					<view class="name">Ta访问了你<span style="color: #a4a5ff;padding: 0 10rpx;">{{item.access_total}}</span>
						次</view>
				</view>
				<!-- //0陌生人 1是我关注的  2是互相关注的 3关注我的 4我自己 -->
				<view class="fous" @click="vipTips">
					揭秘访客
				</view>
			</view>
			<view class="btn" @click="vipTips" v-if="arr.length>0">解锁谁看过我</view>
			<view style="height:67rpx ;" />
		</scroll-view>
		<u-toast ref='notify' />
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirmVip" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgCurrent: 0,
				arr: [],
				total: 0,
				checked: false,
				vipLevel: "",
				pageConfig: {
					page: 1,
					size: 10,
				}
			}
		},
		async onLoad() {
			let userInfo = ""
			await this.$http.get('/api/user/info').then(async (res) => {
				userInfo = res.message
			})
			// this.checked = uni.getStorageSync('switch').includes(4)
			this.vipLevel = userInfo.vip_level
			this.getData()
		},
		methods: {
			vipTips() {
				if (this.vipLevel != 2) {
					this.imgCurrent = 3
					this.$refs.vipTips.open()
				}
			},
			confirmVip() {
				this.goVip('/pages/vipCenter/vipCenter')
			},
			invisibleAccess(val) {
				if (this.vipLevel != 2) {
					this.checked = !val
					this.imgCurrent = 5
					this.$refs.vipTips.open()
				} else {
					this.$http.post('/api/user/set-switch', {
						"switch_type": 4,
						"is_on": ~~val
					})
				}
			},
			goVip(url) {
				uni.redirectTo({
					url
				})
			},
			goNav(url, flag) {
				if (flag) return
				uni.navigateTo({
					url
				})
			},
			cancelFous(uuid, ids) {
				this.$http.post('/api/user/follow/del', {
					uuid
				}).then(res => {
					this.arr[ids].relation = res.message
				})
			},
			addFous(uuid, ids) {
				this.$http.post('/api/user/follow/add', {
					uuid
				}).then(res => {
					this.arr[ids].relation = 1
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			lower() {
				if (this.total > 0) {
					this.pageConfig.page++
					this.getData()
				}
			},
			getData() {
				this.$http.get('/api/guest/listv2', {
					...this.pageConfig
				}).then(res => {
					this.total = res.message.length
					this.arr.push(...res.message)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.btn {
		font-weight: 400;
		font-size: 32rpx;
		width: 686rpx;
		height: 98rpx;
		line-height: 98rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 140rpx 140rpx 140rpx 140rpx;
		text-align: center;
		margin: 32rpx;
	}

	.item {
		padding: 24rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.fous {
			width: 148rpx;
			height: 58rpx;
			text-align: center;
			line-height: 58rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
		}

		.avatar {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			border: 2rpx solid #FFFFFF;
			box-sizing: border-box;
			// &::before {
			// 	content: "";
			// 	width: 110rpx;
			// 	height: 110rpx;
			// 	border-radius: 50%;
			// 	background-color: rgba(0, 0, 0, 0.36);
			// 	filter: blur(36rpx);
			// 	-webkit-backdrop-filter: blur(36rpx);
			// 	backdrop-filter: blur(36rpx);
			// 	position: absolute;
			// }
		}

		.masking {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			background: linear-gradient(45deg, #585453 40%, #6c6663 100%);
			opacity: 0.95;
			position: absolute;
			filter: blur(5px);
			-webkit-filter: blur(5px);
			z-index: 11;
		}

		.name {
			margin-left: 42rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>