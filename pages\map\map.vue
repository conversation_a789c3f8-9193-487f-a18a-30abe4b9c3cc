<template>
	<view class="map-page">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode="widthFix"></image>
			<view class="nav-title">模型展示地点</view>
		</view>
		<view v-if="!isShowMap" class="page-main">
			<view class="page-main-content">
				<!-- 搜索 -->


				<!-- 地图 -->
				<!-- <map id="map" class="map" zIndex="1" ref="map" :style="{width: '100%',height:screenHeight + 'px'}"
					:latitude="latitude" :longitude="longitude" scale="14" :markers="markerData"
					@callouttap="onCalloutTap" :show-compass='true' show-location>
					<cover-view class="search-container" :style="{top:searchTop + 'px'}">
						<cover-view class="search-input">
							<cover-view class="search-input-text" @click="isShowMap = true">
								<cover-image style="width:20px;height:20px;margin-left:8rpx;" mode="widthFix" src="../../static/images/search-icon.png"></cover-image>
								<cover-view class="form-item-input" @click="isShowMap = true">搜索地址</cover-view>
							</cover-view>
							<cover-view class="search-input-btn" @click="isShowMap = true">搜索</cover-view>
						</cover-view>
					</cover-view>
					<cover-view slot="callout" class="address-bubble-content">
						<block v-for="(item,index) in markerData" :key="index">
							<cover-view class="address-bubble-describe">北京测试</cover-view>
							<cover-view class="address-bubble-button">
								<cover-view id="confirmBtn" class="address-bubble-confirm">确认</cover-view>
								<cover-view id="cancelBtn" class="address-bubble-cancel">取消</cover-view>
							</cover-view>
						</block>
					</cover-view>
					<cover-view class="submit-container">
						<cover-image class="submit-btn" src="../../static/images/submit-btn.png" @click="submit">提交审核</cover-image>
					</cover-view>
				</map> -->
				
				<liu-easy-map class="map-container" :style="{width: '100%',height:screenHeight + 'px'}" ref="liuEasyMap" :centerLat="'36.05709'" :centerLng="'103.82538'" :scale="14"
					:markerData="markerDatas" :polygons="polygonstest"></liu-easy-map>
				<u-toast ref='notify' />
			</view>
		</view>

		<!-- 搜索地址 -->
		<view v-if="isShowMap" class="search-map-container">
			<!-- 搜索 -->
			<view class="search-container">
				<view class="search-input">
					<view class="search-input-text">
						<image style="width:20px;height:20px;margin-right: 8rpx;" mode="widthFix" src="../../static/images/search-icon.png"></image>
						<!-- <uni-icons type="search" color="#000000" style="margin-right: 8rpx;"></uni-icons> -->
						<input id='tipinput' class="form-item-input" focus="true" v-model="keyword" @input="getAddress"
							placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="搜索地址" />
					</view>
					<view class="search-input-btn" @click="getAddress">搜索</view>
				</view>
				<view v-if="addressArr && addressArr.length>0" class="search-result">
					<view class="search-result-title">搜索地址结果</view>
					<scroll-view scroll-y="true" style="max-height: 600rpx;" @scrolltolower="addressBottom">
						<view class="search-result-list">
							<view class="search-result-list-item" v-for="(item,index) in addressArr" :key="index"
								@click="goNavLoaction(item)">
								<image class="position-icon" mode="widthFix">
								</image>
								<view class="search-result-list-item-position">{{item.name}}</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "MapContainer",
		// props: {
		// 	knownLnglat: {
		// 		type: Array,
		// 		default: () => [],
		// 	},
		// },
		data() {
			return {
				screenHeight: uni.getSystemInfoSync().screenHeight - uni.getSystemInfoSync().statusBarHeight - 44,
				// searchTop:uni.getSystemInfoSync().statusBarHeight + 44,
				searchTop: 140,
				longitude: 123.599861,
				latitude: 43.758234,
				MapGeolocation: null,
				mouseTool: null,
				polygon: null,
				polyEditor: null,
				autoOptions: {
					input: "tishikuang", //绑定的搜索关键字的input标签ID，用这个注册
				},
				auto: null,
				keyword: "", //绑定的搜索关键字的的内容
				placeSearch: null,
				searchHere: null, //根据搜索组件搜索到以后的地点信息
				lng: "",
				lat: "",
				options: [], // 可选数据列表，详见组件文档
				selectedOptions: [], // 当前已选数据
				addressList: [],
				marker: null,
				infoWindow: null,
				AMap: null,
				Geocoder: null,
				currentAddress: {},
				business_id: "",

				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: '',
					location: "",
					city: '',
					region: '',
					output: 'JSON',
					city_limit: true,
					sortrule: 'distance',
				},
				addressArr: [],
				total: 0,
				markerData: [],
				isShowMap: false,
				markerDatas: [{
				    id: 1,
				    name: '兰州市政府', //标记点展示名字
				    address: '甘肃省兰州市城关区人民政府',
				    latitude: '36.05989', //标记点纬度
				    longitude: '103.83502', //标记点经度
				    markerUrl: 'https://img0.baidu.com/it/u=550544800,2229099292&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', //标记点图标地址
				    iconWidth: 32, //标记点图标宽度
				    iconHeight: 32, //标记点图标高度
				    calloutColor: '#ffffff', //气泡窗口 文本颜色
				    calloutFontSize: 14, //气泡窗口 文本大小
				    calloutBorderRadius: 6, //气泡窗口 边框圆角
				    calloutPadding: 8, //气泡窗口 文本边缘留白
				    calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
				    calloutDisplay: 'ALWAYS', //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
				}, {
				    id: 2,
				    name: '测试地址', //标记点展示名字
				    address: '测试地址详细地址测试地址详细地址测试地址详细地址测试地址详细地址',
				    latitude: "36.05064",
				    longitude: "103.82538"
				}],
				//展示区域点位信息
				polygonstest: [{
				    points: [{
				        latitude: "36.06637",
				        longitude: "103.82471"
				    }, {
				        latitude: "36.06255",
				        longitude: "103.82321"
				    }, {
				        latitude: "36.06234",
				        longitude: "103.81928"
				    }, {
				        latitude: "36.06036",
				        longitude: "103.82175"
				    }, {
				        latitude: "36.05653",
				        longitude: "103.82152"
				    }, {
				        latitude: "36.05953",
				        longitude: "103.82476"
				    }, {
				        latitude: "36.05690",
				        longitude: "103.82785"
				    }, {
				        latitude: "36.06023",
				        longitude: "103.82747"
				    }, {
				        latitude: "36.06243",
				        longitude: "103.83014"
				    }, {
				        latitude: "36.06245",
				        longitude: "103.82616"
				    }], //经纬度数组
				    strokeWidth: 2, //描边的宽度
				    strokeColor: "#FF000060", //描边的颜色
				    fillColor: "#FF000090" //填充颜色
				}]
			}
		},
		onLoad(options) {
			if (options.shopId) {
				this.business_id = options.shopId;
			}
			this.getShopInfo()
			this.MapSearchForm.location = uni.getStorageSync('location')
		},
		mounted() {
			// this.initAMap();
		},
		methods: {
			goBack() {
				if (this.isShowMap) {
					this.isShowMap = false;
				} else {
					uni.navigateTo({
						url: '/pages/shop/shop'
					});
				}

			},
			// 获取商户信息
			getShopInfo() {
				this.$http.get('/api/user/business/get').then((res) => {
					var shopInfo = res.message;
					console.log('shopinfo', shopInfo);
					// this.lon = shopInfo.coordinate.split(',')[0];
					// this.lat = this.shopInfo.coordinate.split(',')[1];
					this.knownLnglat = shopInfo.coordinate
				});
			},

			// 搜索地址
			getAddress() {
				this.MapSearchForm.keywords = this.keyword
				this.markerData = []
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v5/place/around?parameters',
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: (res) => {
						if (res.statusCode == 200) {
							this.total = res.data.count
							if (this.MapSearchForm.page_num == 1) {
								this.addressArr = res.data.pois;
								return
							}
							this.addressArr = [...this.addressArr, ...res.data.pois]
						} else {
							this.toast('搜索地址信息出错');
						}
					},
					fail(err) {
						this.toast(err.errMsg);
					}
				})
			},
			addressBottom() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
					this.getAddress()
				}
			},
			goNavLoaction(item) {
				this.isShowMap = false;
				console.log(item)
				const locat = item.location.split(",")
				console.log(locat)
				this.keyword = ""; //清空
				this.addressArr = [];
				var address = item.pname + item.cityname + item.adname + item.address
				let marker = {
					id: 0,
					name: item.name, //标记点展示名字
					address: address,
					latitude: locat[1], //标记点纬度
					longitude: locat[0], //标记点经度
					iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
					title: item.name,
					customCallout: {
						content: `<cover-view class="address-bubble-content">
									<cover-view class="address-bubble-describe">${address}</cover-view>
									<cover-view class="address-bubble-button">
										<cover-view id="confirmBtn" class="address-bubble-confirm">确认</cover-view>
										<cover-view id="cancelBtn" class="address-bubble-cancel">取消</cover-view>
									</cover-view>
								</cover-view>`,
						// content: `地址: ${item.pname}${item.cityname}${item.adname}${item.address}\n[确认] [取消]`,
						anchorY: 0, // Y轴偏移量
						anchorX: 100, // X轴偏移量
						display: 'ALWAYS',
						textAlign: 'center',
						borderRadius: '6',
					}
				}
				this.markerData.push(marker);
				console.log(this.markerData)
				console.log("添加")
				// 更新地图的中心坐标
				this.latitude = locat[1];
				this.longitude = locat[0];
			},
			onCalloutTap(e) {
				console.log(e)
				console.log('Marker tapped:', e.detail.markerId);
				const markerId = e.detail.markerId;
				const marker = this.markerData.find(m => m.id === markerId);
				if (marker) {
					// 这里模拟点击确认和取消按钮的操作
					// 实际操作中，你可能需要自定义UI或者使用第三方库来创建按钮
					console.log('Marker tapped:', markerId);
					var that = this;
					// 确认
					document.getElementById('confirmBtn').addEventListener('click',
						function() {
							that.confirmAction(marker);
							marker.display = "BYCLICK";
						});
					// 取消
					document.getElementById('cancelBtn').addEventListener('click',
						function() {
							that.cancelAction(markerId);
						});
				}
			},
			confirmAction(marker) {
				console.log('确认按钮被点击，地址是：', marker);
				// 这里可以执行确认后的操作，例如打印地址
				this.currentAddress = {
					address: marker.address,
					latitude: marker.latitude,
					longitude: marker.longitude,
				}
				this.markerData = []
				let markers = {
					id: 0,
					name: marker.name, //标记点展示名字
					address: marker.address,
					latitude: marker.latitude, //标记点纬度
					longitude: marker.longitude, //标记点经度
					iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
				}
				this.markerData.push(markers);
			},
			cancelAction(markerId) {
				console.log('取消按钮被点击，移除marker:', markerId);
				// 移除marker
				this.removeMarker(markerId);
			},
			removeMarker(markerId) {
				this.markerData = this.markerData.filter(marker => marker.id !== markerId);
			},


			// 提交审核
			submit() {
				this.$http.post('/api/user/business/set-location', {
						address: this.currentAddress.address,
						latitude: this.currentAddress.latitude,
						longitude: this.currentAddress.longitude,
						business_id: this.business_id
					})
					.then((res) => {
						console.log(res);
						if (res.code == 200) {
							// 显示提交成功弹框
							uni.showToast({
								title: `提交成功！`
							})
							this.goBack()
						}
					});
			},
		},
		beforeDestroy() {
			// if (this.MapGeolocation) {
			// 	this.removeMarker();
			// 	this.MapGeolocation.destroy();
			// }
		},
	};
</script>
<style lang="scss">
	// map {
	// 	width: 100%;
	// 	height: calc(100vh - 44px);
	// 	/* 减去顶部导航栏的高度 */
	// 	position: relative;
	// 	z-index: 0;
	// }

	// page {
	// 	width: 750rpx;
	// 	min-height: 370px;
	// 	height: 100vh;
	// 	max-height: 100vh;
	// 	background-color: #F5F7FB;
	// 	position: relative;
	// }

	.map-page {
		width: 100%;
		height: 100vh;
		padding-top: 100px;
		box-sizing: border-box;
		// background-color: #F5F7FB;
		position: relative;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 140px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: absolute;
			z-index: 0;
			top: 0;
			left: 0;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.navigation-zhezhao {
			width: 100%;
			height: 140px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position: absolute;
			z-index: 10;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			height: 140px;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}



		.page-main {
			width: 100%;
			height: 85vh;
			position: absolute;
			top: 140px;
			left: 0;
			right: 0;
			bottom: 0;

			.page-main-content {
				width: 100%;
				height: 100%;
				position: relative;

				.submit-container {
					position: fixed;
					left: 0;
					right: 0;
					bottom: 0;
					z-index: 100;
					padding: 20px;
					box-sizing: border-box;

					.submit-btn {
						width: 100%;
						height: 52px;
						color: rgb(255, 255, 255);
						font-family: 阿里巴巴普惠体;
						font-size: 16px;
						font-weight: 400;
						line-height: 52px;
						letter-spacing: 0px;
						text-align: center;
						background-color: #52C3EE;
						// background-image: url('../../static/images/vip/active-add-button.png');
						// background-repeat: no-repeat;
						// background-size: 100% 100%;
						border-radius: 10px;
					}
				}

				.map {
					position: absolute;
					left: 0;
					right: 0;
					top: 0;
					bottom: 0;
					width: 100%;
					height: 100vh;
				}

				// .map-container {

				// 	// position: relative;
				// 	// z-index: 0;
				// 	#map {
				// 		width: 100%;
				// 		height: 100vh;
				// 		// z-index:1;
				// 	}
				// }
			}
		}

		.search-map-container {
			width: 100%;
			height: 85vh;
			position: absolute;
			top: 140px;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 10000;
			background-color: #efefef;
		}

		.search-container {
			position: fixed;
			left: 0;
			right: 0;
			z-index: 10000;
			padding: 16px 20px 0 20px;
			box-sizing: border-box;

			.search-input {
				width: 100%;
				// height: 38px;
				border-radius: 50px;
				background-color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 3px 3px 3px 16px;
				box-sizing: border-box;

				.search-input-text {
					flex:1;
					height:100%;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					padding: 3px 3px 3px 0px;
					box-sizing: border-box;
					.form-item-input {
						width: 100%;
						font-size: 14px;
						color: #9BA0AE;
						padding:0 14px;
						box-sizing: border-box;
					}

					/deep/.uni-input-input {
						color: #212121 !important;
						font-family: 阿里巴巴普惠体;
						font-size: 16px;
						font-weight: 400;
					}
				}

				.search-input-btn {
					width: 60px;
					height: 32px;
					border-radius: 50px;
					background-color: #52C3EE;
					// background: linear-gradient(270.00deg, rgb(190, 147, 250), rgb(82, 195, 238) 100%);
					color: rgb(255, 255, 255);
					font-family: 思源黑体;
					font-size: 14px;
					font-weight: 400;
					line-height: 32px;
					letter-spacing: 0px;
					text-align: center;
					margin-top:3px;
					margin-bottom:3px;
					margin-right:3px;
				}
			}

			.search-result {
				margin-top: 10px;
				width: 100%;
				border-radius: 10px;
				background: rgb(255, 255, 255);
				padding: 0 12px;
				box-sizing: border-box;

				.search-result-title {
					color: rgb(152, 152, 152);
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 400;
					line-height: 14px;
					letter-spacing: 0px;
					text-align: center;
					padding: 12px 0;
					box-sizing: border-box;
				}

				.search-result-list {
					border-top: 1px solid #F7F7F7;

					.search-result-list-item {
						padding: 12px 0;
						box-sizing: border-box;
						display: flex;
						align-items: center;

						.position-icon {
							width: 16px;
							height: 16px;
							margin-right: 2px;
						}

						.search-result-list-item-position {
							flex: 1;
							color: rgb(33, 33, 33);
							font-family: HarmonyOS Sans;
							font-size: 16px;
							font-weight: 500;
							line-height: 19px;
							letter-spacing: 0px;
							text-align: left;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}
			}
		}

	}

	/deep/.amap-overlay-text-container {
		width: 150px;
		height: auto;
		border-radius: 10px;
		padding: 12px 10px;
		box-sizing: border-box;
		word-break: break;
		text-align: left !important;

		.amap-info-close {
			display: none;
		}

		.address-bubble-content {
			.address-bubble-describe {
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 500;
				line-height: 16px;
				letter-spacing: 0px;
				text-align: left;
				text-indent: 0;
			}

			.address-bubble-button {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 8px;

				.address-bubble-confirm {
					width: 45%;
					height: 22px;
					border-radius: 60px;
					background: rgb(83, 194, 238);
					color: rgb(255, 255, 255);
					font-family: HarmonyOS Sans;
					font-size: 10px;
					font-weight: 400;
					line-height: 12px;
					letter-spacing: 0px;
					text-align: center;
					border: none;
					outline: none;
				}

				.address-bubble-cancel {
					width: 45%;
					height: 22px;
					box-sizing: border-box;
					border: 0.8px solid rgb(120, 120, 120);
					border-radius: 60px;
					color: rgb(120, 120, 120);
					font-family: HarmonyOS Sans;
					font-size: 10px;
					font-weight: 400;
					line-height: 12px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

		}

		.address-bubble-icon {
			width: 25px;
			height: auto;
		}
	}

	/deep/.amap-icon {
		img {
			width: 100% !important;
		}
	}
</style>