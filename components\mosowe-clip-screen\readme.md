# mosowe-clip-screen

## props

| prop        | 说明                               | 类型      | 默认            |
| ----------- | -------------------------------- | ------- | ------------- |
| selector    | 要截取成图片的元素，支持class、 id，如：.content | string  | -             |
| base64      | 是否需要反回base64                     | boolean | false         |
| loadingText | 处理中提示文案                          | string  | '正在处理，请勿操作手机' |

## emits

| emit    | 说明      | 类型                   |
| ------- | ------- | -------------------- |
| success | 处理成功后调用 | (image:string)=>void |

## slots

| 名称      | 说明             |
| ------- | -------------- |
| default | 默认插槽，点击此区域开始截取 |

## expose

| 名称    | 说明     | 类型                      |
| ----- | ------ | ----------------------- |
| start | 开始截取元素 | (selector:string)=>void |

## 示例

```html

<template>
  <view class="box">
    <view class="just-number">
      <view v-for="item in 10">{{ item }}</view>
    </view>
    <view class="just-number2">
      <view v-for="item in 50">{{ item }}</view>
    </view>
    <mosowe-button @click="clipRef.start('.just-number')">截屏1</mosowe-button>
    <mosowe-button @click="clipRef.start('.just-number2')">截屏2</mosowe-button>
    <mosowe-clip-screen
      ref="clipRef"
      @success="success"></mosowe-clip-screen>

    <view>生成后图片：</view>
    <image
      :src="pageImage"
      style="width: 100%; height: 100vh"
      mode="aspectFit" />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { onShow, onPageScroll } from '@dcloudio/uni-app';
onPageScroll(() => {});
// 截屏测试
const clipRef = ref<any>(null);
const canvasImage = ref<any>(null);
const pageImage = ref<string>('');
const success = (res: string) => {
  console.log('res', res);
  pageImage.value = res;
};
</script>

<style lang="scss" scoped>
.just-number,
.just-number2 {
  padding: 30rpx;
  border: 2px solid #ff0000;
  box-sizing: border-box;
  margin: 2px;
}
</style>
```
