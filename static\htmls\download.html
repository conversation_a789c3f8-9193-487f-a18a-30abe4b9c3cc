<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载 LightingBall</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #FFD700;
            --secondary-color: #1A1A1A;
            --accent-color: #E0A800;
            --text-color: #F8F8F8;
            --dark-text: #121212;
            --light-gray: #2A2A2A;
            --card-bg: #222222;
            --footer-bg: #0F0F0F;
            --gold-gradient: linear-gradient(135deg, #FFD700 0%, #FFC107 50%, #E0A800 100%);
        }

        body {
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            color: var(--text-color);
            background-color: #141414;
            position: relative;
        }

        .navbar {
            background: linear-gradient(180deg, rgba(20, 20, 20, 0.98) 0%, rgba(20, 20, 20, 0.95) 100%);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
            border-bottom: none;
            z-index: 1000;
        }

        .navbar::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(180deg, 
                rgba(26, 26, 26, 0.3) 0%,
                rgba(26, 26, 26, 0) 100%
            );
            pointer-events: none;
        }

        .navbar-brand {
            background: linear-gradient(
                45deg,
                rgba(150, 150, 150, 0.7) 0%,
                rgba(200, 200, 200, 0.8) 40%,
                rgba(255, 255, 255, 0.9) 70%,
                rgba(255, 255, 255, 1) 100%
            );
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: none;
        }

        .download-section {
            padding: 8rem 0 6rem;
            position: relative;
            overflow: hidden;
            border-top: none;
        }

        .download-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.5;
        }

        .download-options {
            margin-top: 3rem;
        }

        .store-button {
            display: inline-flex;
            align-items: center;
            padding: 15px 30px;
            border-radius: 30px;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .store-button:hover {
            background: var(--gold-gradient);
            color: var(--dark-text);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .store-button img {
            height: 40px;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .download-section {
                padding: 7rem 0 4rem;
            }

            .store-button {
                padding: 12px 25px;
                font-size: 0.9rem;
            }

            .store-button img {
                height: 30px;
            }
        }

        /* 星星背景样式 */
        .stars-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }
        
        .star {
            position: absolute;
            background: #fff;
            border-radius: 50%;
            animation: twinkle var(--duration) infinite;
            opacity: 0;
            box-shadow: 0 0 15px #fff, 0 0 30px #fff, 0 0 45px #fff;
        }
        
        /* 随机生成星星位置和动画 */
        .star:nth-child(1) {
            width: 3px;
            height: 3px;
            top: 15%;
            left: 25%;
            animation-delay: 0.2s;
            --duration: 4s;
            animation-name: twinkle1;
        }
        
        .star:nth-child(2) {
            width: 4px;
            height: 4px;
            top: 35%;
            left: 85%;
            animation-delay: 1.5s;
            --duration: 3.5s;
            animation-name: twinkle2;
        }
        
        .star:nth-child(3) {
            width: 3.5px;
            height: 3.5px;
            top: 55%;
            left: 45%;
            animation-delay: 2.8s;
            --duration: 5s;
            animation-name: twinkle3;
        }
        
        .star:nth-child(4) {
            width: 5px;
            height: 5px;
            top: 75%;
            left: 65%;
            animation-delay: 1.2s;
            --duration: 4.5s;
            animation-name: twinkle4;
        }
        
        .star:nth-child(5) {
            width: 4px;
            height: 4px;
            top: 25%;
            left: 75%;
            animation-delay: 3.5s;
            --duration: 3.8s;
            animation-name: twinkle5;
        }
        
        /* 下载按钮动画效果 */
        .store-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .store-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .store-button:hover::before {
            left: 100%;
        }
        
        .store-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(255, 215, 0, 0.2);
        }
        
        /* 应用预览图样式增强 */
        .app-preview {
            position: relative;
            perspective: 1000px;
        }
        
        .app-preview img {
            transition: all 0.5s ease;
            transform-style: preserve-3d;
        }
        
        .app-preview:hover img {
            transform: rotateY(10deg) rotateX(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        /* 添加响应式优化 */
        @media (max-width: 768px) {
            .app-preview {
                max-width: 250px;
            }
            
            .store-button {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
            
            .store-button img {
                height: 30px;
            }
        }
        
        @media (max-width: 576px) {
            .app-preview {
                max-width: 200px;
            }
            
            .store-button {
                padding: 8px 16px;
                font-size: 0.8rem;
            }
            
            .store-button img {
                height: 25px;
            }
        }

        /* 星星闪烁动画 */
        @keyframes twinkle1 {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.2); }
            100% { opacity: 0; transform: scale(0.3); }
        }
        
        @keyframes twinkle2 {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.1); }
            100% { opacity: 0; transform: scale(0.3); }
        }
        
        @keyframes twinkle3 {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.3); }
            100% { opacity: 0; transform: scale(0.3); }
        }
        
        @keyframes twinkle4 {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.2); }
            100% { opacity: 0; transform: scale(0.3); }
        }
        
        @keyframes twinkle5 {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.1); }
            100% { opacity: 0; transform: scale(0.3); }
        }

        /* 页脚样式 */
        .footer {
            background: transparent;
            padding: 4rem 0 2rem;
            color: #fff;
            position: relative;
            z-index: 1;
        }

        .footer hr {
            border-color: rgba(255, 255, 255, 0.1);
            margin: 2rem 0;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
            transition: color 0.3s ease;
        }

        .footer a.text-muted:hover {
            color: var(--primary-color) !important;
        }

        .footer ul li {
            margin-bottom: 0.8rem;
        }

        .footer .bi {
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .footer a:hover .bi {
            transform: translateY(-2px);
            color: rgba(255, 255, 255, 1) !important;
        }

        .footer .text-center .text-muted {
            opacity: 0.8;
            background: linear-gradient(
                45deg,
                rgba(150, 150, 150, 0.6) 0%,
                rgba(200, 200, 200, 0.7) 40%,
                rgba(255, 255, 255, 0.8) 70%,
                rgba(255, 255, 255, 0.9) 100%
            );
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        @media (max-width: 768px) {
            .footer {
                padding: 3rem 0 1.5rem;
            }

            .footer .col-md-4 {
                margin-bottom: 2rem;
            }

            .footer .col-md-4:last-child {
                margin-bottom: 0;
            }
        }

        /* 更新应用预览占位样式 */
        .app-preview-placeholder {
            width: 300px;
            height: 600px;
            margin: 0 auto;
            background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
            border-radius: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }

        /* 更新应用内容样式为纯色背景 */
        .app-screen {
            width: 100%;
            height: 100%;
            position: relative;
            background: #1a1a1a; /* 深色背景 */
            overflow: hidden;
        }

        /* 添加地图图片样式 */
        .app-map-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
        }

        /* 更新状态栏样式，确保在图片上方显示 */
        .app-status {
            position: relative;
            z-index: 2;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
        }

        /* 更新位置标签样式，确保在图片上方显示 */
        .app-location {
            z-index: 2;
        }

        .app-header {
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 更新地图内容样式 */
        .app-map-content {
            height: calc(100% - 60px);
            background: #1B3C6B; /* 设置为图片中的蓝色背景 */
            position: relative;
            overflow: hidden;
        }

        .map-land {
            background: #F7C748; /* 设置为图片中的金黄色陆地 */
            position: absolute;
        }

        .map-water {
            background: #1B3C6B; /* 设置为图片中的蓝色水域 */
            position: absolute;
        }

        /* 添加顶部状态栏样式 */
        .app-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 15px;
            color: white;
            font-size: 12px;
        }

        .app-status-left {
            display: flex;
            align-items: center;
        }

        .app-status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .map-marker {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--primary-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--primary-color);
        }

        .map-marker::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: var(--primary-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .map-road {
            position: absolute;
            background: linear-gradient(90deg, 
                rgba(255, 255, 255, 0.1),
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0.1)
            );
            height: 3px; /* 增加线条高度 */
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            100% {
                transform: scale(3);
                opacity: 0;
            }
        }

        /* 商店图标样式 */
        .store-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .store-icon svg {
            width: 100%;
            height: 100%;
            color: currentColor;
        }

        @media (max-width: 768px) {
            .app-preview-placeholder {
                width: 250px;
                height: 500px;
            }
        }

        @media (max-width: 576px) {
            .app-preview-placeholder {
                width: 200px;
                height: 400px;
            }
        }

        /* 月亮容器样式 */
        .moon-container {
            position: fixed;
            top: 80px;
            right: 50px;
            width: 100px;
            height: 100px;
            z-index: 0;
        }
        
        .moon {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f4f4f4);
            border-radius: 50%;
            box-shadow: 0 0 50px rgba(255, 255, 255, 0.5);
        }
        
        .shadow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(90deg, rgba(0,0,0,0.2), transparent);
        }

        /* 添加网格背景 */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(32, 37, 55, 0.7) 1px, transparent 1px),
                linear-gradient(90deg, rgba(32, 37, 55, 0.7) 1px, transparent 1px);
            background-size: 40px 40px;
            background-position: center center;
            z-index: -1;
            opacity: 0.3;
        }

        /* 添加渐变叠加，增加深度感 */
        body::after {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(0, 0, 0, 0) 0%, 
                rgba(0, 0, 0, 0.5) 100%
            );
            z-index: -1;
        }

        /* 星星动画 */
        @keyframes twinkle {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.3); }
        }

        /* 移动端布局调整 */
        @media (max-width: 991px) {
            .download-section {
                padding-top: calc(8rem + 20px);
            }

            .display-4 {
                background: linear-gradient(
                    45deg,
                    rgba(150, 150, 150, 0.7) 0%,
                    rgba(200, 200, 200, 0.8) 40%,
                    rgba(255, 255, 255, 0.9) 70%,
                    rgba(255, 255, 255, 1) 100%
                );
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                text-shadow: none;
                font-weight: bold;
                position: relative;
                text-align: center;
                margin-top: 20px;
                border: none;
                box-shadow: none;
            }

            .display-4::before {
                display: none;
            }
        }

        /* 更小屏幕的额外调整 */
        @media (max-width: 576px) {
            .download-section {
                padding-top: calc(6rem + 20px);
            }

            .display-4 {
                font-size: 2rem;
            }
        }

        /* 更新标题渐变效果 */
        @media (max-width: 991px) {
            .display-4 {
                background: linear-gradient(
                    45deg, /* 调整角度，使上方最亮 */
                    rgba(150, 150, 150, 0.7) 0%, /* 最暗处 - 底部 */
                    rgba(200, 200, 200, 0.8) 40%,
                    rgba(255, 255, 255, 0.9) 70%,
                    rgba(255, 255, 255, 1) 100% /* 最亮处 - 顶部，靠近月亮 */
                );
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                text-shadow: none;
                font-weight: bold;
                position: relative;
                text-align: center;
                padding: 10px;
            }

            /* 添加上方光晕效果 */
            .display-4::before {
                content: '';
                position: absolute;
                top: -10px;
                left: 0;
                width: 100%;
                height: 100px;
                background: radial-gradient(
                    circle at top,
                    rgba(255, 255, 255, 0.1) 0%,
                    transparent 70%
                );
                pointer-events: none;
            }
        }

        /* 更小屏幕的额外调整 */
        @media (max-width: 576px) {
            .display-4 {
                font-size: 2rem;
                background: linear-gradient(
                    45deg,
                    rgba(150, 150, 150, 0.6) 0%,
                    rgba(200, 200, 200, 0.7) 30%,
                    rgba(255, 255, 255, 0.85) 60%,
                    rgba(255, 255, 255, 1) 100%
                );
                -webkit-background-clip: text;
                background-clip: text;
            }
        }

        /* 兼容性处理 */
        @supports not (background-clip: text) {
            .display-4 {
                color: #ffffff;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            }
        }

        /* 兼容性处理 */
        @supports not (background-clip: text) {
            .navbar-brand {
                color: #ffffff;
            }
        }

        /* 更新描述文字样式 */
        .lead {
            text-align: center;
            line-height: 1.8; /* 增加行高使两行文字间距适中 */
        }

        @media (max-width: 991px) {
            .lead {
                margin-top: 1rem;
                margin-bottom: 2rem;
            }
        }

        /* 添加更多星星的样式 */
        .star:nth-child(6) {
            width: 4px;
            height: 4px;
            top: 40%;
            left: 30%;
            animation-delay: 2.5s;
            --duration: 4.2s;
            animation-name: twinkle1;
        }

        .star:nth-child(7) {
            width: 5px;
            height: 5px;
            top: 60%;
            left: 80%;
            animation-delay: 1.8s;
            --duration: 3.7s;
            animation-name: twinkle2;
        }

        .star:nth-child(8) {
            width: 3px;
            height: 3px;
            top: 20%;
            left: 60%;
            animation-delay: 3.2s;
            --duration: 4.8s;
            animation-name: twinkle3;
        }

        /* 优化地球样式 */
        .earth-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 240px;  /* 增加尺寸 */
            height: 240px;
            border-radius: 50%;
            background: linear-gradient(160deg, 
                #1B2735 0%,   /* 深蓝色海洋 */
                #2E5170 40%,  /* 中蓝色海洋 */
                #3E7CB8 80%,  /* 浅蓝色海洋 */
                #81B8E9 100%  /* 最浅的海洋色 */
            );
            box-shadow: 
                inset 0 0 50px rgba(0,0,0,0.8),
                0 0 60px rgba(30,144,255,0.3);
            overflow: hidden;
            animation: earthFloat 6s ease-in-out infinite;
        }

        /* 优化大陆样式 */
        .continent {
            position: absolute;
            background: linear-gradient(
                45deg,
                #2D5A27 0%,    /* 深绿色 */
                #3D7A37 50%,   /* 中绿色 */
                #4C9947 100%   /* 浅绿色 */
            );
            opacity: 0.85;
            border-radius: 40%;
            box-shadow: inset 0 0 15px rgba(0,0,0,0.3);
        }

        .continent-1 {
            width: 100px;
            height: 120px;
            top: 25%;
            left: 35%;
            transform: rotate(15deg);
            clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
        }

        .continent-2 {
            width: 90px;
            height: 60px;
            top: 65%;
            left: 25%;
            transform: rotate(-20deg);
            clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);
        }

        .continent-3 {
            width: 70px;
            height: 90px;
            top: 15%;
            left: 55%;
            transform: rotate(45deg);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }

        /* 优化地球光晕效果 */
        .earth-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(120deg, 
                transparent 20%,
                rgba(255,255,255,0.1) 40%,
                rgba(255,255,255,0.2) 50%,
                rgba(255,255,255,0.1) 60%,
                transparent 80%
            );
            animation: earthRotate 15s linear infinite;
        }

        /* 添加大气层效果 */
        .atmosphere {
            position: absolute;
            top: -5%;
            left: -5%;
            width: 110%;
            height: 110%;
            border-radius: 50%;
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255,255,255,0.1) 0%,
                rgba(255,255,255,0.05) 30%,
                transparent 70%
            );
        }

        @keyframes earthFloat {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -52%) rotate(2deg); }
        }

        @keyframes earthRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 更新 App Store 按钮样式 */
        .app-store-button {
            display: inline-flex;
            align-items: center;
            padding: 12px 30px;
            background: linear-gradient(
                45deg,
                rgba(150, 150, 150, 0.7) 0%,
                rgba(200, 200, 200, 0.8) 40%,
                rgba(255, 255, 255, 0.9) 70%,
                rgba(255, 255, 255, 1) 100%
            );
            border: none;
            border-radius: 25px;
            color: var(--dark-text);
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            margin-top: 20px;
            /* 修改阴影效果，使用更柔和的白色阴影 */
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
        }

        .app-store-button:hover {
            transform: translateY(-2px);
            /* 修改悬浮时的阴影效果 */
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.15);
            background: linear-gradient(
                45deg,
                rgba(170, 170, 170, 0.8) 0%,
                rgba(220, 220, 220, 0.9) 40%,
                rgba(255, 255, 255, 1) 70%,
                rgba(255, 255, 255, 1) 100%
            );
        }

        /* 更新按钮内的图标颜色 */
        .app-store-button i {
            color: var(--dark-text);
            margin-right: 8px;
        }

        .qr-code-container {
            position: absolute;
            top: -160px;
            left: 50%;
            transform: translateX(-50%) scale(0.8);
            background: white;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .qr-code-container::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid white;
        }

        .app-store-button:hover .qr-code-container {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) scale(1);
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
        }

        /* 更新平台图标样式 */
        .footer .platform-icons {
            margin-top: 1rem;
        }

        .footer .platform-icons a {
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;
        }

        .footer .platform-icons a:hover {
            color: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        /* 添加品牌图标的 CSS 样式 */
        .bi-samsung::before { content: "S"; font-weight: bold; }
        .bi-mi::before { content: "MI"; font-weight: bold; }
        .bi-oppo::before { content: "OPPO"; font-weight: bold; font-size: 0.8em; }
        .bi-vivo::before { content: "vivo"; font-weight: bold; }
        .bi-huawei::before { content: "华为"; font-weight: bold; font-size: 0.8em; }
    </style>
</head>
<body>
    <!-- 在月亮容器后添加女巫 -->
    <div class="moon-container">
        <div class="moon">
            <div class="shadow"></div>
        </div>
    </div>

    <!-- 更新星星容器，添加更多星星和流星 -->
    <div class="stars-container">
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">LightingBall</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">返回首页</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 下载区域 -->
    <section class="download-section">
        <div class="container">
            <div class="row align-items-center">
                <!-- 保留并居中标题部分 -->
                <div class="col-lg-12 text-center">
                    <h1 class="display-4 fw-bold mb-4">欢迎加入 LightingBall</h1>
                    <p class="lead mb-4">
                        体验全新的社交生活方式<br>
                        连接你身边的精彩世界
                    </p>
                    <a href="#" class="app-store-button">
                        <i class="bi bi-apple me-2"></i>
                        App Store
                        <div class="qr-code-container">
                            <div class="qr-code">
                                扫描下载<br>LightingBall
                                </div>
                                </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <!-- 删除第一列的 LightingBall 标题 -->
                <!-- <div class="col-md-4">
                    <h5 class="mb-3">LightingBall</h5>
                </div> -->
                
                <div class="col-md-4">
                    <h5 class="mb-3">联系我们</h5>
                    <ul class="list-unstyled">
                        <li class="text-muted"><i class="bi bi-envelope me-2"></i><EMAIL></li>
                        <li class="text-muted"><i class="bi bi-telephone me-2"></i>(+86) 150 0462 9768</li>
                        <li class="text-muted"><i class="bi bi-wechat me-2"></i>微信公众号：LightingBall</li>
                    </ul>
                    <div class="mt-3">
                        <!-- 更新平台图标，删除鸿蒙，添加微信小程序 -->
                        <a href="#" class="text-muted me-3"><i class="bi bi-apple"></i></a>
                        <a href="#" class="text-muted me-3"><i class="bi bi-android2"></i></a>
                        <a href="#" class="text-muted me-3"><i class="bi bi-wechat"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="text-muted mb-0">© 2024 鲁鲁修信息技术（哈尔滨）有限责任公司保留所有权利.</p>
                <p class="text-muted mt-2">
                    <a href="https://beian.miit.gov.cn/" target="_blank" class="text-muted text-decoration-none">
                        黑ICP备2023013144号-2
                    </a>
                </p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新设备检测函数中的默认安卓设备返回值
        function detectDevice() {
            const ua = navigator.userAgent.toLowerCase();
            if (/iphone|ipad|ipod/.test(ua)) {
                return {type: 'ios', icon: 'bi-apple', store: 'App Store'};
            } else if (/android/.test(ua)) {
                // 检测具体的安卓品牌
                if (/samsung/.test(ua)) {
                    return {type: 'samsung', icon: 'bi-samsung', store: 'Galaxy Store'};
                } else if (/xiaomi/.test(ua)) {
                    return {type: 'xiaomi', icon: 'bi-mi', store: '小米应用商店'};
                } else if (/oppo/.test(ua)) {
                    return {type: 'oppo', icon: 'bi-oppo', store: 'OPPO商店'};
                } else if (/vivo/.test(ua)) {
                    return {type: 'vivo', icon: 'bi-vivo', store: 'vivo商店'};
                } else if (/huawei/.test(ua)) {
                    return {type: 'huawei', icon: 'bi-huawei', store: '华为应用市场'};
                } else {
                    // 修改这里：将 'Google Play' 改为 '应用商店'
                    return {type: 'android', icon: 'bi-android2', store: '应用商店'};
                }
            }
            return {type: 'default', icon: 'bi-apple', store: 'App Store'};
        }

        // 同时更新按钮文字替换的正则表达式
        function updateButtonText(button, text) {
            button.childNodes.forEach(node => {
                if (node.nodeType === 3) { // 文本节点
                    node.textContent = node.textContent.replace(/App Store|应用商店|Galaxy Store|小米应用商店|OPPO商店|vivo商店|华为应用市场/g, text);
                }
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const device = detectDevice();
            const appStoreButton = document.querySelector('.app-store-button');
            
            if (appStoreButton) {
                const icon = appStoreButton.querySelector('i');
                if (icon) {
                    // 移除现有的图标类
                    icon.className = 'bi';
                    // 添加新的图标类
                    icon.classList.add(device.icon);
                }
                // 更新按钮文字
                updateButtonText(appStoreButton, device.store);
            }
        });
    </script>
</body>
</html> 