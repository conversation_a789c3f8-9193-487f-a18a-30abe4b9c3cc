<!--
 截屏
 @Author: mosowe
 @Date:2024-02-26 14:32:15
-->

<template>
  <view class="mosowe-clip-screen">
    <view @click="start(props.selector)">
      <slot name="default"></slot>
    </view>
    <view class="canvas-wrap">
      <canvas
        v-if="showCanvas"
        :style="{
          width: canvasWidth * 2 + 'px',
          height: canvasHeight * 2 + 'px'
        }"
        :width="canvasWidth + 'px'"
        :height="canvasHeight + 'px'"
        canvas-id="canvasMain"
        id="canvasMain"
        class="canvas-main"></canvas>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue';
import { onShow, onReady, onPageScroll } from '@dcloudio/uni-app';
const instance = getCurrentInstance();
const props = defineProps<{
  selector?: string;
  base64?: boolean;
  loadingText?: string;
}>();
const emit = defineEmits<{
  (e: 'success', str: string): void;
}>();
/**
 * 页面生成图片
 * @Author: mosowe
 * @Date:2024-02-26 09:47:38
 * 获取截屏范围，获取截屏范围的根节点ref值，获取该ref到顶部的距离，使用uni.pageScrollTo(OBJECT)滚动到该节点，
 * 若页面"navigationStyle": "custom"，滚动距离需减去statusBarHeight高度，如此，需要截屏区域的顶部已经呈现出来，
 * 开始截屏
 * 判断是否一屏可以截取完，不能就滚动截取，每次滚动屏幕的80%
 * 滚动截取后，去掉头尾，拼接成一张图
 *
 */
// 获取系统信息
const systemInfo = uni.getSystemInfoSync();

// 判断是否为iOS系统
const isIOS = systemInfo.platform === 'ios';
let canvas: any = null;

let clipTop = '0px'; // 截屏开始顶部位置
let clipLeft = '0px'; // 截屏开始左侧位置，根据selector计算
let clipWidth = '0px'; // 截屏宽度，不能大于屏幕宽度，根据selector计算
let clipHeight = '0px'; // 截屏高度，等于安全高度的80%

let safeHeight = 0; // 页面安全区域高度
let scrollLen = 0; // 每次滚动安全高度
let statusBarHeight = 0; // 状态栏高度
let screenTop = 0; // 系统自带顶部栏高度，若为0，表示该页面"navigationStyle": "custom"了

let elHeight = 0; // 页面高度

let selectorOffsetTop = 0; // 元素距离顶部距离

const canvasHeight = ref(300);
const canvasWidth = ref(300);

let times = 0; // 滚动次数
let lastHeight = '0px'; // 最后一屏有效高度
let images = ref<string[]>([]); // 截屏图片数组

// 获取需要滚动次数，向上取整
const scrollTimes = (scrollLen: number, elHeight: number) => {
  return Math.ceil(elHeight / scrollLen);
};
// 最后一次滚动距离，向上取整
const lastScrollLen = (scrollLen: number, elHeight: number) => {
  return scrollTimes(scrollLen, elHeight) > 0 ? Math.ceil(elHeight % scrollLen) : 0;
};
// 截屏，核心代码：
let currentWebview: any = null;
let bitmap: any = null;
const clipScreen = (top: string, left: string, width: string, height: string) => {
  return new Promise<string>((resolve, reject) => {
    // 将webview内容绘制到Bitmap对象中
    currentWebview.draw(
      bitmap,
      function () {
        bitmap.save(
          '_doc/' + generateRandomLetterNumber(10) + '.jpg',
          {},
          function (i) {
            resolve(i.target);
          },
          function (e) {
            console.log('截屏绘制图片失败：' + JSON.stringify(e));
          }
        );
      },
      function () {
        resolve('');
      },
      {
        clip: { top, left, height, width } // 设置截屏区域
      }
    );
  });
};

// 准备裁剪，递归
const deepReadyClip = (i: number) => {
  if (i < times - 1) {
    clipScreen(clipTop, clipLeft, clipWidth, parseInt(clipHeight) - (isIOS ? parseInt(clipTop) : 0) + 'px').then(
      (res) => {
        images.value.push(res);
        const index = i + 1;
        uni.pageScrollTo({
          scrollTop: index * scrollLen + selectorOffsetTop,
          duration: 0,
          success: async () => {
            await timeSleep(1000);
            deepReadyClip(index);
          }
        });
      }
    );
  } else {
    clipScreen(clipTop, clipLeft, clipWidth, lastHeight).then(async (res) => {
      images.value.push(res);
      if (times > 1) {
        // 需要拼合的
        await timeSleep(1000);
        canvasDrawImages(0);
      } else {
        // 不需要拼合
        success(res);
      }
    });
  }
};
// 合并图片
let drawHeight = 0;
const canvasDrawImages = (i: number) => {
  plus.io.getImageInfo({
    src: images.value[i],
    success: async (res: any) => {
      canvas.drawImage(images.value[i], 0, drawHeight, parseInt(res.width), parseInt(res.height));
      drawHeight += res.height;
      await timeSleep(1000);
      if (i < images.value.length - 1) {
        canvasDrawImages(++i);
      } else {
        canvasImage();
      }
    }
  });
};

// 延时
const timeSleep = (time = 100) => {
  return new Promise((resolve) => {
    let t: any = setTimeout(() => {
      clearTimeout(t);
      t = null;
      resolve(true);
    }, time);
  });
};
// 绘制总图片
const canvasImage = () => {
  canvas.draw(true, () => {
    uni.canvasToTempFilePath(
      {
        x: 0,
        y: 0,
        width: canvasWidth.value * 2,
        height: canvasHeight.value * 2,
        fileType: 'png',
        canvasId: 'canvasMain',
        success: (res) => {
          success(res.tempFilePath);
        },
        fail: (res) => {
          console.log(res);
        }
      },
      instance
    );
  });
};
// 开始
const showCanvas = ref(true);
const start = (selector: any) => {
  if (!selector) {
    uni.showToast({
      title: 'selector 不能为空',
      icon: 'none',
      mask: true
    });
    return;
  }
  showCanvas.value = false;

  uni.showLoading({
    title: props.loadingText || '正在处理，请勿操作手机',
    mask: true
  });

  let pages = getCurrentPages();
  let page = pages[pages.length - 1];

  currentWebview = page.$getAppWebview();
  bitmap = new plus.nativeObj.Bitmap('screenPage');

  uni.pageScrollTo({
    selector: selector,
    duration: 0,
    success: async () => {
      await timeSleep(100);
      images.value = [];
      drawHeight = 0;

      const windowInfo = uni.getWindowInfo();
      safeHeight = windowInfo.safeArea.height; // 页面安全区域高度
      statusBarHeight = windowInfo.statusBarHeight; // 状态栏高度
      screenTop = windowInfo.screenTop; // 系统自带顶部导航栏高度，若为0，表示该页面"navigationStyle": "custom"了

      clipTop = (screenTop ? screenTop : 0) + 'px'; // 截屏开始顶部位置
      clipHeight = safeHeight + 'px'; // 截屏高度

      scrollLen = safeHeight - parseInt(clipTop);
      const query = uni.createSelectorQuery().in(this);
      query
        .select(selector)
        .boundingClientRect(async (data: any) => {
          elHeight = data.height;
          if (elHeight < safeHeight) {
            clipHeight = elHeight + parseInt(clipTop) + 'px';
          }
          selectorOffsetTop = pageScrollLen;
          if (data.top) {
            clipTop = parseInt(clipTop) + data.top + 'px'; // 截屏开始顶部位置
          }
          clipLeft = data.left + 'px';
          clipWidth = data.width + (isIOS ? 0 : data.left) + 'px';
          canvasHeight.value = data.height;
          canvasWidth.value = data.width;
          showCanvas.value = true;
          times = scrollTimes(scrollLen, elHeight);
          lastHeight = lastScrollLen(scrollLen, elHeight) + (isIOS ? 0 : parseInt(clipTop)) + 'px';
          await timeSleep(1000);
          canvas = uni.createCanvasContext('canvasMain', instance);
          deepReadyClip(0);
        })
        .exec();
    }
  });
};
let pageScrollLen = 0;
onPageScroll((e: any) => {
  pageScrollLen = e.scrollTop;
});
// 随机字符串
const generateRandomLetterNumber = (length: number) => {
  let result = '';
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    const randomChar = characters.charAt(randomIndex);
    result += randomChar;
  }
  return result;
};
//操作成功
const success = (res: any) => {
  bitmap.clear();
  bitmap = null;
  currentWebview = null;
  uni.showToast({
    title: '已完成',
    icon: 'none',
    mask: true
  });
  if (props.base64) {
    const bitmap64 = new plus.nativeObj.Bitmap('base64');
    bitmap64.save(
      res,
      {},
      function () {
        bitmap64.clear();
        emit('success', bitmap64.toBase64Data().replace('image/null', 'image/png'));
      },
      function (e) {
        console.log('base64转码失败：', JSON.stringify(e));
      }
    );
  } else {
    emit('success', res);
  }
};
defineExpose({
  start
});
</script>

<style lang="scss" scoped>
.mosowe-clip-screen {
  .canvas-wrap {
    position: fixed;
    left: 0;
    top: 0;
    overflow: hidden;
    height: 0;
    width: 0;
    .canvas-main {
      overflow: hidden;
      height: 0;
      width: 0;
    }
  }
}
</style>
