// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.1.2 effective-5.10 (swiftlang-*******.2 clang-1700.0.13.5)
// swift-module-flags: -target arm64-apple-ios15.6 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -module-name LocationModule
// swift-module-flags-ignorable:  -interface-compiler-version 6.1.2
import BackgroundTasks
import CoreLocation
import Foundation
import HealthKit
import Swift
import UIKit
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
@_inheritsConvenienceInitializers @objc(LocationSDK) public class LocationSDK : ObjectiveC.NSObject {
  @objc override dynamic public init()
  @objc public static func setBaseUrl(_ baseUrl: Swift.String)
  @objc public static func setToken(_ accessToken: Swift.String, refreshToken: Swift.String)
  @objc public static func debug(_ debug: Swift.Bool)
  @objc public static func authWhenInUse()
  @objc public static func authAlways()
  @objc public static func authStatus() -> Swift.Int
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class LocationService : ObjectiveC.NSObject {
  public static let shared: LocationModule.LocationService
  @objc deinit
}
extension LocationModule.LocationService {
  public func enableFullBackgroundPersistence()
}
extension LocationModule.LocationService : CoreLocation.CLLocationManagerDelegate {
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didUpdateLocations locations: [CoreLocation.CLLocation])
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didFailWithError error: any Swift.Error)
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didChangeAuthorization status: CoreLocation.CLAuthorizationStatus)
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didVisit visit: CoreLocation.CLVisit)
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didEnterRegion region: CoreLocation.CLRegion)
  @objc dynamic public func locationManager(_ manager: CoreLocation.CLLocationManager, didExitRegion region: CoreLocation.CLRegion)
}
extension LocationModule.LocationService {
  public func scheduleBackgroundTask()
}
