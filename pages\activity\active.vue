<template>
	<view class="appPage">
		<!-- <view class="" style="height: 60rpx;"></view>
		<view class="head" style="margin-top: 30rpx;">
			<uni-icons type="left" color="#fff" style="margin-left: 30rpx;" @click="goBack"></uni-icons>
			<view class="inputBg">
				<input v-model="keyword" type="text" placeholder="搜索活动" @confirm="()=>{
					this.page = 1
				this.dataArr = []
				getData()
			} " :border="false" clearable focus />
			</view>
			<span class="search" @click="()=>{
				this.dataArr = []
				this.page = 1
				getData()
			} ">
				搜索
			</span>
		</view> -->
		<view class="head">
			<view class="inputBg">
				<uni-icons type="search" color="#fff" style="margin-right: 8rpx;"></uni-icons>
				<input v-model="keyword" type="text" placeholder="输入活动ID或关键字" @confirm="()=>{
				this.page = 1
			this.dataArr = []
			getData()
		} " clearable focus style="width: 82%;" />
				<image src="../../static/active/search.png" mode="aspectFill" style="width: 80rpx; height: 56rpx;"
					@click="goNav('/pages/index/hbPosition')" />
			</view>
		</view>
		<view style="margin-top: 40rpx; margin-bottom: 40rpx;">
			搜索结果活动列表
		</view>
		<view v-if="dataArr.length === 0"
			style="width: 100%; height: 60vh;margin-top: 10vh; display: flex; align-items: center; justify-content: center;">
			<image src="@/static/active/wfxhd.png" mode="aspectFill" style="width: 302rpx; height: 80rpx;"></image>
		</view>
		<scroll-view @scrolltolower="scrolltolower" :refresher-threshold="150" scroll-y="true" v-else
			refresher-background="grey41" :scroll-x="false" style="width: 100%; height: 82vh;">
			<!-- <view v-if="!recommendTotal" style="color: #fff;margin-top: 20rpx;text-align: center;">到底了~
			</view> -->
			<view class="list" v-for="(item, index) in dataArr">
				<image :src="item.cover" mode="aspectFill"
					style="width: 244rpx; height: 244rpx; margin-right: 20rpx; border-radius: 12rpx;"
					@click="toDetails(item.uuid)"></image>
				<view class="active">
					<view class="title" @click="toDetails(item.uuid)" style="font-weight: bold;">
						{{item.title}}
					</view>
					<view class="avatar" style="margin-top: 18px;">
						<image v-for="(src, index2) in item.member_avatar" :src="src" mode="aspectFill"
							@click="toMember(item.uuid)" :style="{left: index2*20 + 'rpx'}"></image>
						<span v-if="item.people_cnt >= 5"
							style="width: 40rpx; height: 40rpx; text-align: center; line-height: 25rpx; position: absolute; top: 0; left: 80rpx; background-color: rgb(180, 180, 180); border-radius: 50%;">
							...
						</span>
						<span
							:style="{marginLeft: item.member_avatar.length*20+40 + 'rpx', color: 'rgb(224, 224, 224)', fontSize: '18rpx' }">共{{item.people_cnt}}人</span>
					</view>
					<view class="t-distance">
						<span class="t-distance">
							<image src="../../static/active/time.png" mode="aspectFill"
								style="width: 22rpx; height: 22rpx; margin-right: 10rpx;">
							</image>
							{{item.timeline}}
						</span>
						<span class="t-distance" style="margin-right: 12rpx;">
							<image src="../../static/active/distance.png" mode="aspectFill"
								style="width: 22rpx; height: 22rpx; margin-right: 12rpx;">
							</image>
							{{item.distance}}
						</span>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '',
				page: 1,
				dataArr: [],
				longitude: '',
				latitude: ''
			}
		},
		watch: {
			'$store.state.hbLocation'(nVal) {
				let location = nVal.location.split(",")
				this.keyword = nVal ? nVal.name : ''
				let longitude = nVal ? location[0].trim() : ''
				let latitude = nVal ? location[1].replace(/\s+/g, '') : ''

				this.getLocation(longitude, latitude)
			},
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			getData() {
				this.$http.get('/activity/search', {
					keyword: this.keyword,
					page: this.page,
				}).then(res => {
					if (this.page === 1) {
						this.dataArr = res.message
					} else {
						this.dataArr.push(...res.message)
					}
					console.log(res);
				})
			},
			getLocation(longitude, latitude) {
				console.log(longitude, latitude);
				this.$http.get('/activity/list', {
					longitude,
					latitude,
					list_type: 4,
					page: this.page,
				}).then(res => {
					if (this.page === 1) {
						this.dataArr = res.message
					} else {
						this.dataArr.push(...res.message)
					}
					console.log(res);
				})
			},
			goNav(url) {
				uni.navigateTo({
					url,
				})
			},
			scrolltolower(s) {
				if (this.recommendTotal) {
					this.page++;
					this.getData()
				}
			},
			toMember(e) {
				uni.navigateTo({
					url: '/pages/activity/activeMembers?uuid=' + e
				})
			},
			toDetails(e) {
				uni.navigateTo({
					url: '/pages/activity/details?id=' + e
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding-left: 40rpx;
		padding-right: 40rpx;

	}

	.head {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.search {
			width: 100rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			width: 100%;
			color: #fff;
			background: #22252F;
			height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 50rpx;
			padding: 0 24rpx;
		}
	}

	.list {
		width: 100%;
		height: 284rpx;
		border-radius: 20rpx;
		background: rgb(56, 58, 68);
		margin-bottom: 30rpx;
		padding: 20rpx 34rpx 20rpx 20rpx;
		display: flex;

		.active {
			width: 352rpx;
			margin-top: 8rpx;

			.title {
				font-size: 32rpx;
				width: 100%;
				height: 88rpx;
				line-height: 44rpx;
				margin-bottom: 20rpx;
				overflow: hidden;
				-webkit-line-clamp: 2;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			.avatar {
				position: relative;
				margin-bottom: 40rpx;
				z-index: 9;

				image {
					position: absolute;
					border-radius: 50%;
					display: inline-block;
					top: 0;
					left: 0;
					width: 40rpx;
					height: 40rpx;
				}
			}

			.t-distance {
				font-size: 18rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
		}
	}
</style>