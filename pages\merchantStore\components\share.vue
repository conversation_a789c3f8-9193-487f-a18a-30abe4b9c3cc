<template>
	<view>
		<uni-popup ref="sharePopup" type="bottom" :safe-area="false">
			<view class="cPopup" :style="{marginBottom:securityBottom+'rpx'}">
				<view class="greyCard"></view>
				<view class="text">
					分享至
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view id="demo1" class="scroll-view-item_H" v-for="(item,index) in arr" :key="index">
						<view class="" style="position: relative;">
							<image class="img102" style="border-radius: 50%;" :src="item.avatar" mode="aspectFill"
								@click="setChoose(index)">
							</image>
							<image class="choose img36" src="@/static/images/xuanze.png" mode=""
								v-if="chooseIds&&item.uid==chooseIds.uid">
							</image>
						</view>
						<view class="name">
							{{item.nickname}}
						</view>
					</view>
				</scroll-view>
				<view class="line"></view>

				<view class="cancal" @click="send">
					发送
				</view>

				<view class="" style="height: 66rpx;"></view>
				<u-toast ref='notify' />
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: "share",
		props: {

			//底部安全距离
			securityBottom: {
				type: Number,
				default: 0,
			}
		},
		data() {
			return {
				value: "",
				type: 'textarea',
				border: true,
				height: 100,
				autoHeight: true,
				flag: false,
				chooseIds: null,
				arr: [],
				post: {}
			};
		},
		methods: {
			setChoose(index) {
				if (this.chooseIds && this.chooseIds.uid == this.arr[index].uid) {
					this.chooseIds = null
				} else {

					this.chooseIds = this.arr[index]
				}
			},
			async getData() {
				await this.$http.get('/api/user/friend/get', {}).then(res => {
					console.log('fuleyou', 'arrrrr', res);
					this.arr = res.message.list
				})
			},
			async send() {
				if (!this.chooseIds) {
					uni.showToast({
						title: '请选择好友',
						icon: 'none',
						duration: 2000
					})
					return false
				}
				console.log('wahaha', this.post, );
				const result = await this.$Yxim.msg.sendCustomMsg({
					attach: '占位',
					scene: 'p2p', //消息的场景  "p2p" | "team" | "superTeam"
					to: this.chooseIds.im_id, //接收人
					ext: JSON.stringify({
						name: this.post.remark,
						img: this.post.avatar,
						location: this.post.location,
						longitude: this.post.coordinate.split(',')[0],
						latitude: this.post.coordinate.split(',')[1],
						type: 'location',
						appAvatar: uni.getStorageSync('avatar')
					}),
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
				this.close()

			},
			close() {
				this.$refs.sharePopup.close()
			},
			open(option) {
				this.post = option
				this.flag = false
				this.getData()
				this.$refs.sharePopup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-popup {
		z-index: 9999;
	}

	.rightInfo {
		padding: 14rpx;
		width: 170rpx;
		height: 200rpx;
		border-radius: 16rpx;
		font-size: 24rpx;
		background: #F7F7F7;
		color: #888888;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 4;
		overflow: hidden;
		word-break: break-all;
	}

	.cover {
		width: 170rpx;
		height: 200rpx;
		border-radius: 16rpx;
	}

	.cPopup {
		padding: 20rpx 32rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;
		display: flex;
		align-items: center;
		flex-direction: column;

		.icons {
			width: 100%;
			display: flex;
			justify-content: space-around;
			margin-bottom: 128rpx;

			.tit {
				text-align: center;
				font-size: 24rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #626262;
			}
		}

		.line {
			width: 750rpx;
			height: 1rpx;
			background: #EDEDED;
			margin: 40rpx 0;
		}

		.scroll-view_H {
			white-space: nowrap;
			width: 100%;
			display: flex;
			align-items: center;
		}

		.scroll-view-item_H {
			display: inline-block;
			text-align: center;
			padding: 22rpx;

			.choose {
				position: absolute;
				bottom: 0;
				right: 0;
			}

			.name {
				transform: translateX(-12rpx);
				text-align: center;
				font-size: 24rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #3D3D3D;
			}
		}

		.text {
			font-size: 34rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #3D3D3D;

		}

		.greyCard {
			width: 102rpx;
			height: 8rpx;
			background: #D5D5D5;
			border-radius: 50rpx;
			margin: 28rpx 0;
		}

		.cancal {
			width: 622rpx;
			margin-top: 24rpx;

			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #fff;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}
</style>