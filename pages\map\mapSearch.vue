<template>
	<view class="map-page">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="goBack" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">模型展示地点</view>
		</view>
		<view class="page-main">
			<view class="page-main-content">
				<!-- 搜索 -->
				<view class="search-container" :style="{top:searchTop + 'px'}">
					<view class="search-input">
						<view class="search-input-text">
							<!-- <uni-icons type="search" color="#000000" style="margin-right: 8rpx;"></uni-icons> -->
							<input id='tipinput' class="form-item-input" v-model="keyword" @input="getAddress"
								placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="搜索地址" />
						</view>
						<view class="search-input-btn" @click="getAddress">搜索</view>
					</view>
					<view v-if="addressArr && addressArr.length>0" class="search-result">
						<view class="search-result-title">搜索地址结果</view>
						<scroll-view scroll-y="true" style="max-height: 300rpx;" @scrolltolower="addressBottom">
							<view class="search-result-list">
								<view class="search-result-list-item" v-for="(item,index) in addressArr" :key="index"
									@click="goNavLoaction(item)">
									<image class="position-icon" mode="widthFix">
									</image>
									<view class="search-result-list-item-position">{{item.name}}</view>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "MapContainerSearch",
		// props: {
		// 	knownLnglat: {
		// 		type: Array,
		// 		default: () => [],
		// 	},
		// },
		data() {
			return {
				screenHeight: uni.getSystemInfoSync().screenHeight - uni.getSystemInfoSync().statusBarHeight - 44,
				// searchTop:uni.getSystemInfoSync().statusBarHeight + 44,
				searchTop: 120,
				longitude: 123.599861,
				latitude: 43.758234,
				MapGeolocation: null,
				mouseTool: null,
				polygon: null,
				polyEditor: null,
				autoOptions: {
					input: "tishikuang", //绑定的搜索关键字的input标签ID，用这个注册
				},
				auto: null,
				keyword: "", //绑定的搜索关键字的的内容
				placeSearch: null,
				searchHere: null, //根据搜索组件搜索到以后的地点信息
				lng: "",
				lat: "",
				options: [], // 可选数据列表，详见组件文档
				selectedOptions: [], // 当前已选数据
				addressList: [],
				marker: null,
				infoWindow: null,
				AMap: null,
				Geocoder: null,
				currentAddress: {},
				business_id: "",

				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: '',
					location: "",
					city: '',
					region: '',
					output: 'JSON',
					city_limit: true,
					sortrule: 'distance',
				},
				addressArr: [],
				total: 0,
				markerData: [],
			}
		},
		onLoad(options) {
			if (options.shopId) {
				this.business_id = options.shopId;
			}
			this.getShopInfo()
			this.MapSearchForm.location = uni.getStorageSync('location')
		},
		mounted() {
			// this.initAMap();
		},
		methods: {
			goBack() {
				uni.navigateTo({
					url: '/pages/shop/shop'
				});
			},
			// 获取商户信息
			getShopInfo() {
				this.$http.get('/api/user/business/get').then((res) => {
					var shopInfo = res.message;
					console.log('shopinfo', shopInfo);
					// this.lon = shopInfo.coordinate.split(',')[0];
					// this.lat = this.shopInfo.coordinate.split(',')[1];
					this.knownLnglat = shopInfo.coordinate
				});
			},

			// 搜索地址
			getAddress() {
				this.MapSearchForm.keywords = this.keyword
				this.markerData = []
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v5/place/around?parameters',
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: (res) => {
						if (res.statusCode == 200) {
							this.total = res.data.count
							if (this.MapSearchForm.page_num == 1) {
								this.addressArr = res.data.pois;
								return
							}
							this.addressArr = [...this.addressArr, ...res.data.pois]
						} else {
							this.toast('搜索地址信息出错');
						}
					},
					fail(err) {
						this.toast(err.errMsg);
					}
				})
			},
			addressBottom() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
					this.getAddress()
				}
			},
			goNavLoaction(item) {
				console.log(item)
				const locat = item.location.split(",")
				console.log(locat)
				this.keyword = ""; //清空
				this.addressArr = [];
				var address = item.pname + item.cityname + item.adname + item.address
				let marker = {
					id: 0,
					name: item.name, //标记点展示名字
					address: address,
					latitude: locat[1], //标记点纬度
					longitude: locat[0], //标记点经度
					iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
					title: item.name,
					callout: {
						content: `<view class="address-bubble-content">
									<view class="address-bubble-describe">${address}</view>
									<view class="address-bubble-button">
										<button id="confirmBtn" class="address-bubble-confirm">确认</button>
										<button id="cancelBtn" class="address-bubble-cancel">取消</button>
									</view>
								</view>`,
						// content: `地址: ${item.pname}${item.cityname}${item.adname}${item.address}\n[确认] [取消]`,
						display: 'ALWAYS',
						textAlign: 'center',
						borderRadius: '6',
					}
				}
				this.markerData.push(marker);
				console.log(this.markerData)
				console.log("添加")
				// 更新地图的中心坐标
				this.latitude = locat[1];
				this.longitude = locat[0];
			},
			onCalloutTap(e) {
				console.log(e)
				console.log('Marker tapped:', e.detail.markerId);
				const markerId = e.detail.markerId;
				const marker = this.markerData.find(m => m.id === markerId);
				if (marker) {
					// 这里模拟点击确认和取消按钮的操作
					// 实际操作中，你可能需要自定义UI或者使用第三方库来创建按钮
					console.log('Marker tapped:', markerId);
					var that = this;
					// 确认
					document.getElementById('confirmBtn').addEventListener('click',
						function() {
							that.confirmAction(marker);
							marker.display = "BYCLICK";
						});
						// 取消
					document.getElementById('cancelBtn').addEventListener('click',
						function() {
							that.cancelAction(markerId);
						});
				}
			},
			confirmAction(marker) {
				console.log('确认按钮被点击，地址是：', marker);
				// 这里可以执行确认后的操作，例如打印地址
				this.currentAddress = {
					address: marker.address,
					latitude: marker.latitude,
					longitude: marker.longitude,
				}
				this.markerData = []
				let markers = {
					id: 0,
					name: marker.name, //标记点展示名字
					address: marker.address,
					latitude: marker.latitude, //标记点纬度
					longitude: marker.longitude, //标记点经度
					iconPath: "http://img.lluuxiu.com/photo/20240907/925b156b-d153-4742-af34-4e502a1ff960.png",
				}
				this.markerData.push(markers);
			},
			cancelAction(markerId) {
				console.log('取消按钮被点击，移除marker:', markerId);
				// 移除marker
				this.removeMarker(markerId);
			},
			removeMarker(markerId) {
				this.markerData = this.markerData.filter(marker => marker.id !== markerId);
			},


			// 提交审核
			submit() {
				this.$http.post('/api/user/business/set-location', {
						address: this.currentAddress.address,
						latitude: this.currentAddress.latitude,
						longitude: this.currentAddress.longitude,
						business_id: this.business_id
					})
					.then((res) => {
						console.log(res);
						if (res.code == 200) {
							// 显示提交成功弹框
							uni.showToast({
								title: `提交成功！`
							})
							this.goBack()
						}
					});
			},
		},
		beforeDestroy() {
			// if (this.MapGeolocation) {
			// 	this.removeMarker();
			// 	this.MapGeolocation.destroy();
			// }
		},
	};
</script>
<style lang="scss">
	// map {
	// 	width: 100%;
	// 	height: calc(100vh - 44px);
	// 	/* 减去顶部导航栏的高度 */
	// 	position: relative;
	// 	z-index: 0;
	// }

	// page {
	// 	width: 750rpx;
	// 	min-height: 370px;
	// 	height: 100vh;
	// 	max-height: 100vh;
	// 	background-color: #F5F7FB;
	// 	position: relative;
	// }

	.map-page {
		width: 100%;
		height: 100vh;
		padding-top: 100px;
		box-sizing: border-box;
		// background-color: #F5F7FB;
		position: relative;
		background-color: red;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 140px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: absolute;
			z-index: 0;
			top: 0;
			left: 0;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.navigation-zhezhao {
			width: 100%;
			height: 140px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position: absolute;
			z-index: 10;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			height: 140px;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				position: relative;
				z-index: 2;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}



		.page-main {
			width: 100%;
			height: 85vh;
			position: absolute;
			top:140px;
			left:0;
			right: 0;
			bottom:0;
			.page-main-content{
				width:100%;
				height:100%;
				background:red;
				position:relative;
				.search-container {
					position: fixed;
					left: 0;
					right: 0;
					z-index: 10000;
					padding: 16px 20px 0 20px;
					box-sizing: border-box;
					.search-input {
						width: 100%;
						height: 38px;
						border-radius: 50px;
						background-color: #ffffff;
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding: 3px 3px 3px 16px;
						box-sizing: border-box;

						.search-input-text {
							flex: 1;
							display: flex;
							align-items: center;
							justify-content: flex-start;

							/deep/.uni-input-input {
								color: #212121 !important;
								font-family: 阿里巴巴普惠体;
								font-size: 16px;
								font-weight: 400;
							}
						}

						.search-input-btn {
							width: 60px;
							height: 32px;
							border-radius: 50px;
							background-color: #52C3EE;
							// background: linear-gradient(270.00deg, rgb(190, 147, 250), rgb(82, 195, 238) 100%);
							color: rgb(255, 255, 255);
							font-family: 思源黑体;
							font-size: 14px;
							font-weight: 400;
							line-height: 32px;
							letter-spacing: 0px;
							text-align: center;
						}
					}

					.search-result {
						margin-top: 10px;
						width: 100%;
						border-radius: 10px;
						background: rgb(255, 255, 255);
						padding: 0 12px;
						box-sizing: border-box;

						.search-result-title {
							color: rgb(152, 152, 152);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							letter-spacing: 0px;
							text-align: center;
							padding: 12px 0;
							box-sizing: border-box;
						}

						.search-result-list {
							border-top: 1px solid #F7F7F7;

							.search-result-list-item {
								padding: 12px 0;
								box-sizing: border-box;
								display: flex;
								align-items: center;

								.position-icon {
									width: 16px;
									height: 16px;
									margin-right: 2px;
								}

								.search-result-list-item-position {
									flex: 1;
									color: rgb(33, 33, 33);
									font-family: HarmonyOS Sans;
									font-size: 16px;
									font-weight: 500;
									line-height: 19px;
									letter-spacing: 0px;
									text-align: left;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}
							}
						}
					}
				}

				.submit-container {
					position: fixed;
					left: 0;
					right: 0;
					bottom: 0;
					z-index: 100;
					padding: 20px;
					box-sizing: border-box;
					border-radius: 10px;
					background-color: #52C3EE;
					.submit-btn {
						width: 100%;
						height: 52px;
						color: rgb(255, 255, 255);
						font-family: 阿里巴巴普惠体;
						font-size: 16px;
						font-weight: 400;
						line-height: 52px;
						letter-spacing: 0px;
						text-align: center;
					}
				}
				
				.map{
					position: absolute;
							left:0;
							right:0;
							top:0;
							bottom:0;
					width: 100%;
					height: 100vh;
				}
				// .map-container {

				// 	// position: relative;
				// 	// z-index: 0;
				// 	#map {
				// 		width: 100%;
				// 		height: 100vh;
				// 		// z-index:1;
				// 	}
				// }
			}
		}
	}

	/deep/.amap-overlay-text-container {
		width: 150px;
		height: auto;
		border-radius: 10px;
		padding: 12px 10px;
		box-sizing: border-box;
		word-break: break;
		text-align: left !important;

		.amap-info-close {
			display: none;
		}

		.address-bubble-content {
			.address-bubble-describe {
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 500;
				line-height: 16px;
				letter-spacing: 0px;
				text-align: left;
				text-indent: 0;
			}

			.address-bubble-button {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 8px;

				.address-bubble-confirm {
					width: 45%;
					height: 22px;
					border-radius: 60px;
					background: rgb(83, 194, 238);
					color: rgb(255, 255, 255);
					font-family: HarmonyOS Sans;
					font-size: 10px;
					font-weight: 400;
					line-height: 12px;
					letter-spacing: 0px;
					text-align: center;
					border: none;
					outline: none;
				}

				.address-bubble-cancel {
					width: 45%;
					height: 22px;
					box-sizing: border-box;
					border: 0.8px solid rgb(120, 120, 120);
					border-radius: 60px;
					color: rgb(120, 120, 120);
					font-family: HarmonyOS Sans;
					font-size: 10px;
					font-weight: 400;
					line-height: 12px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

		}

		.address-bubble-icon {
			width: 25px;
			height: auto;
		}
	}

	/deep/.amap-icon {
		img {
			width: 100% !important;
		}
	}
</style>