<template>
	<uni-popup ref="popup" type="bottom" backgroundColor="transparent" :safe-area="false">
		<!-- 地址搜索 -->
		<view class="sscontent">
			<view class="head">
				<view class="gb" @click="close">关闭</view>
				<view class="gb">位置</view>
				<view class="gb fs" @click="send">发送</view>
			</view>
			<view class="search">
				<uni-icons type="search" size="24"></uni-icons>
				<input class="uni-input" @input="serchAddress" placeholder="请输入地址" />
			</view>
			<MapTool :location="location" />
			<scroll-view scroll-y="true" style="height: 38vh;" lower-threshold="300" @scrolltolower="load_more">
				<block v-for="(item,index) in address_data_lists" :key="index">
					<view class="item" @tap="goReapAddress(item,index)">
						<view style="width: 90%;">
							<view>{{item.name}}</view>
							<view>{{item.pname+item.cityname+item.adname+item.address}}</view>
						</view>
						<icon class="dui" v-if="current == index" style="margin-right: 20rpx;" type="success"
							color="#000" size="20" />
					</view>
				</block>
			</scroll-view>
			<view class="img24" />
			<u-toast ref="notify"></u-toast>
		</view>
	</uni-popup>
</template>

<script>
	import MapTool from "./index.vue";
	export default {
		components: {
			MapTool
		},
		data() {
			return {
				value: "",
				MapSearchForm: {
					page_num: 1,
					page_size: 20,
					keywords: uni.getStorageSync('userLocation') || '哈尔滨',
					city: "",
					region: "",
					output: "JSON",
					city_limit: true,
					sortrule: "distance"
				},
				address_data_lists: [],
				address: {},
				scr_height: 0,
				total: 0,
				location: [],
				current: null
			};
		},
		mounted() {

		},
		onReady() {
			uni.getSystemInfo({
				success: res => {
					this.scr_height = res.windowHeight - uni.upx2px(80 + 20 + 20);
				}
			});
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			close() {
				this.$refs.popup.close();
			},
			send() {
				this.$emit("sendCustomLocaltion", this.address);
			},
			open() {
				this.$refs.popup.open();
				this.getAdress();
			},
			goReapAddress(address, index) {
				this.current = index;
				console.log(address, "address");
				this.address = address;
				this.location = address.location.split(",");
			},
			/// 获取地址根据关键字
			async getAdress() {
				await uni.request({
					method: "GET",
					url: "https://restapi.amap.com/v5/place/text",
					data: {
						...this.MapSearchForm,
						key: "26d3a980c0c4b411f9c13929bbc6559f"
					},
					success: res => {
						if (res.statusCode == 200) {
							console.log(res);
							this.total = res.data.count;
							if (this.MapSearchForm.page_num == 1) {
								return (this.address_data_lists = res.data.pois);
							}
							return (this.address_data_lists = [
								...this.address_data_lists,
								...res.data.pois
							]);
						}
						this.toast("搜索地址信息出错")
					},
					fail(err) {
						this.toast(err.errMsg)
					}
				});
			},
			serchAddress(e) {
				this.MapSearchForm.keywords = e.detail.value;
				this.MapSearchForm.page_num = 1;
				this.address_data_lists = [];
				this.getAdress();
			},
			//下滑加载更多
			load_more() {
				if (this.total > 0) {
					this.MapSearchForm.page_num++;
				}
				this.getAdress();
			}
		}
	};
</script>

<style lang="less" scoped>
	.sscontent {
		width: 100%;
		height: 90vh;
		background-color: #fff;
		border-radius: 20rpx;
		box-sizing: border-box;
		padding: 20rpx 20rpx 0;

		.head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-weight: bold;

			.gb {
				color: #333;
				font-size: 24rpx;
			}

			.fs {
				color: #71d2cf;
			}
		}

		.search {
			margin: 20rpx;
			padding-left: 10rpx;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			background-color: #22252f;
			overflow: hidden;
			height: 80rpx;

			.uni-input {
				padding-left: 10rpx;
			}
		}

		.item {
			padding: 20rpx 30rpx 10px 30rpx;
			border-bottom: 1px solid #d8d8d8;
			color: #333;
			display: flex;
			align-items: center;

			view:first-child {
				// white-space: nowrap;
				// overflow: hidden;
				// text-overflow: ellipsis;
			}

			view:last-child {
				// font-size: 24rpx;
				// color: #000;
				// line-height: 2em;
				// white-space: nowrap;
				// overflow: hidden;
				// text-overflow: ellipsis;
			}
		}
	}
</style>