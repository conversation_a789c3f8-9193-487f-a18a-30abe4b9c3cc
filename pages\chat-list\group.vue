<template>
	<view class="groupPage">
		<u-search placeholder="搜索" v-model="keyword" :showAction="false" bgColor="#272727"
			style="width: 710rpx;margin: auto;" @search="search"></u-search>
		<scroll-view scroll-y="true">
			<uni-list :border="false">
				<uni-list-chat v-for="item in searchData" :key="item.teamId" :title="item.name" :clickable="true"
					:avatarList="item.avatarList" :note="getNote(item)" :time="item.msg?getTimeFmt(item.msg.time):' '"
					@click="goP2p(item)"></uni-list-chat>
			</uni-list>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		timeFmt
	} from '@/utils/common.js'
	export default {
		data() {
			return {
				keyword: '',
				groupData: [],
				searchData: []
			}
		},
		onLoad(option) {

			this.getData()
		},
		onShow() {},
		methods: {
			goP2p(item) {
				console.log(123);
				this.navigateTo({
					url: '/pages/HM-chat/HM-chat?userItem=' + encodeURIComponent(JSON.stringify(({
						chatName: item.name,
						uid: 'team-' + item.teamId,
						type: 'team',
						teamId: item.teamId,
						nuck: item.name,
					})))
				})
			},
			search() {
				if (this.keyword) {
					this.searchData = this.groupData.filter(item => item.name.includes(this.keyword))

				} else {
					this.searchData = this.groupData
				}
			},
			getNote(item) {
				let str = 'team'
				let context = item.msgType == 'text' || item.msgType == 'meme' ? item.newMsg : item.msgType == 'image' ?
					'[图片]' : item.msgType == 'audio' ? '[语音]' : item.msgType == 'video' ? '[视频]' : item.msgType ==
					'custom' ? '自定义消息' : ''
				console.log(context, 'context', item);
				if (context) {
					if (str) {
						return item.msg.fromNick + '：' + context
					} else {
						return context
					}
				} else {
					return ''
				}
			},
			getTimeFmt(str) {
				// console.log(str,'sss');
				if (str) {
					try {
						return timeFmt(str, 'yyyy-MM-DD hh:mm:ss')
					} catch (err) {
						console.log(err);
					}
				} else {
					return ''
				}
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			getData() {
				this.$store.state._Yxim.team.getTeams().then(async res => {
					console.log(res, 'getTeams');
					const groupData = []
					for (let i = 0; i < res.length; i++) {
						const item = res[i]
						const users = await this.$store.state._Yxim.team.getTeamMembers({
							teamId: item.teamId
						})
						const userRes = await this.$store.state._Yxim.user
							.getUsersNameCardFromServer({
								"accounts": users.map(per => per.account)
							})
						const msg = await this.$store.state._Yxim.msgLog.getHistoryMsgs({
							scene: 'team',
							to: item.teamId,
							limit: 1,
						})
						console.log(msg);
						const obj = {
							...item,
							users: users.map(item => item),
							avatarList: userRes.map(per => {
								return {
									url: per.avatar ||
										'https://img2.baidu.com/it/u=**********,*********&fm=253&fmt=auto?w=800&h=1067'
								}
							}),
							msg: msg[0],
							msgType: msg && msg.length ? msg[0].type : ''
						}
						if (msg.length > 0 && msg[0].type == 'text') {
							obj.newMsg = msg[0].body
						}
						groupData.push(obj)
					}
					this.groupData = groupData
					this.searchData = groupData

				})
			}
		}
	}
</script>
<style scoped>
	::v-deep .uni-list--border:after {
		background-color: transparent;
	}
</style>
<style scoped lang="scss">
	.groupPage {
		.li {
			.avatar {
				width: 96rpx;
				height: 96rpx;
				border-radius: 50%;
			}

			.info {
				.name {
					height: 42rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 42rpx;
				}

				.text {
					height: 40rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #A2A2A4;
					line-height: 40rpx;
				}
			}

			.time {
				width: 134rpx;
				height: 34rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #A2A2A4;
				line-height: 34rpx;
			}
		}
	}
</style>