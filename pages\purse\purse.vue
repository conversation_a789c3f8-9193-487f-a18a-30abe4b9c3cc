<template>
  <view class="appPage">
    <image class="bg" src="../../static/images/chongzhibg.png" mode=""></image>
    <u-navbar
      title="充值"
      :border-bottom="false"
      title-color="#fff"
      back-icon-color="#fff"
      :background="background"
    >
    </u-navbar>
    <view class="" style="padding: 0 32rpx">
      <view class="title">钱包金额（元）</view>
      <view class="price">
        ￥{{ balance }}
        <uni-icons
          type="right"
          color="#fff"
          style="margin-left: 32rpx"
          @click="goNav('/pages/purse/priceDetail')"
        ></uni-icons>
      </view>
    </view>
    <view class="content">
      <view class="first t_betweent">
        <view class="left"> 余额充值 </view>
        <view class="t_display" @click="goNav('/pages/purse/withdrawal')">
          <view class="withdrawal"> 去提现 </view>
          <image
            class="img24"
            src="../../static/images/pay/right.png"
            mode=""
          ></image>
        </view>
      </view>
      <view class="otherPrice t_display">
        <view class="txt">其他金额</view>
        <input
          class="input-width"
          v-model="payPirce"
          @focus="focus"
          @input="numberFixedDigit"
          placeholder="请输入充值金额"
          type="number"
          cursor-color="#000"
        />
      </view>

      <view class="gridBox">
        <view
          class="box"
          v-for="(item, idx) in priceArr"
          :key="idx"
          @click="clickItem(idx)"
          style="position: relative"
        >
          <!-- 背景图片 -->
          <image
            class="money"
            :src="
              index === idx
                ? '../../static/images/pay/jine2.png'
                : '../../static/images/pay/jine.png'
            "
            mode=""
          >
          </image>
          <!-- 价格文字，绝对定位并居中，稍微向上和向右调整以平衡视觉效果 -->
          <text
            style="
              position: absolute;
              top: 45%;
              left: 55%;
              transform: translate(-50%, -50%);
              font-weight: 700;
              color: #3d3d3d;
              z-index: 2;
              padding-right: 4rpx;
              display: flex;
              align-items: flex-end;
            "
          >
            <text style="font-size: 30rpx; line-height: 30rpx">￥</text>
            <text style="font-size: 48rpx; line-height: 48rpx">{{ item }}</text>
          </text>
        </view>
      </view>
      <view class="status">
        <view
          class="t_betweent"
          v-for="(item, idx) in payStatus"
          :key="idx"
          @click="payIndex = idx"
          :style="idx > 0 ? 'margin-top: 32rpx;' : ''"
        >
          <view class="t_display">
            <image
              class="img42"
              :src="'../../static/images/pay/' + item.img"
              mode=""
            ></image>
            <span class="txt">{{ item.name }}</span>
          </view>
          <image
            class="img32"
            :src="
              payIndex === idx
                ? '../../static/images/pay/radio2.png'
                : '../../static/images/pay/radio.png'
            "
            mode=""
          ></image>
        </view>
      </view>
      <!-- <view class="protocol t_display">充值即代表同意
			<view class="catalogue"
					@click="goNav('/pages/privacyAgreement/agreement')">《充值协议》</view>
			</view> -->
      <view class="btn" @click="recharge"> 确定充值 </view>
    </view>
    <u-toast ref="notify" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      balance: "", //余额
      ali_bind: false, //是否绑定阿里
      background: {
        backgroundColor: "transparent",
      },
      priceArr: [6, 18, 88],
      index: 0, //选择的金额下标
      payPirce: 0, //充值的金额
      payIndex: 0, //支付方式下标
      payStatus: [
        {
          name: "支付宝支付",
          img: "zhifubao.png",
        },
        // {
        // 		name: "微信支付",
        // 		img: "weixin.png"
        // 	},
      ],
    };
  },

  onShow() {
    this.getData();
  },
  methods: {
    toast(title) {
      this.$refs.notify.show({
        title,
        position: "top",
      });
    },
    numberFixedDigit(e) {
      // 只能输入整数
      e.target.value = e.target.value.replace(/^0|[^\d]|[.]/g, "");
      this.$nextTick(() => {
        this.payPirce = e.target.value;
      });
    },
    focus() {
      this.index = null;
      this.payIndex = null;
    },
    goNav(url) {
      this.navigateTo({
        url,
      });
    },
    //充值
    recharge() {
      let amount = "";
      if (this.index !== null) {
        amount = this.priceArr[this.index];
      } else {
        amount = this.payPirce;
      }
      //支付宝
      if (this.payIndex == 0) {
        this.$http
          .post("/api/user/wallet/recharge/ali", {
            amount,
          })
          .then((res) => {
            //统一各平台的客户端支付API
            uni.requestPayment({
              provider: "alipay", //服务提供商（支付宝）（服务提供商，通过uni.getProvider获取）
              orderInfo: res.message, //后台返回的支付宝订单数据
              success(res) {
                this.getData();
              },
              fail(err) {
                console.log("fail:" + JSON.stringify(err));
              },
            });
          });
      } else if (this.payIndex == 1) {
        //微信
      }
    },
    setPriceOther(event) {
      this.payPirce = event.detail.value;
    },
    getData() {
      this.$http.get("/api/user/wallet").then((res) => {
        this.balance = res.message.total_amount;
        this.ali_bind = res.message.ali_bind;
      });
    },
    clickItem(index) {
      this.index = index;
    },
    goNav(url) {
      this.navigateTo({
        url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .uni-input-input {
  color: #000 !important;
}

.withdrawal {
  width: 96rpx;
  height: 46rpx;
  font-size: 32rpx;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  line-height: 46rpx;
  background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 24rpx;
}

.appPage {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .content {
    flex: 1;
    padding: 46rpx 32rpx;
    width: 750rpx;
    margin-top: 26rpx;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0rpx 0rpx;

    .protocol {
      color: #6f6f6f;
      margin-top: 32rpx;
      text-align: center;
      justify-content: center;

      .catalogue {
        font-size: 32rpx;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        line-height: 46rpx;
        background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .btn {
      width: 686rpx;
      height: 94rpx;
      background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
      border-radius: 140rpx 140rpx 140rpx 140rpx;
      opacity: 1;
      font-size: 32rpx;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      color: #ffffff;
      line-height: 94rpx;
      text-align: center;
      margin-top: 42rpx;
    }

    .status {
      color: #3d3d3d;
      margin-top: 32rpx;
      width: 686rpx;
      // height: 181rpx;
      background: #f9f9f9;
      border-radius: 16rpx;
      padding: 32rpx;

      .txt {
        margin-left: 24rpx;
        font-size: 28rpx;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 41rpx;
      }
    }

    .gridBox {
      display: grid;
      /*  声明了三列，宽度分别为 200px 100px 200px */
      grid-template-columns: 100px 100px 100px;
      grid-gap: 25px;

      .box {
        margin-top: 32rpx;

        .num {
          // background-color: red;
          padding: 42rpx 55rpx;
          color: #3d3d3d;
          position: absolute;
          z-index: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;

          .priceNum {
            // margin-right: 8rpx;
            font-size: 48rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 700;
            color: #3d3d3d;
            line-height: 156rpx;
          }

          .bottom_price {
            margin-top: 42rpx;
            font-size: 32rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            color: rgba(61, 61, 61, 0.82);
            line-height: 35rpx;
            text-align: center;
          }

          .bottom_price::first-letter {
            font-size: 50%;
          }
        }

        .money {
          width: 221rpx;
          height: 240rpx;
        }
      }
    }

    .input-width {
      width: 250rpx;
      margin-left: 44rpx;
    }

    .otherPrice {
      height: 110rpx;
      background: #f9f9f9;
      box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.16);
      border-radius: 16rpx;
      padding: 32rpx;

      .txt {
        width: 128rpx;
        font-size: 32rpx;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 46rpx;
      }
    }

    .first {
      margin-bottom: 42rpx;

      .left {
        font-size: 32rpx;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        color: #3d3d3d;
      }
    }
  }

  .price {
    margin-top: 46rpx;
    font-size: 32px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    color: #ffffff;
  }

  .price::first-letter {
    font-size: 42%;
  }

  .title {
    font-size: 28rpx;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    color: #ffffff;
    line-height: 41rpx;
    margin-top: 33rpx;
  }

  .bg {
    z-index: -1;
    position: absolute;
    width: 100%;
    height: 470rpx;
  }
}
</style>
