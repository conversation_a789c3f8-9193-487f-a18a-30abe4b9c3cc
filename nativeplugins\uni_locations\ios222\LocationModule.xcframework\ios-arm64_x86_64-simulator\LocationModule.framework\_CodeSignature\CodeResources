<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		T3OgQ7qIRSpLMXYp+gSh77uDGm8=
		</data>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		N8IVXpZYHssTJP8Fw35oREcxAl4=
		</data>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		Ut5JUFtvWybOUP1OvSBY1vraKp8=
		</data>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zNi5d9kTHNJdNEyyg/sxvfvtBJE=
		</data>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		Ut5JUFtvWybOUP1OvSBY1vraKp8=
		</data>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		JBKTKL87WD7eCK1XteodN8HTgRE=
		</data>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		N8IVXpZYHssTJP8Fw35oREcxAl4=
		</data>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		YlkdbySod45SlYidupLr4bSD5RA=
		</data>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		6SDL/LVZC0bdAu7A+osPxhsVgCk=
		</data>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		YlkdbySod45SlYidupLr4bSD5RA=
		</data>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		8/5lUWj4tJHQtrfjjMM3DEVdUS0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			tePfKEiT970W8mfxakvv5ppLFe3pzdRchMJQdeNAUDA=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WcJU6kTMAiJGkqseqKtRC4UYHKtgiN8oOMZPqxirXUY=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			9MrY4mGOJd31Ubw4FrBXHqRvAjOUoca2Xq8rxBeInpA=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WcJU6kTMAiJGkqseqKtRC4UYHKtgiN8oOMZPqxirXUY=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			O1j1GY4KmtlO1ZjGdPn0OSHHlbLiZjlFYbUUk0TG2PI=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			tePfKEiT970W8mfxakvv5ppLFe3pzdRchMJQdeNAUDA=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			Jm4g01sRKo0guzZyYOS5DPpQOd3tdrs79p+oGC0gkfM=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			SX5UIQLGwojKQGdmb8RFaVsAWPETFX7R4M+3DUaiFwo=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			Jm4g01sRKo0guzZyYOS5DPpQOd3tdrs79p+oGC0gkfM=
			</data>
		</dict>
		<key>Modules/LocationModule.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			S24KELhzClmrWXWj00805B9utOjnJakSsclyIIK/R/c=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
