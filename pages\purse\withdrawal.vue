<template>
	<view class="appPage">
		<image class="bg" src="../../static/images/chongzhibg.png" mode=""></image>
		<u-navbar title="提现" :border-bottom="false" title-color="#fff" back-icon-color="#fff"
			:background="background"></u-navbar>
		<view class="" style="padding:0 32rpx;">
			<view class="title">钱包金额（元）</view>
			<view class="price">￥{{balance}}</view>
		</view>
		<view class="content">
			<view class="first ">
				<view class="left">
					提现
				</view>
			</view>
			<view class="otherPrice t_display">
				<view class="txt">提现金额</view>
				<input v-model="price" class="input-width" @input="replaceInput" placeholder="请输入提现金额" type="digit"
					cursor-color="#000" />
			</view>
			<view class="withdrawalStatus">
				选择提现方式
			</view>
			<view class="status">
				<view class="t_betweent" v-for="(item,idx) in payStatus" :key="idx" @click="payIndex=idx"
					:style="idx>0?'margin-top: 32rpx;':''">
					<view class="t_display">
						<image class="img42" :src="'../../static/images/pay/'+item.img" mode=""></image>
						<span class="txt">{{item.name}}</span>
					</view>
					<image class="img32"
						:src="payIndex===idx?'../../static/images/pay/radio2.png':'../../static/images/pay/radio.png'"
						mode=""></image>
				</view>
			</view>
			<view class="protocol">提现须知</view>
			<view class="explain">
				1.您可以选择提现到微信或支付宝内。
			</view>
			<view class="explain">
				2.提交提申请后，正常订单会在47小时内到账
			</view>
			<view class="explain">
				3.单笔可提现上线500（自定义）元
			</view>
			<view class="explain">
				4.提现服务费率0.10%（最低¥ 0.10）低于¥ 0.10免服务费
			</view>
			<view class="btn" @click="withdrawal">
				确定提现
			</view>
		</view>
		<u-toast ref='notify' />
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				price: "",
				background: {
					backgroundColor: 'transparent',
				},
				index: 0, //选择的金额下标
				payIndex: 0, //支付方式下标
				payStatus: [{
						name: "支付宝支付",
						img: "zhifubao.png"
					},
					// {
					// 	name: "微信支付",
					// 	img: "weixin.png"
					// },
				],
				balance: "",
				ali_bind: false,
			}
		},
		onShow() {
			this.getData()
		},
		methods: {
			replaceInput(event) {
				// 必须在nextTick中
				this.$nextTick(() => {
					this.price = event.target.value.match(/^\d*(\.?\d{0,2})/g)[0]
				})
			},
			getData() {
				this.$http.get('/api/user/wallet').then(res => {
					this.balance = res.message.total_amount
					this.ali_bind = res.message.ali_bind
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			withdrawal() {
				if (!this.price) return this.toast('请输入提现金额')
				switch (this.payIndex) {
					case 0:
						//判断是否绑定支付宝
						if (!this.ali_bind) {
							this.navigateTo({
								url: "/pages/purse/bindingAlipay"
							})
							return
						}
						this.pay(1)
						break;
					default:
						break;
				}

			},
			//支付宝提现
			pay(channel) {
				this.$http.post('/api/user/wallet/extract/ali', {
					"amount": parseFloat(this.price),
					channel //1 支付宝 2 微信
				}).then(res => {
					this.getData()
					this.toast('提现成功')
				})
			},
			clickItem(index) {
				this.index = index
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .uni-input-input {
		color: #000 !important;
	}

	.withdrawal {
		width: 96rpx;
		height: 46rpx;
		font-size: 32rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		line-height: 46rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		margin-right: 24rpx;
	}

	.appPage {
		display: flex;
		flex-direction: column;
		height: 100vh;

		.content {
			flex: 1;
			padding: 46rpx 32rpx;
			width: 750rpx;
			margin-top: 26rpx;
			background-color: #fff;
			border-radius: 24rpx 24rpx 0rpx 0rpx;

			.explain {
				margin-top: 16rpx;
				font-size: 24rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				color: #6F6F6F;
				line-height: 35rpx;
			}

			.withdrawalStatus {
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #3D3D3D;
				line-height: 46rpx;
				margin-top: 162rpx;
			}

			.protocol {
				margin-top: 86rpx;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #6F6F6F;
				line-height: 46rpx;
				text-align: center;
			}

			.btn {
				width: 686rpx;
				height: 94rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 140rpx 140rpx 140rpx 140rpx;
				opacity: 1;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 94rpx;
				text-align: center;
				margin-top: 42rpx;
			}

			.status {
				color: #3D3D3D;
				margin-top: 32rpx;
				width: 686rpx;
				// height: 181rpx;
				background: #F9F9F9;
				border-radius: 16rpx;
				padding: 32rpx;

				.txt {
					margin-left: 24rpx;
					font-size: 28rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #3D3D3D;
					line-height: 41rpx;
				}
			}

			.gridBox {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.box {
					margin-top: 32rpx;

					.num {
						padding: 42rpx 53rpx;
						color: #3D3D3D;
						position: absolute;
						z-index: 1;

						.priceNum {
							margin-right: 8rpx;
							font-size: 48rpx;
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 700;
							color: #3D3D3D;
							line-height: 70rpx;
						}

						.bottom_price {
							margin-top: 42rpx;
							font-size: 32rpx;
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 400;
							color: rgba(61, 61, 61, 0.82);
							line-height: 35rpx;
							text-align: center;
						}

						.bottom_price::first-letter {
							font-size: 50%;
						}
					}

					.money {
						width: 221rpx;
						height: 240rpx;
					}
				}
			}

			.input-width {
				width: 250rpx;
				margin-left: 44rpx;
			}

			.otherPrice {
				height: 110rpx;
				background: #F9F9F9;
				box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.16);
				border-radius: 16rpx;
				padding: 32rpx;

				.txt {
					width: 128rpx;
					font-size: 32rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #3D3D3D;
					line-height: 46rpx;
				}
			}

			.first {
				margin-bottom: 42rpx;



				.left {
					font-size: 32rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 700;
					color: #3D3D3D;
				}
			}

		}

		.price {
			margin-top: 46rpx;
			font-size: 32px;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
		}

		.price::first-letter {
			font-size: 42%;
		}

		.title {
			font-size: 28rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 41rpx;
			margin-top: 33rpx;
		}

		.bg {
			z-index: -1;
			position: absolute;
			width: 100%;
			height: 470rpx;
		}
	}
</style>