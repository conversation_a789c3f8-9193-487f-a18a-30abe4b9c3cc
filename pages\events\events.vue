<template>
	<view class="appPage" style="">
		<!-- <image class="bg" src="../../static/images/huodong.png" mode="aspectFill"></image> -->
		<!-- <image class="bg" src="../../static/images/faxian.png" mode="aspectFill" @click="showMessage"></image> -->
		<!-- <view class="txt">
			活动暂未开通
		</view> -->
		<u-navbar :customBack="goBack" :isBack="false" back-text="" title="活动主页"
			:background="{ backgroundColor: '#191C26' }" :border-bottom="false" height="60" title-color="#fff"
			back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">活动主页</view>
				</view>
			</view>
			<view class="nav_right" slot="right">
				<image class="hint" src="../../static/active/hint.png" @touchstart="showTooltip = true"
					@touchend="showTooltip = false" @touchcancel="showTooltip = false"></image>
				<image class="search" src="../../static/active/mine.png" @click="goMine"></image>
			</view>

			<!-- 气泡提示框 -->
			<view class="tooltip" v-if="showTooltip">
				<view class="tooltip-content">
					门票购买规则：规定了限制人数的门票活动最多可有200人购买门票，已核验人数达到限制人数后其余门票原额退款。
				</view>
				<view class="tooltip-arrow"></view>
			</view>
		</u-navbar>

		<view class="inputS">
			<uni-icons type="search" color="#fff" style="margin-right: 8rpx"></uni-icons>
			<u-input style="height: 68rpx; font-size: 28rpx; line-height: 38rpx" type="text" placeholder="搜索活动"
				@click="getSearch" :border="false" clearable disabled />
		</view>
		<view class="btn">
			<view :class="latest ? 'choosed' : 'choose'" @click="
          () => {
            this.page = 1
            this.dataArr = []
            this.newOrRecent = !newOrRecent
            if (this.newOrRecent) {
              getData('1')
            } else {
              getData('2')
            }
          }
        ">
				{{ newOrRecent ? '最新' : '距离最近' }}
				<image src="../../static/active/newOrRecent.png" mode="aspectFill" :style="
            newOrRecent
              ? 'width: 10rpx; height: 28rpx; margin-left: 20rpx;'
              : 'width: 10rpx; height: 28rpx; margin-left: 20rpx;transform: rotate(180deg);'
          ">
				</image>
			</view>
			<view :class="ranking ? 'choosed' : 'choose'" @click="
          () => {
            this.page = 1
            this.dataArr = []
            getData('3')
          }
        ">
				活动排行
			</view>
		</view>
		<scroll-view @scrolltolower="scrolltolower" :refresher-threshold="150" scroll-y="true" ref="scrollViewRef"
			:scroll-into-view="scrollIntoView" refresher-background="grey41" :scroll-x="false"
			style="width: 100%; height: 67vh">
			<!-- <view v-if="!recommendTotal" style="color: #fff;margin-top: 20rpx;text-align: center;">到底了~
			</view> -->
			<view id="top"></view>
			<view class="list" v-if="dataArr.length > 0" v-for="(item, index) in dataArr" @click="toDetails(item.uuid)">
				<image :src="item.cover" mode="aspectFill" style="
            width: 244rpx;
            height: 244rpx;
            margin-right: 20rpx;
            border-radius: 12rpx;
          "></image>
				<view class="active">
					<view class="title" style="font-weight: bold;">
						{{ item.title }}
					</view>
					<view class="t-distance" style="justify-content: space-between;">
						<view class="avatar" style="margin-top: 18px;">
							<image v-for="(src, index2) in item.member_avatar" :src="src" mode="aspectFill"
								:style="{ left: index2 * 20 + 'rpx' }"></image>
							<span v-if="item.people_cnt >= 5" style="
						  width: 40rpx;
						  height: 40rpx;
						  text-align: center;
						  line-height: 40rpx;
						  position: absolute;
						  top: 0;
						  left: 80rpx;
						  background-color: rgb(180, 180, 180);
						  border-radius: 50%;
						">
								...
							</span>
							<span v-if="item.member_avatar" :style="{
						  marginLeft: item.member_avatar.length * 20 + 40 + 'rpx',
						  color: 'rgb(224, 224, 224)',
						  fontSize: '18rpx',
						  lineHeight: '40rpx'
						}">共{{ item.people_cnt }}人</span>
						</view>
						<view v-show="item.need_ticket" class="ticket_tag">
							门票活动
						</view>
					</view>
					<view class="t-distance">
						<view class="t-distance">
							<image src="../../static/active/time.png" mode="aspectFill"
								style="width: 24rpx; height: 24rpx; margin-right: 10rpx">
							</image>
							{{ item.timeline }}
						</view>
						<view class="t-distance" style="margin-right: 22rpx;">
							<image src="../../static/active/distance.png" mode="aspectFill"
								style="width: 24rpx; height: 24rpx; margin-right: 12rpx">
							</image>
							{{ item.distance }}
						</view>
					</view>
				</view>
			</view>
			<view class="list" v-else style="text-align: center">
				<image src="../../static/active/zwhd.png" mode="aspectFill" style="
            width: 274rpx;
            height: 80rpx;
            margin-top: 140rpx;
            margin-bottom: 108rpx;
          ">
				</image>
			</view>
			<view style="width: 100%; height: 150rpx"> </view>
		</scroll-view>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				recommendTotal: true,
				page: 1,
				dataArr: [],
				latest: true,
				ranking: false,
				list_type: '1',
				scrollIntoView: '',
				newOrRecent: true,
				showTooltip: false
			}
		},
		mounted() {
			this.getData('1')
		},
		methods: {
			//隐藏在显示之后重新加载数据
			getFirstData() {
				console.log("隐藏在显示之后重新加载数据");
				this.page = 1
				this.dataArr = []
				this.latest = true
				this.ranking = false
				this.newOrRecent = true
				this.getData('1')
			},
			goBack() {
				uni.navigateBack()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: 'top',
				})
			},
			toDetails(e) {
				this.$http.get("/activity/detail", {
						uuid: e,
					})
					.then((res) => {
						console.log(res.message.activity.closed);
						if (res.message.activity.closed == 1) {
							this.toast('活动已关闭')
						} else if (res.message.activity.expired == 1) {
							this.toast('活动已结束')
						} else {
							uni.navigateTo({
								url: '/pages/activity/details?id=' + e,
							})
						}
					})

			},
			goMine() {
				uni.navigateTo({
					url: '/pages/activity/mine',
				})
			},
			scrollToTop() {
				let that = this
				this.$nextTick(() => {
					that.scrollIntoView = 'top'
					// this.$refs.scrollViewRef.scrollTo({
					// 	scrollTop: 0,
					// 	// duration: 300 // 动画持续时间，可选
					// });
				})
			},
			getData(e) {
				let type = this.list_type
				this.list_type = e
				if (e === '2' || e === '1') {
					this.latest = true
					this.ranking = false
				} else {
					this.latest = false
					this.ranking = true
				}
				if (type !== this.list_type) {
					this.page = 1
					this.scrollIntoView = ''
				}
				this.$http
					.get('/activity/list', {
						list_type: this.list_type,
						page: this.page,
					})
					.then((res) => {
						this.recommendTotal = res.message.length
						if (type === this.list_type) {
							this.dataArr.push(...res.message)
						} else {
							this.dataArr = res.message
							this.scrollToTop()
						}
					})
			},
			getSearch() {
				uni.navigateTo({
					url: '/pages/activity/active',
				})
			},
			scrolltolower(s) {
				if (this.recommendTotal) {
					this.page++
					this.getData(this.list_type)
				}
			},
			toggleTooltip() {
				this.showTooltip = !this.showTooltip
			}
			// showMessage() {
			// 	uni.navigateTo({
			// 		url: "/pages/events/event_detail"
			// 	})
			// },
		},
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding-left: 40rpx;
		padding-right: 40rpx;
	}

	.nav_right {
		display: flex;
		align-items: center;
	}

	.content {
		position: relative;
		background-color: #131517;
		font-size: 28rpx;
		color: #999;
		height: 100vh;
		width: 100vw;

		.txt {
			display: flex;
			align-items: center;
			text-align: center;
			margin-left: 250rpx;
			margin-top: 200rpx;
			color: #999;
			font-size: 36rpx;
		}

		.bg {
			margin-top: 140rpx;
			width: 730rpx;
			height: 10vh;
			display: flex;
			justify-content: center;
			// height: 34vh;
		}
	}

	.hint {
		width: 44rpx;
		height: 44rpx;
		margin-right: 40rpx;
		margin-top: 12rpx;
		padding: 12rpx;
	}

	.search {
		width: 60rpx;
		height: 60rpx;
		margin-right: 40rpx;
		margin-top: 12rpx;
	}

	.inputS {
		// width: 600rpx;
		width: 100%;
		color: #fff;
		background: #22252f;
		height: 97rpx;
		display: flex;
		align-items: center;
		border-radius: 50rpx;
		padding: 0 24rpx;
		margin-top: 17rpx;
		margin-bottom: 14rpx;
	}

	.btn {
		width: 100%;
		height: 96rpx;
		display: flex;
		justify-content: space-between;

		.choose {
			width: 314rpx;
			height: 68rpx;
			margin-top: 16rpx;
			border-radius: 12rpx;
			border: 1rpx solid rgb(59, 62, 70);
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.choosed {
			width: 314rpx;
			height: 68rpx;
			margin-top: 16rpx;
			border-radius: 12rpx;
			border: 1px solid rgb(82, 195, 238);
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.list {
		width: 100%;
		height: 284rpx;
		border-radius: 20rpx;
		background: rgb(56, 58, 68);
		margin-bottom: 30rpx;
		padding: 20rpx 34rpx 20rpx 20rpx;
		display: flex;

		.active {
			width: 352rpx;
			margin-top: 8rpx;

			.title {
				font-size: 32rpx;
				width: 100%;
				height: 88rpx;
				line-height: 44rpx;
				margin-bottom: 20rpx;
				overflow: hidden;
				-webkit-line-clamp: 2;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			.avatar {
				position: relative;
				margin-bottom: 40rpx;
				z-index: 9;

				image {
					position: absolute;
					border-radius: 50%;
					display: inline-block;
					top: 0;
					left: 0;
					width: 40rpx;
					height: 40rpx;
				}
			}

			.t-distance {
				font-size: 18rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.ticket_tag {
					background-image: url('../../static/active/ticket_tag.png');
					color: rgb(100, 61, 27);
					font-size: 20rpx;
					font-weight: 400;
					line-height: 28rpx;
					text-align: center;
					padding-left: 32rpx;
					padding-right: 8rpx;
					margin-right: 8rpx;
				}
			}
		}
	}

	.tooltip {
		position: fixed;
		right: 40rpx;
		top: 220rpx;
		background-color: rgb(102, 104, 115);
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
		z-index: 998;
		max-width: 600rpx;
	}

	.tooltip-content {
		font-size: 24rpx;
	}

	.tooltip-arrow {
		position: absolute;
		top: -16rpx;
		right: 100rpx;
		width: 0;
		height: 0;
		border-left: 16rpx solid transparent;
		border-right: 16rpx solid transparent;
		border-bottom: 16rpx solid rgb(102, 104, 115);
	}
</style>