<template>
	<view class="appPage">
		<barHeight></barHeight>
		<view class="head">
			<uni-icons type="left" color="#fff" style="margin-left: 30rpx;" @click="goBack"></uni-icons>
			<view class="inputBg">
				<uni-icons type="search" color="#fff" style="margin-right: 8rpx;"></uni-icons>
				<u-input v-model="username" type="text" placeholder="请输入关键字" :border="false" clearable />
			</view>
			<span class="search" @click="getData">
				搜索
			</span>
		</view>
		<view class="titleCard">
			展示结果
		</view>
		<Post :list="dataArr" :moreFlag="false" @setLike="setLike"></Post>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import Post from "@/components/post/post.vue"
	export default {
		components: {
			Post
		},
		data() {
			return {
				popupInfo: "",
				username: "",
				statusBarHeight: "",
				dataArr: [],
			}
		},
		watch: {
			// username: {
			// 	handler(oldVal, newVal) {
			// 		if (this.username) this.getData()
			// 	}
			// }
		},

		onLoad() {
			const system = uni.getStorageSync('system')
			this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
			// this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			setLike(item) {
				console.log(item.momentId);
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					"like": item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArr, item.index, {
						...this.dataArr[item.index],
						is_like: !item.isLike,
						like: this.dataArr[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			goBack() {
				uni.navigateBack()
			},
			cancelBlack(uid) {
				this.$http.post('/api/user/black-set', {
					uid,
					"opt": 2
				}).then(res => {
					this.getData()
					this.$refs.popup.close()
				})
			},
			getData() {
				this.$http.get('/api/moment/search-list', {
					keyword: this.username,
					page: 1,
					size: 10,
				}).then(res => {
					this.dataArr = res.message
				})
			},
			cancel() {
				this.$refs.popup.close()
			},
			showPopup(item) {
				this.popupInfo = item
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// height: 304rpx;
		background: #FFFFFF;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.titleCard {
		margin-bottom: 24rpx;
		width: 100%;
		height: 70rpx;
		background: #22252F;
		text-align: center;
		color: rgba(255, 255, 255, 0.64);

		line-height: 70rpx;
	}

	.head {
		display: flex;
		align-items: center;

		.search {
			width: 100rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}

		.inputBg {
			// width: 600rpx;
			width: 100%;
			margin: 32rpx;
			color: #fff;
			background: #22252F;
			height: 110rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 62rpx;
		}
	}



	.item {
		padding: 24rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.fous {
			width: 148rpx;
			height: 58rpx;
			text-align: center;
			line-height: 58rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #FFFFFF;
		}

		.avatar {
			width: 108rpx;
			height: 108rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 42rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 46rpx;
		}
	}
</style>