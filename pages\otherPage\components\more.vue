<template>
	<view class="">
		<uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="t_display" style="margin-bottom: 20rpx;">
					<image class="avatar" src="@/static/logo.png" mode="aspectFill"></image>
					<view class="name">{{popupInfo.nickName}}</view>
				</view>
				<view class="item t_display">
					<image class="disable" src="../../../static/images/lahei.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">拉黑</view>
						<view style="font-size: 24rpx;">将此人加入黑名单</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display">
					<image class="disable" src="../../../static/images/jubao.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">举报</view>
						<view style="font-size: 24rpx;">举报该内容</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="item t_display">
					<image class="disable" src="../../../static/images/lahei.png" mode=""></image>
					<view class="rightInfo">
						<view style="font-size: 26rpx;">减少此类推荐</view>
					</view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="close">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			popupInfo: {
				type: Object,
				default: {
					nickname: ""
				}
			}
		},
		data() {
			return {

			}
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			first() {
				this.$emit('first')
			},
			close() {
				this.$refs.popup.close()
			},
			open() {
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		// padding-top: 30rpx;
		// height: 304rpx;
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;


		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
		}

		.item {
			margin-top: 27rpx;
			padding-bottom: 18rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 20rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}
</style>