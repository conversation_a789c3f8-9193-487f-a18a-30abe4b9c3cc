<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">营销活动{{isEdit?'修改':'添加'}}</view>
		</view>
		<view class="content-container">
			<view v-if="isEdit" class="audit-status">
				<view class="audit-status-title">
					<view>活动审核状态</view>
					<view class="audit-status-title-label1" v-if="status == 1">已通过</view>
					<view class="audit-status-title-label2" v-else-if="status == 2">未通过</view>
				</view>
				<view v-if="status == 2" class="audit-status-content">
					<view class="audit-status-content-title">
						未通过原因：
					</view>
					<view>
						这是一段文字描述不一定几行，底部的告诉就是正常可以撑开的3行文字4行文字都可以
					</view>
				</view>
			</view>
			<view class="title">
				<textarea class="title-input" placeholder="请输入活动名称" v-model="title"></textarea>
				<hr class="title-hr" />
				<view class="title-images">
					<view class="title-big-image-container">
						<image class="title-big-image" :src="cover" mode="widthFix"></image>
					</view>
					<view class="images-add">
						<view class="images-tips">注：禁止发布违法内容</view>
						<view class="images-small">
							<image @click="upload" class="title-small-image" src="../../static/images/vip/image-add.png"
								mode=""></image>
						</view>
					</view>
				</view>
				<!-- <hr class="title-hr" />
				<view class="title-time">
					<view class="time-left">
						<image class="icon-time" src="../../static/images/vip/icon-time.png" mode=""></image>
						<view class="time-text">活动时间</view>
					</view>
					<view class="time-right">
						<view class="time-time" @click="open(1)">
							{{ begin_time }}
							<br />
							{{ end_time }}
						</view>
						<image class="right-icon" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view> -->
			</view>
			<view class="active-time">
				<view class="title-time">
					<view class="time-left">
						<image class="icon-time" src="../../static/images/vip/time-icon.png" mode=""></image>
						<view class="time-text">无时间限制</view>
					</view>
					<view class="time-right" @click="changeSwitch">
						<view v-if="isTimeLimit" class="time-switch">
							<image class="icon-switch" src="../../static/images/vip/switch-icon.png" mode=""></image>
						</view>
						<view v-else class="time-switch-active">
							<image class="icon-switch" src="../../static/images/vip/switch-icon.png" mode=""></image>
						</view>
					</view>
				</view>
			
				<view v-if="isTimeLimit" class="title-time border-top">
					<view class="time-left">
						<image class="icon-time" src="../../static/images/vip/icon-time.png" mode=""></image>
						<view class="time-text">活动截止时间</view>
					</view>
					<view class="time-right">
						<view class="time-time" @click="open(2)">
							<!-- 						{{ begin_time }}
							<br /> -->
							截止至 {{ end_time.split(" ")[0] }}
						</view>
						<image class="right-icon" src="../../static/images/vip/icon-go.png" mode=""></image>
					</view>
				</view>
			</view>
			<view class="active-content">
				<view class="content-title">
					<image class="content-icon" src="../../static/images/vip/iocn-image.png" mode=""></image>
					<view class="content-title-text">营销文案</view>
				</view>
				<hr class="content-hr" />
				<view class="content-content" v-if="content" v-html="content" @click="goContent"></view>
				<view class="content-content no-content" v-else @click="goContent">描述活动的要求和活动内容等</view>
				<!-- <view class="content-images">
					<view class="content-images-view">
						<image class="content-image-add" src="../../static/images/vip/content-add-image.png" mode=""></image>
						<image class="content-image-item" src="../../static/images/title-image.png" mode=""></image>
					</view>
				</view> -->
			</view>
			<!-- <view class="active-time"></view> -->
			<view class="active-button">
				<button class="button" type="default" @click="createActive">提交</button>
			</view>
		</view>
		<uv-datetime-picker ref="datetimePicker" :minDate="todayStart"  v-model="value" mode="date" @confirm="confirm"></uv-datetime-picker>
		<uv-datetime-picker ref="datetimePicker1" :minDate="todayStart"  v-model="value" mode="date" @confirm="confirm"></uv-datetime-picker>
		<!-- 提交成功弹框 -->
		<liu-popup bgColor="#ffffff" type="center" ref="submitSuccess" width="295px" height="140px" radius="20px"
			@open="popOpen">
			<view class="pop">
				<view class="pop-title">商户活动提交成功</view>
				<button class="pop-button" @click="back">返回上一页</button>
			</view>
		</liu-popup>
	</view>
</template>

<script>
	import color from '../../uview-ui/libs/function/color';
	import {
		config
	} from '@/config.js';
	export default {
		data() {
			return {
				title: '',
				cover: '',
				content: '',
				contentPlaceholder: "描述活动的要求和活动内容等",
				begin_time: '',
				end_time: '',
				business_id: '',
				value: Number(new Date()),
				times: 0,
				isTimeLimit: true,
				isEdit: false,
				status: 2,
				todayStart:""
			};
		},
		onShow() {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 前景色值，包括按钮、标题、状态栏的颜色
				backgroundColor: '#000000', // 背景颜色值，包括背景图
				animation: {
					duration: 400,
					timingFunc: 'easeIn'
				}
			});
			if (uni.getStorageSync('activeInfo') != '') {
				let activeInfo = uni.getStorageSync('activeInfo');
				this.title = activeInfo.title;
				this.cover = activeInfo.cover;
				this.begin_time = activeInfo.begin_time;
				this.end_time = activeInfo.end_time;
			}
			if (uni.getStorageSync('content') != '') {
				this.content = uni.getStorageSync('content');
				console.log(this.content);
			}
		},
		onLoad(options) {
			if (options.shopId) {
				this.business_id = options.shopId;
			}
			if (options.activeId) {
				this.isEdit = true;
			}

			this.getCurrentTime();


		},
		methods: {
			getCurrentTime() {
				const now = new Date();
				const year = now.getFullYear();
				const month = (now.getMonth() + 1).toString().padStart(2, '0');
				const day = now.getDate().toString().padStart(2, '0');
				const hours = now.getHours().toString().padStart(2, '0');
				const minutes = now.getMinutes().toString().padStart(2, '0');
				const seconds = now.getSeconds().toString().padStart(2, '0');
				this.begin_time = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
				this.end_time = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
				this.todayStart = new Date(year,now.getMonth(), day).getTime();
				console.log("今日时间戳",this.todayStart)
			},
			currentTime(date) {
				const now = new Date(date);
				const year = now.getFullYear();
				const month = (now.getMonth() + 1).toString().padStart(2, '0');
				const day = now.getDate().toString().padStart(2, '0');
				const hours = now.getHours().toString().padStart(2, '0');
				const minutes = now.getMinutes().toString().padStart(2, '0');
				const seconds = now.getSeconds().toString().padStart(2, '0');
				return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
			},
			open(e) {
				if (e == 1) {
					this.times = e;
				}
				this.$refs.datetimePicker.open();
			},
			confirm(e) {
				console.log('confirm', e);
				if (this.times == 1) {
					this.begin_time = this.currentTime(e.value);
					this.times = 0;
					this.$refs.datetimePicker1.open();
				} else {
					this.end_time = this.currentTime(e.value);
				}
			},
			back() {
				uni.setStorageSync('activeInfo', '');
				uni.setStorageSync('content', '');
				uni.navigateBack({
					delta: 1
				});
			},
			createActive() {
				this.$http
					.post('/api/user/business/activity/post', {
						title: this.title,
						cover: this.cover,
						content: this.content,
						begin_time: this.isTimeLimit ? this.begin_time : "",
						end_time: this.isTimeLimit ? this.end_time : "",
						business_id: this.business_id
					})
					.then((res) => {
						console.log(res);
						if (res.code == 200) {
							// 显示提交成功弹框
							this.openPopup("submitSuccess");
							uni.setStorageSync('activeInfo', '');
							uni.setStorageSync('content', '');
						}
					});
			},
			openPopup(e) {
				console.log(e)
				this.$refs[e].open();
			},
			popOpen() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0 // 动画时长，默认300ms
				});
			},
			upload() {
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let arr = [];
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4
								});
								this.cover = img;
							})
						);
					}
				});
			},
			goContent() {
				uni.setStorageSync('activeInfo', {
					title: this.title,
					cover: this.cover,
					begin_time: this.begin_time,
					end_time: this.end_time,
					business_id: this.business_id
				});
				uni.navigateTo({
					url: '/pages/activeAdd/activeContent'
				});
			},
			// 选择时间限制
			changeSwitch() {
				this.isTimeLimit = !this.isTimeLimit
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #f5f7fb;
	}

	.border-top {
		border-top: 1px solid #eaeaea;
	}

	.content {
		width: 100%;
		height: 100%;
		overflow:scroll;
		padding-top: 100px;
		background-color: #f5f7fb;

		.navigation-bar {
			width: 100%;
			display: flex;
			align-items: center;
			height: 100px;
			background-image: url('../../static/images/vip/newBackground.png');
			/* 背景图路径 */
			background-size: cover;
			position: fixed;
			z-index: 1000;
			top: 0;
			left: 0;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				z-index: 1;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
			}
		}

		.navigation-zhezhao {
			width: 100%;
			height: 100px;
			background-image: url('../../static/images/vip/nav-zhezhao.png');
			/* 背景图路径 */
			background-size: 100%;
			background-repeat: no-repeat;
			background-position: bottom;
			position:fixed;
			z-index: 1000;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			// padding-bottom: 10%;
			padding-top:3%;

			.back {
				width: 31px;
				height: 31px;
				margin-left: 2%;
				z-index: 1;
			}

			.nav-title {
				width: 82%;
				height: 30px;
				color: #000000;
				font-family: 阿里巴巴普惠体;
				font-size: 18px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0px;
				text-align: center;
				z-index: 1;
			}
		}
		.content-container{
			width: 92%;
			margin:0 auto;
		}
		.audit-status {
			position: relative;
			z-index: 1;
			width: 95%;
			height: calc(100vh - 170px);
			overflow-y: auto;
			margin-left: 2.5%;
			margin-top: 5%;
			padding: 17px 20px;
			box-sizing: border-box;
			border-radius: 10px;
			background: #ffffff;

			.audit-status-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #212121;
				font-family: HarmonyOS Sans;
				font-size: 16px;
				font-weight: 600;
				line-height: 20px;
				letter-spacing: 0px;
				text-align: left;

				.audit-status-title-label1 {
					border-radius: 4px;
					border: 1px solid #E3B772;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #E3B772;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}

				.audit-status-title-label2 {
					border-radius: 4px;
					border: 1px solid #F35323;
					font-family: 'HarmonyOS Sans';
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #F35323;
					letter-spacing: 0px;
					text-align: left;
					padding: 6px 12px;
				}
			}

			.audit-status-content {
				display: flex;
				align-items: flex-start;
				justify-content: space-evenly;
				padding-top: 13px;
				box-sizing: border-box;
				color: #666666;
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 400;
				line-height: 14px;
				border-top: 1px solid #EAEAEA;
				margin-top: 13px;

				.audit-status-content-title {
					width: 45%;
					max-width: 150px;
				}
			}
		}

		.title {
			width: 100%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;
			z-index: 1;

			.title-input {
				width: 90%;
				height: 100%;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin: 5%;
				color: #000;
				border: 0;
			}

			.title-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			.title-images {
				width: 100%;
				height: 190px;
				display: flex;
				justify-content: space-around;
				align-items: center;
			}

			.title-big-image-container {
				width: 140px;
				height: 140px;
				border-radius: 12px;
				overflow: hidden;
			}

			.title-big-image {
				width: 100%;
				height: 100%;
				border-radius: 12px;
				object-fit: cover;
			}

			.images-add {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				height: 150px;

				.images-tips {
					width: 100%;
					// height: 100%;
					font-size: 24rpx;
					font-weight: 400;
					color: #666666;
					margin-top: 10%;
				}

				.images-small {
					width: 100%;

					.title-small-image {
						width: 75px;
						height: 75px;
					}
				}
			}

			.title-time {
				display: flex;
				align-items: center;
				justify-content: center;
				padding-top: 5%;
				padding-bottom: 5%;
				width: 94%;

				.time-left {
					// margin: 3% auto;
					width: 50%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-time {
						width: 16px;
						height: 16px;
						margin-left: 5%;
					}

					.time-text {
						font-size: 16px;
						font-weight: 400;
						line-height: 22px;
						color: #212121;
						margin-left: 2%;
					}
				}

				.time-right {
					width: 50%;
					display: flex;
					justify-content: flex-end;
					align-items: center;

					.right-icon {
						width: 16px;
						height: 16px;
						margin-right: 5%;
					}

					.time-time {
						// width: 100px;
						font-size: 12px;
						font-weight: 400;
						line-height: 16px;
						color: #989898;
						margin-right: 2%;
						text-align: right;
					}
				}
			}
		}

		.active-content {
			width: 100%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;
			margin-bottom: 25%;

			.content-title {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin: 3% auto;

				.content-icon {
					width: 16px;
					height: 16px;
					margin-left: 5%;
				}

				.content-title-text {
					font-size: 16px;
					font-weight: 400;
					line-height: 22px;
					color: #212121;
					margin-left: 2%;
				}
			}

			.content-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			// .content-content {
			// 	width: 90%;
			// 	font-size: 16px;
			// 	font-weight: 400;
			// 	line-height: 22px;
			// 	color: #989898;
			// 	margin: 3% auto;
			// 	img{
			// 		width: 100%;
			// 		height: 100%;
			// 	}
			// }

			.content-content::v-deep {
				width: 95%;
				color: #212121;
				font-family: 'HarmonyOS Sans';
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				letter-spacing: 0px;
				text-align: left;
				// margin-top: 10px;
				border-radius: 10px;
				// background: #F5F7FB;
				// border: 1px solid #B2ABDA;
				padding: 3%;
				// margin-bottom: 10px;
				// min-height: 200px;
				overflow: hidden;

				p {
					margin-bottom: 16px;
					color: rgb(33, 33, 33);
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 20px;
					letter-spacing: 0px;
					text-align: left;
				}

				img {
					width: 100%;
					height: auto;
					border-radius: 10px;
					margin: 16px 0;
				}
			}

			.no-content {
				color: rgb(152, 152, 152);
				padding:3% 3% 10% 3%;
				box-sizing: border-box;
			}

			.content-images {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: space-around;
				align-items: center;

				.content-images-view {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;

					.content-image-add {
						width: 75px;
						height: 75px;
						margin: 3%;
					}

					.content-image-item {
						width: 75px;
						height: 75px;
						margin-right: 3%;
					}
				}
			}

		}

		.active-time {
			width: 100%;
			// height: 100%;
			background-color: #ffffff;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 5%;

			.title-hr {
				width: 90%;
				height: 1px;
				background-color: #eaeaea;
				border: 0;
			}

			.title-time {
				display: flex;
				align-items: center;
				justify-content: center;
				padding-top: 5%;
				padding-bottom: 5%;
				width: 94%;

				.time-left {
					width: 50%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.icon-time {
						width: 16px;
						height: 16px;
						margin-left: 5%;
					}

					.time-text {
						font-size: 16px;
						font-weight: 400;
						line-height: 22px;
						color: #212121;
						margin-left: 2%;
					}
				}

				.time-right {
					width: 50%;
					display: flex;
					justify-content: flex-end;
					align-items: center;

					.right-icon {
						width: 16px;
						height: 16px;
						margin-right: 5%;
					}

					.time-time {
						font-size: 12px;
						font-weight: 400;
						line-height: 16px;
						color: #989898;
						margin-right: 2%;
						text-align: right;
					}

					.time-switch {
						width: 28px;
						height: 16px;
						border-radius: 30px;
						background: rgba(215, 215, 215, 0.3);
						padding: 0 1px;
						box-sizing: border-box;
						display: flex;
						justify-content: start;
						align-items: center;

						.icon-switch {
							width: 14px;
							height: 14px;
						}
					}

					.time-switch-active {
						width: 28px;
						height: 16px;
						border-radius: 30px;
						background: rgba(116, 201, 255, 0.3);
						padding: 0 1px;
						box-sizing: border-box;
						display: flex;
						justify-content: flex-end;
						align-items: center;

						.icon-switch {
							width: 14px;
							height: 14px;
						}
					}
				}
			}

		}

		.active-button {
			width: 100%;
			background-color: #f5f7fb;
			height: 80px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: fixed;
			bottom: 0;
			left:0;
			padding-bottom: 1%;

			.button {
				width: 90%;
				height: 52px;
				background-image: url('../../static/images/vip/active-add-button.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border-radius: 10px;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				color: #ffffff;
			}
		}

		// 弹框
		.pop {
			width: 100%;
			height: 100%;
			// background-color: rgba(0, 0, 0, 0.5);
			z-index: 999;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 32px 0 20px 0;
			box-sizing: border-box;

			.pop-title {
				color: rgb(33, 33, 33);
				font-family: HarmonyOS Sans;
				font-size: 18px;
				font-weight: 400;
				line-height: 21px;
				letter-spacing: 0px;
				text-align: center;
				margin-bottom: 32px;
				width: 90%;
			}

			.pop-button {
				width: 136px;
				height: 36px;
				box-sizing: border-box;
				border: 1px solid rgb(120, 120, 120);
				border-radius: 60px;
				color: rgb(120, 120, 120);
				font-family: HarmonyOS Sans;
				font-size: 14px;
				font-weight: 400;
				line-height: 36px;
				background-color: transparent;
			}
		}
	}
</style>