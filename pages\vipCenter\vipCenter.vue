<template>
	<view class="appPage" :style="'background-image: url(../../static/images/vip/bg'+(swiperCurrnet+1)+'.png);'">
		<u-navbar back-text="" title="会员权益" :background="{backgroundColor: 'transparent'}" :border-bottom="false"
			title-color="#fff" back-icon-color="#fff" />
		<liu-slide-img :list="list" :type="2" :circular="false" :autoplay="false" @change="change"
			:current="swiperCurrnet"></liu-slide-img>
		<view class="">
			<swiper class="swiper" :current="swiperCurrnet" @change="change">
				<swiper-item v-for="item in list">
					<scroll-view class="swiper" :scroll-y="true">
						<view>
							<scroll-view :scroll-x="true">
								<view class="choose" v-if="swiperCurrnet!=0">
									<view :class="[index==cardIndex?'card_border':'card_grey']"
										v-for="(item,index) in arr" style="margin-right: 24rpx;">
										<view class="card" :class="{'d_border':index==cardIndex}"
											@click="setCardIndex(index)">
											<view class="name">{{item.name}}</view>
											<view class="price" :class="{'d_zt':index==cardIndex}">￥
												<span class="number"
													style="font-size: 55rpx;">{{item.now_amount}}</span>
											</view>
											<view class="oldPrice">￥
												<span class="numbers">{{item.origin_amount}}</span>
											</view>
											<view class="saveMoney" :class="{'d_bg':index==cardIndex}">
												立省￥{{item.preferential}}
											</view>
										</view>
									</view>
								</view>
							</scroll-view>

							<!-- <view class="tips" v-if="swiperCurrnet!=0">
								<span>自动订阅，随时取消；付款费用将记入itunes账户，到期前24小时会自动扣费并续订服务，购买后随时前往iTunes商店设置页面取消订阅。点击购买即表示同意</span>
								<span class="protocol" style="margin-right: 10rpx;"
									@click="goNav('/pages/privacyAgreement/userAgreement')">《用户服务》</span>
								<span class="protocol" @click="goNav('/pages/privacyAgreement/vip')">《连续包月协议》</span>
								<span class="protocol" @click="goNav('/pages/privacyAgreement/privacyAgreement')">
									《用户隐私政策》</span>
								
							</view> -->
							<view class="tips" v-if="swiperCurrnet!=0">
								<div style="display: flex;align-items: flex-start">
									<!-- <radio :checked="checked" @click="checked = !checked"
										style="transform:scale(0.6)" /> -->
									<image src="../../static/images/vip/svg.png" alt="" mode="widthFix" v-if="checked"
										style="width: 30rpx;transform: translateY(5px);" @click="checked = !checked" />
									<image src="../../static/images/vip/svg2.png" alt="widthFit" mode="widthFix" v-else
										style="width: 30rpx;transform: translateY(5px);" @click="checked = !checked" />
									<div style="margin-left: 20rpx;">
										<span style="line-height: 45rpx;">我已阅读并同意</span>
										<span class="protocol"
											@click="goNav('/pages/privacyAgreement/userAgreement')">《用户服务》</span>
										<span style="transform: translateX(-2px);">及</span>
										<span class="protocol"
											@click="goNav('/pages/privacyAgreement/vip')">《连续包月协议》</span>
										<span>及</span>
										<span class="protocol"
											@click="goNav('/pages/privacyAgreement/privacyAgreement')">
											《用户隐私政策》</span>
									</div>

								</div>
							</view>
							<view class="" style="padding: 32rpx;">
								您可通过以下方式取消连续包月服务 --- 如果是支付宝，可以在支付宝的我的>设置>支付设置>免密支付/自动扣款页面进行解约。
							</view>
							<view class="content">
								<view class="title">尊享{{privilegeNum[swiperCurrnet]}}项特权</view>
								<image :src="'../../static/images/vip/'+explainImg[swiperCurrnet]+'1.png'"
									class="firstImg" mode="widthFix">
								</image>
								<image :src="'../../static/images/vip/'+explainImg[swiperCurrnet]+'2.png'"
									class="firstImg" mode="widthFix">
								</image>
								<image :src="'../../static/images/vip/'+explainImg[swiperCurrnet]+'3.png'"
									class="secondImg" mode="widthFix"></image>
								<view class="" style="height: 20rpx;" />
							</view>
						</view>

						<view class="img323" />
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
		<view class="bottom">
			<view class="btn" :style="'background: '+btnColorArr[swiperCurrnet]" @click="goPay">
				{{btnTitle}}
			</view>
		</view>

		<!-- 选择支付方式弹窗 -->
		<Pay ref="pay" @confirm="confirm" :gid="arr[cardIndex] ? arr[cardIndex].id : null"
			:price="arr[cardIndex] ? arr[cardIndex].now_amount : 0"></Pay>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	import Pay from "./components/pay.vue"
	export default {
		components: {
			Pay
		},
		mounted() {
			this.getData()
		},
		data() {
			return {
				checked: false,
				cardIndex: 0,
				arr: [],
				explainImg: ["pt", "gj", "zx"],
				// btnTitle: ["以成为普通会员", "去解锁 ", "去解锁 "],
				privilegeNum: [2, 6, 9],
				btnColorArr: ['linear-gradient( 93deg, #4D486E 0%, #2F344D 100%)',
					'linear-gradient( 93deg, #20358E 0%, #72B1F9 100%);',
					'linear-gradient( 93deg, #422670 0%, #BC93F2 100%);'
				],
				list: [{
						src: "../../static/images/vip/swiper1.png"
					},
					{
						src: "../../static/images/vip/swiper2.png"
					},
					{
						src: "../../static/images/vip/swiper3.png"
					}
				],
				swiperCurrnet: 2,
				iapChannel: "",
			}
		},
		computed: {
			btnTitle() {
				const price = this.arr[this.cardIndex] ? this.arr[this.cardIndex].now_amount : '0'
				return this.swiperCurrnet == 0 ? '已成为普通会员' : '支付-总计￥' + price
			},
		},
		onLoad() {
			const userInfo = uni.getStorageSync('userInfo')
		},
		methods: {
			getData() {
				this.$http.get('/api/pay/vip-goods', {
					goods_type: this.swiperCurrnet,
					platform: {
						"android": 1,
						"ios": 2
					} [uni.getSystemInfoSync().platform],
				}).then(res => {
					this.arr = res.message
				})
			},
			setCardIndex(idx) {
				this.cardIndex = idx
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			confirm(pay) {
				const that = this
				plus.payment.getChannels((channels) => {
					for (var i in channels) {
						// 判断是否苹果支付
						if (channels[i].id === 'appleiap') {
							that.iapChannel = channels[i]
							that.requestOrder()
						}
					}

					if (that.iapChannel == '') {
						uni.showToast({
							icon: 'none',
							title: '暂不支持苹果 iap 支付'
						});
					}
				})
			},
			requestOrder() {
				const that = this
				const data = this.arr[this.cardIndex]
				// console.log('获取订单信息中...', data.apple_product_id)
				// ids 是平台申请拿到的内购商品的id
				// var ids = [data.apple_product_id.toString()];
				var ids = [data.apple_product_id];
				that.iapChannel.requestOrder(ids, function(event) {
					// 获取订单信息成功回调方法
					console.log('获取订单信息成功=>', event)
					that.topay(data.apple_product_id)
				}, function(erroemsg) {
					// 获取订单信息失败回调方法  
					console.log('requestOrder failed: ' + JSON.stringify(erroemsg));
					uni.hideLoading()
					uni.showToast({
						title: "获取支付通道失败：" + errormsg.message,
						icon: 'none'
					})
				});
			},
			topay(id) {
				const that = this
				uni.showLoading({
					title: '充值中请勿离开',
					mask: true
				})
				uni.requestPayment({
					provider: 'appleiap',
					orderInfo: {
						productid: id // 内购产品的product_id
					},
					success: (res => {
						console.log("==res===", res);
						const data = this.arr[this.cardIndex]
						this.$http.post('/api/pay/goods/vip/buy/apple', {
							"gid": data.id,
							"apple_productid": data.apple_product_id,
							"transactionIdentifier": res.transactionIdentifier, //交易id
							"transactionReceipt": res.transactionReceipt //校验体
						}).then(res => {
							// 根据平台显示不同的成功提示
							const platform = uni.getSystemInfoSync().platform;
							if (platform === 'ios') {
								this.toast('订阅成功')
							} else {
								this.toast('支付成功')
							}
							this.$http.get('/api/user/info').then(res => {
								uni.setStorageSync('userInfo', res.message.user_info)
							})
						})
					}),
					fail: (e) => {
						console.log('支付失败=>', e)
						uni.hideLoading()
						uni.showModal({
							content: "支付失败",
							showCancel: false
						})
					},
					complete: () => {
						uni.hideLoading()
						that.$refs.pay.close()
					}
				})
			},

			goPay() {
				if (!this.checked) return this.toast('请阅读并勾选同意协议')
				if (this.swiperCurrnet == 0) return
				let platform = uni.getSystemInfoSync().platform;
				if (platform === 'android') {
					this.$refs.pay.open()
				} else {
					this.$refs.pay.create()
				}
			},
			goNav(url) {
				if (this.swiperCurrnet == 0) return
				this.$common.navigateTo({
					url
				})
			},
			change(val) {
				this.swiperCurrnet = val.detail.current
				this.cardIndex = 0
				this.getData()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		background-size: cover;
		background-repeat: no-repeat;


		.swiper {
			height: 1300rpx;
		}

		.swiper-item {
			display: block;
			// height: 300rpx;
			// line-height: 300rpx;
			// text-align: center;
		}

		.choose {
			padding: 56rpx 32rpx 0 32rpx;
			display: flex;

			text-align: center;

			.cardIndex {
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			}

			.card_border {
				padding: 4rpx;
				background: linear-gradient(93deg, rgba(75.00000312924385, 198.00000339746475, 237.0000010728836, 1), rgba(188.0000039935112, 147.00000643730164, 242.00000077486038, 1));
				border-radius: 16rpx;
			}

			.card_grey {
				padding: 4rpx;
				background-color: #999BA1;
				border-radius: 16rpx;
			}

			.card {
				width: 208rpx;
				height: 224rpx;
				background: rgb(36, 38, 67);
				border-radius: 16rpx;
				// border: 4rpx solid #999BA1;
				overflow: hidden;
				position: relative;
				// box-sizing: border-box;

				.name {
					font-weight: 500;
					font-size: 28rpx;
					margin-top: 16rpx;
				}

				.saveMoney {
					position: absolute;
					bottom: 0;
					font-size: 18rpx;
					height: 36rpx;
					line-height: 36rpx;
					width: 100%;
					background: #10111E;
					text-align: center;
				}

				.oldPrice {
					font-size: 26rpx;
					font-weight: 500;
					color: #9E9E9E;
					transform: translateY(-18rpx);
					text-decoration: line-through;
				}

				.d_border {}

				.d_zt {
					background-image: linear-gradient(to right, #4BC6ED 10%, 60%, #BC93F2 70%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}

				.d_bg {
					background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				}

				.price {
					margin-top: 16rpx;
					font-size: 26rpx;
					font-weight: 600;
				}
			}

		}



		.tips {
			padding: 32rpx;
			font-size: 24rpx;
			color: #858585;
			line-height: 35rpx;
			padding-bottom: 0;

			.protocol {
				color: #fff;
			}
		}

		.bottom {
			position: fixed;
			width: 100%;
			height: 180rpx;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: start;

			.btn {
				margin-top: 45rpx;
				text-align: center;
				font-weight: 400;
				font-size: 32rpx;
				width: 686rpx;
				height: 98rpx;
				line-height: 98rpx;
				border-radius: 140rpx;
			}
		}


		.content {
			padding: 0 30rpx;

			.firstImg {
				width: 100%;
				margin-top: 32rpx;
				// height: 450rpx;
			}

			.secondImg {
				width: 100%;
				margin-top: 32rpx;
				// height: 637rpx;
			}

			.title {
				font-weight: 700;
				font-size: 32rpx;
				margin-top: 56rpx;
			}
		}
	}
</style>