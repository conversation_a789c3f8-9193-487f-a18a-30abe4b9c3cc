<template>
	<view>
		<uni-popup ref="popup" :safe-area="true" background-color="transparent">
			<view class="popup-content">
				<view class="user">
					<image class="avatar" :src="user.avatar" mode="aspectFill"></image>
					<text>{{user.remark_name||user.nickname}}</text>
				</view>
				<view class="item" @click="handleLahe">
					<image src="../../static/map/lahei.png"></image>
					<view class="row">
						<view class="label">拉到黑名单</view>
						<view class="val">你们将无法互发消息和申请加好友</view>
					</view>
				</view>
				<view class="item" @click="handleBeizhu">
					<image src="../../static/map/xiugai.png"></image>
					<view class="row">
						<view class="label">备注</view>
						<view class="val">为好友添加备注</view>
					</view>
				</view>
				<view class="item" @click="handleShanchu">
					<image src="../../static/map/shanchu.png"></image>
					<view class="row">
						<view class="label">删除</view>
						<view class="val">删除该好友</view>
					</view>
				</view>
				<view class="cancel">取消</view>
			</view>

		</uni-popup>
		<uni-popup ref="cc-popup">
			<view class="ccc">
				<view class="titlea">
					设置备注
				</view>
				<input type="text" v-model="setRemark">
				<view class="btnss">
					<view class="c" @click="cancel">取消</view>
					<view class="s" @click="submit">保存</view>
				</view>
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		apiUserBlackList,
		apiUserBlackSet,
		apiUserFollowDel,
		apiFansSetRemarkName
	} from '@/api/common.js'
	export default {
		data() {
			return {
				user: {},
				setRemark: ''
			}
		},
		methods: {
			submit() {
				console.log(this.user, 'aaaaaaaaaaaaaaaaa');
				apiFansSetRemarkName({
					"uid": this.user.uid,
					"remark_name": this.setRemark
				}).then(res => {
					if (res.code == 200) {
						this.toast('操作成功');
						this.$store.dispatch('GET_REMARK_LIST')
						this.$emit('refresh')
						this.$refs['cc-popup'].close('')
						this.close()
					}
				})
			},
			cancel() {
				this.$refs['cc-popup'].close('')
			},
			handleShanchu() {
				apiUserFollowDel({
					"uuid": this.user.uuid
				}).then(res => {
					if (res.code == 200) {
						this.toast('操作成功');
						this.$emit('refresh')
						this.close()
					}
				})
			},
			handleBeizhu() {
				this.close()
				this.setRemark = this.user.remark_name
				this.$refs['cc-popup'].open('center')

			},
			handleLahe() {
				apiUserBlackSet({
					"uid": this.user.uid,
					"opt": 1
				}).then(res => {
					if (res.code == 200) {
						this.toast('操作成功');
						this.$emit('refresh')
						this.close()
					}
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			open(option) {
				this.user = option
				this.$refs.popup.open('bottom')
			},
			close() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.ccc {
		width: 70vw;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 20rpx;

		.titlea {
			font-size: 28rpx;
			color: #333;
			text-align: center;
			margin-bottom: 40rpx;
			font-weight: bold;
		}

		input {
			background-color: #0f1117;
			color: #fff;
			border-radius: 50rpx;
			font-size: 24rpx;
			padding: 20rpx 10rpx 20rpx 30rpx;
			margin-bottom: 40rpx;
		}

		.btnss {
			display: flex;
			align-items: center;
			justify-content: space-between;

			view {
				width: 45%;
				padding: 20rpx;
				text-align: center;
				border-radius: 50rpx;
			}

			.c {
				color: #0f1117;
				border: 1rpx solid #0f1117;
			}

			.s {
				background-color: #0f1117;
				color: #fff;
				border: 1rpx solid #0f1117;

			}
		}
	}

	.popup-content {
		// height: 30vh;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		color: #000;
		padding: 20rpx;
		box-sizing: border-box;

		.user {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.avatar {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				margin-right: 20rpx;

			}

			text {
				font-weight: bold;
				font-size: 24rpx;
			}
		}

		.item {
			display: flex;
			align-items: center;
			border-bottom: 1rpx solid #ddd;
			padding: 20rpx 0;

			image {
				width: 50rpx;
				height: 50rpx;
				margin-right: 20rpx;
			}

			.row {
				font-size: 24rpx;

				.label {
					font-weight: bold;
				}

				.val {
					color: #999;
				}
			}
		}

		.cancel {
			width: 100%;
			height: 80rpx;
			background-color: #f6f6f6;
			font-weight: bold;
			text-align: center;
			line-height: 80rpx;
			margin-top: 30rpx;
			border-radius: 20rpx;
		}
	}
</style>