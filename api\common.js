import request from '@/utils/request'
// 上报位置信息
export function apiLocationReport(data) {
	return request.post('/location/report', data)
}
// 获取可见范围
export function apiLocationVisibleGet(data) {
	return request.get('/location/visible/get', data)
}

// 我在地图的位置信息
export function apiLocationMe(data) {
	return request.get('/location/me', data)
}
// 常用地址添加/编辑
export function apiAddressSet(data) {
	return request.post('/address/set', data)
}
// 常用地址删除
export function apiAddressDel(data) {
	return request.post('/address/del', data)
}
// 常用地址获取
export function apiAddressGet(data) {
	return request.get('/address/get', data)
}
// 搜索地图上的用户
export function apiLocationSearchPeople(data) {
	return request.get('/location/searchPeople', data)
}
// 设置可见范围
export function apiLocationVisible(data) {
	return request.post('/location/visible', data)
}
// 幽灵模式切换
export function apiLocationGhostSwitch(data) {
	return request.post('/location/ghost/switch-one', data)
}
// 幽灵模式获取
export function apiLocationGhostGet(data) {
	return request.post('/location/ghost/get', data)
}

// 获取月亮位置
export function apiGetNight(data) {
	return request.get('/location/night/get', data)
}
// 关注某人
export function apiFollowAdd(data) {
	return request.post('/api/user/follow/add', data)
}
// 页面聊天数提醒
export function apiNotifyCnt(data) {
	return request.get('/api/notify/cnt', data)
}
// 新的好友添加请求
export function apiNewFriend(data) {
	return request.get('/api/user/friend/record', data)
}
// 处理加好友请求
export function apiFriendAsk(data) {
	return request.post('/api/user/friend/ack', data)
}

// 获取token
export function apiToken(data) {
	return request.post('/auth/login-by-phone-code', {
		"phone": "15045135753",
		"captcha": "123"
	})
}
// 设置月亮
export function apiNightChange(data) {
	return request.post('/location/night/change', data)
}
export function apiNotifyComment(data) {
	return request.get('/api/notify/list/comment', data)
}
export function apiNotifyLike(data) {
	return request.get('/api/notify/list/like', data)
}


export function apiMomentReply(data) {
	return request.post('/api/moment/reply', data)
}
export function apiUserBlackList(data) {
	return request.get('/api/user/black-list', data)
}
export function apiUserBlackSet(data) {
	return request.post('/api/user/black-set', data)
}
export function apiUserFollowDel(data) {
	return request.post('/api/user/follow/del', data)
}
export function apiFansSetRemarkName(data) {
	return request.post('/api/fans/set-remarkName', data)
}

export function apiUserFollowAddMapInfo(data) {
	return request.post('/api/user/follow/add-mapinfo', data)
}


export function apiUserInfo(uuid) {
	return request.get('/share/user/im/' + uuid)
}

export function apiRemarkList(uuid) {
	return request.get('/api/by-im/remark-list')
}
// 发红包
export function apiWalletRedpacketGrant(data) {
	return request.post('/api/user/wallet/redpacket/grant', data)
}
// 获取地图红包
export function apiGetRedpacket(data) {
	return request.get('/api/user/redpacket-list', data)
}

// 红包详情
export function apiGetRedpacketDetail(data) {
	return request.get('/api/user/redpacket-detail', data)
}
// 领红包
export function apiRedpacketTake(data) {
	return request.post('/api/user/redpacket-take', data)
}
// 地图留言
export function apiMapMsg(data) {
	return request.get('/api/location-msg/map/search', data)
}
// 留言详情
export function apiMsgDetail(data) {
	return request.get('/api/location-msg/detail', data)
}
// 商家列表
export function apiBusinessList(data) {
	return request.get('/api/user/business/map-search', data)
}