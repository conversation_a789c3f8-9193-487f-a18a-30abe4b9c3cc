<template>
	<view>
		<uni-popup ref="popup" safe-area="true" type="bottom" @maskClick="close" :safe-area="false">
			<!-- 商户信息 -->
			<view class="merchant-list-father">
				<view class="merchant-list-container">
					<view class="merchant-list-title">
						<view class="title-container">
							<view class="shop">
								<view class="shop-title">{{ shopInfo.title }}</view>
								<view v-if="distance" class="shop-localtions">
									<image class="localtions-icon" src="../../../static/images/address-icon.png"
										mode="widthFix">
									</image>
									{{ distance }}km
								</view>
							</view>
							<view class="customer">
								<view class="customer-title" @click="showActionMenu">
									<image class="customer-title-item-icon"
										src="../../../static/images/report/gengduo.png" mode="">
									</image>
								</view>
								<view class="customer-total">
									参与人数 <view class="customer-total-number">{{ shopInfo.member_total }}</view>
								</view>
							</view>
						</view>
						<!-- 店铺公告 -->
						<view class="shop-notice" :style="{ height: isShopOpen ? 'auto' : '80px' }">
							<view class="shop-notice-item">
								<view class="shop-notice-title">店铺公告</view>
								<view class="shop-content">
									{{ shopInfo.describe ? shopInfo.describe : '暂无' }}
								</view>
							</view>
							<view class="shop-notice-item">
								<view class="shop-notice-title">客服电话</view>
								<view v-if="shopInfo.srv_phone" class="shop-content">{{ shopInfo.srv_phone }}</view>
								<view v-else class="shop-content">暂无</view>
							</view>
							<view class="shop-notice-item">
								<view class="shop-notice-title">商户地址</view>
								<view v-if="shopInfo.address" class="shop-content">{{ shopInfo.address }}</view>
								<view v-else class="shop-content">暂无</view>
							</view>
							<view class="shop-open-container" @click="isShopOpen = !isShopOpen">
								<view v-if="!isShopOpen" class="shop-button">
									展开
									<image class="arrow-icon" src="../../../static/images/down-icon.png" mode="">
									</image>
								</view>
								<view v-else class="shop-button">
									收起
									<image class="arrow-icon" src="../../../static/images/up-icon.png" mode="">
									</image>
								</view>
							</view>
						</view>
					</view>
					<!-- 活动列表 -->
					<view class="merchant-activity">
						<view class="merchant-activity-title-container">
							<view class="merchant-activity-title" @click="changeTitle(index)"
								:class="{ 'merchant-activity-title-active': index === activityTitleIndex }"
								v-for="(item, index) in activityTitle" :key="index">
								<image class="activity-title-icon" src="../../../static/images/pinglun.png" mode="">
								</image>
								{{ item.name }}
							</view>
						</view>
						<!-- 活动列表 -->
						<!-- <view v-if="!show"> -->
						<view v-if="activityTitleIndex == 0" class="merchant-activity-list">
							<view v-if="activeList && activeList.length > 0">
								<view v-for="(item, index) in activeList" class="item" @click.stop="goDetails(item)">
									<view class="item-cover">
										<view class="item-cover-active-image" @click="goDetails(item)">
											<image class="activeimage" :src="item.cover" mode="widthFix">
											</image>
										</view>

										<view class="activeimagecover" v-if="item.expired">
											<image class="activeimageend" src="../../../static/images/activeEnd.png"
												mode="">
											</image>
										</view>
									</view>
									<view class="item-info">
										<view class="item-title twoDot">
											{{ item.title }}
										</view>
										<view class="item-total">
											<view>参与人数{{ item.member_total }}</view>
											<view v-if="item.expired" class="item-button item-button-end">
												活动结束
											</view>
											<view v-else-if="item.is_join" class="item-button item-button-unjoin"
												type="default" @click.stop="exitActivity(item)">
												已参加
											</view>
											<view v-else class="item-button item-button-unjoin" type="default"
												@click.stop="joinActivity(item)">
												参加
											</view>
											<!-- <view v-if="state==1" class="item-button item-button-unjoin" type="default">
													参加
												</view>
												<view v-else-if="state==2" class="item-button item-button-join">
													已参加
												</view> -->
										</view>
										<view class="item-time">
											<image class="time-clock" src="../../../static/images/vip/clock.png"
												mode=""></image>
											<view class="time">{{ item.timeline }}</view>
										</view>
									</view>
								</view>
							</view>
							<view v-else>
								暂无活动列表！
							</view>
						</view>
						<!-- 展品列表 -->
						<view v-else-if="activityTitleIndex == 1">
							<view v-if="exhibitList && exhibitList.length > 0" class="exhibit-list">
								<view v-for="(item, index) in exhibitList" @click="goDetails(item)" :key="index"
									class="exhibit-list-item">
									<view class="exhibit-list-item-cover">
										<image class="exhibit-list-item-cover-image" :src="item.cover"
											mode="aspectFill">
										</image>
									</view>
									<view class="exhibit-list-item-title twoDot">{{ item.title }}</view>
									<view class="exhibit-list-item-price">￥{{ item.price }}</view>
									<!-- v-if="distance*1>0 && distance*1000<1000" -->
									<view class="exhibit-list-item-total">
										<view class="exhibit-list-item-total-like exhibit-list-item-total-item">
											<image class="exhibit-list-item-total-icon"
												src="../../../static/images/like-icon.png" mode="widthFix"></image>
											{{ item.like >= 1000 ? item.like / 1000 : item.like }} <text
												v-if="item.like >= 1000">k</text>
										</view>
										<view class="exhibit-list-item-total-comment exhibit-list-item-total-item">
											<image class="exhibit-list-item-total-icon"
												src="../../../static/images/comment-icon.png" mode="widthFix"></image>
											{{ item.comment >= 1000 ? item.comment / 1000 : item.comment }} <text
												v-if="item.comment >= 1000">k</text>
										</view>
									</view>
								</view>
								<!-- <view class="exhibit-list-tips">一百米以内可进行点赞或评论</view> -->
							</view>
							<view v-else style="padding:20px;box-sizing: border-box;">
								暂无展品列表！
							</view>
						</view>
						<!-- </view> -->
						<!-- <Load text="加载中.." :mask="true" :show="show" ref="loading" /> -->
					</view>
				</view>
				<mark-simpleshare ref="shareComponent" type="sh"></mark-simpleshare>
				<Uqrcode ref="uqrcode" canvas-id="qrcode" size="500" :value="href" :options="{ margin: 10 }" hide>
				</Uqrcode>
			</view>
		</uni-popup>

		<!-- 底部操作菜单弹窗 -->
		<uni-popup ref="actionPopup" type="bottom" @change="popupChange" :safe-area="false">
			<view class="action-popup">
				<view class="action-popup-content">
					<view class="action-item" @click="handleAction('分享')">
						<image class="action-icon" src="../../../static/images/report/fenxianggeihaoyou.png"
							mode="aspectFit"></image>
					</view>
					<view class="action-item" @click="handleAction('客服')">
						<image class="action-icon" src="../../../static/images/report/lianxikefu.png" mode="aspectFit">
						</image>
					</view>
					<view class="action-item" @click="handleAction('举报')">
						<image class="action-icon" src="../../../static/images/report/shangjiajubao.png"
							mode="aspectFit">
						</image>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import Load from "../../my/components/t_loading.vue"
	import markSimpleshare from "@/components/mark-simpleshare/mark-simpleshare.vue";
	import Uqrcode from "@/components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue"
	export default {
		components: {
			Load,
			Uqrcode,
			markSimpleshare
		},
		data() {
			return {
				business_id: "",
				business_lat: "",
				business_lng: "",
				// 活动列表
				state: 3,
				isShopOpen: false, //是否展开
				activityTitle: [{
						name: "活动列表",
						icon: "pinglun.png"
					},
					{
						name: "展品列表",
						icon: "topTop.png"
					}
				],
				href: "",
				activityTitleIndex: 0,
				shopInfo: {},
				activeList: [],
				exhibitList: [],
				activePage: 1,
				activePagesize: 10,
				distance: 0,
				show: true,
				memberTotalSum: 0,
				qrCodeImage: "",
			}
		},
		onLoad(options) {
			console.log("商家id")
			console.log(options)
			this.business_id = options.business_id
			this.business_lat = options.business_lat
			this.business_lng = options.business_lng
		},
		onPullDownRefresh() {
			// if ([0].includes(this.current)) {
			// 	uni.stopPullDownRefresh();
			// }
		},
		onReachBottom() {

		},
		onShow() {

			// setTimeout(() => {
			// 	this.$refs.mapRef.handelCenter()
			// 	this.$refs.mapRef.openTimeInterval('go')
			// }, 300)

		},
		onHide() {
			// this.$refs.mapRef.cleatTimeout()
		},

		watch: {
			current: {
				handler() {
					// this.$refs.myRef.getUpdate()
				}
			}
		},
		//		uniapp子组件不支持应用生命周期， 所以只能用vue生命周期
		created() {

		},
		methods: {
			// 打开分享面板
			openShare() {
				// 使用uqrcode组件获取base64图像
				// if (this.$refs.uqrcode) {
				// 	this.$refs.uqrcode.toBase64({
				// 		success: res => {
				// 将base64赋值给cardData.qrCodeImage
				// this.qrCodeImage = res.base64;
				this.$http
					.get("/api/user/qrcode-generate", {
						content: this.href,
					})
					.then((res) => {
						this.qrCodeImage = 'data:image/*;base64,' + res.message
						const latLng = this.shopInfo.coordinate.split(',')
						// 准备卡片数据
						const cardData = {
							title: this.shopInfo.title,
							lat: latLng[0],
							lng: latLng[1],
							address: this.shopInfo.province,
							memberCount: this.shopInfo.member_total,
						};
						console.log("商户详情信息", this.shopInfo)
						// 调用分享组件
						this.$refs.shareComponent.show({
							...cardData,
							qrCodeImage: this.qrCodeImage
						});

						// 监听保存卡片图片事件
						uni.$on('saveCardImage', (cardId) => {
							if (cardId === this.activeData.card.uuid) {
								this.saveCardAsImage();
							}
						});
					})
				// 		},
				// 		fail: err => {
				// 			console.error("二维码生成失败:", err);
				// 			uni.showToast({
				// 				icon: "none",
				// 				title: "二维码生成失败"
				// 			})
				// 		}
				// 	});
				// }

			},
			open(option) {
				this.BusinessId = option.Name.split(':')[0];
				this.$http.get('/api/user/business/detail', {
					business_id: option.Name.split(':')[0]
				}).then((res) => {
					if (res.code === 200) {
						this.shopInfo = res.message;
						console.log("商户详情信息", this.shopInfo)
						this.href =
							`https://static.lluuxiu.com/v1/download.html?type=sh&uuid=${option.Name}&coordinate=${this.shopInfo.coordinate}`;
						this.getActiveList();
						this.getExhibitList();
						this.getDistance(option); //计算距离
					}
				});
				// this.$refs.popup.open('bottom')
				// this.$http.get('/api/user/info').then(res => {
				// 	this.userInfo = res.message.user_info
				// 	uni.setStorageSync('userInfo', res.message.user_info)
				// 	console.log('.............', res);
				// 	this.$refs.popup.open('bottom')
				// })
			},
			close() {
				this.$refs.popup.close()
			},
			// 去活动详情页
			goDetails(item) {
				console.log("去活动列表详情", item)
				var type = this.activityTitleIndex * 1 === 0 ? 'activity' : 'exhibits'
				var isCanLike = this.distance > 0 && this.distance * 1000 < 1000 ? 1 : 0;
				uni.navigateTo({
					url: '/pages/merchantStoreDetails/merchantStoreDetails?id=' + item.id + "&type=" + type +
						"&business_id=" + this.BusinessId + "&isCanLike=" + isCanLike

				})
			},
			// 切换title
			changeTitle(index) {
				this.show = true;
				this.activityTitleIndex = index;
				this.activePage = 1;
				// this.getActiveList()
				console.log(this.distance)
			},
			// 获取商户信息
			getShopInfo() {
				// this.$http.get('/api/user/business/get').then((res) => {
				// 	if (res.code === 200) {
				// 		this.shopInfo = res.message;
				// 		console.log("商户信息")
				// 		console.log(this.shopInfo)
				// 		this.getActiveList();//获取活动列表
				// 		this.getDistance();//计算距离
				// 	}
				// });
				console.log("获取商户信息 BusinessId", this.BusinessId);
				this.$http.get('/api/user/business/detail', {
					business_id: this.BusinessId
				}).then((res) => {
					if (res.code === 200) {
						this.shopInfo = res.message;
						console.log("商户信息", this.shopInfo)
						this.getActiveList();
						this.getExhibitList();
						// this.getDistance();//计算距离
					}
				});
			},
			// 获取活动列表
			getActiveList() {
				// var url = this.activityTitleIndex * 1 === 0 ? '/api/user/business/activity/list' :
				// 	'/api/user/business/exhibits/list'
				var url = '/api/user/business/activity/list';
				var aa = {
					business_id: this.shopInfo.id,
					page: this.activePage,
					size: this.activePagesize
				}
				this.$http.get(url, {
					business_id: this.shopInfo.id,
					page: this.activePage,
					size: this.activePagesize
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						// this.show = true
						this.activeList = res.message.list ? res.message.list : res.message;
						this.memberTotalSum = this.activeList.reduce((sum, item) => sum + item.member_total, 0);
						this.$refs.popup.open('bottom')
						setTimeout(() => {
							this.show = false
						}, 300)
					} else {
						setTimeout(() => {
							this.show = false
						}, 300)
					}
				});
			},
			// 获取展品列表
			getExhibitList() {
				var url = '/api/user/business/exhibits/list'
				this.$http.get(url, {
					business_id: this.shopInfo.id,
					page: this.activePage,
					size: this.activePagesize
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						// this.show = true
						this.exhibitList = res.message.list ? res.message.list : res.message;
						this.$refs.popup.open('bottom')
						setTimeout(() => {
							this.show = false
						}, 300)
					} else {
						setTimeout(() => {
							this.show = false
						}, 300)
					}
				});
			},
			// 参加活动
			joinActivity(item) {
				this.$http.post("/api/user/business/activity/join", {
					id: item.id
				}).then((res) => {
					console.log("点击参加活动", res);
					if (res.code === 200) {
						uni.showToast({
							title: "参加成功！"
						})
						// this.memberTotalSum++;
						// item.member_total++;
						// item.is_join= true;
						this.getActiveList();
						// this.getExhibitList();
						// this.getShopInfo();
					}
				});
			},
			// 退出活动
			exitActivity(item) {
				console.log("退出活动", item)
				this.$http.post("/api/user/business/activity/quit", {
					id: item.id
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						uni.showToast({
							title: "已退出该活动！"
						})
						// this.memberTotalSum--;
						// item.is_join= false;
						this.getShopInfo();
						// this.getExhibitList();
					}
				});
			},
			// 计算距离
			getDistance(option) {
				uni.getLocation({
					type: 'wgs84',
					isHighAccuracy: true,
					success: async res => {
						// 商家和用户之间距离
						console.log("商家和用户之间距离")
						console.log(res)
						console.log(option)
						this.calculateDistance(res.latitude, res.longitude, option.Latitude, option.Longitude);
					}
				})
			},
			// 计算距离
			calculateDistance(lat1, lon1, lat2, lon2) {
				var R = 6371; // 地球半径，单位千米
				var dLat = (lat2 - lat1) * Math.PI / 180; // 纬度差转换为弧度
				var dLon = (lon2 - lon1) * Math.PI / 180; // 经度差转换为弧度

				var a =
					Math.sin(dLat / 2) * Math.sin(dLat / 2) +
					Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
					Math.sin(dLon / 2) * Math.sin(dLon / 2); // Haversine公式中的a部分
				var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)); // Haversine公式中的c部分
				var d = R * c; // 最终距离

				// return d; // 返回距离，单位千米
				console.log(d.toFixed(2) + ' km');
				this.distance = d.toFixed(2) == 'NaN' ? 0 : d.toFixed(2);
			},
			// 发消息
			sendMsg() {
				// uni.navigateTo({
				// 	url: '/pages/chat-list/chat-list'
				// });
				console.log("发消息")
				console.log(this.shopInfo)
				let params = {
					account: this.shopInfo.user_info.im_id,
					chatHeadImg: this.shopInfo.user_info.avatar ? this.shopInfo.user_info.avatar : "",
					chatName: this.shopInfo.user_info.nickname,
					uid: 'p2p-' + this.shopInfo.user_info.im_id,
					roomtype: 'p2p',
					nuck: this.shopInfo.user_info.nickname,
				}
				uni.navigateTo({
					url: "/pages/HM-chat/HM-chat?userItem=" + encodeURIComponent(JSON.stringify(params))
				})
			},
			// 联系客服
			makePhoneCall(phoneNumber) {
				// 检查是否在5+ App环境中
				if (typeof plus !== 'undefined') {
					// 使用plus.device.dial方法拨打电话
					plus.device.dial(phoneNumber, false);
				} else {
					// 如果不在5+ App环境中，可以给出提示或者使用其他方法
					console.log('当前环境不支持拨打电话');
				}
			},
			// 显示更多菜单
			showActionMenu() {
				console.log('显示操作菜单');
				this.$refs.actionPopup.open('bottom');
			},
			// 关闭更多菜单
			closeActionPopup() {
				console.log('关闭操作菜单');
				this.$refs.actionPopup.close();
			},
			// 处理操作菜单点击事件
			handleAction(action) {
				console.log('处理操作:', action);
				switch (action) {
					case '分享':
						this.openShare();
						break;
					case '客服':
						this.makePhoneCall(this.shopInfo.srv_phone);
						break;
					case '举报':
						uni.navigateTo({
							url: "/pages/report/report?params=" + this.shopInfo.id + '&type=3',
						})
						break;
					default:
						break;
				}
				this.closeActionPopup();
			},
			// 监听弹窗状态变化
			popupChange(e) {
				console.log('弹窗状态变化', e);
			}
		},
	}
</script>
<style lang="scss" scoped>
	.uni-popup {
		z-index: 999;
	}

	// 活动列表
	.merchant-list-father {
		width: 100%;
		height: 80vh;
		border-radius: 20px 20px 0px 0px;
		background: rgb(35, 35, 45);
		overflow-y: scroll;
		padding-bottom: 0;
		box-sizing: border-box;
	}

	.merchant-list-container {

		padding: 20px 0;
		box-sizing: border-box;

		.title-hr {
			width: 100%;
			height: 1px;
			background-color: #787878;
			border: 0;
		}

		.merchant-list-title {
			width: 100%;
			padding: 0 20px;
			box-sizing: border-box;
			margin-bottom: 20px;

			.title-container {
				display: flex;
				align-items: flex-start;
				justify-content: space-between;

				// margin-bottom:20px;
				.shop {
					.shop-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 22px;
						font-weight: 700;
						line-height: 26px;
						margin-bottom: 8px;
					}

					.shop-localtions {
						width: auto;
						min-width: 48px;
						// height: 14px;
						padding: 2px 4px;
						box-sizing: border-box;
						border-radius: 1px;
						background: rgba(143, 163, 201, 0.23);
						display: flex;
						align-items: center;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 9px;
						font-weight: 400;
						line-height: 11px;

						.localtions-icon {
							width: 10px;
							height: 10px;
						}
					}
				}

				.customer {
					.customer-title {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						margin-bottom: 20px;

						.customer-title-item-icon {
							width: 14px;
							height: 14px;
							margin-right: 2px;
						}

						.customer-title-item {
							width: 64px;
							padding: 1px;
							box-sizing: border-box;
							border-radius: 60px;
							background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250) 100%);
							margin-left: 10px;

							.customer-title-item-bg {
								width: 100%;
								height: 100%;
								border-radius: 60px;
								background-color: #23232D;

								.customer-title-item-word-bg {
									height: 24px;
									padding: 0px 8px;
									box-sizing: border-box;
									border-radius: 60px;
									display: flex;
									align-items: center;
									justify-content: center;
									background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250));
									-webkit-background-clip: text;
									-webkit-text-fill-color: transparent;
									background-clip: text;
									text-fill-color: transparent;
									font-family: HarmonyOS Sans;
									font-size: 10px;
									font-weight: 400;
									line-height: 12px;

									.customer-title-item-icon {
										width: 14px;
										height: 14px;
										margin-right: 2px;
									}
								}
							}
						}
					}

					.customer-total {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 10px;
						font-weight: 400;
						line-height: 12px;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.customer-total-number {
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 20px;
							font-weight: 700;
							line-height: 23px;
							margin-left: 4px;
						}
					}
				}
			}

			// 店铺公告
			.shop-notice {
				height: 80px;
				overflow-y: hidden;
				position: relative;

				.shop-notice-item {
					padding: 15px 0;
					box-sizing: border-box;
					border-bottom: 1px solid #787878;

					.shop-notice-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 500;
						line-height: 16px;
						margin-bottom: 8px;
					}

					.shop-content {
						color: rgb(206, 206, 206);
						font-family: 思源黑体;
						font-size: 14px;
						font-weight: 400;
						line-height: 20px;
						word-break: break-all;
					}
				}

				.shop-notice-item:nth-child(3) {
					border-bottom: 0;
					margin-bottom: 28px;
				}

				.shop-open-container {
					position: absolute;
					left: 0;
					right: 0;
					bottom: 0;
					width: 100%;
					height: 20px;
					background-image: linear-gradient(to bottom, rgba(35, 35, 45, 0.1), #23232D);
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.shop-button {
						background-color: #23232D;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 500;
						line-height: 16px;
						padding-left: 20px;
						box-sizing: border-box;
						display: flex;
						align-items: center;

						.arrow-icon {
							width: 16px;
							height: 16px;
						}
					}
				}


			}
		}

		// 活动列表
		.merchant-activity {
			width: 100%;

			.merchant-activity-title-container {
				width: 100%;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;

				.merchant-activity-title {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 10px 0;
					box-sizing: border-box;
					width: 53%;
					background-repeat: no-repeat;
					background-size: cover;
					position: absolute;

					.activity-title-icon {
						width: 20px;
						height: 20px;
						margin-right: 6px;
					}
				}

				.merchant-activity-title.merchant-activity-title-active {
					z-index: 100;
				}

				.merchant-activity-title:nth-of-type(1).merchant-activity-title-active {
					background-image: url("../../../static/images/shop-title-bg-left-active.png");
					left: 0;
				}

				.merchant-activity-title:nth-of-type(2).merchant-activity-title-active {
					background-image: url("../../../static/images/shop-title-bg-right-active.png");
					right: 0;
				}

				.merchant-activity-title:nth-of-type(1) {
					left: 0;
					background-image: url("../../../static/images/shop-title-bg-left.png");
				}

				.merchant-activity-title:nth-of-type(2) {
					right: 0;
					background-image: url("../../../static/images/shop-title-bg-right.png");
				}
			}

			// 商品列表
			.merchant-activity-list {
				width: 100%;
				padding: 20px;
				box-sizing: border-box;

				.item {
					width: 100%;
					display: flex;
					justify-content: space-evenly;
					align-items: flex-start;
					border-radius: 10px;
					background: #383A44;
					margin-bottom: 20px;
					padding: 10px;
					box-sizing: border-box;

					.item-cover {
						position: relative;
						margin-right: 10px;
						border-radius: 6px;

						.item-cover-active-image {
							width: 122px;
							height: 122px;
							border-radius: 6px;
							margin-right: 10px;
							display: flex;
							align-items: center;
							justify-content: center;
							overflow: hidden;
							position: relative;

							.activeimage {
								position: absolute;
								width: 100%;
							}
						}


						.activeimagecover {
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							width: 122px;
							height: 122px;
							border-radius: 9px;
							background: rgba(0, 0, 0, 0.7);
							display: flex;
							align-items: center;
							justify-content: center;

							.activeimageend {
								width: 102px;
								height: 48px;
								margin: 0 auto;
							}
						}
					}


					.item-info {
						flex: 1;
						height: 122px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						padding: 6px 0 10px 0;
						box-sizing: border-box;

						.item-title {
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 18px;
							font-weight: 700;
							line-height: 22px;
							margin-bottom: 10px;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.item-total {
							display: flex;
							align-items: flex-end;
							justify-content: space-between;
							color: rgb(224, 224, 224);
							font-family: HarmonyOS Sans;
							font-size: 10px;
							font-weight: 400;
							line-height: 12px;
							margin-bottom: 13px;

							.item-button {
								width: 64px;
								height: 26px;
								color: rgb(255, 255, 255);
								font-family: HarmonyOS Sans;
								font-size: 12px;
								font-weight: 500;
								line-height: 14px;
								padding: 0;
								margin: 0;
								border-radius: 60px;
								display: flex;
								align-items: center;
								justify-content: center;
							}

							.item-button-join {
								border: 1px solid #EDEDED;
								background: #383A44;
							}

							.item-button-unjoin {
								background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250) 100%);
							}

							.item-button-end {
								background-color: #50515a;
							}
						}

						.item-time {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 10px;
							font-weight: 400;
							line-height: 12px;
							letter-spacing: 0px;
							text-align: left;

							.time-clock {
								width: 12px;
								height: 12px;
								margin-right: 3px;
							}
						}
					}
				}

			}

			// 展品列表
			.exhibit-list {
				width: 100%;
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: 10px;
				box-sizing: border-box;

				.exhibit-list-item {
					width: 48%;
					border-radius: 6px;
					background: rgb(56, 58, 68);
					padding-top: 10px;
					padding-left: 10px;
					box-sizing: border-box;
					margin-bottom: 20px !important;

					.exhibit-list-item-cover {
						height: 153px;
						height: 153px !important;
						border-radius: 6px;
						margin-right: 10px;
						display: flex;
						align-items: center;
						justify-content: center;
						overflow: hidden;
						position: relative;
						margin-bottom: 10px;

						.exhibit-list-item-cover-image {

							position: absolute;
							width: 100%;
						}
					}

					.exhibit-list-item-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom: 12px;
					}

					.exhibit-list-item-price {
						color: rgb(89, 192, 239);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom: 12px;
					}

					.exhibit-list-item-total {
						padding: 14px 12px;
						box-sizing: border-box;
						border-top: 1px solid rgba(196, 196, 196, 0.12);
						display: flex;
						align-items: center;
						justify-content: space-between;
						color: rgb(197, 209, 230);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
						letter-spacing: 0px;
						text-align: left;

						.exhibit-list-item-total-item {
							display: flex;
							align-items: center;

							.exhibit-list-item-total-icon {
								width: 15px;
								height: auto;
								margin-right: 5px;
							}
						}

					}
				}

				.exhibit-list-tips {
					position: fixed;
					left: 0;
					right: 0;
					bottom: 0;
					height: 30px;
					background: rgba(0, 0, 0, 0.7);
					color: rgb(185, 150, 249);
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 30px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

			.twoDot {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.action-popup {
		width: 100%;
		background-color: #EDEDED;
		padding: 20px;
		box-sizing: border-box;
		border-radius: 12px 12px 0 0;
		margin-bottom: -10px;

		.action-popup-content {
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin-bottom: 20px;
			padding: 10px 0;

			.action-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.action-icon {
					width: 80px;
					height: 80px;
				}
			}
		}

		.action-popup-cancel {
			width: 100%;
			height: 50px;
			background: #383A44;
			border-radius: 10px;
			color: #fff;
			font-size: 16px;
			text-align: center;
			line-height: 50px;
		}
	}

	/* 修改uni-popup底部样式 */
	:deep(.uni-popup-bottom) {
		bottom: 0 !important;
		padding-bottom: 0 !important;
		transform: translateY(0) !important;
		border-radius: 20px 20px 0 0;
	}

	:deep(.uni-popup__wrapper-bottom) {
		bottom: 0 !important;
		padding-bottom: 0 !important;
	}
</style>