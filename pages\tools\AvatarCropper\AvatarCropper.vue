<!-- 头像裁剪 -->
<template>
	<view>
		<qf-image-cropper :src="imgurl" :width="500" :height="500" :radius="30" @crop="handleCrop"></qf-image-cropper>

	</view>
</template>

<script>
	import QfImageCropper from '@/uni_modules/qf-image-cropper/components/qf-image-cropper/qf-image-cropper.vue';

	export default {
		components: {
			QfImageCropper
		},
		data() {
			return {
				imgurl: ""
			}
		},
		onLoad(option) {
			this.imgurl = option.avatar
		},
		onShow() {
			console.log("--------this.imgurl -----------", this.imgurl);
			this.$nextTick(() => {
				// this.$refs.cropper.init(this.imgurl);
			})
		},
		methods: {
			handleCrop(src) {
				uni.$emit('crop', src.tempFilePath)
				uni.navigateBack()
			},
		}
	}
</script>

<style>

</style>