<template>
	<view class="merchant-store-details-conatiner">
		<u-navbar :customBack="goBack" back-text="" title="活动详情" :background="{backgroundColor: '#191C26'}"
			:border-bottom="false" height="60" title-color="#fff" back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">展品详情</view>
				</view>
			</view>
		</u-navbar>
	<!-- 评论区 -->
		<view class="exhibits-container">
			<view class="exhibits-title">评论区<view v-if="commentList && commentList.length*1>0">({{commentList.length}})</view></view>
			<view class="exhibits-list">
				<view class="exhibits-list-item" v-for="(item,index) in commentList" :key="index">
					<image class="exhibits-list-item-cover" :src="item.user_info.avatar" mode="widthFix">	
					<view class="exhibits-list-item-content">
						<view class="exhibits-list-item-user">
							<view>
								<view class="exhibits-list-item-name">{{item.user_info.nickname}}</view>
								<view class="exhibits-list-item-time">{{item.created_at}}</view>
							</view>
							<view v-if="isCanLike*1==1">
								<view v-if="item.is_like" class="exhibits-list-item-comment-like" @click="cancelCommentLikes(item)">
									<image class="exhibits-list-item-like-icon" src="@/static/images/comment-like-active-icon.png" mode="widthFix">
									{{item.like>=1000?item.like/1000:item.like}} <text v-if="item.like>=1000">k</text>
								</view>
								<view v-else class="exhibits-list-item-comment-like" @click="goCommentLikes(item)">
									<image class="exhibits-list-item-like-icon" src="@/static/images/comment-like-icon.png" mode="widthFix">	
									{{item.like>=1000?item.like/1000:item.like}} <text v-if="item.like>=1000">k</text>
								</view>
							</view>
						</view>
						<view class="exhibits-list-item-comment">{{item.content}}</view>
					</view>
				</view>
				<view v-if="commentList && commentList.length*1>4" class="exhibits-comment-more">更多{{commentList.length}}条评价 <image class="comment-more-icon" src="@/static/images/comment-more-icon.png" mode="widthFix">				</view>
			</view>
			<!-- 发表评论 -->
			<view class="fixed-bottom">
				<!-- 发表评论 -->
				<!-- v-if="isCanLike*1==1" 
				<view class="exhibit-list-tips">一百米以内可进行点赞或评论</view> -->
				<view class="submit-comment">
					<view v-if="activeInfo.is_like" class="submit-comment-like comment-like-active" @click="cancelLikes">
						<image class="submit-comment-like-icon" src="@/static/images/comment-like-active-icon2.png" mode="widthFix">	
						已点赞
					</view>
					<view v-else class="submit-comment-like" @click="goLikes">
						<image class="submit-comment-like-icon" src="@/static/images/comment-like-icon2.png" mode="widthFix">	
						点赞
					</view>
					
					<view  v-if="userInfo.user_info" class="submit-comment-form">
						<!-- <image class="submit-comment-headpic" src="@/static/images/default-headpic.png" mode="widthFix">	 -->
						<image v-if="userInfo.user_info.avatar" class="submit-comment-headpic" :src="userInfo.user_info.avatar"  mode="widthFix"></image>
						<image v-else class="submit-comment-headpic" src="@/static/images/default-headpic.png" mode="widthFix">	
						<input class="submit-comment-input" v-model="commentText" placeholder-style="fontSize:14px;color:#A9A9A9" placeholder="请输入评论内容" />
						
					</view>
					<view class="submit-comment-button" @click="submitComment">发布</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isShowExit:false,//是否显示退出活动
				activeId: 0,
				commentList:[],//评论列表
				commentText:"",//评论内容
				activeInfo:{},
				isCanLike:0, 
				userInfo:{}
			}
		},
		onLoad(options) {
			if(options.id){
				this.activeId = options.id;
				this.activiType = options.type;
				this.business_id = options.business_id;
				this.isCanLike = options.isCanLike
				this.getActiveInfo();
				this.getCommentList();
				this.getUserInfo();
			}
		},
		onShow() {
			
		},

		methods: {
			// 获取用户信息
			getUserInfo() {
			
				this.$http.get('/api/user/info').then(async (res) => {
					this.userInfo = res.message
					console.log('---------this.info------------', this.userInfo);
					
				})
			},
			goBack() {
				// uni.navigateBack();
				uni.navigateBack({
					delta: 1
				});
				// uni.navigateTo({
				// 	url: '/pages/merchantStoreDetails/merchantStoreDetails?business_id='+this.business_id+"&id="+this.activeId+"&type="+this.activiType
				// })
			},
			// 取消点赞
			cancelLikes(){
				this.$http.post('/api/user/business/exhibits/unlike',{exhibits_id:this.activeId}).then((res) => {
					console.log(res);
					if(res.code == 200){
						uni.showToast({
							title: '取消点赞!',
							icon: 'none'
						});
						this.getActiveInfo()
					}
				});
			},
			// 点赞
			goLikes(){
				this.$http.post('/api/user/business/exhibits/like',{exhibits_id:this.activeId}).then((res) => {
					console.log(res);
					if(res.code == 200){
						uni.showToast({
							title: '点赞成功!',
							icon: 'none'
						});
						this.getActiveInfo()
					}
				});
			},
			// 评论列表
			getCommentList(){
				var params = {
					exhibits_id:this.activeId,
					comment_id:0,
					direction:1,
					size:3
				}
				this.$http.get('/api/user/business/exhibits/comment/list',params).then((res) => {
					console.log(res);
					if(res.code == 200){
						this.commentList = res.message
						// this.getActiveInfo()
					}
				});
			},
			// 取消评论点赞
			cancelCommentLikes(item){
				this.$http.post('/api/user/business/exhibits/comment/unlike',{comment_id:item.id}).then((res) => {
					console.log(res);
					if(res.code == 200){
						uni.showToast({
							title: '取消点赞!',
							icon: 'none'
						});
						this.getCommentList()
					}
				});
			},
			// 评论点赞
			goCommentLikes(item){
				this.$http.post('/api/user/business/exhibits/comment/like',{comment_id:item.id}).then((res) => {
					console.log(res);
					if(res.code == 200){
						uni.showToast({
							title: '点赞成功!',
							icon: 'none'
						});
						this.getCommentList()
					}
				});
			},
			// 提交评论
			submitComment(){
				this.$http.post('/api/user/business/exhibits/comment/post',{exhibits_id:this.activeId,content:this.commentText}).then((res) => {
					console.log(res);
					if(res.code == 200){
						uni.showToast({
							title: '评论成功!',
							icon: 'none'
						});
						this.commentText = ""
						this.getCommentList()
					}
				});
			},
			// 获取活动信息
			getActiveInfo(){
				var url = this.activiType == 'activity' ? '/api/user/business/activity/detail' : '/api/user/business/exhibits/detail'
				var params =  this.activiType == 'activity' ? {id:this.activeId} : {exhibits_id:this.activeId}
				this.$http.get(url,params).then((res) => {
					// console.log(res);
					if(res.code == 200){
						this.activeInfo = res.message
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	/deep/ .u-collapse-item_content {
		height: auto;
	}

	/deep/ .u-collapse-head {
		padding: 0;
	}

	
	.merchant-store-details-conatiner{
		width:100%;
		// 评论区
		.exhibits-container{
			width:100%;
			padding:0px 0px 102px 0px;
			box-sizing: border-box;
			.exhibits-title{
				color: rgb(255, 255, 255);
				font-family: 阿里巴巴普惠体;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				letter-spacing: 0px;
				text-align: left;
				display: flex;
				padding:10px 20px;
				box-sizing: border-box;
				background-color: #292C33;
			}
			.exhibits-list{
				width:100%;
				padding:0 20px;
				box-sizing: border-box;
				.exhibits-list-item{
					width:100%;
					display: flex;
					align-items: flex-start;
					justify-content: flex-start;
					.exhibits-list-item-cover{
						width:40px;
						height:40px;
						border:1px solid #ffffff;
						box-sizing: border-box;
						border-radius: 50%;
						margin-right:10px;
						margin-top:20px;
					}
					.exhibits-list-item-content{
						flex:1;
						padding:20px 0;
						box-sizing: border-box;
						border-top: 1px solid rgba(196, 196, 196, 0.12);
						.exhibits-list-item-user{
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 8px;
							.exhibits-list-item-name{
								color: rgb(255, 255, 255);
								font-family: 阿里巴巴普惠体;
								font-size: 14px;
								font-weight: 400;
								line-height: 19px;
								letter-spacing: 0px;
								text-align: left;
								margin-bottom:2px;
							}
							.exhibits-list-item-time{
								color: rgb(169, 169, 169);
								font-family: 阿里巴巴普惠体;
								font-size: 10px;
								font-weight: 400;
								line-height: 14px;
								letter-spacing: 0px;
								text-align: right;
							}
							.exhibits-list-item-comment-like{
								display: flex;
								align-items: center;
								color: rgb(255, 255, 255);
								font-family: 阿里巴巴普惠体;
								font-size: 14px;
								font-weight: 400;
								line-height: 19px;
								letter-spacing: 0px;
								text-align: right;
								.exhibits-list-item-like-icon{
									width:15px;
									height: auto;
									margin-right:2px;
								}
							}
						}
						.exhibits-list-item-comment{
							color: rgb(215, 216, 217);
							font-family: HarmonyOS Sans;
							font-size: 14px;
							font-weight: 400;
							line-height: 16px;
							letter-spacing: 0px;
							text-align: left;
						}
					}
				}
				.exhibits-list-item:nth-of-type(1){
					.exhibits-list-item-content{
						border-top: 1px solid #191c26;
					}
				}
				.exhibits-comment-more{
					color: rgb(169, 169, 169);
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 400;
					line-height: 14px;
					letter-spacing: 0px;
					text-align: right;
					display: flex;
					align-items: center;
					justify-content: center;
					border-top:1px solid rgba(196, 196, 196, 0.12);
					padding:10px 0 30px 0;
					box-sizing: border-box;
					.comment-more-icon{
						width:14px;
						height: auto;
						margin-left:2px;
					}
				}
			}
			// 发表评论
			.fixed-bottom{
				position: fixed;
				left:0;
				right:0;
				bottom:0;
				// 发表评论
					.exhibit-list-tips{
						width:100%;
						// position: fixed;
						// left:0;
						// right:0;
						// bottom:72px;
						height:30px;
						background:#1C1C1C;
						color: rgb(185, 150, 249);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 30px;
						letter-spacing: 0px;
						text-align: center;
					}
					.submit-comment{
						width:100%;
						// position: fixed;
						// left:0;
						// right:0;
						// bottom:0;
						border-radius: 10px 10px 0px 0px;
						background: rgb(28, 28, 28);
						padding:10px 20px 20px 20px;
						box-sizing: border-box;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.submit-comment-like{
							color:#D7D8D9;
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							.submit-comment-like-icon{
								width:28px;
								height:auto;
							}
						}
						.comment-like-active{
							color: rgb(255, 93, 80);
						}
						.submit-comment-form{
							flex:1;
							height:40px;
							margin:0 10px 0 12px;
							border-radius: 60px;
							background: rgb(56, 58, 67);
							padding:5px;
							box-sizing: border-box;
							display: flex;
							align-items: center;
							.submit-comment-headpic{
								width:30px;
								height: 30px;
								margin-right:10px;
								border-radius: 50%;
							}
							.submit-comment-input{
								
							}
						}
						.submit-comment-button{
							width: 78px;
							height: 40px;
							line-height: 40px;
							border-radius: 60px;
							background: linear-gradient(90.00deg, rgb(79, 197, 238),rgb(191, 147, 250) 100%);
							color: rgb(255, 255, 255);
							font-family: 阿里巴巴普惠体;
							font-size: 14px;
							font-weight: 400;
							text-align: center;
						}
					}
				
			}
		}
	}
</style>