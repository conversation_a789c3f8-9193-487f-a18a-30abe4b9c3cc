<template>
	<view class="appPage">
		<view class="inputBg">
			<u-input v-model="phone" type="number" maxlength="11" placeholder="请输入手机号" :border="false" clearable />
		</view>
		<view class="inputBg">
			<u-input v-model="password" type="password" placeholder="请输入新密码" :border="false" clearable />
		</view>
		<view class="inputBg">
			<u-input v-model="captcha" maxlength="6" type="number" placeholder="请输入验证码" :border="false" clearable />
			 <view @tap="getCheckNum" class="codoColor">{{!codeTime?'获取验证码':codeTime+'秒重新获取'}}</view>
		</view>
		<view class="login" @click="goLogin">确定
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				"phone": "",
				"captcha": "",
				"password": "",
				codeTime: 0,
			}
		},
		computed: {

		},
		methods: {
			goLogin() {
				this.$http.post('/auth/reset-password', {
					"phone": this.phone,
					"captcha": this.captcha,
					"password": this.password,
				}).then(res => {
					this.toast('密码修改成功')
					setTimeout(() => {
						uni.navigateBack()
					}, 500)
				})
			},
			goBack() {
				uni.navigateBack()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			getCheckNum() {
				if (this.codeTime > 0) {
					this.toast('不能重复获取');
					return;
				} else {
					this.$http.post('/auth/apply-reset-password-sms', {
						"phone": this.phone
					})
					this.codeTime = 60
					let timer = setInterval(() => {
						this.codeTime--;
						if (this.codeTime < 1) {
							clearInterval(timer);
							this.codeTime = 0
						}
					}, 1000)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 32rpx;

		.codoColor {
			background: linear-gradient(to right, #4BC6ED, #BC93F2);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.txt {
			font-size: 26rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
			display: flex;
			justify-content: flex-end;
		}

		.login {
			height: 94rpx;
			line-height: 94rpx;
			margin-top: 100rpx;
			border-radius: 14rpx;
			font-size: 32rpx;
			text-align: center;
			background: linear-gradient(#4BC6ED, #BC93F2);
		}

		.inputBg {
			color: #fff;
			background: #3D404A;
			height: 97rpx;
			// line-height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
			margin-bottom: 42rpx;
		}

		.tips {
			margin-top: 32rpx;
			font-size: 22rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: #848484;
			line-height: 32rpx;
		}
	}
</style>