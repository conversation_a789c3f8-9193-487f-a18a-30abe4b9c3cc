<template>
	<!-- 话题页面 -->
	<view>
		<u-navbar backIconName="search" back-text="" title="发布" :custom-back="customBack"
			:background="{ backgroundColor: $style.backgroundColor }" :border-bottom="false">
			<!-- title-color="#fff"
      back-icon-color="#fff" -->
			<view slot="content" style="width: 240rpx">
				<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" :show-bar="true"
					:active-color="$style.fontColor" :inactive-color="$style.greyFontColor"
					:bg-color="$style.backgroundColor"></u-tabs>
				<!-- bg-color="#191C26"
		inactive-color="rgba(255,255,255,0.72)"
		active-color="#fff" -->
			</view>
			<view class="navbar-right t_display" slot="right" style="margin-right: 34rpx" v-if="false">
				<view class="" style="color: rgba(255, 255, 255, 0.72)"> 时间 </view>
				<image src="../../static/images/time.png" class="img24" mode="" style="margin-left: 16rpx"></image>
			</view>
		</u-navbar>
		<view style="height: 15rpx; width: 100vw; background: #fff"></view>
		<swiper class="swiperC" :current="current" @change="setCurrent"
			:style="{ height: '81vh', backgroundColor: $style.greyBackgroundColor }" disable-touch>
			<swiper-item>
				<view style="display: block; width: 750rpx">
					<scroll-view @scrolltolower="scrolltolowerS" :refresher-threshold="150" scroll-y="true"
						:refresher-background="$style.backgroundColor" :scroll-x="false"
						style="height: 81vh; width: 750rpx" refresher-enabled @refresherrefresh="onRefreshS"
						:lower-threshold="200" :refresher-triggered="triggeredS">
						<Post :showTop="false" :list="dataArrS" @more="goMore" @share="share" @setLike="setLikes"
							:previewFlag="true" :showAuthority="false" @goAddress="goNavLoaction"
							@goMainText="openMainText">
						</Post>
						<view class="bottom-space"></view>
					</scroll-view>
				</view>
			</swiper-item>
			<swiper-item>
				<view style="display: block; width: 750rpx">
					<scroll-view @scrolltolower="scrolltolowerB" scroll-y="true"
						:refresher-background="$style.backgroundColor" :lower-threshold="200" :refresher-threshold="150"
						style="height: 81vh" refresher-enabled @refresherrefresh="onRefreshB"
						:refresher-triggered="triggeredB">
						<Post :list="dataArrB" @more="goMore" @share="share" @setLike="setLike" :previewFlag="true"
							:showAuthority="false" @goAddress="goNavLoaction" @goMainText="openMainText">
						</Post>
						<view class="bottom-space"></view>
					</scroll-view>
				</view>
			</swiper-item>
		</swiper>
		<sharePopup ref="share" :post="shareItem" :securityBottom="0"></sharePopup>
		<uni-popup ref="popup" type="bottom" background-color="#fff">
			<view class="cPopup">
				<view class="t_display">
					<image class="avatar" :src="popupInfo.user_info.avatar" mode="aspectFill"></image>
					<view class="name">{{ popupInfo.user_info.nickname }}</view>
				</view>
				<view class="item t_display" @click="cancelBlack(popupInfo.moment_id, popupInfo.index)"
					style="margin: 20rpx; margin-top: 40rpx">
					<image class="disable" src="../../static/images/shanchu.png" mode=""></image>
					<view class="rightInfo"> 删除 </view>
				</view>
				<u-line color="rgba(0,0,0,0.13)" />
				<view class="cancal" @click="cancel"> 取消 </view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref="notify" />
		<simpleShare ref="simpleShare" :zIndxe="9999"></simpleShare>

		<!-- 在最外层页面中直接使用mainText组件 -->
		<mainText ref="mainTextRef"></mainText>
	</view>
</template>

<script>
	import Post from "@/components/post/waterfall.vue";
	import simpleShare from "@/components/mark-simpleshare/mark-simpleshare.vue";
	import mainText from "@/components/mainText/mainText.vue";

	export default {
		components: {
			Post,
			simpleShare,
			mainText,
		},
		data() {
			return {
				triggeredS: false,
				triggeredB: false,
				recommendTotal: true,
				show: true,
				popupInfo: {
					user_info: {
						avatar: "",
						nickname: "",
					},
				},
				list: [{
						name: "热门",
					},
					{
						name: "关注",
					},
				],
				dataArrS: [],
				dataArrB: [],
				page: 1,
				current: 0,
				followTotal: true,
				shareItem: {},
				tab2: {
					momentId: 0,
					option: 2,
				},
			};
		},
		created() {
			this.getFirstData();
		},
		mounted() {
			setTimeout(() => {
				if (this.dataArrS.length === 0) {
					this.getRecommend().then(() => {
						this.$forceUpdate();
					});
				}
				if (this.dataArrB.length === 0) {
					this.getFollowUp().then(() => {
						this.$forceUpdate();
					});
				}
			}, 500);
		},
		methods: {
			// 打开正文弹窗
			openMainText(data) {
				this.$refs.mainTextRef.open(data);
			},
			// 测试地图功能
			testMapFunction() {
				console.log('测试地图功能');
				const testData = {
					moment_id: 'test123',
					user_info: {
						avatar: '/static/images/default-headpic.png',
						nickname: '测试用户',
						relation: 0,
						uuid: 'test-uuid'
					},
					content: '这是一个测试动态，包含位置信息',
					images: ['/static/images/default-headpic.png'],
					location: '北京市朝阳区测试地点',
					coordinate: '116.4074,39.9042', // 北京坐标
					like: 10,
					is_like: false,
					comment_total: 5,
					share_total: 2
				};
				this.$refs.mainTextRef.open(testData);
			},
			goNavLoaction(item) {
				this.$emit("goAddress", item);
			},
			filterData(momentId) {
				this.dataArrS = this.dataArrS.filter((item) => {
					return item.moment_id != momentId;
				});
				this.$forceUpdate();
			},
			onRefreshS() {
				this.triggeredS = true;
				setTimeout(() => {
					this.dataArrS = [];
					this.page = 1;
					this.getRecommend();
					this.triggeredS = false;
				}, 500);
			},
			onRefreshB() {
				this.triggeredB = true;
				setTimeout(() => {
					const momentId = this.dataArrB.length ? this.dataArrB[0].moment_id : "";
					this.tab2.momentId = momentId;
					this.getFollowUp();
					this.triggeredB = false;
				}, 500);
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},
			refresherpulling() {
				console.log("----------refresherpulling---------");
			},
			refresherrefresh() {
				console.log("---------refresherrefresh-----------");
			},
			setLikes(item) {
				this.$http
					.post("/api/moment/like", {
						momentId: item.momentId,
						like: item.isLike ? 2 : 1,
					})
					.then((res) => {
						this.$set(this.dataArrS, item.index, {
							...this.dataArrS[item.index],
							is_like: !item.isLike,
							like: this.dataArrS[item.index].like + (!item.isLike ? 1 : -1),
						});
					});
			},
			setLike(item) {
				this.$http
					.post("/api/moment/like", {
						momentId: item.momentId,
						like: item.isLike ? 2 : 1,
					})
					.then((res) => {
						this.$set(this.dataArrB, item.index, {
							...this.dataArrB[item.index],
							is_like: !item.isLike,
							like: this.dataArrB[item.index].like + (!item.isLike ? 1 : -1),
						});
					});
			},
			cancelBlack(momentId, index) {
				switch (this.current) {
					case 0:
						this.$http
							.post("/api/moment/del", {
								momentId,
							})
							.then((res) => {
								this.dataArrS.splice(index, 1);
								this.$refs.popup.close();
							});
						break;
					case 1:
						this.$http
							.post("/api/moment/del", {
								momentId,
							})
							.then((res) => {
								this.dataArrB.splice(index, 1);
								this.$refs.popup.close();
							});
						break;
					default:
						break;
				}
			},
			scrolltolowerS(s) {
				if (this.recommendTotal) {
					this.page++;
					this.getRecommend()
						.then(() => {
							uni.hideLoading();
						})
						.catch(() => {
							uni.hideLoading();
						});
				}
			},
			scrolltolowerB(s) {
				if (this.followTotal && this.dataArrB.length > 0) {
					this.tab2.momentId = this.dataArrB[this.dataArrB.length - 1].moment_id;
					this.getFollow()
						.then(() => {
							uni.hideLoading();
						})
						.catch(() => {
							uni.hideLoading();
						});
				}
			},
			getFirstData() {
				this.dataArrB = [];
				this.dataArrS = [];
				this.recommendTotal = true;
				this.page = 1;

				Promise.all([this.getFollowUp(), this.getRecommend()])
					.then(() => {
						uni.hideLoading();
						this.$nextTick(() => {
							this.$forceUpdate();
						});
					})
					.catch(() => {
						uni.hideLoading();
					});
			},
			getData() {
				this.getFollowUp().then(() => {
					this.$nextTick(() => this.$forceUpdate());
				});
				this.getRecommend().then(() => {
					this.$nextTick(() => this.$forceUpdate());
				});
				setTimeout(() => {
					this.show = false;
				}, 500);
			},
			getRecommend() {
				return new Promise((resolve, reject) => {
					this.$http
						.get("/api/moment/recommend-list", {
							page: this.page,
						})
						.then((res) => {
							this.recommendTotal = res.message.length;
							if (res.message && res.message.length > 0) {
								this.dataArrS = [...this.dataArrS, ...res.message];

								this.$nextTick(() => {
									this.$forceUpdate();
								});
							}
							resolve(res);
						})
						.catch((err) => {
							reject(err);
						});
				});
			},
			getFollowUp() {
				return new Promise((resolve, reject) => {
					this.$http
						.get("/api/moment/list", {
							option: 2,
							range: 2,
						})
						.then((res) => {
							if (res.message && res.message.length > 0) {
								this.dataArrB = [...res.message];

								this.$nextTick(() => {
									this.$forceUpdate();
								});
							}
							resolve(res);
						})
						.catch((err) => {
							reject(err);
						});
				});
			},
			getFollow(option = 1, customFlag) {
				return new Promise((resolve, reject) => {
					this.$http
						.get("/api/moment/list", {
							option: option,
							momentId: this.tab2.momentId,
							range: 2,
						})
						.then((res) => {
							this.followTotal = res.message.length;
							if (res.message && res.message.length > 0) {
								this.dataArrB = [...this.dataArrB, ...res.message];
								this.tab2.momentId =
									this.dataArrB[this.dataArrB.length - 1].moment_id;
								this.$nextTick(() => {
									this.$forceUpdate();
								});
							}
							resolve();
						})
						.catch((err) => {
							reject(err);
						});
				});
			},
			share(item) {
				this.shareItem = item;
				this.$refs.share.open();
			},
			cancel() {
				this.$refs.popup.close();
			},
			goMore(item) {
				console.log(item);
				this.popupInfo = item;
				this.$refs.popup.open();
			},
			customBack() {
				uni.navigateTo({
					url: "/pages/searchPost/searchPost",
				});
			},
			change(index) {
				this.current = index;
			},
			setCurrent(ids) {
				this.current = ids.detail.current;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.cPopup {
		padding: 20rpx 32rpx;
		background: #ffffff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 14rpx;

		.cancal {
			margin-top: 24rpx;
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #f6f6f6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3d3d3d;
		}

		.item {
			margin-top: 27rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 35rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}

	.swiperC {
		background-color: v-bind('$style ? $style.greyBackgroundColor : "#D9D9D9"');
	}

	.img140 {
		height: 140rpx;
	}

	.bottom-space {
		height: 200rpx;
		width: 100%;
	}
</style>