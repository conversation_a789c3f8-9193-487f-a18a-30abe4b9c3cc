<template>
	<view class="content">
		<view class="navigation-bar">
			<!-- 导航栏内容，如返回按钮等 -->
		</view>
		<view class="navigation-zhezhao">
			<image @click="back" class="back" src="../../static/images/vip/newBack.png" mode=""></image>
			<view class="nav-title">文案编辑</view>
			<view class="nav-save" @click="save()">保存</view>
		</view>
		<div class="list">
			<kn-edit ref="contentEdit" showInsertImg @uploadImgBefore="uploadImgBefore" :pageScrollTop="pageScrollTop" v-model="content"></kn-edit>
		</div>
	</view>
</template>
<script>
import knEdit from '@/components/lf-kn-editor/kn_editor.vue';
export default {
	components: { knEdit },
	data() {
		return {
			content: '',
			pageScrollTop: 0,
			shopInfo:{}
		};
	},
	onLoad() {
		this.content = uni.getStorageSync('content') || ''
		this.getShopInfo()
	},
	methods: {
		getShopInfo() {
			this.$http.get('/api/user/business/get').then((res) => {
				this.shopInfo = res.message;
				this.getActiveList();
			});
		},
		uploadImgBefore(tempFiles) {
			console.log(tempFiles);
			let path = [tempFiles.path];
			Promise.all(
				path.map(async (item) => {
					const img = await this.$common.uploads(item, {
						type: 4
					});
					this.$refs.contentEdit.insertImage(img);
				})
			);
		},
		back() {
			uni.setStorageSync('content',this.content);
			uni.navigateBack()
			// uni.navigateTo({
			// 	url: '/pages/exhibitAdd/exhibitAdd?shopId=' + this.shopInfo.id
			// });
			// uni.navigateBack({
			// 	delta: 1
			// });
		},
		save(){
			uni.setStorageSync('content',this.content);
			uni.navigateBack()
			// uni.navigateBack({
			// 	delta: 1
			// });
			// uni.navigateTo({
			// 	url: '/pages/exhibitAdd/exhibitAdd?shopId=' + this.shopInfo.id
			// });
		}
	},
	onPageScroll(e) {
		console.log(e);
		this.pageScrollTop = e.scrollTop;
	}
};
</script>

<style lang="scss">
page {
	background-color: #f5f7fb;
}
.content {
	width: 100%;
	height: 100vh;
	padding-top: 100px;
	background-color: #f5f7fb;
	.navigation-bar {
		width: 100%;
		display: flex;
		align-items: center;
		height: 170px;
		background-image: url('../../static/images/vip/newBackground.png'); /* 背景图路径 */
		background-size: cover;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
	}
	.navigation-zhezhao {
		width: 100%;
		height: 170px;
		background-image: url('../../static/images/vip/nav-zhezhao.png'); /* 背景图路径 */
		background-size: 100%;
		background-repeat: no-repeat;
		background-position: bottom;
		position: absolute;
		z-index: 0;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		padding-bottom: 10%;
		.back {
			width: 31px;
			height: 31px;
			margin-left: 2%;
			z-index: 1;
		}
		.nav-title {
			width: 82%;
			height: 30px;
			color: #000000;
			font-family: 阿里巴巴普惠体;
			font-size: 18px;
			font-weight: 500;
			line-height: 30px;
			letter-spacing: 0px;
			text-align: center;
			z-index: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		.nav-save{
			width:30px;
			margin-right:2%;
			color: #725EE9;
			font-family: 阿里巴巴普惠体;
			font-size: 14px;
			font-weight: 400;
			line-height: 19px;
		}
	}
	.list{
		width: 100%;
		height: 90%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		// margin: 3% auto;
		position: relative;
		z-index: 1;
		// margin-top: 5%;
		background:#f5f7fb;
		padding:3%;
		box-sizing: border-box;
		color: #212121;
		font-family: HarmonyOS Sans;
		font-size: 16px;
		font-weight: 400;
		line-height: 22px;
		letter-spacing: 0px;
		text-align: left;
		
	}
}
</style>
