<template>
	<view class="container">
		<view ref="AmapRender" id="AmapRender" class="map" :center="center" :change:center="AmapRender.receive_Center"
			:person="person" :change:person="AmapRender.receive_Person" :house="house"
			:change:house="AmapRender.receive_House" :moon="moon" :change:moon="AmapRender.receive_Moon" :me="me"
			:redPacket="redPacket" :change:redPacket="AmapRender.receive_redPacket" :change:me="AmapRender.receive_Me"
			:toolEvent="toolEvent" :change:toolEvent="AmapRender.receive_toolEvent">
		</view>
		<!-- <view ref="AmapRender" id="AmapRender" class="map" :center="center" :change:center="AmapRender.receive_Center">
		</view> -->
		<!-- 搜索 -->
		<view class="searchTool">
			<image src="../../static/map/search.png" @click="handelSearch"></image>
		</view>
		<!-- 天气 地区 -->
		<view class="tianqi">
			<view class="temperature" v-if="temperature">
				<image mode="widthFix" v-if="weather=='qing'" src="../../static/map/qing.png" class="weather"></image>
				<image mode="widthFix" v-if="weather=='yu'" src="../../static/map/yu.png" class="weather"></image>
				<image mode="widthFix" v-if="weather=='xue'" src="../../static/map/xue.png" class="weather"></image>
				<text>{{ temperature }}</text>
			</view>
			<view class="district" v-if="district">
				{{ district&&district.length>5?district.slice(0,5):district }}
			</view>
		</view>
		<!-- 设置 -->
		<view class="settingTool" @click="handelSet('/pages/mapSetting/mapSetting')">
			<image src="../../static/map/set.png"></image>
		</view>
		<!-- 商户信息 -->
		<view class="merchant-list-father">
			<view class="merchant-list-container">
				<view class="merchant-list-title">
					<view class="title-container">
						<view class="shop">
							<view class="shop-title">{{shopInfo.title}}</view>
							<view v-if="distance" class="shop-localtions">
								<image class="localtions-icon" src="@/static/images/address-icon.png" mode="widthFix">
								</image>
								{{distance}}km
							</view>
						</view>
						<view class="customer">
							<view class="customer-title">
								<view class="customer-title-item">
									<view class="customer-title-item-bg">
										<view class="customer-title-item-word-bg">
											<image class="customer-title-item-icon" src="@/static/images/email-icon.png"
												mode="">
											</image>
											发消息
										</view>
									</view>
								</view>
								<view class="customer-title-item">
									<view class="customer-title-item-bg">
										<view class="customer-title-item-word-bg" @click="makePhoneCall(shopInfo.srv_phone)">
											<image class="customer-title-item-icon" src="@/static/images/phone-icon.png"
												mode="">
											</image>
											客服
										</view>
									</view>
								</view>
							</view>
							<view class="customer-total">
								参与人数 <view class="customer-total-number">{{shopInfo.member_total}}</view>
							</view>
						</view>
					</view>
					<!-- 店铺公告 -->
					<view class="shop-notice" :style="{ height: isShopOpen ? 'auto' : '80px' }">
						<view class="shop-notice-item">
							<view class="shop-notice-title">店铺公告</view>
							<view class="shop-content">
								{{shopInfo.describe?shopInfo.describe:'暂无'}}
							</view>
						</view>
						<view class="shop-notice-item">
							<view class="shop-notice-title">客服电话</view>
							<view v-if="shopInfo.srv_phone" class="shop-content">{{shopInfo.srv_phone}}</view>
							<view v-else class="shop-content">暂无</view>
						</view>
						<view class="shop-notice-item">
							<view class="shop-notice-title">商户地址</view>
							<view v-if="shopInfo.address" class="shop-content">{{shopInfo.address}}</view>
							<view v-else class="shop-content">暂无</view>
						</view>
						<view class="shop-open-container" @click="isShopOpen = !isShopOpen">
							<view v-if="!isShopOpen" class="shop-button">
								展开
								<image class="arrow-icon" src="@/static/images/down-icon.png" mode="">
								</image>
							</view>
							<view v-else class="shop-button">
								收起
								<image class="arrow-icon" src="@/static/images/up-icon.png" mode="">
								</image>
							</view>
						</view>
					</view>
				</view>
				<!-- 活动列表 -->
				<view class="merchant-activity">
					<view class="merchant-activity-title-container">
						<view class="merchant-activity-title" @click="changeTitle(index)"
							:class="{ 'merchant-activity-title-active': index === activityTitleIndex }"
							v-for="(item,index) in activityTitle" :key="index">
							<image class="activity-title-icon" src="@/static/images/pinglun.png" mode=""></image>
							{{item.name}}
						</view>
					</view>
					<!-- 活动列表 -->
					<view v-if="activityTitleIndex==0" class="merchant-activity-list">
						<view v-if="activeList && activeList.length>0">
							<view v-for="(item,index) in activeList" class="item" @click.stop="goDetails(item)">
								<view class="item-cover">
									<image class="activeimage" :src="item.cover" mode="">
									</image>
									<view class="activeimagecover" v-if="item.expired">
										<image class="activeimageend" src="@/static/images/activeEnd.png" mode="">
										</image>
									</view>
								</view>
								<view class="item-info">
									<view class="item-title twoDot">
										{{item.title}}
									</view>
									<view class="item-total">
										<view>参与人数{{item.member_total}}</view>
										<view v-if="item.expired" class="item-button item-button-end">
											活动结束
										</view>
										<view v-else-if="item.is_join" class="item-button item-button-unjoin" type="default" >
											已参加
										</view>
										<view v-else class="item-button item-button-unjoin" type="default" @click.stop="joinActivity(item)">
											参加
										</view>
										<!-- <view v-if="state==1" class="item-button item-button-unjoin" type="default">
											参加
										</view>
										<view v-else-if="state==2" class="item-button item-button-join">
											已参加
										</view> -->
									</view>
									<view class="item-time">
										<image class="time-clock" src="@/static/images/vip/clock.png" mode=""></image>
										<view class="time">{{item.timeline}}</view>
									</view>
								</view>
							</view>
						</view>
						<view v-else>
							暂无活动列表！
						</view>
					</view>
					<!-- 展品列表 -->
					<view v-else-if="activityTitleIndex==1" >
						<view v-if="activeList && activeList.length>0" class="exhibit-list">
							<view v-for="(item,index) in activeList" @click="goDetails(item)" :key="index"
								class="exhibit-list-item">
								<image class="exhibit-list-item-cover" :src="item.cover"
									mode="widthFix">
								</image>
								<view class="exhibit-list-item-title twoDot">{{item.title}}</view>
								<view class="exhibit-list-item-price">￥{{item.price}}</view>
								<view v-if="distance*1>0 && distance*1000<100" class="exhibit-list-item-total">
									<view class="exhibit-list-item-total-like exhibit-list-item-total-item">
										<image class="exhibit-list-item-total-icon" src="@/static/images/like-icon.png"
											mode="widthFix"></image>
										{{item.like>=1000?item.like/1000:item.like}} <text v-if="item.like>=1000">k</text>
									</view>
									<view class="exhibit-list-item-total-comment exhibit-list-item-total-item">
										<image class="exhibit-list-item-total-icon" src="@/static/images/comment-icon.png"
											mode="widthFix"></image>
										{{item.comment>=1000?item.comment/1000:item.comment}} <text
											v-if="item.comment>=1000">k</text>
									</view>
								</view>
							</view>
							<view class="exhibit-list-tips">一百米以内可进行点赞或评论</view>
						</view>
						<view v-else>
							暂无展品列表！
						</view>
						
					</view>
				</view>
			</view>

		</view>
	</view>
</template>
<script module="AmapRender" lang="renderjs">
	import renderJs from './renderJs.js'
	export default renderJs
</script>
<script>
	const timer = null
	import PointType from './components/pointTools.vue';
	import StorePopup from './components/storeDialog.vue';
	import InputDialog from './components/inputDialog.vue';
	import PersonDialog from './components/personDialog.vue';
	import HouseDialog from './components/houseDialog.vue';
	import DianPopup from './components/dianPopup.vue';
	import MoonDialog from './components/moonDialog.vue';
	import dragButton from '@/components/drag-button/drag-button.vue';
	import Share from './components/share.vue';
	import YdPopup from './components/ydPopup.vue';
	import HbGet from './components/hbGet.vue'
	import MerchantList from './components/merchantList'
	import {
		wgs84_to_gcj02
	} from "./wgs84_to_gcj02"
	import {
		apiLocationReport,
		apiLocationVisibleGet,
		apiLocationMe,
		apiAddressSet,
		apiLocationSearchPeople,
		apiAddressGet,
		apiGetNight,
		apiToken,
	} from '@/api/common.js';
	export default {
		components: {
			PointType,
			StorePopup,
			InputDialog,
			PersonDialog,
			HouseDialog,
			dragButton,
			MoonDialog,
			Share,
			YdPopup,
			DianPopup,
			MerchantList
		},
		data() {
			return {
				mapLoad: false,
				userInfo: {},
				visibleRole: 0,
				center: uni.getStorageSync('location') ? uni.getStorageSync('location').split(',') : null,
				person: null,
				redPacket: null,
				me: uni.getStorageSync('setStorageSync') || null,
				moon: null,
				house: null,
				timer: null,
				fireShow: false,
				fireStatus: false,
				district: '',
				toolEvent: null,
				notify: {
					at: '',
					comment: '0',
					friendApply: '2',
					like: '8'
				},
				gocenter: false,
				temperature: '',
				weather: 'qing',
				searchPeople_option: {},
				mapComplete: false,
				mapStyle: 'red',
				locaErr: 'success',
				outInPage: false,
				count: 1,
				hbChooseLocation: {},
				redPacketInfo: {},
				// 活动列表
				state: 3,
				isShopOpen: false, //是否展开
				activityTitle: [{
						name: "活动列表",
						icon: "pinglun.png"
					},
					{
						name: "展品列表",
						icon: "topTop.png"
					}
				],
				activityTitleIndex: 0,
				shopInfo: {},
				activeList: [],
				activePage: 1,
				activePagesize: 10,
				distance:0
			};
		},
		computed: {
			// newFriendList(newVal, oldVal) {
			// 	return this.$store.state.Yxim_info.friendApply;
			// }
		},
		props: {
		    BusinessId: {
		      type: String,
		      default: () => ({}) // 默认值是一个空对象
		    },
			BusinessLat:{
				type: String,
				default: () => ({}) // 默认值是一个空对象
			},
			BusinessLng:{
				type: String,
				default: () => ({}) // 默认值是一个空对象
			}
		},
		async mounted() {

			const appAuthorizeSetting = uni.getAppAuthorizeSetting()
			console.log('appAuthorizeSetting', appAuthorizeSetting);
			this.pageInit()
		},
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
			console.log(this.timer, 'this.timer');
			this.timer = null;
		},
		methods: {
			// toast(title) {
			// 	this.$refs.notify.show({
			// 		title,
			// 		position: "top"
			// 	})
			// },

			async pageInit() {
				// this.fireShow = !uni.getStorageSync('firstRegister') ? false : true; //获取是否显示指引
				// await this.httpLocationVisibleGet()
				// this.openTimeInterval()
				// await this.httpLocationReport(async () => {
				// 	// //本机用户 位置 电量 上报
				// 	await this.httpLocationMe(false);
				// });
				// 获取商户信息
				this.getShopInfo();

			},
		
			cleatTimeout() {
				clearTimeout(this.timer)
				this.timer = null;
			},
	
			styleChange() {
				this.mapStyle = this.mapStyle == 'red' ? 'bule' : 'red'
				this.toolEvent = {
					act: 'mapStyleChange',
					params: this.mapStyle == 'red' ? 'amap://styles/461d0df8becbaaefd1e2dcbd7c8971df' :
						'amap://styles/e0973364545af4fb92420a3bdf9409b5'
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},

			// 设置
			handelSet(url) {
				uni.navigateTo({
					url
				});
			},

			/**
			 * 	地图层 回调
			 */
			async callApp({
				act,
				option,
				interval
			} = {}) {
				console.log('最外层回调', option)
				switch (act) {
					case 'map:complete': //地图加载完成
						this.mapComplete = true
						// await this.httpLocationReport(async () => {
						// 	// //本机用户 位置 电量 上报
						// 	this.httpLocationMe((me) => {
						// 		console.log(me)
						// 		if (!this.gocenter) {
						// 			this.gocenter = true;
						// 			this.toolEvent = {
						// 				act: 'move-view-center',
						// 				params: {
						// 					lng: me.longitude,
						// 					lat: me.latitude
						// 				}
						// 			};
						// 			setTimeout(() => {
						// 				this.toolEvent = null;
						// 			}, 1400);
						// 		}
						// 	});
						// });

					
						this.httpAddressGet();
						break;
					
					case 'change:maxDist': //重置可视半径
						this.searchPeople_option = option;
						// this.httpLocationSearchPeople(option);
						// this.httpRedpacket(option)
						break;

					case 'temperature:change':
						this.temperature = option.temperature;
						this.weather = option.weather
						break;
					
					case 'closeStoreDialog':
						this.$refs.storePopup.guanbi(option);
						break;
					case 'SET_EVENT_NULL':
						this.event = null;
						break;
					case 'changeCountry':
						this.district = option;
						break;
					case 'close_storePopup':
						this.$refs.storePopup.close();
						break;
					case 'open_personPopup':
						this.$refs.personDialog.open(option);
						break;
					case 'open_housePopup':
						this.$refs.houseDialog.open(option);
						break;
					case 'mapLongClick':
						this.$refs.dianPopup.open(option)

						// this.mapLongClick(option);
						break;
					case 'update:data':
						const {
							key, value
						} = option;
						this[key] = value;
						break;
					default:
						break;
				}
			},
			// 添加标点
			poiMapAddMarker(option) {
				this.outInPage = true
				this.$refs.dianPopup.open(option)
				this.toolEvent = {
					act: 'poiMapAddMarker',
					params: option
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
				setTimeout(() => {
					this.outInPage = false
				}, 2000);
			},
			closeAddress() {
				this.toolEvent = {
					act: 'houseSetPopupClose'
				};
				setTimeout(() => {
					this.toolEvent = null;
				}, 100);
			},
			// followAddSuccess(data) {
			// 	this.toolEvent = {
			// 		act: 'person_guanzhu',
			// 		params: data
			// 	};
			// 	setTimeout(() => {
			// 		this.toolEvent = null;
			// 	}, 100);
			// 	// this.httpLocationSearchPeople(data);
			// },
			// houseDelSuccess() {
			// 	this.$refs.storePopup.close();
			// 	this.httpAddressGet();
			// 	// this.toolEvent = {
			// 	// 	act: 'houseDataDel'
			// 	// }
			// 	// setTimeout(() => {
			// 	// 	this.toolEvent = null
			// 	// }, 100);
			// },
		
			
			// 地图点击事件！
			mapLongClick(ev) {
				// this.$refs.dianPopup.close()
				// this.$refs.pointType.open({
				// 	...ev
				// });
			},
			
			// 搜索
			handelSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},
			
	
			/**
			 * 	获取地图中的房子
			 */
			// httpAddressGet() {
			// 	apiAddressGet().then(res => {
			// 		if (res.code == 200) {
			// 			this.house = res.message;
			// 			setTimeout(function() {
			// 				this.house = null;
			// 			}, 100);
			// 			// this.event = {
			// 			// 	key: 'getAddressGet',
			// 			// 	params: res.message
			// 			// }
			// 		}
			// 	}).catch(err => {})
			// },
			/**
			 * 	获取可见范围
			 */
			httpLocationVisibleGet() {
				apiLocationVisibleGet().then(res => {
					if (res.code == 200) {
						this.visibleRole = res.message.role.role;
						console.log("获取可见范围")
						console.log(this.visibleRole)
						// this.event = {
						// 	key: 'getLocationVisibleGet',
						// 	params: res.message
						// }
					}
				});
			},
			/**
			 * 	获取用户位置
			 */
			httpLocationMe(fn) {
				const fnc = fn
				apiLocationMe().then(res => {
					if (res.code == 200) {
						this.userInfo = res.message;
						uni.setStorageSync('userLoglat', [this.userInfo.longitude, this.userInfo.latitude])
						console.log(uni.getStorageSync('userLoglat'),
							` uni.getStorageSync('userLoglat') uni.getStorageSync('userLoglat') uni.getStorageSync('userLoglat')`
						);
						if (!this.mapComplete) return false

						fnc && fnc(res.message)
						this.me = null;
						this.count = 0
						setTimeout(() => {
							this.me = {
								...this.userInfo,
								longitude: this.userInfo.longitude + this.count * 1 * .0002,
								latitude: this.userInfo.latitude + this.count * 1 * .0002
							};
							// console.log(';;;;;;;;;;', this.me);
						}, 200);
						// this.event = {
						// 	key: 'getLocationMe',
						// 	params: res.message,
						// 	interval
						// }
						// 获取用户和商家的距离
						this.calculateDistance(this.userInfo.latitude + this.count * 1 * .0002, this.userInfo.longitude + this.count * 1 * .0002, this.shopInfo.coordinate.split(',')[0], this.shopInfo.coordinate.split(',')[1]);
					}
				});
			},
			/**
			 * 	本机用户 位置 电量 上报
			 */
			async httpLocationReport(fn) {
				this.$common.getBattery(level => {
					// console.log(level, '==================level');
					uni.getLocation({
						type: 'wgs84',
						isHighAccuracy: true,
						success: async res => {
							this.locaErr = 'success'
							// 商家和用户之间距离
							// console.log("商家和用户之间距离")
							// console.log(res)
							this.calculateDistance(res.latitude, res.longitude, this.shopInfo.coordinate.split(',')[0], this.shopInfo.coordinate.split(',')[1]);
							// console.log('00000', res.longitude, res.latitude);
							const gcj02 = wgs84_to_gcj02(res.longitude, res
								.latitude)
							const result = await apiLocationReport({
								longitude: gcj02[0], //经度
								latitude: gcj02[1], //维度
								electricity: level //电量 0-100
							}).then(res1 => {
								// console.log('//////////////////', res1, res);
								fn && fn();
							});
						},
						fail: err => {
							console.log('dingweo', err);

							if (err.errMsg.indexOf('fail') > -1 && this.locaErr !== 'fail') {
								// 1. 获取当前页面栈实例（此时最后一个元素为当前页）
								this.locaErr = 'fail'
								// uni.navigateTo({
								// 	url: '/pages/permissions/permissions',
								// 	success: res => {},
								// 	fail: () => {},
								// 	complete: () => {}
								// });
							}
						}
					});
				});
			},
			// 去活动详情页
			goDetails(item) {
				var type = this.activityTitleIndex * 1 === 0 ? 'activity' : 'exhibits'
				var isCanLike = this.distance>0 && this.distance*1000<100 ? 1 : 0;
				uni.navigateTo({
					url: '/pages/merchantStoreDetails/merchantStoreDetails?id=' + item.id + "&type=" + type + "&business_id=" + this.BusinessId + "&isCanLike="+isCanLike
					
				})
			},
			// 获取图片
			getImageUrl(iconName) {
				return require('@/static/images/' + iconName);
			},
			// 切换title
			changeTitle(index) {
				this.activityTitleIndex = index;
				this.activePage = 1;
				this.getActiveList()
				console.log(this.distance)
			},
			// 获取商户信息
			getShopInfo() {
				// this.$http.get('/api/user/business/get').then((res) => {
				// 	if (res.code === 200) {
				// 		this.shopInfo = res.message;
				// 		console.log("商户信息")
				// 		console.log(this.shopInfo)
				// 		this.getActiveList();//获取活动列表
				// 		this.getDistance();//计算距离
				// 	}
				// });
				
				this.$http.get('/api/user/business/detail',{business_id:this.BusinessId}).then((res) => {
					if (res.code === 200) {
						this.shopInfo = res.message;
						console.log(this.shopInfo)
						this.getActiveList();
						this.getDistance();//计算距离
					}
				});
			},
			// 获取活动列表
			getActiveList() {
				var url = this.activityTitleIndex * 1 === 0 ? '/api/user/business/activity/list' :
					'/api/user/business/exhibits/list'
				this.$http.get(url, {
					business_id: this.shopInfo.id,
					page: this.activePage,
					size: this.activePagesize
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						this.activeList = res.message.list ? res.message.list : res.message;
					}
				});
			},
			// 参加活动
			joinActivity(item){
				this.$http.post("/api/user/business/activity/join", {
					id: item.id
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						uni.showToast({
							title: "参加成功！"
						})
						this.getActiveList()
					}
				});
			},
			// 计算距离
			getDistance(){
				uni.getLocation({
					type: 'wgs84',
					isHighAccuracy: true,
					success: async res => {
						// 商家和用户之间距离
						console.log("商家和用户之间距离")
						console.log(res)
						this.calculateDistance(res.latitude, res.longitude, this.BusinessLat, this.BusinessLng);
						// var lat1 = res.latitude,
						// 	lon1 = res.longitude,
						// 	lat2 = this.shopInfo.coordinate.split(',')[0],
						// 	lon2 = this.shopInfo.coordinate.split(',')[1];
						
						// var R = 6371; // 地球半径，单位千米
						// var dLat = (lat2 - lat1) * Math.PI / 180; // 纬度差转换为弧度
						// var dLon = (lon2 - lon1) * Math.PI / 180; // 经度差转换为弧度
									
						// var a = 
						//   Math.sin(dLat / 2) * Math.sin(dLat / 2) +
						//   Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
						//   Math.sin(dLon / 2) * Math.sin(dLon / 2); // Haversine公式中的a部分
						// var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)); // Haversine公式中的c部分
						// var d = R * c; // 最终距离
									
						// // return d; // 返回距离，单位千米
						// console.log(d.toFixed(2) + ' km');
						// this.distance = d.toFixed(2);
						
					}
				})
			},
			// 计算距离
			calculateDistance(lat1, lon1, lat2, lon2) {
			  var R = 6371; // 地球半径，单位千米
			  var dLat = (lat2 - lat1) * Math.PI / 180; // 纬度差转换为弧度
			  var dLon = (lon2 - lon1) * Math.PI / 180; // 经度差转换为弧度
			
			  var a = 
			    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
			    Math.sin(dLon / 2) * Math.sin(dLon / 2); // Haversine公式中的a部分
			  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)); // Haversine公式中的c部分
			  var d = R * c; // 最终距离
			
			  // return d; // 返回距离，单位千米
			  console.log(d.toFixed(2) + ' km');
			  this.distance = d.toFixed(2);
			},
			// 联系客服
			 makePhoneCall(phoneNumber) {
			      // 检查是否在5+ App环境中
			      if (typeof plus !== 'undefined') {
			        // 使用plus.device.dial方法拨打电话
			        plus.device.dial(phoneNumber, false);
			      } else {
			        // 如果不在5+ App环境中，可以给出提示或者使用其他方法
			        console.log('当前环境不支持拨打电话');
			      }
			    }
		}
	};
</script>
<style>
	.amap-marker-label {
		border: 0;
		background-color: transparent;
	}
</style>
<style scoped lang="scss">
	.houseinfo {
		background-color: #000 !important;
	}

	.location-content {
		width: 540rpx;
		margin: auto;
		background-color: #1a1a1a;
		border-radius: 20rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 40rpx;

		.loca-name {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
			text-align: center;

		}

		.loca-tip {
			width: 100%;
			text-align: left;
			font-size: 28rpx;
			margin-bottom: 10rpx;
		}

		.loca-txt {
			font-size: 24rpx;
		}

		.loca-ty {
			padding: 20rpx 150rpx;
			color: #333;
			margin-top: 100rpx;
			background-color: #fff;
			border-radius: 60rpx;
		}

		.loca-quit {
			margin-top: 40rpx;
			font-weight: bold;
			font-size: 28rpx;
		}
	}

	.changeStyle {
		display: flex;
		position: fixed;
		right: 200rpx;
		bottom: 500rpx;

		.red,
		.blue {
			width: 100rpx;
			height: 100rpx;
			background: red;
		}

		.blue {

			background: blue;
		}
	}

	.container {
		position: relative;

		#AmapRender {
			width: 100%;
			height: 100vh;
			background-color: transparent;
		}

		.centerTool {
			width: 82rpx;
			height: 82rpx;
			background: #ffffff;
			box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(0, 0, 0, 0.62);
			border-radius: 50%;
			position: fixed;
			bottom: 210rpx;
			left: 50%;
			transform: translateX(-50%);
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 102rpx;
				height: 102rpx;
				margin-left: -8rpx;
			}
		}

		.searchTool {
			width: 74rpx;
			height: 74rpx;
			position: fixed;
			top: 95rpx;
			left: 32rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.settingTool {
			width: 74rpx;
			height: 74rpx;
			position: fixed;
			top: 95rpx;
			right: 32rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.hbTool {
			width: 74rpx;
			height: 74rpx;
			position: fixed;
			top: 327rpx;
			right: 32rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.firendTool {
			width: 74rpx;
			height: 74rpx;
			position: fixed;
			top: 211rpx;
			right: 32rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tianqi {
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			left: 50%;
			top: 100rpx;
			transform: translateX(-50%);

			.temperature {
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 600;
				font-size: 20rpx;
				color: #FFFFFF;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-right: 20rpx;
				margin-top: 10rpx;
				position: relative;

				text {
					width: 74rpx;
					height: 74rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					z-index: 2;
				}

				.weather {
					width: 74rpx;
					position: absolute;
					left: 0;
					top: 4rpx;
				}
			}

			.district {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 208rpx;
				height: 74rpx;
				background: rgba(63, 63, 63, 0.38);
				border-radius: 14rpx 14rpx 14rpx 14rpx;
				opacity: 1;
				font-size: 32rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				color: #ffffff;
				line-height: 45rpx;


			}
		}


		.huobaMask {
			width: 100vw;
			height: 100vh;

			background-color: rgba(0, 0, 0, 0.4);
			position: fixed;
			left: 0;
			top: 0;
			z-index: 20;

			.know {
				position: absolute;
				left: 50%;
				bottom: 750rpx;
				transform: translateX(-50%);

				.knowImg {
					width: 168rpx;
					height: 79rpx;
				}
			}

			.box {
				width: 655rpx;
				height: 241.66rpx;
				position: absolute;
				left: 30rpx;
				bottom: 450rpx;
				padding: 42rpx 52rpx;
				box-sizing: border-box;

				&::after {
					width: 5rpx;
					height: 182rpx;
					background: linear-gradient(217deg, #54c2ee 0%, rgba(84, 194, 238, 0) 100%);
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					content: '';
					position: absolute;
					left: 70rpx;
					top: 250rpx;
				}

				.boxTitle {
					width: 96rpx;
					height: 70rpx;
					font-size: 48rpx;
					font-family: Source Han Sans-Medium, Source Han Sans;
					font-weight: 500;
					color: #ffffff;
					line-height: 70rpx;
					position: relative;
					z-index: 2;
				}

				.boxTxt {
					width: 476rpx;
					height: 49rpx;
					font-size: 34rpx;
					font-family: Source Han Sans-Regular, Source Han Sans;
					font-weight: 400;
					color: #ffffff;
					line-height: 49rpx;
					margin-top: 12rpx;
					position: relative;
					z-index: 2;
				}

				image {
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
				}
			}
		}

		.huobaTool {
			width: 102rpx;
			height: 102rpx;
			// background: #FFFFFF;
			// box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(0, 0, 0, 0.62);
			border-radius: 50%;
			position: fixed;
			bottom: 200rpx;
			left: 50rpx;
			z-index: 30;

			.fire {
				width: 100%;
				height: 100%;
			}

			.shou {
				width: 88rpx;
				height: 100rpx;
				margin-left: 14rpx;
				z-index: 29;
			}

			.dian {
				width: 54rpx;
				height: 54rpx;
				position: absolute;
				bottom: -30rpx;
				left: 24rpx;
				z-index: 27;
			}
		}

		.newFriend-list {
			position: fixed;
			left: 50%;
			top: 50rpx;
			transform: translateX(-50%);
			z-index: 10;

			.new-friend {
				width: 90vw;
				background-color: rgba(0, 0, 0, 0.7);
				border-radius: 30rpx;
				box-shadow: 0 0 3rpx 6rpx rgba(0, 0, 0, 0.3);
				font-size: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx;
				box-sizing: border-box;
				margin-bottom: 20rpx;

				.avatar {
					width: 100rpx;
					height: 100rpx;
					border-radius: 20rpx;
					margin-right: 20rpx;
				}

				.info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
					box-sizing: border-box;

					.name {
						font-size: 32rpx;
						color: #fff;
						margin-bottom: 10rpx;
					}

					.desc {
						font-size: 20rpx;

						.age,
						.addr {
							display: inline-block;
							padding: 2rpx 10rpx;
							background-color: rgba(0, 0, 0, 0.9);
							color: #fff;
							opacity: 0.7;
							border-radius: 5rpx;

							.iconfont {
								font-size: 20rpx;
								color: #1296db;
								margin-right: 10rpx;
							}

							.icon-xingbienv {
								color: #ff3ec9;
							}
						}
					}
				}

				.btns {
					.cuo {
						width: 50rpx;
						height: 50rpx;
					}

					.dui {
						width: 50rpx;
						height: 50rpx;
					}
				}
			}
		}

		.notifyMsg {
			width: 100%;
			height: 100%;

			.point {
				width: 10rpx;
				height: 10rpx;
				border-radius: 50%;
				background-color: red;
				position: absolute;
				right: 20rpx;
				top: 20rpx;
				z-index: 2;
			}

			.notifyMsg-icon {
				width: 120rpx;
				height: 120rpx;
				// width: 100%;
				// height: 100%;
			}
		}

		button {
			position: fixed;
			left: 0;
			top: 0;
		}

		::v-deep .amap-logo {
			display: none !important;
		}

		::v-deep .amap-copyright {
			display: none !important;
		}

		// 商户信息
		.merchant-list-father {
			position: fixed;
			left: 0;
			right: 0;
			top: 220rpx;
			bottom: 0;
			border-radius: 20px 20px 0px 0px;
			background: rgb(35, 35, 45);
			overflow-y: scroll;
		}
	}

	.getToken {
		position: fixed;
		left: 100rpx;
		top: 600rpx;
		margin: 300rpx;
	}

	// 活动列表
	.merchant-list-container {
		padding: 20px 0;
		box-sizing: border-box;

		.title-hr {
			width: 100%;
			height: 1px;
			background-color: #787878;
			border: 0;
		}

		.merchant-list-title {
			width: 100%;
			padding: 0 20px;
			box-sizing: border-box;
			margin-bottom: 20px;

			.title-container {
				display: flex;
				align-items: flex-start;
				justify-content: space-between;

				// margin-bottom:20px;
				.shop {
					.shop-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 22px;
						font-weight: 700;
						line-height: 26px;
						margin-bottom: 8px;
					}

					.shop-localtions {
						min-width: 48px;
						// height: 14px;
						padding: 2px 4px;
						box-sizing: border-box;
						border-radius: 1px;
						background: rgba(143, 163, 201, 0.23);
						display: flex;
						align-items: center;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 9px;
						font-weight: 400;
						line-height: 11px;

						.localtions-icon {
							width: 10px;
							height: 10px;
						}
					}
				}

				.customer {
					.customer-title {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						margin-bottom: 20px;

						.customer-title-item {
							width: 64px;
							padding: 1px;
							box-sizing: border-box;
							border-radius: 60px;
							background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250) 100%);
							margin-left: 10px;

							.customer-title-item-bg {
								width: 100%;
								height: 100%;
								border-radius: 60px;
								background-color: #23232D;

								.customer-title-item-word-bg {
									height: 24px;
									padding: 0px 8px;
									box-sizing: border-box;
									border-radius: 60px;
									display: flex;
									align-items: center;
									justify-content: center;
									background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250));
									-webkit-background-clip: text;
									-webkit-text-fill-color: transparent;
									background-clip: text;
									text-fill-color: transparent;
									font-family: HarmonyOS Sans;
									font-size: 10px;
									font-weight: 400;
									line-height: 12px;

									.customer-title-item-icon {
										width: 14px;
										height: 14px;
										margin-right: 2px;
									}
								}
							}
						}
					}

					.customer-total {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 10px;
						font-weight: 400;
						line-height: 12px;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.customer-total-number {
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 20px;
							font-weight: 700;
							line-height: 23px;
							margin-left: 4px;
						}
					}
				}
			}

			// 店铺公告
			.shop-notice {
				height: 80px;
				overflow-y: hidden;
				position: relative;

				.shop-notice-item {
					padding: 15px 0;
					box-sizing: border-box;
					border-bottom: 1px solid #787878;

					.shop-notice-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 500;
						line-height: 16px;
						margin-bottom: 8px;
					}

					.shop-content {
						color: rgb(206, 206, 206);
						font-family: 思源黑体;
						font-size: 14px;
						font-weight: 400;
						line-height: 20px;
						word-break: break-all;
					}
				}

				.shop-notice-item:nth-child(3) {
					border-bottom: 0;
					margin-bottom: 28px;
				}

				.shop-open-container {
					position: absolute;
					left: 0;
					right: 0;
					bottom: 0;
					width: 100%;
					height: 20px;
					background-image: linear-gradient(to bottom, rgba(35, 35, 45, 0.1), #23232D);
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.shop-button {
						background-color: #23232D;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 500;
						line-height: 16px;
						padding-left: 20px;
						box-sizing: border-box;
						display: flex;
						align-items: center;

						.arrow-icon {
							width: 16px;
							height: 16px;
						}
					}
				}


			}
		}

		// 活动列表
		.merchant-activity {
			width: 100%;

			.merchant-activity-title-container {
				width: 100%;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;

				.merchant-activity-title {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 10px 0;
					box-sizing: border-box;
					width: 53%;
					background-repeat: no-repeat;
					background-size: cover;
					position: absolute;

					.activity-title-icon {
						width: 20px;
						height: 20px;
						margin-right: 6px;
					}
				}

				.merchant-activity-title.merchant-activity-title-active {
					z-index: 100;
				}

				.merchant-activity-title:nth-of-type(1).merchant-activity-title-active {
					background-image: url("../../static/images/shop-title-bg-left-active.png");
					left: 0;
				}

				.merchant-activity-title:nth-of-type(2).merchant-activity-title-active {
					background-image: url("../../static/images/shop-title-bg-right-active.png");
					right: 0;
				}

				.merchant-activity-title:nth-of-type(1) {
					left: 0;
					background-image: url("../../static/images/shop-title-bg-left.png");
				}

				.merchant-activity-title:nth-of-type(2) {
					right: 0;
					background-image: url("../../static/images/shop-title-bg-right.png");
				}
			}

			// 商品列表
			.merchant-activity-list {
				width: 100%;
				padding: 20px;
				box-sizing: border-box;

				.item {
					width: 100%;
					display: flex;
					justify-content: space-evenly;
					align-items: flex-start;
					border-radius: 10px;
					background: #383A44;
					margin-bottom: 20px;
					padding: 10px;
					box-sizing: border-box;

					.item-cover {
						position: relative;
						margin-right: 10px;
						border-radius: 6px;

						.activeimage {
							width: 122px;
							height: 122px;
							border-radius: 6px;
						}

						.activeimagecover {
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							width: 122px;
							height: 122px;
							border-radius: 9px;
							background: rgba(0, 0, 0, 0.7);
							display: flex;
							align-items: center;
							justify-content: center;

							.activeimageend {
								width: 102px;
								height: 48px;
								margin: 0 auto;
							}
						}
					}


					.item-info {
						flex: 1;
						height: 122px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						padding: 6px 0 10px 0;
						box-sizing: border-box;

						.item-title {
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 18px;
							font-weight: 700;
							line-height: 22px;
							margin-bottom: 10px;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.item-total {
							display: flex;
							align-items: flex-end;
							justify-content: space-between;
							color: rgb(224, 224, 224);
							font-family: HarmonyOS Sans;
							font-size: 10px;
							font-weight: 400;
							line-height: 12px;
							margin-bottom: 13px;

							.item-button {
								width: 64px;
								height: 26px;
								color: rgb(255, 255, 255);
								font-family: HarmonyOS Sans;
								font-size: 12px;
								font-weight: 500;
								line-height: 14px;
								padding: 0;
								margin: 0;
								border-radius: 60px;
								display: flex;
								align-items: center;
								justify-content: center;
							}

							.item-button-join {
								border: 1px solid #EDEDED;
								background: #383A44;
							}

							.item-button-unjoin {
								background: linear-gradient(90.00deg, rgb(83, 194, 238), rgb(207, 178, 250) 100%);
							}

							.item-button-end {
								background-color: #50515a;
							}
						}

						.item-time {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 10px;
							font-weight: 400;
							line-height: 12px;
							letter-spacing: 0px;
							text-align: left;

							.time-clock {
								width: 12px;
								height: 12px;
								margin-right: 3px;
							}
						}
					}
				}

			}

			// 展品列表
			.exhibit-list {
				width:100%;
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: 10px;
				box-sizing: border-box;

				.exhibit-list-item {
					width: 48%;
					border-radius: 6px;
					background: rgb(56, 58, 68);
					padding: 10px;
					box-sizing: border-box;

					.exhibit-list-item-cover {
						width: 100%;
						height: auto;
						max-height: 153px;
						object-fit: cover;
						margin-bottom: 12px;
					}

					.exhibit-list-item-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom: 12px;
					}

					.exhibit-list-item-price {
						color: rgb(89, 192, 239);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom: 12px;
					}

					.exhibit-list-item-total {
						padding: 14px 12px;
						box-sizing: border-box;
						border-top: 1px solid rgba(196, 196, 196, 0.12);
						display: flex;
						align-items: center;
						justify-content: space-between;
						color: rgb(197, 209, 230);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
						letter-spacing: 0px;
						text-align: left;

						.exhibit-list-item-total-item {
							display: flex;
							align-items: center;

							.exhibit-list-item-total-icon {
								width: 15px;
								height: auto;
								margin-right: 5px;
							}
						}

					}
				}

				.exhibit-list-tips {
					position: fixed;
					left: 0;
					right: 0;
					bottom: 0;
					height: 30px;
					background: rgba(0, 0, 0, 0.7);
					color: rgb(185, 150, 249);
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 30px;
					letter-spacing: 0px;
					text-align: center;
				}
			}

			.twoDot {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
</style>