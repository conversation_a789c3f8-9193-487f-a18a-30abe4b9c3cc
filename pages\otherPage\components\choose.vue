<template>
	<view class="">
		<uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="" @click="setCurrnet(0)">
					<image class="chooseItem" src="../../../static/images/jingqueweizhi2.png" mode="widthFix"
						v-if="current==0"></image>
					<image class="chooseItem" src="../../../static/images/jingque.png" mode="widthFix" v-else>
					</image>
				</view>
				<view class="" @click="setCurrnet(1)">
					<image class="chooseItem" src="../../../static/images/mohuweizhi2.png" mode="widthFix"
						v-if="current==1">
					</image>
					<image class="chooseItem" src="../../../static/images/mohuweizhi.png" mode="widthFix" v-else>
					</image>
				</view>
				<view class="" @click="setCurrnet(2)">
					<image class="chooseItem" src="../../../static/images/dongjieweizhi2.png" mode="widthFix"
						v-if="current==2">
					</image>
					<image class="chooseItem" src="../../../static/images/dongjieweizhi.png" mode="widthFix" v-else>
					</image>
				</view>
				<view class="cancal" @click="close">
					取消
				</view>
				<view class="img24" />
			</view>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		props: {
			// current: {
			// 	default: 2,
			// },
			uid: {
				require: true,
				type: Number
			},
			uuid: {
				require: true,
				type: String
			}
		},
		data() {
			return {
				current: 0,
			}
		},
		mounted() {
			console.log('=================');

		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			setCurrnet(ids) {

				this.$http.post('/location/ghost/switch', {
					"mode": ids,
					"uids": [
						this.uid
					]
				}).then(res => {
					console.log('res', res);
					this.$emit('switch', res.message)
					this.close()
				})
			},
			close() {
				this.$refs.popup.close()
			},
			open() {
				this.$http.get('/share/user/' + this.uuid).then(res => {
					const {
						ghost_mode
					} = res.message
					this.current = ghost_mode
				})
				this.$refs.popup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cPopup {
		background: #fff;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
		padding-top: 30rpx;
		padding-bottom: 30rpx;

		.chooseItem {
			width: 750rpx;
			height: 97rpx;
		}

		.cancal {
			height: 86rpx;
			line-height: 86rpx;
			text-align: center;
			background: #F6F6F6;
			border-radius: 14rpx 14rpx 14rpx 14rpx;
			font-size: 26rpx;
			color: #3D3D3D;
			margin: 20rpx 32rpx;
		}

		.item {
			margin-top: 27rpx;
			padding-bottom: 18rpx;
			color: rgba(61, 61, 61, 0.82);
			justify-content: flex-start;

			.rightInfo {
				margin-left: 35rpx;
			}

			.disable {

				width: 42rpx;
				height: 42rpx;
			}
		}

		.avatar {
			width: 42rpx;
			height: 42rpx;
			border-radius: 50%;
		}

		.name {
			margin-left: 20rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #232323;
		}
	}
</style>
