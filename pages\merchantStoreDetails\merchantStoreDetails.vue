<template>
	<view class="merchant-store-details-conatiner">
		<u-navbar :customBack="goBack" back-text="" title="活动详情" :background="{backgroundColor: '#191C26'}"
			:border-bottom="false" height="60" title-color="#fff" back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">{{activiType == 'activity' ? "活动详情" : "展品详情"}}</view>
				</view>
			</view>
			<view v-if="activiType == 'activity' && !activeInfo.expired && activeInfo.is_join" class="navbar-right"
				@click="clickMore()" slot="right">
				<image class="more-icon" src="@/static/images/more-icon.png" mode=""></image>
				<view class="exit-button" v-if="isShowExit" @click="exitActivity">退出活动</view>
			</view>
		</u-navbar>
		<!-- 活动详情 -->
		<view class="merchant-store-details-content">
			<view class="item">
				<view class="item-cover">
					<!-- 	<image class="activeimage" src="@/static/images/title-image.png" mode="">
					</image> -->
					<view class="activeimageContainer">
						<image class="activeimage" :src="activeInfo.cover" mode="widthFix"></image>
					</view>
					<view class="activeimagecover" v-if="activiType == 'activity' && activiType.expired">
						<image class="activeimageend" src="@/static/images/activeEnd.png" mode="">
						</image>
					</view>
				</view>
				<!-- <image class="activeimage" :src="item.cover" mode=""></image> -->
				<view class="item-info">
					<view class="item-title">
						{{activeInfo.title}}
					</view>
					<view v-if="activiType == 'activity'" class="item-content">
						<view class="item-people">参与人数{{activeInfo.member_total}}</view>
						<view v-if="activeInfo.timeline" class="item-time">
							<image class="time-clock" src="@/static/images/vip/clock.png" mode=""></image>
							<view class="time">{{activeInfo.timeline}}</view>
						</view>
					</view>
					<view v-else class="item-content">
						<view class="item-like">
							<view class="item-like-content" v-if="activeInfo.like>0">
								<image class="item-like-icon" src="@/static/images/like-active-icon.png"
									mode="widthFix">
									<view class="item-like-number">
										{{activeInfo.like>=1000?activeInfo.like/1000:activeInfo.like}} <text
											v-if="activeInfo.like>=1000">k</text></view>
									的人认为此展品很棒
							</view>
						</view>
						<view v-if="activeInfo.price" class="item-price">
							指导价格<view class="item-price-number">￥{{activeInfo.price}}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="activiType == 'activity'" class="item-button-content">
				<!-- <button v-if="new Date > new Date(activeInfo.end_time)" class="item-button item-button-end" type="default">
					<view>活动结束</view>
				</button> -->
				<button v-if="activeInfo.expired" class="item-button item-button-end" type="default">
					<view>活动结束</view>
				</button>
				<button v-else-if="activeInfo.is_join" class="item-button item-button-unjoin" type="default">
					<view>已参加</view>
				</button>
				<button v-else class="item-button item-button-unjoin" type="default">
					<view>参加</view>
				</button>
			</view>
		</view>
		<!-- 活动内容 -->
		<view class="info">
			<view class="title">
				<view class="icon">
					<image class="title-icon" src="../../static/images/vip/icon-info.png" mode=""></image>
				</view>
				<view class="title-text">
					{{activiType == 'activity'?"活动内容":"展品介绍"}}
				</view>
			</view>
			<view class="info-content" v-html="activeInfo.content">
			</view>
		</view>
		<!-- 评论区 -->
		<view v-if="activiType == 'exhibits'" class="exhibits-container">
			<view class="exhibits-title">评论区<view v-if="commentList && commentList.length*1>0">({{commentList.length}})
				</view>
			</view>
			<view class="exhibits-list">
				<view class="exhibits-list-item" v-for="(item,index) in commentList" :key="index">
					<image class="exhibits-list-item-cover" :src="item.user_info.avatar" mode="widthFix">
						<view class="exhibits-list-item-content">
							<view class="exhibits-list-item-user">
								<view>
									<view class="exhibits-list-item-name">{{item.user_info.nickname}}</view>
									<view class="exhibits-list-item-time">{{item.created_at}}</view>
								</view>
								<!-- <view v-if="isCanLike*1==1"> -->
								<view v-if="item.is_like" class="exhibits-list-item-comment-like"
									@click="cancelCommentLikes(item)">
									<image class="exhibits-list-item-like-icon"
										src="@/static/images/comment-like-active-icon.png" mode="widthFix">
										{{item.like>=1000?item.like/1000:item.like}} <text
											v-if="item.like>=1000">k</text>
								</view>
								<view v-else class="exhibits-list-item-comment-like" @click="goCommentLikes(item)">
									<image class="exhibits-list-item-like-icon"
										src="@/static/images/comment-like-icon.png" mode="widthFix">
										{{item.like>=1000?item.like/1000:item.like}} <text
											v-if="item.like>=1000">k</text>
								</view>
								<!-- </view> -->
							</view>
							<view class="exhibits-list-item-comment">{{item.content}}</view>
						</view>
				</view>
				<view @click="moreComment" v-if="commentList && commentList.length*1>1" class="exhibits-comment-more">
					更多{{commentList.length}}条评价 <image class="comment-more-icon"
						src="@/static/images/comment-more-icon.png" mode="widthFix">
				</view>
			</view>
			<view class="fixed-bottom">
				<!-- 发表评论 -->
				<!-- v-if="isCanLike*1==1"
				<view class="exhibit-list-tips">一百米以内可进行点赞或评论</view> -->
				<view class="submit-comment">
					<view v-if="activeInfo.is_like" class="submit-comment-like comment-like-active"
						@click="cancelLikes">
						<image class="submit-comment-like-icon" src="@/static/images/comment-like-active-icon2.png"
							mode="widthFix">
							已点赞
					</view>
					<view v-else class="submit-comment-like" @click="goLikes">
						<image class="submit-comment-like-icon" src="@/static/images/comment-like-icon2.png"
							mode="widthFix">
							点赞
					</view>

					<view v-if="userInfo.user_info" class="submit-comment-form">
						<image v-if="userInfo.user_info.avatar" class="submit-comment-headpic"
							:src="userInfo.user_info.avatar" mode="widthFix"></image>
						<image v-else class="submit-comment-headpic" src="@/static/images/default-headpic.png"
							mode="widthFix">
							<input class="submit-comment-input" v-model="commentText"
								placeholder-style="fontSize:14px;color:#A9A9A9" placeholder="请输入评论内容" />

					</view>
					<view class="submit-comment-button" @click="submitComment">发布</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				state: 3,
				info_content: "",
				isShowExit: false, //是否显示退出活动
				activeId: 0,
				activeInfo: {}, //活动详情
				activiType: "", //活动类型 activity 活动  exhibits 展品
				commentList: [], //评论列表
				commentText: "", //评论内容
				business_id: "",
				isCanLike: 0,
				userInfo: {}
			}
		},

		onLoad(options) {
			if (options.id) {
				this.activeId = options.id;
				this.activiType = options.type;
				this.business_id = options.business_id
				this.isCanLike = options.isCanLike
				this.getUserInfo()
				this.getActiveInfo();
				this.getCommentList();

			}
		},
		onShow() {

		},

		methods: {
			goBack() {
				uni.navigateBack();
				// uni.navigateTo({
				// 	url: '/pages/merchantStore/merchantStore?business_id=' + this.business_id
				// })
			},
			clickMore() {
				this.isShowExit = !this.isShowExit
			},
			// 获取用户信息
			getUserInfo() {
				this.$http.get('/api/user/info').then(async (res) => {
					this.userInfo = res.message
					console.log('---------this.info------------', this.userInfo.user_info);

				})
			},
			// 获取活动信息
			getActiveInfo() {
				var url = this.activiType == 'activity' ? '/api/user/business/activity/detail' :
					'/api/user/business/exhibits/detail'
				var params = this.activiType == 'activity' ? {
					id: this.activeId
				} : {
					exhibits_id: this.activeId
				};
				console.log("获取活动信息", params)
				this.$http.get(url, params).then((res) => {
					if (res.code == 200) {
						this.activeInfo = res.message
						console.log("-------------活动列表详情------------------", this.activeInfo);
					}
				});
			},
			// 取消点赞
			cancelLikes() {
				this.$http.post('/api/user/business/exhibits/unlike', {
					exhibits_id: this.activeId
				}).then((res) => {
					console.log(res);
					if (res.code == 200) {
						uni.showToast({
							title: '取消点赞!',
							icon: 'none'
						});
						this.getActiveInfo()
					}
				});
			},
			// 点赞
			goLikes() {
				this.$http.post('/api/user/business/exhibits/like', {
					exhibits_id: this.activeId
				}).then((res) => {
					console.log(res);
					if (res.code == 200) {
						uni.showToast({
							title: '点赞成功!',
							icon: 'none'
						});
						this.getActiveInfo()
					}
				});
			},
			// 评论列表
			getCommentList() {
				var params = {
					exhibits_id: this.activeId,
					comment_id: 0,
					direction: 1,
					size: 3
				}
				this.$http.get('/api/user/business/exhibits/comment/list', params).then((res) => {
					console.log(res);
					if (res.code == 200) {
						this.commentList = res.message
						// this.getActiveInfo()
					}
				});
			},
			// 取消评论点赞
			cancelCommentLikes(item) {
				this.$http.post('/api/user/business/exhibits/comment/unlike', {
					comment_id: item.id
				}).then((res) => {
					console.log(res);
					if (res.code == 200) {
						uni.showToast({
							title: '取消点赞!',
							icon: 'none'
						});
						this.getCommentList()
					}
				});
			},
			// 评论点赞
			goCommentLikes(item) {
				this.$http.post('/api/user/business/exhibits/comment/like', {
					comment_id: item.id
				}).then((res) => {
					console.log(res);
					if (res.code == 200) {
						uni.showToast({
							title: '点赞成功!',
							icon: 'none'
						});
						this.getCommentList()
					}
				});
			},
			// 提交评论
			submitComment() {
				this.$http.post('/api/user/business/exhibits/comment/post', {
					exhibits_id: this.activeId,
					content: this.commentText
				}).then((res) => {
					console.log(res);
					if (res.code == 200) {
						uni.showToast({
							title: '评论成功!',
							icon: 'none'
						});
						this.commentText = ""
						this.getCommentList()
					}
				});
			},
			// 更多评价
			moreComment() {
				uni.navigateTo({
					url: '/pages/merchantStoreDetails/merchantStoreDetailsComment?id=' + this.activeId +
						"&business_id=" + this.business_id + "&type=" + this.activiType + "&isCanLike=" + this
						.isCanLike
				})
			},
			// 退出活动
			exitActivity() {
				this.$http.post("/api/user/business/activity/quit", {
					id: this.activeId
				}).then((res) => {
					console.log(res);
					if (res.code === 200) {
						uni.showToast({
							title: "已退出该活动！"
						})
						this.getActiveInfo();
						this.getCommentList();
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	/deep/ .u-collapse-item_content {
		height: auto;
	}

	/deep/ .u-collapse-head {
		padding: 0;
	}

	.navbar-right {
		position: relative;

		.more-icon {
			width: 20px;
			height: 20px;
			margin-right: 34rpx;
		}

		.exit-button {
			position: absolute;
			right: 10px;
			width: 120px;
			height: 48px;
			border-radius: 10px;
			box-shadow: 0px 4px 10px 0px rgba(20, 21, 24, 0.5);
			background: rgb(102, 104, 115);
			padding: 16px 20px;
			box-sizing: border-box;
		}
	}

	.merchant-store-details-conatiner {
		width: 100%;
		background-color: #292C33;

		.merchant-store-details-content {
			width: 100%;
			padding: 20px 20px 0 20px;
			box-sizing: border-box;

			.item {
				width: 100%;
				display: flex;
				justify-content: space-evenly;
				align-items: flex-start;
				align-content: center;
				border-radius: 10px;
				// background: #292C33;
				margin-bottom: 20px;
				padding: 10px;
				box-sizing: border-box;
				border: 1px solid #666873;

				.item-cover {
					position: relative;
					margin-right: 10px;
					border-radius: 6px;

					.activeimageContainer {
						width: 122px;
						height: 122px;
						border-radius: 6px;
						margin-right: 10px;
						display: flex;
						align-items: center;
						justify-content: center;
						overflow: hidden;
						position: relative;
					}

					.activeimage {
						position: absolute;
						width: 100%;
						// height:100%;
						// width: 122px;
						// height: 122px;
						// border-radius: 6px;
						// object-fit: cover;
						// overflow: hidden;
					}

					.activeimagecover {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						width: 122px;
						height: 122px;
						border-radius: 9px;
						background: rgba(0, 0, 0, 0.7);
						display: flex;
						align-items: center;
						justify-content: center;

						.activeimageend {
							width: 102px;
							height: 48px;
							margin: 0 auto;
						}
					}
				}


				.item-info {
					flex: 1;
					height: 122px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 6px 0 10px 0;
					box-sizing: border-box;

					.item-title {
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 18px;
						font-weight: 700;
						line-height: 21px;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.item-content {
						.item-people {
							color: rgb(224, 224, 224);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							letter-spacing: 0px;
							text-align: left;
							margin-bottom: 12px;
						}

						.item-time {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							color: rgb(152, 152, 152);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;

							.time-clock {
								width: 14px;
								height: 14px;
								margin-right: 2px;
							}
						}

						.item-like {
							color: rgb(197, 209, 230);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							letter-spacing: 0px;
							text-align: left;
							display: flex;
							align-items: center;

							.item-like-content {
								display: flex;
								align-items: center;
							}

							.item-like-icon {
								width: 12px;
								height: auto;
								margin-right: 4px;
							}

							.item-like-number {
								margin-right: 4px;
								font-weight: 700;
							}
						}

						.item-price {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							color: rgb(169, 169, 169);
							font-family: HarmonyOS Sans;
							font-size: 12px;
							font-weight: 400;
							line-height: 14px;
							letter-spacing: 0px;
							text-align: left;
							margin-top: 24px;

							.item-price-number {
								color: rgb(89, 192, 239);
								font-family: HarmonyOS Sans;
								font-size: 18px;
								font-weight: 700;
								line-height: 21px;
								letter-spacing: 0px;
								text-align: left;
								margin-left: 2px;
							}
						}
					}

				}
			}

		}

		.item-button-content {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			color: rgb(224, 224, 224);
			font-family: HarmonyOS Sans;
			font-size: 10px;
			font-weight: 400;
			line-height: 12px;
			margin-bottom: 20px;

			.item-button {
				width: 100%;
				height: 52px;
				border: 1px solid #666873;
				color: rgba(255, 255, 255, 0.8);
				font-family: 阿里巴巴普惠体;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				padding: 0;
				margin: 0;
				border-radius: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.item-button-join {
				border: 1px solid #666873;
				background: #292c33;
			}

			.item-button-unjoin {
				// background: linear-gradient(90deg, rgb(83, 194, 238),rgb(207, 178, 250) 100%);
				background-image: url('../../static/images/vip/active-add-button.png');
				background-repeat: no-repeat;
				background-size: cover;
				border-radius: 12px;
			}

			.item-button-end {
				border: 1px solid #666873;
				background-color: #292c33;
			}
		}

		.info {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 20px;
			box-sizing: border-box;

			.title {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.icon {
					width: 14px;
					height: 14px;

					.title-icon {
						width: 14px;
						height: 14px;
					}
				}

				.title-text {
					color: rgb(255, 255, 255);
					font-family: 阿里巴巴普惠体;
					font-size: 16px;
					font-weight: 400;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: left;
					margin-left: 5px;

				}
			}

			.info-content::v-deep {
				width: 100%;
				color: #FFFFFF !important;
				font-family: 'HarmonyOS Sans';
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				letter-spacing: 0px;
				text-align: left;
				margin-top: 10px;
				border-radius: 10px;
				background: #292C33;
				border: 1px solid #666873;
				padding: 15px 16px;
				margin-bottom: 20px;
				min-height: 200px;
				overflow: hidden;

				p {
					margin-bottom: 16px;
					color: #FFFFFF !important;
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 20px;
					letter-spacing: 0px;
					text-align: left;
				}

				img {
					width: 100%;
					height: auto;
					border-radius: 10px;
					margin: 16px 0;
				}
			}
		}

		// 评论区
		.exhibits-container {
			width: 100%;
			padding: 20px 20px 102px 20px;
			box-sizing: border-box;
			background-color: #23232d;

			.exhibits-title {
				margin-bottom: 10px;
				color: rgb(255, 255, 255);
				font-family: 阿里巴巴普惠体;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				letter-spacing: 0px;
				text-align: left;
				display: flex;
			}

			.exhibits-list {
				width: 100%;

				.exhibits-list-item {
					width: 100%;
					display: flex;
					align-items: flex-start;
					justify-content: flex-start;

					.exhibits-list-item-cover {
						width: 40px;
						height: 40px;
						border: 1px solid #ffffff;
						box-sizing: border-box;
						border-radius: 50%;
						margin-right: 10px;
						margin-top: 20px;
					}

					.exhibits-list-item-content {
						flex: 1;
						padding: 22px 0 20px 0;
						box-sizing: border-box;
						border-top: 1px solid rgba(196, 196, 196, 0.12);

						.exhibits-list-item-user {
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 8px;

							.exhibits-list-item-name {
								color: rgb(255, 255, 255);
								font-family: 阿里巴巴普惠体;
								font-size: 14px;
								font-weight: 400;
								line-height: 19px;
								letter-spacing: 0px;
								text-align: left;
								margin-bottom: 2px;
							}

							.exhibits-list-item-time {
								color: rgb(169, 169, 169);
								font-family: 阿里巴巴普惠体;
								font-size: 10px;
								font-weight: 400;
								line-height: 14px;
								letter-spacing: 0px;
								text-align: right;
							}

							.exhibits-list-item-comment-like {
								display: flex;
								align-items: center;
								color: rgb(255, 255, 255);
								font-family: 阿里巴巴普惠体;
								font-size: 14px;
								font-weight: 400;
								line-height: 19px;
								letter-spacing: 0px;
								text-align: right;

								.exhibits-list-item-like-icon {
									width: 15px;
									height: auto;
									margin-right: 2px;
								}
							}
						}

						.exhibits-list-item-comment {
							color: rgb(215, 216, 217);
							font-family: HarmonyOS Sans;
							font-size: 14px;
							font-weight: 400;
							line-height: 16px;
							letter-spacing: 0px;
							text-align: left;
						}
					}
				}

				.exhibits-list-item:nth-of-type(1) {
					.exhibits-list-item-content {
						border-top: 1px solid #23232d;
					}
				}

				.exhibits-comment-more {
					color: rgb(169, 169, 169);
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 400;
					line-height: 14px;
					letter-spacing: 0px;
					text-align: right;
					display: flex;
					align-items: center;
					justify-content: center;
					border-top: 1px solid rgba(196, 196, 196, 0.12);
					padding: 10px 0 30px 0;
					box-sizing: border-box;

					.comment-more-icon {
						width: 14px;
						height: auto;
						margin-left: 2px;
					}
				}
			}

			.fixed-bottom {
				position: fixed;
				left: 0;
				right: 0;
				bottom: 0;

				// 发表评论
				.exhibit-list-tips {
					width: 100%;
					// position: fixed;
					// left:0;
					// right:0;
					// bottom:72px;
					height: 30px;
					background: #1C1C1C;
					color: rgb(185, 150, 249);
					font-family: HarmonyOS Sans;
					font-size: 14px;
					font-weight: 400;
					line-height: 30px;
					letter-spacing: 0px;
					text-align: center;
				}

				.submit-comment {
					width: 100%;
					// position: fixed;
					// left:0;
					// right:0;
					// bottom:0;
					border-radius: 10px 10px 0px 0px;
					background: rgb(28, 28, 28);
					padding: 10px 20px 20px 20px;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.submit-comment-like {
						color: #D7D8D9;
						font-family: HarmonyOS Sans;
						font-size: 12px;
						font-weight: 400;
						line-height: 14px;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.submit-comment-like-icon {
							width: 28px;
							height: auto;
						}
					}

					.comment-like-active {
						color: rgb(255, 93, 80);
					}

					.submit-comment-form {
						flex: 1;
						height: 40px;
						margin: 0 10px 0 12px;
						border-radius: 60px;
						background: rgb(56, 58, 67);
						padding: 5px;
						box-sizing: border-box;
						display: flex;
						align-items: center;

						.submit-comment-headpic {
							width: 30px;
							height: 30px;
							margin-right: 10px;
							border-radius: 50%;
						}

						.submit-comment-input {}
					}

					.submit-comment-button {
						width: 78px;
						height: 40px;
						line-height: 40px;
						border-radius: 60px;
						background: linear-gradient(90.00deg, rgb(79, 197, 238), rgb(191, 147, 250) 100%);
						color: rgb(255, 255, 255);
						font-family: 阿里巴巴普惠体;
						font-size: 14px;
						font-weight: 400;
						text-align: center;
					}
				}

			}
		}
	}
</style>