/**
 * 页面生成图片
 * @Author: mosowe
 * @Date:2024-02-26 09:47:38
 * 获取截屏范围，获取截屏范围的根节点ref值，获取该ref到顶部的距离，使用uni.pageScrollTo(OBJECT)滚动到该节点，
 * 若页面"navigationStyle": "custom"，滚动距离需减去statusBarHeight高度，如此，需要截屏区域的顶部已经呈现出来，
 * 开始截屏
 * 判断是否一屏可以截取完，不能就滚动截取，每次滚动屏幕的80%
 * 滚动截取后，去掉头尾，拼接成一张图
 *
 */

let clipTop = '0px'; // 截屏开始顶部位置
let clipLeft = '0px'; // 截屏开始左侧位置，根据selector计算
let clipWidth = '0px'; // 截屏宽度，不能大于屏幕宽度，根据selector计算
let clipHeight = '0px'; // 截屏高度，等于安全高度的80%

let safeHeight = 0; // 页面安全区域高度
let scrollLen = 0; // 每次滚动安全高度
let statusBarHeight = 0; // 状态栏高度
let screenTop = 0; // 系统自带顶部栏高度，若为0，表示该页面"navigationStyle": "custom"了

let boxHeight = 0; // 页面高度

let times = 0; // 滚动次数
let lastLen = '0px'; // 最后一屏滚动距离
let images = []; // 截屏图片数组

let loading = false; // 处理中

// 获取需要滚动次数，向上取整
const scrollTimes = function(scrollLen, boxHeight) {
  return Math.ceil(boxHeight / scrollLen);
};

// 最后一次滚动距离，向上取整
const lastScrollLen = function(scrollLen, boxHeight) {
  return scrollTimes(scrollLen, boxHeight) > 0 ? Math.ceil(boxHeight % scrollLen) : 0;
};

// 截屏，核心代码：
const clipScreen = function(top, left, width, height) {
  return new Promise((resolve, reject) => {
    let pages = getCurrentPages();
    let page = pages[pages.length - 1];

    let bitmap = null;
    let currentWebview = page.$getAppWebview();

    // #ifdef APP-PLUS
    bitmap = new plus.nativeObj.Bitmap('screenPage');
    // 将webview内容绘制到Bitmap对象中
    currentWebview.draw(
      bitmap,
      function () {
        console.log('截屏绘制图片成功');
        const base64 = bitmap.toBase64Data().replace('data:image/null', 'data:image/png');
        resolve(base64);
        // bitmap.save(
        //   '_doc/a.jpg',
        //   {},
        //   function (i) {
        //     console.log('保存图片成功：' + JSON.stringify(i));
        //     uni.hideLoading();
        //     uni.saveImageToPhotosAlbum({
        //       filePath: i.target,
        //       success(res) {
        //         console.log(111111, res);
        //       }
        //     });
        //   },
        //   function (e) {
        //     console.log('保存图片失败：' + JSON.stringify(e));
        //   }
        // );
      },
      function () {
        resolve('');
      },
      {
        clip: { top, left, height, width } // 设置截屏区域
      }
    );
  });
};

// 准备裁剪，递归
const deepReadyClip = function(i) {
  if (i < times - 1) {
    clipScreen(clipTop, clipLeft, clipWidth, clipHeight).then((res) => {
      images.push(res);
      const index = ++i;
      uni.pageScrollTo({
        scrollTop: index * scrollLen,
        duration: 0,
        success() {
          setTimeout(() => {
            deepReadyClip(index);
          }, 100);
        }
      });
    });
  } else {
    clipScreen(clipTop, clipLeft, clipWidth, clipHeight).then((res) => {
      images.push(res);
      console.log(images);
      loading = false;
    });
  }
};

// 合并图片
const canvasDrawImages = function() {};

export default function(selector) {
  return new Promise((resolve, reject) => {
    loading = true;
    const windowInfo = uni.getWindowInfo();
    console.log(windowInfo);
    safeHeight = windowInfo.screenHeight; // 页面安全区域高度
    scrollLen = safeHeight - windowInfo.screenTop; // 每次滚动安全高度
    statusBarHeight = windowInfo.statusBarHeight; // 状态栏高度
    screenTop = windowInfo.screenTop; // 系统自带顶部栏高度，若为0，表示该页面"navigationStyle": "custom"了

    clipTop = (screenTop ? screenTop : statusBarHeight) + 'px'; // 截屏开始顶部位置
    clipHeight = safeHeight + 'px'; // 截屏高度

    const query = uni.createSelectorQuery().in(this);
    query
      .select(selector)
      .boundingClientRect((data) => {
        console.log('元素信息：', data);
        boxHeight = Math.floor(data.height);
        if (boxHeight < safeHeight) {
          clipHeight = boxHeight + 'px';
        }
        clipLeft = Math.floor(data.left) + 'px';
        clipWidth = Math.floor(data.width) + 'px';
        times = scrollTimes(scrollLen, boxHeight);
        lastLen = lastScrollLen(scrollLen, boxHeight) + 'px';
        uni.pageScrollTo({
          selector: selector,
          duration: 0,
          success() {
            setTimeout(() => {
              deepReadyClip(0);
              let t = setInterval(() => {
                if (!loading) {
                  clearInterval(t);
                  t = null;
                  resolve(images);
                }
              }, 1000);
            }, 100);
          }
        });
      })
      .exec();
  });
}
