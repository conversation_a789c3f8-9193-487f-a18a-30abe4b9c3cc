<template>
	<view class="appPage">
		<!-- <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#000"></uni-icons>
      </view>
      <view class="title">发布</view>
      <view
        class="publish-btn-top"
        @click="$u.debounce(momentId ? edit : send, 500)"
        >发布</view
      >
    </view> -->

		<view class="content-area">
			<!-- 图片上传区域 -->
			<view class="image-container">
				<view class="image-list">
					<view class="image-item" v-for="(src, index) in imgArr" :key="index">
						<image class="uploaded-img" :src="src" mode="aspectFill"></image>
						<view class="delete-btn" @click="delImg(index)">
							<uni-icons type="clear" size="18" color="#fff"></uni-icons>
						</view>
					</view>
					<view class="upload-box" v-if="imgArr.length < 9" @click="upload">
						<view class="plus-icon">
							<view class="plus-horizontal"></view>
							<view class="plus-vertical"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 标题输入区域 -->
			<input style="color: #333 !important; -webkit-text-fill-color: #333 !important" class="title-input"
				placeholder="添加标题" v-model="form.title" />

			<!-- 正文输入区域 -->
			<textarea class="content-input" v-model="form.introduction" placeholder="添加正文" :auto-height="true"
				maxlength="150" @input="introduction" />
			<view class="word-count">{{ currentLength }}/150</view>

			<!-- 位置标记和限权设置部分 -->
			<view class="footer-options">
				<view class="option-item" @click="goNav('/pages/searchMap/searchMap')">
					<image class="option-icon" src="../../static/images/topic/localtions.png"></image>
					<text class="option-text">标记地点</text>
					<view class="option-right">
						<text class="option-value">{{ address.name || "" }}</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>

				<view class="option-item" @click="openPop">
					<image class="option-icon" src="../../static/images/topic/firends.png"></image>
					<text class="option-text">限权设置</text>
					<view class="option-right">
						<text class="option-value">{{ seeData[role] || "" }}</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部工具栏 -->
		<view class="bottom-bar">
			<!-- <view class="draft-btn" @click="saveDraft">
        <image
          class="draft-icon"
          src="../../static/images/topic/caogao.png"
          mode="aspectFit"
        ></image>
        <text class="draft-text">存草稿</text>
      </view> -->
			<view class="publish-btn" @click="$u.debounce(momentId ? edit : send, 500)">发布</view>
		</view>

		<!-- 弹出层 - 权限设置 -->
		<uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="cPopup">
				<view class="head"></view>

				<!-- 公开可见 + 不给谁看 -->
				<view class="permission-group">
					<view class="permission-item" @click="setRole(5)">
						<view class="permission-text">公开可见</view>
						<view class="permission-check" v-if="role === 5">
							<uni-icons type="checkmarkempty" size="20" color="#f66"></uni-icons>
						</view>
					</view>

					<view class="divider"></view>

					<view class="permission-item" @click="setRole(4)">
						<view class="permission-text">不给谁看</view>
						<view class="userInfo" v-if="role === 4">{{ userListInfo }}</view>
						<view class="permission-right">
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 只给谁看 + 仅互关好友可见 -->
				<view class="permission-group">
					<view class="permission-item" @click="setRole(3)">
						<view class="permission-text">只给谁看</view>
						<view class="userInfo" v-if="role === 3">{{ userListInfo }}</view>
						<view class="permission-right">
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>

					<view class="divider"></view>

					<view class="permission-item" @click="setRole(1)">
						<view class="permission-text">仅互关好友可见</view>

						<view class="permission-check" v-if="role === 1">
							<uni-icons type="checkmarkempty" size="20" color="#f66"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 仅自己可见 -->
				<view class="permission-group single">
					<view class="permission-item" @click="setRole(2)">
						<view class="permission-text">仅自己可见</view>
						<view class="permission-check" v-if="role === 2">
							<uni-icons type="checkmarkempty" size="20" color="#f66"></uni-icons>
						</view>
					</view>
				</view>

				<view class="img36"></view>
			</view>
		</uni-popup>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				seeAuthority: false,
				imgArr: [],
				seeData: [
					"",
					"仅互关好友可见",
					"仅自己可见",
					"只给谁看",
					"不给谁看",
					"公开可见",
				],
				userListInfo: "",
				nickname: "",
				oldPages: "",
				address: "",
				momentId: "",
				userList: [],
				role: 0,
				form: {
					title: "",
					introduction: "",
				},
				currentLength: 0,
				hasDraft: false,
			};
		},
		mounted() {},
		onLoad(options) {
			//编辑帖子
			if (options.hasOwnProperty("momentId")) {
				this.momentId = options.momentId;
				this.getData(options.momentId);
			} else {
				// 检查是否有草稿
				this.checkDraft();
			}

			this.imgArr = options.imgArr ?
				JSON.parse(decodeURIComponent(options.imgArr)) :
				[];
			this.oldPages = options.pages;
			this.nickname = uni.getStorageSync("nickname");
		},
		onShow() {
			let that = this;
			uni.$on("address", function(data) {
				that.address = data;
			});
			uni.$on("infoArr", function(data) {
				console.log(data);
				that.userList = data.map((item) => {
					return item.ext.id;
				});
				const userListInfo = data ?
					data
					.map((item) => {
						return item.nickname;
					})
					.join("、") :
					"";
				this.$nextTick(() => {
					this.$set(that, "userListInfo", userListInfo);
				});
			});
		},
		methods: {
			checkDraft() {
				// 检查是否有草稿
				const draft = uni.getStorageSync("moment_draft");
				if (draft) {
					// this.hasDraft = true;
					// 显示提示是否加载草稿
					// uni.showModal({
					//   title: "提示",
					//   content: "检测到草稿箱内有未发布的内容，是否替换。",
					//   confirmText: "是",
					//   cancelText: "否",
					//   success: (res) => {
					//     if (res.confirm) {
					//       this.loadDraft(draft);
					//     } else {
					//       // 清除草稿
					//       uni.removeStorageSync("moment_draft");
					//     }
					//   },
					// });
				}
			},
			loadDraft(draft) {
				// 加载草稿数据
				if (draft) {
					this.form.title = draft.title || "";
					this.form.introduction = draft.content || "";
					this.currentLength = this.form.introduction.length;
					this.imgArr = draft.images || [];
					this.address = draft.address || {};
					this.role = draft.role || 0;
				}
			},
			getData(moment_id) {
				this.$http
					.get("/api/moment/detail", {
						moment_id,
					})
					.then((res) => {
						const {
							content,
							images,
							location,
							coordinate,
							role
						} = res.message;
						this.form.introduction = content;
						this.imgArr = images;
						this.address = {
							name: location,
							location: coordinate,
						};
						this.role = role;
					});
			},
			introduction(e) {
				this.currentLength = e.detail.value.length;
				if (this.currentLength > 150) {
					this.form.introduction = e.detail.value.slice(0, 150);
					this.currentLength = 150;
				}
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},
			setRole(ids) {
				this.role = ids;
				if ([3, 4].includes(ids)) {
					uni.navigateTo({
						url: "/pages/checkMember/checkMember?mode=3",
					});
				}
				this.$refs.popup.close();
			},
			cancel() {
				this.$refs.popup.close();
			},
			openPop() {
				this.$refs.popup.open();
			},
			goNav(url) {
				uni.navigateTo({
					url,
				});
			},
			goBack() {
				uni.navigateBack();
			},
			delImg(idx) {
				this.imgArr.splice(idx, 1);
			},
			upload() {
				uni.chooseImage({
					count: 9 - this.imgArr.length,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(
							tempFilePaths.map(async (item) => {
								const img = await this.$common.uploads(item, {
									type: 4,
								});
								this.imgArr.push(img);
							})
						);
					},
				});
			},
			saveDraft() {
				// 存草稿功能实现
				if (
					!this.form.title &&
					!this.form.introduction &&
					this.imgArr.length === 0
				) {
					this.toast("内容为空，无法保存");
					return;
				}

				// 创建草稿数据
				const draftData = {
					title: this.form.title,
					content: this.form.introduction,
					images: this.imgArr,
					address: this.address,
					role: this.role,
					time: new Date().getTime(),
				};

				// 存储草稿
				uni.setStorageSync("moment_draft", draftData);

				// 同时也保存到草稿列表中
				let drafts = uni.getStorageSync("moment_drafts") || [];
				drafts.unshift(draftData);
				uni.setStorageSync("moment_drafts", drafts);

				this.toast("已保存到草稿箱");
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			},
			edit() {
				console.log("---------edit-------------");
				const {
					pname,
					cityname,
					name,
					adname
				} = this.address;
				this.$http
					.post("/api/moment/edit", {
						moment_id: this.momentId,
						content: this.form.introduction,
						media: this.imgArr,
						location: (pname || "") + (cityname || "") + (adname || "") + name,
						coordinate: this.address.location,
						role: this.role,
						role_id: this.userList,
					})
					.then((res) => {
						// 清除草稿
						uni.removeStorageSync("moment_draft");

						this.toast("发布成功");
						setTimeout(() => {
							uni.navigateBack();
						}, 1000);
					});
			},
			send() {
				if (this.form.title === "") {
					this.toast("请输入标题");
					return;
				}
				if (this.form.introduction === "") {
					this.toast("请输入内容");
					return;
				}
				if (this.imgArr.length === 0) {
					this.toast("请上传图片");
					return;
				}
				const {
					pname,
					cityname,
					name,
					adname
				} = this.address;
				this.$http
					.post("/api/moment/post", {
						title: this.form.title,
						content: this.form.introduction,
						media: this.imgArr,
						location: pname + cityname + adname + name,
						coordinate: this.address.location,
						role: this.role,
						role_id: this.userList,
					})
					.then((res) => {
						// 清除草稿
						uni.removeStorageSync("moment_draft");

						this.imgArr = res.message;
						// #ifdef APP
						// let pages = getCurrentPages();
						// let currPage = pages[pages.length - 2];
						// currPage.$vm.myUpdate();
						// #endif
						this.toast("发布成功");
						setTimeout(() => {
							uni.navigateBack();
						}, 1000);
					});
			},
		},
		// 离开页面前检查是否需要保存草稿
		onUnload() {
			// 如果有内容但未保存为草稿，可以在这里自动保存
			if (
				(this.form.title || this.form.introduction || this.imgArr.length > 0) &&
				!this.momentId
			) {
				// 创建草稿数据
				const draftData = {
					title: this.form.title,
					content: this.form.introduction,
					images: this.imgArr,
					address: this.address,
					role: this.role,
					time: new Date().getTime(),
				};

				// 存储草稿
				uni.setStorageSync("moment_draft", draftData);
			}
		},
	};
</script>

<style lang="scss" scoped>
	.appPage {
		color: #333 !important;
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fafafa;
		padding-bottom: 120rpx;
	}

	/* 全局样式，确保所有输入控件文字为黑色 */
	::v-deep input {
		color: #333 !important;
		-webkit-text-fill-color: #333 !important;
		caret-color: #333;
		opacity: 1 !important;
	}

	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		position: relative;
	}

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
	}

	.title {
		font-size: 36rpx;
		font-weight: 500;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.publish-btn-top {
		font-size: 30rpx;
		color: #333;
	}

	.content-area {
		padding: 20rpx 30rpx;
	}

	.image-container {
		padding: 20rpx;
		padding-left: 0;
		border-radius: 12rpx;
		// margin-bottom: 30rpx;
	}

	.image-list {
		display: flex;
		flex-wrap: wrap;
	}

	.image-item {
		position: relative;
		width: 171rpx;
		height: 171rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
	}

	.image-item:nth-child(3n) {
		margin-right: 0;
	}

	.upload-box {
		width: 171rpx;
		height: 171rpx;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
		// border: 1px dashed #ddd;
	}

	.plus-icon {
		position: relative;
		width: 60rpx;
		height: 60rpx;
	}

	.plus-horizontal,
	.plus-vertical {
		position: absolute;
		background-color: #ccc;
	}

	.plus-horizontal {
		width: 60rpx;
		height: 4rpx;
		top: 50%;
		left: 0;
		transform: translateY(-50%);
	}

	.plus-vertical {
		width: 4rpx;
		height: 60rpx;
		left: 50%;
		top: 0;
		transform: translateX(-50%);
	}

	.uploaded-img {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.delete-btn {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 36rpx;
		height: 36rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.title-input {
		width: 100%;
		padding: 20rpx 0;
		color: #333 !important;
		-webkit-text-fill-color: #333 !important;
		font-size: 31rpx;
		// font-weight: 500;
		border-bottom: 1rpx solid rgba(51, 53, 59, 0.2);
	}

	.content-input {
		width: 100%;
		padding: 20rpx 0;
		font-size: 28rpx;
		min-height: 200rpx;
		color: #333 !important;
		-webkit-text-fill-color: #333 !important;
	}

	.word-count {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		margin: 10rpx 0 30rpx;
	}

	.footer-options {
		margin-top: 40rpx;
	}

	.option-item {
		display: flex;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.option-icon {
		width: 24rpx;
		height: 30rpx;
		margin-right: 20rpx;
	}

	.option-text {
		font-size: 28rpx;
		color: #333;
		flex: 1;
	}

	.option-right {
		display: flex;
		align-items: center;
	}

	.option-value {
		font-size: 26rpx;
		color: #999;
		margin-right: 10rpx;
		max-width: 400rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.bottom-bar {
		height: 191rpx;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx 30rpx 60rpx 30rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.draft-btn {
		display: flex;
		align-items: center;
		flex-direction: column;
		margin-right: 30rpx;
	}

	.draft-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.draft-text {
		font-size: 28rpx;
		color: #666;
		margin-top: 10rpx;
	}

	.publish-btn {
		flex: 1;
		height: 84rpx;
		line-height: 84rpx;
		background-color: #333;
		color: #fff;
		text-align: center;
		border-radius: 15rpx;
		font-size: 30rpx;
	}

	.cPopup {
		padding: 20rpx;
		background: #f5f5f5;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;

		.head {
			margin: 0 auto;
			width: 65rpx;
			height: 8rpx;
			background-color: #cbcbcb;
			border-radius: 10rpx;
			margin-bottom: 24rpx;
		}

		.permission-group {
			background-color: #fff;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			padding-right: 10rpx;

			&.single {
				.permission-item {
					border-bottom: none;
				}
			}
		}

		.permission-item {
			padding: 30rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #f5f5f5;
			padding-right: 0;

			.userInfo {
				width: 400rpx;
				text-align: right;
				overflow: hidden;
				text-overflow: ellipsis;
				transform: translateX(30px) !important;
				color: #f8334c !important;
			}

			&:last-child {
				border-bottom: none;
			}
		}

		.divider {
			height: 1rpx;
			background-color: #f5f5f5;
		}

		.permission-text {
			font-size: 30rpx;
			color: #333;
		}

		.permission-check {
			color: #f66;
		}

		.permission-right {
			color: #999;
		}

		.img36 {
			height: 36rpx;
		}
	}
</style>