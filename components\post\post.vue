<template>
	<view class="content">
		<view class="topic-con" v-for="(item,index) in list" :key="index" :class="{'topicBg':item.up==1 && showTop}">

			<view class="line" v-if="index!=0"></view>
			<view class="t_display" v-if="item.up==1 && showTop">
				<image class="img32 " :src="'../../static/images/vip/vipLogo'+item.user_info.vip_level+'.png'"
					mode="" />
				<view class="topText">
					置顶
				</view>
			</view>
			<view class="con-header">
				<!-- 用户头像 -->
				<view class="avatar"
					@click="goNavT('/pages/otherPage/otherPage?uuid='+item.user_info.uuid,item.user_info.uuid)">
					<image class="avatarimg" :src="item.user_info?item.user_info.avatar : ''" mode="aspectFill">
					</image>
				</view>
				<view class="message">
					<view class="nickname t_display" v-if="item.user_info">
						{{item.user_info.nickname}}
						<image class="img32 " :src="'../../static/images/vip/vipLogo'+item.user_info.vip_level+'.png'"
							v-if="showVipLogo" mode="" style="margin-left: 24rpx;" />
						<view class="auth" v-if="false" style="margin-left: 20rpx;">
							<image style="width: 32rpx;" src="@/static/images/authentication.png" mode="widthFix">
							</image>
						</view>
						<view class="auth-border" v-if="item.user_info.relation==2">
							朋友
						</view>
					</view>
					<view class="label" :style="'width:400px;padding-right:20px'">
						<view class=""
							style="max-width: 200rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
							{{item.timeline}}
						</view>

						<view class="address" v-if="item.location && !moreFlag"
							style="width: 350rpx;text-align: right;">
							来自于{{item.location}}
						</view>
						<view class="address" v-if="item.has_edit==1 && !moreFlag">
							已编辑
						</view>
					</view>
				</view>
				<view class="operate" v-if="moreFlag && item.user_info.relation == 4 ">
					<image class="operate-img" @click="goMore(item,index)" src="@/static/images/moreIcon.png"
						mode="widthFix">
					</image>
					<view class="status" style="" v-if="showAuthority">
						{{["","好友可见","自己可见","部分可见","不给谁看","公开"][item.role]}}
					</view>
				</view>
				<view class="follow" v-if="followFlag &&  ![3,4].includes( item.user_info.relation)"
					@click="follow(item)">
					{{["关注","已关注","互相关注"][item.user_info.relation] }}
				</view>
			</view>
			<view class="con-body">
				<view class="con" @click="goNav('/pages/mainText/mainText?momentId='+item.moment_id)">
					{{item.content+''}}
				</view>
				<view class="itempic" v-if="item.images">
					<uni-row :gutter="24" name="1">
						<uni-col :span="24" v-if="item.images.length == 1" style="margin-bottom: 10rpx;">
							<image class="img686" :src="item.images" mode="aspectFill"
								@click="previewFlag?goNav('/pages/mainText/mainText?momentId='+item.moment_id):preview(item.images,0)">
							</image>
						</uni-col>
						<uni-col :span="12" v-if="1 < item.images.length && item.images.length < 5"
							v-for="(item2, key) in item.images" :key="key">
							<image :src="item2" mode="aspectFill" class="img335" style="margin: 10rpx 0;"
								@click="previewFlag?goNav('/pages/mainText/mainText?momentId='+item.moment_id):preview(item.images,index)">
							</image>
						</uni-col>
						<uni-col :span="8" v-if="4 < item.images.length "
							v-for="(item2, key) in item.images.length>9?item.images.slice(0,9): item.images"
							:key="key">
							<image :src="item2" style="width: 224rpx;height: 224rpx;margin-top: 10rpx;"
								mode="aspectFill"
								@click="previewFlag?goNav('/pages/mainText/mainText?momentId='+item.moment_id):preview(item.images,index)">
							</image>
						</uni-col>
					</uni-row>
				</view>
				<view v-else>
					<!-- 无图片 -->
				</view>
				<!-- @click="$common.openMap(item.coordinate,item.location)" -->
				<view class="addresspic" @click="goAddress({coordinate:item.coordinate,location:item.location})"
					v-if="item.coordinate && item.location">
					<image class="map-position" src="@/static/images/locationFollow.png" mode="">
					</image>
				</view>
			</view>
			<!-- //动态里的relation字段关系是1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
			<view class="t_display" style="justify-content: space-between;" v-if="footerFlag  ">
				<image src="../../static/images/disSee.png" class="img24" mode="" v-if="flag" @click="setEye">
				</image>
				<view class="t_display" v-else-if="!flag && item.user_info.relation==4">
					<image src="../../static/images/eye.png" class="img24" mode="" @click="setEye"></image>
					<view class="seeSty">
						{{item.view_total}}次浏览
					</view>
				</view>
				<view class="" v-else></view>
				<view class="con-footer">
					<view class="t_center" @click="setLike(item.moment_id, item.is_like,index)">
						<image src="@/static/images/like.png" mode="widthFix" v-if="!item.is_like"></image>
						<image src="@/static/images/like2.png" mode="widthFix" v-else></image>
						<view class="">
							{{item.like}}
						</view>
					</view>
					<view class="t_center" @click="goNav('/pages/mainText/mainText?momentId='+item.moment_id)">
						<image src="@/static/images/pingluna.png" mode="widthFix"></image>
						<view class="">
							{{item.comment_total}}
						</view>
					</view>

					<view class="t_center" @click="share(item)">
						<image src="@/static/images/share.png" mode="widthFix"></image>
						<view class="">
							{{item.share_total}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			uuid: {
				type: String,
				default: ""
			},
			goFLag: {
				type: Boolean,
				default: false,
			},
			showAuthority: {
				type: Boolean,
				default: true,
			},
			showTop: {
				type: Boolean,
				default: true,
			},
			showVipLogo: {
				type: Boolean,
				default: true,
			},
			moreFlag: {
				type: Boolean,
				default: true,
			},
			footerFlag: {
				type: Boolean,
				default: true,
			},
			followFlag: {
				type: Boolean,
				default: false,
			},
			previewFlag: {
				type: Boolean,
				default: false,
			},
			list: {
				type: Array,
				default: []
			},
			timeFormat: {
				type: String,
				default: "yyyy/M/D"
			}
		},
		data() {
			return {
				likeFlag: false,
				flag: false,
			};
		},
		methods: {
			goAddress(item) {
				this.$emit('goAddress', item)
			},
			follow(item) {
				this.$emit('follow', {
					...item
				})
			},
			preview(urls, current) {
				console.log("urls" + current + ':', urls);
				uni.previewImage({
					current,
					urls,
					// longPressActions: {
					// 	itemList: ['发送给朋友', '保存图片', '收藏'],
					// 	success: function(data) {
					// 		console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
					// 	},
					// 	fail: function(err) {}
					// }
				});
			},
			setLike(momentId, isLike, index) {
				this.$u.debounce(() => {
					this.$emit('setLike', {
						index,
						momentId,
						isLike
					})
				}, 200)
			},
			share(item) {
				this.$emit('share', item)
			},
			goNav(url) {
				this.navigateTo({
					url
				})
			},
			goNavT(url, uuid = "") {
				if (this.goFLag || uuid == this.uuid) return
				this.navigateTo({
					url
				})
			},
			setEye() {
				this.flag = !this.flag
			},
			goMore(item, index) {
				this.$emit('more', {
					...item,
					index
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.status {
		margin-top: 10rpx;
		width: 100rpx;
		font-size: 24rpx;
	}

	.topText {
		font-weight: 500;
		font-size: 24rpx;
		line-height: 34rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		background: linear-gradient(to right, #4BC6ED 50%, #BC93F2 80%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		margin: 30rpx 0 30rpx 12rpx;
	}

	.topBg {
		width: 750rpx;
		height: 100rpx;
	}

	.line {
		width: 750rpx;
		height: 12rpx;
		background: #323232;
		margin-bottom: 14rpx;
	}

	.follow {
		width: 144rpx;
		height: 152rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 134rpx 134rpx 134rpx 134rpx;
		text-align: center;
		line-height: 152rpx;
		font-size: 24rpx;
	}

	.seeSty {
		margin-left: 10rpx;
		font-size: 24rpx;
		font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		font-weight: 400;
		color: #FFFFFF;
	}

	// .swiperC {
	// 	height: 100vh;
	// }

	.topic-con {
		background: #191C26;
		margin-bottom: 8rpx;

		background-size: 100%;
		background-repeat: no-repeat;
	}

	.topicBg {
		background: url('../../static/images/my/postBg.png');
		background-size: 100%;
		background-repeat: no-repeat;
	}

	.topic-con>view {
		padding-left: 32rpx;
		padding-right: 32rpx;
	}

	.con-header {
		width: 100%;
		height: 96rpx;
		display: flex;

		border-bottom: 1rpx solid #191C26;
	}

	.avatar {
		width: 75rpx;
		height: 75rpx;
		margin-top: 5rpx;
	}

	.avatarimg {
		width: 75rpx;
		height: 75rpx;
		border-radius: 50%;
	}

	.message {
		width: 500rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		margin-left: 12rpx;
		padding-left: 6rpx;

	}

	.nickname {
		font-size: 34rpx;
		line-height: 40rpx;
		display: flex;
		align-items: center;
	}

	.auth {
		transform: translateY(5rpx);
	}

	.auth-border {
		padding: 5px 8px;
		cursor: pointer;
		position: relative;
		font-size: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		// padding: 2rpx 12rpx;
		&::before {
			/* 1 */
			display: block;
			content: '';
			border-radius: 6px;
			border: 1px solid transparent;
			background: linear-gradient(90deg, #4BC6ED, #BC93F2) border-box;
			/* 2 */
			-webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
			/* 3 */
			-webkit-mask-composite: xor;
			/* 4 */
			mask-composite: exclude;
			position: absolute;
			width: 72rpx;
			height: 32rpx;
		}
	}


	.label {
		display: flex;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.75);
		line-height: 40rpx;

		margin-top: 5rpx;
	}

	.address {
		display: inline-block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-left: 16rpx;
		font-size: 26rpx;
	}

	.operate {
		width: 140rpx;
		height: 100%;
		text-align: right;
		transform: translateY(-3rpx);
		display: flex;
		flex-direction: column;
		align-items: end;
	}

	.operate-img {
		width: 14rpx;

		// margin-top: 23rpx;
	}

	.con-body {
		/* width: 100%; */
		padding-top: 20rpx;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.75);
		line-height: 34rpx;
	}

	.con {
		word-wrap: break-word;
		margin-bottom: 16rpx;
	}

	.itempic image {
		// width: 100%;
		border-radius: 14rpx;
	}

	.addresspic {
		width: 100%;
		height: 76rpx;
		margin-top: 8rpx;
		border-radius: 14rpx;
		background-image: url("../../static/images/maps.png");
		background-size: cover;
		text-align: center;
	}

	.map-position {
		width: 42rpx;
		height: 42rpx;
		margin-top: 16rpx;
	}

	.con-footer {
		height: 128rpx;
		padding-top: 30rpx;
		display: flex;
		justify-content: flex-end;
	}

	.con-footer>view {
		width: 38rpx;
		margin-left: 52rpx;
		font-size: 24rpx;
	}

	.con-footer>view>image {
		width: 38rpx;
	}
</style>