<template>
	<view class="merchant-authentication-page">
		<view class="merchant-authentication-form">
			<!-- 上传营业执照 -->
			<view class="merchant-authentication-form-item upload-container">
				<view class="form-title"><text>*</text>上传营业执照</view>
				<view class="gridBox">
					<view class="" v-for="(src,index) in imgArr" :key="index">
						<image class="box" @click="preview([src])" :src="src" mode="">
						</image>
						<image class="close" @click="delImg(index)" v-if="is_business!=2"
							src="../../static/images/close.png" mode="">
						</image>
					</view>
					<view class="no-data-box" v-if="imgArr.length!==1" @click="upload">
						<image src="../../static/images/photo-icon.png" class="box-icon">
						</image>
					</view>
				</view>
				<view class="tips">仅支持jpg、png、jpeg格式图片，图片大小不能大于10M，请将资质上传至正确的资质类型内，否则可能会导致审核不通过</view>
			</view>
			<view class="merchant-authentication-form-item">
							<view class="form-style">
								<view class="form-title"><text>*</text>选择营业执照类型</view>
								<view class="form-style-tab">
									<view class="form-style-tab-item" :class="{ 'form-style-tab-item-active': index === 0 }"  v-for="(item,index) in styleItems">{{item}}</view>
								</view>
							</view>
							<uni-forms ref="baseForm" :modelValue="formData" label-position="top">
								<uni-forms-item label="执照编码" required>
									<input class="form-item-input" v-model="formData.licenseCode" placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入执照编码" />
								</uni-forms-item>
								<uni-forms-item label="企业名称" required>
									<input class="form-item-input" v-model="formData.enterpriseName" placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入企业名称" />
								</uni-forms-item>
								<uni-forms-item label="法人姓名" required>
									<input class="form-item-input" v-model="formData.legalPersonName" placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入法人姓名" />
								</uni-forms-item>
								<uni-forms-item label="联系人姓名" required>
									<input class="form-item-input" v-model="formData.name" placeholder-style="fontSize:14px;color:#9BA0AE" placeholder="请输入联系人姓名" />
								</uni-forms-item>
								<uni-forms-item label="联系电话" required>
									<input class="form-item-input" v-model="formData.phone" maxlength="11" type="number" placeholder="请输入联系电话"
										placeholder-style="fontSize:14px;color:#9BA0AE"
									/>
								</uni-forms-item>
								<button class="merchant-authentication-form-button" @click="submit">确认并提交</button>
							</uni-forms>
						</view>
		</view>
		
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		apiWalletRedpacketGrant
	} from '@/api/common.js'
	export default {
		data() {
			return {
				old_business: "",
				deleteFlag: true,
				imgArr: [],
				is_business: "",
				duration_index: null,
				radius_index: null,
				type: 'noCondition',
				btnTxt: '',
				btnStatus: ["认证", "返回", "返回", "修改信息"],
				isStore: false,
				formData: {
					licenseCode: "",
					enterpriseName: '',
					legalPersonName:"",
					name:"",
					phone: '',
				},
				current: 0,
				styleItems: ['我是企业', '我是个体工商'],
			}
		},
		watch: {
			// '$store.state.hbLocation'(nVal) {
			// 	this.formData.locationName = nVal ? nVal.locationName : ''
			// 	this.formData.coordinate = nVal ? nVal.location : ''
			// }
		},
		async onLoad(option) {
			const {
				mode
			} = option
			if (mode === 'noRegister') {

				this.toast('未认证商家');
			}
			await this.$http.get('/api/user/info').then(async (res) => {

				this.is_business = res.message.user_info.is_business
				if ([2, 3].includes(res.message.user_info.is_business)) { //1申请中 2正常 3申请驳回 0未申请
					this.$http.get('/api/user/business/get').then(res => {
						console.log("-=-=-=-=-=-=-=-=-=-=-=-=-=-=", res.message);
						const {
							phone,
							location,
							license,
							remark,
							coordinate
						} = res.message
						this.remark = remark;
						this.formData.phone = phone;
						this.formData.locationName = location;
						this.formData.coordinate = coordinate;
						this.imgArr = license;
					})
				}
			})
		},
		methods: {
			back() {
				// uni.navigateTo({
				//         url: '/pages/shop/shop'
				//     })
			},
			onClickItem(e) {
							console.log(e);
							this.current = e.currentIndex
						},
			goNavLoaction(item) {
				console.log("----------------------", item);
				let pages = getCurrentPages();
				let currPage = pages[pages.length - 2]; //当前页面
				const locat = item.coordinate.split(",")
				// #ifdef APP
				// currPage.$vm.myUpdate({
				currPage.$vm.current = 0
				currPage.$vm.poiMapAddMarker({
					lnglat: {
						lng: locat[0],
						lat: locat[1]
					},
					location: item.locationName,
					addressComponent: {
						city: "商家位置",
						district: item.locationName,
						street: "",
						streetNumber: ""
					}
				});
				// #endif
				uni.navigateBack()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			preview(urls) {
				uni.previewImage({
					urls,
				});
			},
			upload() {
				if (this.is_business == 2) return
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							this.imgArr = [img]
						}, ))
					}
				});
			},
			delImg(idx) {
				if (this.is_business == 2) return
				this.imgArr.splice(idx, 1)
			},
			bindSend() {
				let flag = Object.values(this.formData).every(item => {
					return !!item
				})
				switch (this.is_business) {
					case 0:
						if (!flag) return this.toast('请填写完整商家信息')
						if (this.imgArr.length !== 1) return this.toast('请上传营业执照照片')
						this.$http.post('/api/user/business/apply', {
							"describe": "",
							"phone": this.formData.phone,
							"location": this.formData.locationName,
							"coordinate": this.formData.coordinate,
							"license": this.imgArr
						}).then(res => {
							uni.navigateBack()
						})
						break;
					case 1:
						uni.navigateBack()
						break;
					case 2:
						uni.navigateBack()
						break;
					case 3:
						this.is_business = 0
						break;
					default:
						break;
				}
			},
			bindPosition(formData) {
				if (this.is_business == 2) {
					this.goNavLoaction(formData)
					return
				}
				uni.navigateTo({
					url: '/pages/index/hbPosition',
				})
			},

		}
	}
</script>


<style scoped lang="scss">
	.merchant-authentication-page{
		background-color: #FFFFFF;
		.merchant-authentication-form{
			width: 95%;
			margin-left: 2.5%;
			padding:24px 0;
			box-sizing: border-box;
			.form-title{
				font-family: HarmonyOS Sans;
				font-size: 12px;
				font-weight: 500;
				line-height: 14px;
				color:#646B7C;
				margin-bottom:16px;
				text{
					color:#F35323;
				}
			}
			.merchant-authentication-form-item{
				margin-bottom:24px;
			}
			.upload-container{
				.gridBox{
					margin-bottom: 16px;
					.no-data-box{
						width: 80px;
						height: 80px;
						border-radius: 12px;
						background: rgb(248, 248, 248);
						display: flex;
						align-items: center;
						justify-content: center;
						.box-icon{
							width:30px;
							height: 30px;
						}
					}
				}
				.tips{
					color: rgb(155, 160, 174);
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 400;
					line-height: 18px;
				}
				
			}
			.form-style{
				width:100%;
				margin-bottom: 22px;
				.form-style-tab {
					display: flex;
					align-items: center;
					justify-content: space-between;
				  
				}
				.form-style-tab-item{
					width: 100%; /* 或者具体的宽度 */
					height: auto; /* 或者具体的高度 */
					width:48%;
					box-sizing: border-box;
					border: 1px solid rgb(155, 160, 174);
					border-radius: 4px;
					padding:12px 0;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					justify-content: center;
					color: rgb(155, 160, 174);
					font-family: 阿里巴巴普惠体;
					font-size: 14px;
					font-weight: 400;
					line-height: 19px;
				}
				.form-style-tab-item-active{
					border: 1px solid rgb(93, 150, 255);
					background: rgba(93, 150, 255, 0.07);
					color:#5D96FF;
				}
			}
			/deep/.uni-forms-item{
				.uni-forms-item__label{
					width:100% !important;
					font-family: HarmonyOS Sans;
					font-size: 12px;
					font-weight: 500;
					line-height: 14px;
				}
				.uni-forms-item__content{
					box-sizing: border-box;
					border: 1px solid rgb(240, 242, 247);
					border-radius: 4px;
					padding: 13px 16px;
					box-sizing: border-box;
					.uni-input-input{
						color: rgb(33, 33, 33) !important;
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
					}
					
				}
			}
			.merchant-authentication-form-button{
				border-radius: 30px;
				background: rgb(193, 193, 193);
				color: rgb(255, 255, 255);
				font-family: 阿里巴巴普惠体;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				padding:15px 0;
				box-sizing: border-box;
			}
		}
		
	}
	
</style>