<template>
	<uni-popup ref="popup" type="bottom" backgroundColor="transparent" :safe-area="false">
		<view class="house-content" v-if="JSON.stringify(info)!='{}'">
			<view class="icon-title"></view>
			<view class="title">不是你的家？</view>
			<view class="address">{{info.location}}</view>
			<view class="del-icon">
				<image class="icon1" src="../../../static/map/icon1.png"></image>
				<image class="del" src="../../../static/map/del.png"></image>
			</view>
			<image class="up" src="../../../static/map/up.png" mode="widthFix"></image>
			<view class="btns">
				<view class="type-cancelbtn" @click.stop="cancel">取消</view>
				<view class="type-addbtn" @click.stop="submit">删除</view>
			</view>
			<view class="img24" />
			<u-toast ref='notify' />
		</view>
	</uni-popup>
</template>

<script>
	import {
		apiAddressDel
	} from '@/api/common.js'
	export default {
		data() {
			return {
				active: 1,
				info: {
					a: 1
				}
			}
		},
		mounted() {},
		methods: {
			close() {
				this.$refs.popup.close('bottom')
			},
			open(option) {
				console.log(option, 'option');
				const obj = option.id.split(':')
				this.info = {
					id: obj[0],
					location: obj[1]
				}
				this.$refs.popup.open('bottom')
			},
			submit() {
				console.log(this.info, 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
				apiAddressDel({
					id: this.info.id
				}).then(res => {
					if (res.code == 200) {
						this.$refs.popup.close('bottom')
						this.$emit('delAddress')

					}
				})
			},
			cancel() {
				this.$refs.popup.close('bottom')
				this.$emit('closeAddress')
			},
			changeType(str) {
				this.active = str
			}
		}
	}
</script>

<style lang="scss" scoped>
	.house-content {
		width: 100vw;
		height: 600rpx;
		background-color: #000;
		border-radius: 50rpx 50rpx 0 0;
		box-sizing: border-box;
		color: #fff;
		padding: 50rpx 40rpx 0;
		position: relative;

		&::after {
			width: 100vw;
			height: 120rpx;
			content: '';
			background-color: #000;
			position: absolute;
			left: 0;
			bottom: -90rpx;
		}

		.del-icon {
			text-align: center;
			position: relative;
			padding-top: 40rpx;

			.icon1 {
				width: 150rpx;
				height: 150rpx;
				position: absolute;
				left: 50%;
				top: 57%;
				transform: translate(-50%, -50%);
			}

			.del {
				width: 120rpx;
				height: 120rpx;
			}
		}

		.up {
			width: 50rpx;
			margin-left: 50%;
			transform: translateX(-50%);
			margin-top: 20rpx;
		}

		.btns {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.type-cancelbtn {
				width: 300rpx;
				height: 80rpx;
				border-radius: 20rpx;
				background: #1f2a42;
				color: #fff;
				font-size: 32rpx;
				letter-spacing: 5rpx;
				font-weight: bold;
				margin-top: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;

			}

			.type-addbtn {
				width: 300rpx;
				height: 80rpx;
				border-radius: 20rpx;
				background: linear-gradient(145deg, #4BEAE8 0%, #C095F7 100%);
				color: #fff;
				font-size: 32rpx;
				letter-spacing: 5rpx;
				font-weight: bold;
				margin-top: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;

			}
		}

		.icon-title {
			width: 80rpx;
			height: 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			position: absolute;
			left: 50%;
			top: 20rpx;
			transform: translateX(-50%);
			// margin: 0 auto 20rpx;


		}


	}
</style>