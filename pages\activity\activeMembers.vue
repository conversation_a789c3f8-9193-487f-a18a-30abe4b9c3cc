<template>
	<view class="appPage">
		<scroll-view scroll-y="true"  :refresher-triggered="sendFlag" @refresherrefresh="refresherrefresh" @scrolltolower="scrolltolower"
					:refresher-threshold="150"	refresher-background="grey41" refresher-enabled style="width: 100%; height: calc(100% - 20rpx)">
			<view class="list" v-for="item, index in memberArr">
				<image :src="item.userinfo.avatar" mode="aspectFill" @click="toUser(item.userinfo.uuid)"></image>
				<span>
					{{item.userinfo.nickname}}
				</span>
				<span v-if="item.role === 1"
					style="width: 108rpx; height: 52rpx;  background: rgba(212, 243, 255, 0.83); font-size: 28rpx; color: rgb(30, 126, 161); line-height: 52rpx; text-align: center; border-radius: 50rpx; position: absolute; right: 40rpx;">发起人</span>
					
				<view v-if="item.role === 2" style="border-radius: 16rpx 0px 0px 16rpx;background: rgb(0, 199, 119); height: 48rpx; width: 12rpx; position: absolute; right: 0;"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				memberArr: [],
				total: 0,
				page: 1,
				size: 10,
				sendFlag: true,
				uuid: undefined
			}
		},
		onLoad(option) {
			this.uuid = option.uuid
			this.getData()
		},
		methods: {
			refresherrefresh() {
				this.page = 1;
				this.getData()
				this.sendFlag = true
				setTimeout(() => {
					this.sendFlag = false
				}, 500)
			},
			scrolltolower() {
				if (this.total != 0) {
					this.page++;
					this.getData()
				}
			},
			getData() {
				this.$http.get('/activity/member-list', {
					page: this.page,
					size: this.size,
					uuid: this.uuid
				}).then(res => {
					console.log(res)
					this.memberArr = this.page == 1 ? [...res.message] : [...this.memberArr, ...res.message]
					this.total = res.message.length
				})
			},
			toUser(uuid) {
				uni.navigateTo({
					url: '/pages/otherPage/otherPage?uuid=' + uuid
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		background: rgb(41, 44, 51);
		padding-top: 40rpx;
		height: 100vh;
	}

	.list {
		width: 100%;
		margin-bottom: 60rpx;
		padding-left: 40rpx;
		font-size: 36rpx;
		font-weight: 400;
		line-height: 80rpx;
		display: flex;
		align-items: center;

		image {
			width: 80rpx;
			height: 80rpx;
			margin-right: 40rpx;
			border: 1px solid white;
			border-radius: 50%;
		}
	}
</style>