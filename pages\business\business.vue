<template>
	<view>
		<view class="hb">
			<block v-if="[0,2].includes(is_business)">
				<image v-if="is_business==2" class="successImg" src="../../static/images/shenqingchenggong.png" mode="">
				</image>
				<view class="card" style="max-height: 360rpx;">
					<uni-forms :modelValue="formData" label-width="0px">
						<view class="title">商家信息</view>
						<uni-forms-item label="" name="phone" style="padding-bottom: 0;">
							<view class="label">商家联系方式</view>
							<input v-model="formData.phone" maxlength="11" type="number" placeholder="填写手机号码"
								placeholder-style="fontSize:28rpx;color:rgba(255,255,255,0.74)"
								:disabled="is_business==2" />
						</uni-forms-item>
						<view class="line"></view>
						<uni-forms-item label="" name="locationName">
							<view class="label">商家位置</view>
							<view class="value " @click="bindPosition(formData)">
								<text class="text-overflows"
									style="margin: 0;width: 80%;text-align: right;">{{formData.locationName||"选择位置" }}</text>
								<image src="@/static/map/righticon.png" class="weizhi"></image>
							</view>
						</uni-forms-item>
					</uni-forms>
				</view>
				<view class="card" style="margin-top: 32rpx;">
					<view class="title">店铺信息</view>
					<view class="tips">营业执照照片</view>
					<view class="gridBox">
						<view class="" v-for="(src,index) in imgArr" :key="index">
							<image class="box" @click="preview([src])" :src="src" mode="">
							</image>
							<image class="close" @click="delImg(index)" v-if="is_business!=2"
								src="../../static/images/close.png" mode="">
							</image>
						</view>
						<image v-if="imgArr.length!==1" @click="upload" src="../../static/images/addBig.png" class="box"
							mode="aspectFit">
						</image>
					</view>
					<view style="font-size: 24rpx;">上传营业执照</view>
				</view>
			</block>
			<block v-else-if="[1].includes(is_business)">
				<image class="applicationSuccessful" src="../../static/images/shenqingzhong.png" mode=""></image>
				<view class="tips2">
					预计审核时间：一个工作日，请耐心等待
				</view>
			</block>
			<block v-else-if="[3].includes(is_business)">
				<image class="applicationError" src="../../static/images/shenqingbohui.png" mode=""></image>
				<view class="tips2">
					拒绝原因：一个工作日，请耐心等待
				</view>
			</block>
			<view class="btn" @click="bindSend">{{btnStatus[is_business]}}</view>
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	import {
		apiWalletRedpacketGrant
	} from '@/api/common.js'
	export default {
		data() {
			return {
				old_business: "",
				deleteFlag: true,
				imgArr: [],
				is_business: "",
				duration_index: null,
				radius_index: null,
				type: 'noCondition',
				btnTxt: '',
				btnStatus: ["认证", "返回", "返回", "修改信息"],
				isStore: false,
				formData: {
					coordinate: "",
					locationName: '',
					phone: '',
				},
				hbChooseLocation: uni.getStorageSync('hbChooseLocation') ? JSON.parse(uni.getStorageSync(
					'hbChooseLocation')) : {}
			}
		},
		watch: {
			'$store.state.hbLocation'(nVal) {
				this.formData.locationName = nVal ? nVal.locationName : ''
				this.formData.coordinate = nVal ? nVal.location : ''
			}
		},
		onLoad() {
			console.log("---------onLoad---------");
		},
		async onShow() {
			// const {
			// 	mode
			// } = option
			// if (mode === 'noRegister') {
			// 	this.toast('未认证商家');
			// }
			console.log("---------未认证商家---------");
			await this.$http.get('/api/user/info').then((res) => {
				this.is_business = res.message.user_info.is_business
				console.log("==========this.is_business =======", this.is_business);
				if ([2, 3].includes(res.message.user_info.is_business)) { //1申请中 2正常 3申请驳回 0未申请
					this.$http.get('/api/user/business/get').then(res => {
						const {
							phone,
							location,
							license,
							remark,
							coordinate
						} = res.message
						this.remark = remark;
						this.formData.phone = phone;
						this.formData.locationName = location;
						this.formData.coordinate = coordinate;
						this.imgArr = license;
					})
				}
			})
		},
		methods: {
			goNavLoaction(item) {
				console.log("----------------------", item);
				let pages = getCurrentPages();
				let currPage = pages[pages.length - 2]; //当前页面
				const locat = item.coordinate.split(",")
				// #ifdef APP
				// currPage.$vm.myUpdate({
				currPage.$vm.current = 0
				currPage.$vm.poiMapAddMarker({
					lnglat: {
						lng: locat[0],
						lat: locat[1]
					},
					location: item.locationName,
					addressComponent: {
						city: "商家位置",
						district: item.locationName,
						street: "",
						streetNumber: ""
					}
				});
				// #endif
				uni.navigateBack()
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			preview(urls) {
				uni.previewImage({
					urls,
				});
			},
			upload() {
				if (this.is_business == 2) return
				uni.chooseImage({
					count: 1,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							this.imgArr = [img]
						}, ))
					}
				});
			},
			delImg(idx) {
				if (this.is_business == 2) return
				this.imgArr.splice(idx, 1)
			},
			bindSend() {
				let flag = Object.values(this.formData).every(item => {
					return !!item
				})
				switch (this.is_business) {
					case 0:
						if (!flag) return this.toast('请填写完整商家信息')
						if (this.imgArr.length !== 1) return this.toast('请上传营业执照照片')
						this.$http.post('/api/user/business/apply', {
							"describe": "",
							"phone": this.formData.phone,
							"location": this.formData.locationName,
							"coordinate": this.formData.coordinate,
							"license": this.imgArr
						}).then(res => {
							uni.navigateBack()
						})
						break;
					case 1:
						uni.navigateBack()
						break;
					case 2:
						uni.navigateBack()
						break;
					case 3:
						this.is_business = 0
						break;
					default:
						break;
				}
			},
			bindPosition(formData) {
				if (this.is_business == 2) {
					this.goNavLoaction(formData)
					return
				}
				uni.navigateTo({
					url: '/pages/index/hbPosition',
				})
			},

		}
	}
</script>


<style scoped lang="scss">
	.text-overflows {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.applicationSuccessful {
		width: 100%;
		height: 462rpx;
	}

	.applicationError {
		width: 100%;
		height: 535rpx;
	}

	.successImg {
		height: 240rpx;
		width: 100%;
		margin-bottom: 42rpx;
	}

	.tips2 {
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.74);
		line-height: 41rpx;
		margin-top: 32rpx;
		text-align: center;
	}

	.card {
		background: #2F3341;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		border: 1rpx solid;
		border-color: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
		padding: 32rpx;

		.tips {
			font-size: 28rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
		}
	}

	.line {
		border-bottom: 1rpx solid #939393;
	}

	.hb {
		width: 750rpx;
		height: 80vh;
		background: #191C26;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		opacity: 1;
		border-image: linear-gradient(180deg, rgba(254.00000005960464, 254.00000005960464, 254.00000005960464, 1), rgba(150.0000062584877, 143.00000667572021, 159.0000057220459, 1)) 1 1;
		padding: 63rpx 32rpx;

		::v-deep .uni-forms {
			width: 100%;
			height: 47vh;
			overflow-y: auto;
			padding-bottom: 50rpx;
			// box-sizing: border-box;

			.uni-forms-item {
				// width: 686rpx;
				padding: 32rpx 0;
				box-sizing: border-box;
				background: #2F3341;
				opacity: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-bottom: 32rpx;

				.uni-forms-item__label {
					height: 41rpx;
					font-size: 28rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 41rpx;


				}

				.uni-forms-item__content {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.label {
						display: flex;
						align-items: center;

						.pinImg {
							width: 36rpx;
							height: 36rpx;
							margin-right: 12rpx;
						}
					}

					.value {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						font-size: 28rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						color: rgba(255, 255, 255, 0.74);
						line-height: 41rpx;

						.rightIcon {
							width: 16rpx;
							height: 32rpx;
							margin-left: 24rpx;
						}

						text {
							margin: 0 24rpx;
						}

						.weizhi {
							margin-left: 26rpx;
							width: 28rpx;
							height: 28rpx;
						}
					}

					input {
						flex: 1;
						text-align: right;
					}

					text {
						margin: 0 24rpx;
					}
				}
			}
		}

		.gridBox {
			display: grid;
			grid-template-columns: 1fr 1fr 1fr;
			grid-gap: 18rpx;
			margin: 32rpx 0;
		}

		.close {
			width: 32rpx;
			height: 32rpx;
			position: absolute;
			transform: translate(-40rpx, 5rpx);
		}

		.box {
			width: 154rpx;
			height: 154rpx;
			border-radius: 14rpx;
		}

		.btn {
			width: 686rpx;
			height: 94rpx;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			border-radius: 140rpx 140rpx 140rpx 140rpx;
			opacity: 1;
			margin: auto;
			text-align: center;
			line-height: 94rpx;
			position: absolute;
			bottom: 100rpx;
		}

		.btnConfirm {
			width: 686px;
			height: 94px;
			background: #AAAAAA;
			border-radius: 140px;
		}

		.title {
			height: 75rpx;
			font-size: 32rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 700;
			color: #FFFFFF;
			line-height: 75rpx;
		}

		.types {

			margin-bottom: 32rpx;
			display: flex;
			align-items: center;

			.types-title {
				display: inline-block;
				height: 46rpx;
				font-size: 32rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				line-height: 46rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.bottomIcon {
				width: 32rpx;
				height: 16rpx;
				margin-left: 24rpx;
			}
		}
	}
</style>