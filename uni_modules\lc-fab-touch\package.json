{"id": "lc-fab-touch", "displayName": "lc-fab-touch 可拖拽悬浮按钮", "version": "1.0.0", "description": "可拖拽悬浮按钮，点击可展开一个图标按钮菜单。", "keywords": ["可拖拽", "拖拽按钮", "按钮", "悬浮按钮", "fab"], "repository": "", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "u", "app-nvue": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "y"}, "快应用": {"华为": "u", "联盟": "u"}}}}}