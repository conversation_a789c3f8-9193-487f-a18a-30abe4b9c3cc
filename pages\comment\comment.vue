<template>
	<scroll-view scroll-y="true" class="scroll-view " @scrolltolower="scrolltolower">
		<view class="li" v-for="item in commentList" :key="item.comment_id">
			<view class="user">
				<image class="avatar" :src="item.user_info.avatar" mode="aspectFill"
					@click="goNav('/pages/otherPage/otherPage?uuid='+item.user_info.uuid)"></image>
				<!-- //动态里的relation字段关系是1是我关注的  2是互相关注的 3关注我的 4我自己 0陌生人 -->
				<view class="info">
					<view class="name">{{item.user_info.nickname}}
						<view class="tag" v-if="item.user_info.relation==2">朋友</view>
					</view>
					<view class="time">{{item.timeline}}</view>
				</view>
				<view class="btn" @click="handleComment(item)">回复</view>
			</view>
			<view class="content">
				<view class="comment-text">
					{{item.content}}
				</view>
				<view class="comment-content" @click="goNavMain(item)">
					<view class="fa-user">
						<text>{{userName}}</text>：
						{{item.moment.content}}
					</view>
					<uni-row :gutter="24" name="1">
						<uni-col :span="24" v-if="item.moment_image.length == 1" style="margin-bottom: 10rpx;">
							<image class="img686" :src="item.moment_image" mode="aspectFill"
								style="border-radius: 16rpx;">
							</image>
						</uni-col>
						<uni-col :span="12" v-if="1 < item.moment_image.length && item.moment_image.length < 5"
							v-for="(item2, index) in item.moment_image" :key="index">
							<image :src="item2" mode="aspectFill" class="img335"
								style="margin: 10rpx 0;border-radius: 16rpx;">
							</image>
						</uni-col>
						<uni-col :span="8" v-if="4 < item.moment_image.length && item.moment_image.length < 10"
							v-for="(item2, index) in item.moment_image" :key="index">
							<image :src="item2"
								style="width: 224rpx;height: 224rpx;margin-top: 10rpx;border-radius: 16rpx;"
								mode="aspectFill">
							</image>
						</uni-col>
					</uni-row>
					<view class="fa-imgs">
						<!-- <image v-for="(img,index) in item.moment_image" :key="index" :src="img" mode="aspectFill">
						</image> -->
					</view>
				</view>
			</view>
		</view>
		<u-toast ref='notify' />
	</scroll-view>
</template>

<script>
	import {
		timeFmt
	} from '@/utils/common.js'
	import {
		apiNotifyComment,
		apiMomentReply
	} from '@/api/common.js'
	export default {

		data() {
			return {
				userName: uni.getStorageSync('nickname'),
				reviewArr: [],
				commentList: [],
				size: 10
			}
		},
		onLoad(options) {
			this.init()
		},
		methods: {
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			goNavMain(item) {
				uni.navigateTo({
					url: '/pages/mainText/mainText?momentId=' + item.moment.moment_id
				})
			},
			init() {
				apiNotifyComment({
					size: this.size,
					maxId: this.commentList.length ? this.commentList[this.commentList.length - 1].search_id : ''
				}).then(res => {
					if (res.code == 200) {
						this.commentList = this.commentList.concat(...res.message)
					}
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			handleComment(item) {
				uni.navigateTo({
					url: '/pages/mainText/mainText?momentId=' + item.moment.moment_id
				})
				// uni.showModal({
				// 	editable: true,
				// 	title: '回复',
				// 	confirmText: '发送',
				// 	success: function(res) {
				// 		if (res.confirm) {
				// 			console.log('用户点击确定', res);
				// 			if (res.content.trim() == '') return false
				// 			apiMomentReply({
				// 				"commentId": item.comment_id,
				// 				"momentId": item.moment.moment_id,
				// 				"content": res.content,
				// 				"to_uid": item.user_info.uid
				// 			}).then(res => {
				// 				if (res.code == 200) {
				// 					uni.showToast({
				// 						title: '操作成功',
				// 						duration: 2000,
				// 						icon: 'none'
				// 					})
				// 				}
				// 			})
				// 		} else if (res.cancel) {
				// 			console.log('用户点击取消');
				// 		}
				// 	}
				// })
			},
			scrolltolower() {
				this.init()
			},
			gettimeFmt(str) {
				console.log(str, 'aaaa');
				return timeFmt(str, 'yyyy-MM-DD hh:mm:ss')
			}

		}
	}
</script>

<style scoped lang="scss">
	.scroll-view {
		width: 100%;
		height: 100vh;

		.li {
			padding-bottom: 34rpx;

			.user {
				display: flex;
				align-items: center;
				padding: 24rpx 32rpx;
				box-sizing: border-box;

				.avatar {
					width: 108rpx;
					height: 108rpx;
					border-radius: 280rpx 280rpx 280rpx 280rpx;
					opacity: 1;
					margin-right: 32rpx;
				}

				.info {
					flex: 1;

					.name {
						font-size: 34rpx;
						font-family: Source Han Sans-Medium, Source Han Sans;
						font-weight: 500;
						color: #FFFFFF;
						line-height: 40rpx;
						display: flex;
						align-items: center;
						margin-bottom: 8rpx;

						.tag {
							height: 32rpx;
							opacity: 0.82;
							border: 1rpx solid;
							border-image: linear-gradient(93deg, rgba(75.00000312924385, 198.00000339746475, 237.0000010728836, 1), rgba(188.0000039935112, 147.00000643730164, 242.00000077486038, 1)) 1 1;
							border-radius: 20rpx;
							font-size: 24rpx;
							color: #fff;
							display: flex;
							align-items: center;
							justify-content: center;
							padding: 0 10rpx;
							margin-left: 12rpx;
						}
					}
				}

				.btn {
					width: 144rpx;
					height: 52rpx;
					background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
					border-radius: 134rpx 134rpx 134rpx 134rpx;
					opacity: 1;
					font-size: 24rpx;
					color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.content {

				border-top: 1px solid rgba(255, 255, 255, .3);

				.comment-text {
					padding: 24rpx 32rpx;
					box-sizing: border-box;
					font-family: Source Han Sans-Regular, Source Han Sans;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 40rpx;
				}

				.comment-content {
					width: 750rpx;
					background: #22252F;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					opacity: 1;
					padding: 32rpx 32rpx 0;
					box-sizing: border-box;

					.fa-user {
						font-size: 28rpx;
						font-family: Source Han Sans-Regular, Source Han Sans;
						font-weight: 400;
						line-height: 40rpx;
						color: #fff;
						margin-bottom: 16rpx;
						word-break: break-all;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;

						text {
							color: #4BC6ED;
						}
					}

					.fa-imgs {
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;

						image {
							width: 335rpx;
							height: 335rpx;
							border-radius: 14rpx 14rpx 14rpx 14rpx;
							opacity: 1;
							margin-bottom: 16rpx;
						}
					}
				}
			}
		}
	}
</style>