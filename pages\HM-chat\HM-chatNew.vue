<template>
	<view>
		<u-navbar
		  :customBack="goBack"
		  :background="{ backgroundColor: '#191C26' }"
		  :border-bottom="false"
		  height="50"
		  title-color="#fff"
		  :title="title"
		  back-icon-color="#fff"
		>
		  <view class="navbar-right" slot="right">
		    <image
		      class="more"
		      src="/static/images/team_setting.png"
		      alt=""
			  mode="aspectFit"
		      @click="goActivity"
		    />
		  </view>
		</u-navbar>
		<view class="content" @touchstart="hideDrawer">
			<scroll-view class="msg-list" :style="{ top: (50 + statusBarHeight) + 'px'}" scroll-y="true" :scroll-with-animation="scrollAnimation"
				:scroll-top="scrollTop" :scroll-into-view="scrollToView" @scrolltoupper="loadHistory"
				upper-threshold="50">
				<!-- 加载历史数据waitingUI -->
				<view class="loading" v-if="loading">
					<view class="spinner">
						<view class="rect1"></view>
						<view class="rect2"></view>
						<view class="rect3"></view>
						<view class="rect4"></view>
						<view class="rect5"></view>
					</view>
				</view>
<!-- 				<view>{{myuid}}</view> -->
				<view class="row" v-for="(row,index) in chatRoomData" :key="index" :id="'msg'+row.msg.idServer">
					<!-- 系统消息 -->
					<block v-if="row&&row.type=='system'">
						<view class="system">
							<!-- 文字消息 -->
							<view v-if="row.msg.type=='text'" class="text">
								{{row.msg.content.text}}
							</view>
							<!-- 领取红包消息 -->
							<view v-if="row.msg.type=='redEnvelope'" class="red-envelope">
								<image src="/static/img/red-envelope-chat.png"></image>
								{{row.msg.content.text}}
							</view>
						</view>
					</block>
					<!-- 用户消息 -->
					<block v-if="row&&row.type=='user' ">
						<!-- 自己发出的消息 -->
						<view class="my" v-if="row.msg.userinfo.uid==myuid">
							<!-- 左-消息 -->
							<view class="left">
								<!-- 文字消息 -->
								<view v-if="row.msg.type=='text'" class="bubble">
									<rich-text :nodes="row.msg.content.text"></rich-text>
								</view>
								<!-- 自定义消息 -->
								<view v-if="row.msg.type=='custom' &&row.msg.content.text.type == 'dongtai'"
									class="bubble custom" @click="goNav(row.msg.content.text.url)">
									分享
									<view class="cus-avatar">
										<view class="cus-l">
											<image :src="row.msg.content.text.avatar" style="border-radius: 50%;"
												mode="aspectFill">
											</image>
											{{row.msg.content.text.name}}
										</view>
										<view>
											{{row.msg.content.text.tip}}
										</view>
									</view>
									<div style="width: 100%;margin-top: 5px;font-size: 22rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
										v-if="row.msg.content.text.content">
										{{row.msg.content.text.content}}
									</div>
									<view class="cus-content">
										<view class="cus-content-imgs">
											<image v-for="(col,col_index) in row.msg.content.text.img"
												:key="col_index"
												@tap="showPicCustom(row.msg.content.text.img,col_index)" :src="col"
												mode="aspectFill"
												:style="{'width': `${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`,'height':`${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`}">
											</image>
								
											<image v-if="row.msg.content.text.video"
												:src="row.msg.content.text.video" mode="aspectFill">
											</image>
											<image v-if="row.msg.content.text.audio"
												:src="row.msg.content.text.audio" mode="aspectFill">
											</image>
										</view>
										<text v-if="row.msg.content.text.text">{{row.msg.content.text.text}}</text>
									</view>
									<view class="cus-location" v-if="row.msg.content.text.location">
										<image src="@/static/images/address.png" class="addressimg"></image>
										{{row.msg.content.text.location}}
									</view>
								</view>
								<!-- 自定义位置消息 -->
								<view v-if="row.msg.type=='custom'&&row.msg.content.text.type == 'location'"
									@click="openAmap(row)" class="bubble custom-location">
								
									<view class="custom-location-content">
										<view class="mapimg">
											<image class="mapavatar" :src="row.msg.content.text.img"
												mode="aspectFill">
											</image>
										</view>
										<view class="info">
											<view class="name">
												{{row.msg.content.text.name}}
											</view>
											<view class="location">
												{{row.msg.content.text.location}}
											</view>
										</view>
									</view>
								</view>
								<!-- 语言消息 -->
								<view v-if="row.msg.type=='voice'" class="bubble voice" @tap="playVoice(row.msg)"
									:class="playMsgid == row.msg.id?'play':''">
									<view class="length">{{row.msg.content.length}}</view>
									<view class="icon my-voice"></view>
								</view>
								<!-- 图片消息 -->
								<view v-if="row.msg.type=='image'" class="bubble img" @tap="showPic(row.msg)">
									<image :src="row.msg.content.url" mode="aspectFill"
										:style="{'width': row.msg.content.w+'px','height': row.msg.content.h+'px'}">
									</image>
								</view>
								<!-- 红包 -->
								<view v-if="row.msg.type=='redEnvelope'" class="bubble red-envelope"
									@tap="openRedEnvelope(row.msg,index)">
									<image src="/static/img/red-envelope.png"></image>
									<view class="tis">
										<!-- 点击开红包 -->
									</view>
									<view class="blessing">
										{{row.msg.content.blessing}}
									</view>
								</view>

							</view>
							<!-- 右-头像 -->
							<view class="right">
								<image :src="row.msg.userinfo.face" mode="aspectFill"
									style="border-radius: 50%;"></image>
							</view>
						</view>
						<!-- 别人发出的消息 -->
						<view class="other" v-if="row.msg.userinfo.uid!=myuid">
							<!-- 左-头像 -->
							<view class="left">
								<image :src="row.msg.userinfo.face" mode="aspectFill"
									style="border-radius: 50%;"></image>
							</view>
							<!-- 右-用户名称-时间-消息 -->
							<view class="right">
								<view class="username">
									<view class="name">{{row.msg.userinfo.username}}</view>
									<view class="time">{{row.msg.time}}</view>
								</view>
								<!-- 文字消息 -->
								<view v-if="row.msg.type=='text'" class="bubble">
									<rich-text :nodes="row.msg.content.text"></rich-text>
								</view>
								<!-- 自定义消息 -->
								<view v-if="row.msg.type=='custom' &&row.msg.content.text.type == 'dongtai'"
									class="bubble custom" @click="goNav(row.msg.content.text.url)">
									分享
									<view class="cus-avatar">
										<view class="cus-l">
											<image :src="row.msg.content.text.avatar" style="border-radius: 50%;"
												mode="aspectFill">
											</image>
											{{row.msg.content.text.name}}
										</view>
										<view>{{row.msg.content.text.tip}}</view>
									</view>
									<div style="width: 100%;margin-top: 5px;font-size: 22rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
										v-if="row.msg.content.text.content">
										{{row.msg.content.text.content}}
									</div>
									<view class="cus-content">
										<view class="cus-content-imgs">
											<image v-for="(col,col_index) in row.msg.content.text.img"
												:key="col_index"
												@tap="showPicCustom(row.msg.content.text.img,col_index)" :src="col"
												mode="aspectFill"
												:style="{'width': `${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`,'height':`${(430-(row.msg.content.text.img.length-1)*20)/row.msg.content.text.img.length}rpx`}">
											</image>
											<image v-if="row.msg.content.text.video"
												:src="row.msg.content.text.video" mode="aspectFill">
											</image>
											<image v-if="row.msg.content.text.audio"
												:src="row.msg.content.text.audio" mode="aspectFill">
											</image>
										</view>
										<text v-if="row.msg.content.text.text">{{row.msg.content.text.text}}</text>
									</view>
									<view class="cus-location" v-if="row.msg.content.text.location">
										<image src="@/static/images/address.png" class="addressimg"></image>
										{{row.msg.content.text.location}}
									</view>
								</view>
								<!-- 自定义位置消息 -->
								<view v-if="row.msg.type=='custom'&&row.msg.content.text.type == 'location'"
									class="bubble custom-location" @click="openAmap(row)">
									<view class="custom-location-content">
										<view class="mapimg">
											<image class="mapavatar" :src="row.msg.content.text.img"
												mode="aspectFill">
											</image>
										</view>
										<view class="info">
											<view class="name">
												{{row.msg.content.text.name}}
											</view>
											<view class="location">
												{{row.msg.content.text.location}}
											</view>
										</view>
									</view>
								</view>
								<!-- 语音消息 -->
								<view v-if="row.msg.type=='voice'" class="bubble voice" @tap="playVoice(row.msg)"
									:class="playMsgid == row.msg.id?'play':''">
									<view class="icon other-voice"></view>
									<view class="length">{{12}}</view>
								</view>
								<!-- 图片消息 -->
								<view v-if="row.msg.type=='image'" class="bubble img" @tap="showPic(row.msg)">
									<image :src="row.msg.content.url"
										:style="{'width': row.msg.content.w+'px','height': row.msg.content.h+'px'}">
									</image>
								</view>
								<!-- 红包 -->
								<view v-if="row.msg.type=='redEnvelope'" class="bubble red-envelope"
									@tap="openRedEnvelope(row.msg,index)">
									<image src="/static/img/red-envelope.png"></image>
									<view class="tis">
										<!-- 点击开红包 -->
									</view>
									<view class="blessing">
										{{row.msg.content.blessing}}
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
				<view style="height: 100rpx" id="sscroll-bottom"></view>
			</scroll-view>
			<view v-if="showAtMe" @click="scrollToAtMsg" class="at_wrapper">
				<image class="up" src="/static/images/ic_up.png"></image>
				有人@我
			</view>
		</view>
		<!-- 抽屉栏 -->
		<view class="popup-layer" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<!-- 表情 -->
			<swiper class="emoji-swiper" :class="{hidden:hideEmoji}" indicator-dots="true" duration="150">
				<swiper-item v-for="(page,pid) in emojiList" :key="pid">
					<view v-for="(em,eid) in page" :key="eid" @tap="addEmoji(em)">
						<image mode="widthFix" :src="'/static/img/emoji/'+em.url"></image>
					</view>
				</swiper-item>
			</swiper>
			<!-- 更多功能 相册-拍照-红包 -->
			<view class="more-layer" :class="{hidden:hideMore}">
				<view class="list">
					<view class="box" @tap="chooseImage">
						<view class="icon tupian2"></view>
					</view>
					<view class="box" @tap="camera">
						<view class="icon paizhao"></view>
					</view>
					<view class="box" @tap="handRedEnvelopes">
						<view class="icon hongbao"></view>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部输入栏 -->
		<view class="input-box" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<view class="t_display">
				<view class="textbox">
					<view class="voice-mode" :class="[isVoice?'':'hidden',recording?'recording':'']"
						@touchstart="voiceBegin" @touchmove.stop.prevent="voiceIng" @touchend="voiceEnd"
						@touchcancel="voiceCancel">{{voiceTis}}</view>
					<view class="text-mode" :class="isVoice?'hidden':''">
						<view class="box">
							<textarea auto-height="true" placeholder="请输入~" confirm-hold v-model="textMsg" @focus="textareaFocus" confirm-type="send" @confirm="sendText" cursor-spacing="20" />
						</view>
						<!-- <view class="em" @tap="chooseEmoji">
							<view class="icon biaoqing"></view>
						</view> -->
					</view>
				</view>
			<!-- 	<view class="send" style="margin: 10rpx 0 0 10rpx;" :class="isVoice?'hidden':''" @tap="sendText">
					<view class="btn">发送</view>
				</view> -->
			</view>
			<view class="t_display btnItem" style="margin-top: 15rpx;">
				<!-- H5下不能录音，输入栏布局改动一下 -->
				<!--#ifndef H5 -->
				<!-- <view class="voice"> -->
					<!-- <view class="icon" :class="isVoice?'jianpan':'yuyin'" @tap="switchVoice"></view> -->
				<!-- 	<image v-if="!isVoice" style="width: 56rpx;height: 56rpx;" src="../../static/images/voice.png"
						mode="" @tap="switchVoice"></image>
					<image v-else style="width: 56rpx;height: 56rpx;" src="../../static/images/keword.png" mode=""
						@tap="switchVoice"></image> -->
				<!-- #endif -->
				<image src="../../static/images/emjoy.png" @tap="chooseEmoji" style="width: 56rpx;height: 56rpx;"
					mode=""></image>
				<image src="../../static/images/photo.png" @tap="chooseImage" style="width: 56rpx;height: 56rpx;"
					mode=""></image>
				<image v-if="roomType === 'team'" src="../../static/images/at.png" @tap="chooseAt" style="width: 56rpx;height: 56rpx;"
					mode="" />
				<image src="../../static/images/location.png" style="width: 56rpx;height: 56rpx;" mode=""
					@click="sendGeoLocationMsg"></image>
			</view>
			<!-- #ifndef H5 -->
			<!-- <view class="more" @tap="showMore">
				<view class="icon add"></view>
			</view> -->
			<!-- #endif -->

		</view>
		<!-- 录音UI效果 -->
		<view class="record" :class="recording?'':'hidden'">
			<view class="ing" :class="willStop?'hidden':''">
				<view class="icon luyin2"></view>
			</view>
			<view class="cancel" :class="willStop?'':'hidden'">
				<view class="icon chehui"></view>
			</view>
			<view class="tis" :class="willStop?'change':''">{{recordTis}}</view>
		</view>
		<!-- 红包弹窗 -->
		<view class="windows" :class="windowsState">
			<!-- 遮罩层 -->
			<view class="mask" @touchmove.stop.prevent="discard" @tap="closeRedEnvelope"></view>
			<view class="layer" @touchmove.stop.prevent="discard">
				<view class="open-redenvelope">
					<view class="top">
						<view class="close-btn">
							<view class="icon close" @tap="closeRedEnvelope"></view>
						</view>
						<image src="/static/img/im/face/face_1.jpg"></image>
					</view>
					<view class="from">来自{{redenvelopeData.from}}</view>
					<view class="blessing">{{redenvelopeData.blessing}}</view>
					<view class="money">{{redenvelopeData.money}}</view>
					<view class="showDetails" @tap="toDetails(redenvelopeData.rid)">
						查看领取详情 <view class="icon to"></view>
					</view>
				</view>
			</view>
		</view>
		<u-toast ref='notify' />
		<MapC @sendCustomLocaltion='sendCustomLocaltion' ref="mapc" />
		<!-- 选择群成员 -->
		<u-popup mode="bottom" length="90%" v-model="showChooseMember">
			<view class="chooose-bg">
				<view class="btn_wrapper">
					<view class="btn" @click="chooseMemberCancel">取消</view>
					<view class="btn" @click="chooseMemberConfirm">确认</view>
				</view>
				<scroll-view scroll-y="true">
					<view class="member-item" @click="checkAll()">
						<view class="checkbox"></view>
						<view class="member-name" style="margin-left: 32rpx;">所有人</view>
					</view>
					<view v-for="item in teamMembers" :key="item.account" class="member-item" @click="checkMember(item)">
						<view class="checkbox" :class="chooseAtAccounts.indexOf(item.account) > -1 ? 'checked': ''"></view>
						<image class="avatar" :src="item.avatar" style="border-radius: 50%;"
																		mode="aspectFill">
																	</image>
						<view class="member-name">{{ item.nick }}</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>
<script>
	import emojiData from './emojiData.js'
	import onlineEmojiData from './onlineEmojiData.js'
	import {
		apiLocationMe
	} from '@/api/common.js'
	import MapC from '@/components/map/popup.vue'
	import moment from 'moment'
	export default {
		components: {
			MapC
		},
		data() {
			return {
				//文字消息
				textMsg: '',
				//消息列表
				isHistoryLoading: false,
				scrollAnimation: false,
				scrollTop: 0,
				scrollToView: '',
				msgList: [],
				msgImgList: [],
				myuid: uni.getStorageSync('im').account,
				//录音相关参数
				// #ifndef H5
				//H5不能录音
				RECORDER: uni.getRecorderManager(),
				// #endif
				isVoice: false,
				voiceTis: '按住 说话',
				recordTis: "手指上滑 取消发送",
				recording: false,
				willStop: false,
				initPoint: {
					identifier: 0,
					Y: 0
				},
				recordTimer: null,
				recordLength: 0,

				//播放语音相关参数
				AUDIO: uni.createInnerAudioContext(),
				playMsgid: null,
				VoiceTimer: null,
				// 抽屉参数
				popupLayerClass: '',
				// more参数
				hideMore: true,
				//表情定义
				hideEmoji: true,
				emojiList: emojiData, //表情图片图床名称 ，由于我上传的第三方图床名称会有改变，所以有此数据来做对应，您实际应用中应该不需要
				onlineEmoji: onlineEmojiData,
				//红包相关参数
				windowsState: '',
				redenvelopeData: {
					rid: null, //红包ID
					from: null,
					face: null,
					blessing: null,
					money: null
				},
				userItem: {},
				loading: false,
				limit: 40,
				roomType: 'p2p',
				showChooseMember: false,
				teamMembers: [],
				chooseAtAccounts: [],
				activityId: '',
				title: '',
				statusBarHeight: 0,
				showAtMe: false,
				atMessageID: '',
			};
		},
		computed: {
			chatRoomData() {
				const now = new Date().getTime()
				const nowDay = moment(now).format('yyyy-MM-DD')
				let roomData = this.$store.state.Yxim_info.curChatRoom
				// 过滤掉通知类消息
				roomData = roomData.filter(item => item.type !== 'notification')
				console.log('ChatRoomDataBefore', roomData)
				const data = roomData.map((item, index) => {
					if (!item || item == undefined) return
					const cur = item
					if (item.type == 'text') {
						const ext1 = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext1.appAvatar
							// AT 显示
						if (ext1 && ext1.atList && ext1.atList.indexOf(this.myuid) > -1 && item.from !== this.myuid && !this.atMessageID) {
							if (item.status === 'unread') {
								this.atMessageID = item.idServer
								this.showAtMe = true
								// 创建监听器
								this.$nextTick(() => {
									this.initIntersectionObserver()
								})
							}
						}
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext1.noSend,
								id: item.sessionId,
								type: item.type,
								idServer: item.idServer,
								time: this.$common.diaplayTime2(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									text: item.body
								}
							}
						}
					} else if (item.type == 'image') {
						const ext = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext.appAvatar
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext.noSend,
								idServer: item.idServer,
								id: item.sessionId,
								type: item.type,
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:SS'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									w: item.attach.w,
									h: item.attach.h
								}
							}
						}
					} else if (item.type == 'geo') {
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								idServer: item.idServer,
								id: item.sessionId,
								type: item.type,
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:SS'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: "/static/img/face.jpg"
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									w: item.attach.w,
									h: item.attach.h
								}
							}
						}
					} else if (item.type == 'audio') {
						const ext2 = item.ext ? JSON.parse(item.ext) : {}
						const {
							length
						} = ext2
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext2.appAvatar
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext2.noSend,
								id: item.sessionId,
								idServer: item.idServer,
								type: 'voice',
								time: this.$common.timeFmt(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								states: item.status,
								readTime: item.time,
								content: {
									url: item.attach.url,
									size: item.attach.size,
									length: length ? length : 10
								}
							}
						}
					} else if (item.type == 'custom') {
						const ext3 = item.ext ? JSON.parse(item.ext) : {}
						const appAvatar = item.avatarList && item.avatarList.length > 0 ? item.avatarList[0].url :
							ext3.appAvatar
						const {
							length
						} = ext3
						item = {
							type: "user",
							userUpdateTime: item.userUpdateTime,
							msg: {
								noSend: ext3.noSend,
								id: item.sessionId,
								type: item.type,
								idServer: item.idServer,
								time: this.$common.diaplayTime2(item.time, 'yyyy-MM-DD hh:mm:ss'),
								userinfo: {
									uid: item.from,
									username: this.$store.state.Yxim_info.remarkList[item.from] || item
										.fromNick,
									face: appAvatar ? appAvatar : '/static/img/face.jpg'
								},
								readTime: item.time,
								states: item.status,
								content: {
									text: JSON.parse(item.ext)
								}
							}
						}
					}
					const msgtime = moment(cur.time).format('yyyy-MM-DD')
			
					
					const prev_msgtime = index > 0 ? moment(roomData[index - 1].time).format('yyyy-MM-DD') : 0
					if (index > 0) {
						if (prev_msgtime == msgtime) {
							item.msg.time = ''
						} else {
							item.msg.time = moment(cur.time).format('yyyy-MM-DD')
						}
						if (now - new Date(cur.time).getTime() < 60 * 60 * 1000) {
							item.msg.time = this.$common.diaplayTime2(cur.time)
						}
					}
				
					return item
				})
				
				this.scrollToView = ''
				setTimeout(() => {
					this.scrollToView = 'sscroll-bottom'
				}, 500)
				console.log('ChatRootData----', data)
				// 重置未读
				return data
			}
		},
		onLoad(option) {
			this.userItem = JSON.parse(decodeURIComponent(option.userItem))
			console.log('chat-HM==========params');
			console.log(this.userItem);
			console.log('chat-HM==========params');
			this.roomType = this.userItem.uid.split('-')[0]
			this.$store.commit('SET_p2pSessionId', this.userItem.uid)
			// 获取当前p2p用户得历史会话
			this.$Yxim.msgLog.getHistoryMsgs({
				scene: this.roomType,
				to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account,
				limit: this.limit,
				asc: false
				// lastMsgId:''
			}).then(res => {
				console.log(res, this.$store.state.Yxim_info.curChatRoom);
				this.$store.state.Yxim_info.curChatRoom.splice(0, 0, ...res.reverse())
			})
			// 设置标题
			this.title = this.userItem.nuck
			this.$store.commit('SET_SESSION_ResetSessionUnreadCount', this.userItem.uid)
			this.getMsgList();
			//语音自然播放结束
			this.AUDIO.onEnded((res) => {
				this.playMsgid = null;
			});
			// #ifndef H5
			//录音开始事件
			this.RECORDER.onStart((e) => {
				this.recordBegin(e);
			})
			//录音结束事件
			this.RECORDER.onStop((e) => {
				this.recordEnd(e);
			})
			// #endif
			this.$Yxim.team.getTeamInfo({ teamId: this.userItem.teamId}).then(res => {
				if (res.serverExt) {
					let activityInfo = JSON.parse(res.serverExt)
					this.activityId = activityInfo.uuid
				}
			})
			// 获取状态栏高度
			const res = uni.getSystemInfoSync();
			this.statusBarHeight = res.statusBarHeight;
			// 获取群成员数量
			this.getTeamMembers()
	
		},
		onUnload() {
			console.log(111111111111111);
			this.$store.commit('SET_Clear_CurChatRoom')
		},
		beforeDestroy() {
			if (this.observer) {
			  this.observer.disconnect();
			}
		},
		onShow() {
			// this.scrollTop = 9999999;

			//模板借由本地缓存实现发红包效果，实际应用中请不要使用此方法。
			//
			uni.getStorage({
				key: 'redEnvelopeData',
				success: (res) => {
					console.log(res.data);
					let nowDate = new Date();
					let lastid = this.msgList[this.msgList.length - 1].msg.id;
					lastid++;
					let row = {
						type: "user",
						msg: {
							id: lastid,
							type: "redEnvelope",
							time: nowDate.getHours() + ":" + nowDate.getMinutes(),
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								blessing: res.data.blessing,
								rid: Math.floor(Math.random() * 1000 + 1),
								isReceived: false
							}
						}
					};
					this.screenMsg(row);
					uni.removeStorage({
						key: 'redEnvelopeData'
					});
				}
			});
		},
		onReady() {
			// this.initIntersectionObserver()
		},
		methods: {
			async getTeamMembers() {
				// 获取群聊成员
				const users = await this.$store.state._Yxim.team.getTeamMembers({
					teamId: this.userItem.teamId
				})
				console.log('users', users)
				const userRes = await this.$store.state._Yxim.user
					.getUsersNameCardFromServer({
						"accounts": users.map(per => per.account)
					})
					console.log('userRes', userRes)
				this.teamMembers = userRes
			},
			initIntersectionObserver() {
			  // 创建观察器实例
			  this.observer = uni.createIntersectionObserver(this);
			  
			  console.log('ATMessageID', `#msg${this.atMessageID}`)
			  
			  // 相对于scroll-view视口进行观察
			  this.observer.relativeTo('.msg-list', {
				top: 0,    // 上边距
				bottom: 0,  // 下边距
				left: 0,   // 左边距
				right: 0    // 右边距
			  }).observe(`#msg${this.atMessageID}`, (res) => {
				if (res.intersectionRatio > 0) {
				  console.log('目标View进入可视范围', res);
				  // 执行你的业务逻辑
				  if (!this.atMessageID || !this.showAtMe) return
				  this.showAtMe = false
				} else {
					console.log('目标View离开可视范围', res);
				}
			  });
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			async sendAudioMsg(file) {
				const result = await this.$Yxim.msg.sendAudioMsg({
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.uid, //接收人
					filePath: file.url,
					ext: file.length
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
			},
			// 发送自定义定位消息
			async sendCustomLocaltion(option) {
				this.$refs.mapc.close()
				const result = await this.$Yxim.msg.sendCustomMsg({
					attach: '占位',
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem
						.account, //接收人
					ext: JSON.stringify({
						name: this.nickname,
						img: uni.getStorageSync('avatar'),
						location: option.name,
						longitude: option.location.split(',')[0],
						latitude: option.location.split(',')[1],
						type: 'location',
						appAvatar: uni.getStorageSync('avatar')
					}),
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
			},
			// 发送自定义消息
			async sendCustomMsg() {
				const result = await this.$Yxim.msg.sendCustomMsg({
					attach: '占位',
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.account, //接收人
					ext: JSON.stringify({
						avatar: 'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
						name: 'zhangsan',
						tip: '查看详情',
						img: [
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665',
							'https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=665'
						],
						location: 'haerbon',
						type: 'dongtai',
						w: 40,
						h: 40,
						appAvatar: uni.getStorageSync('avatar')
					}),
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}
			
			},
			// 发送地理位置
			sendGeoLocationMsg() {
				// apiLocationMe().then(async res => {
				// 	const result = await this.$Yxim.msg.sendGeoLocationMsg({
				// 		scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
				// 		to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.uid, //接收人
				// 		attach: {
				// 			lat: res.message.latitude,
				// 			lng: res.message.longitude,
				// 			title: '我的位置'
				// 		},
				// 	});
				// 	if (result) {
				// 		this.$store.commit('SET_onMsg', result)
				// 	}
				// })
				this.$refs.mapc.open()

			},
			openAmap(content) {
				this.$common.openMap(content.msg.content.text.longitude + ',' + content.msg.content.text.latitude, content
					.msg.content.text.location)
			},
			// 发送文字消息
			async sendText() {
				this.hideDrawer(); //隐藏抽屉
				if (!this.textMsg) {
					return;
				}
				let content = this.replaceEmoji(this.textMsg);
				let msg = {
					text: content
				}
				console.log(content, this.textMsg);
				const result = await this.$Yxim.msg.sendTextMsg({
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.uid, //接收人
					body: content, //发送得文本消息 
					ext: JSON.stringify({
						appAvatar: uni.getStorageSync(
							'avatar'),
						atList: this.chooseAtAccounts,
					})
				});
				console.log(result, 'result');
				this.$store.commit('SET_onMsg', result)
				this.textMsg = ''; //清空输入框
				this.chooseAtAccounts = []
			},
			// 发送图片消息
			async sendImageMsg(file) {
				const result = await this.$Yxim.msg.sendImageMsg({
					scene: this.roomType, //消息的场景  "p2p" | "team" | "superTeam"
					to: this.roomType == 'team' ? this.userItem.teamId : this.userItem.uid, //接收人
					filePath: file, //发送得文本消息\
					// body:'1',
					onUploadDone: (res) => {
						console.log(res, ';;;;;;;;;;');
					}
				});
				if (result) {
					this.$store.commit('SET_onMsg', result)
				}

			},
			// 接受消息(筛选处理)
			screenMsg(msg) {
				//从长连接处转发给这个方法，进行筛选处理
				if (msg.type == 'system') {
					// 系统消息
					switch (msg.msg.type) {
						case 'text':
							this.addSystemTextMsg(msg);
							break;
						case 'redEnvelope':
							this.addSystemRedEnvelopeMsg(msg);
							break;
					}
				} else if (msg.type == 'user') {
					// 用户消息
					switch (msg.msg.type) {
						case 'text':
							this.addTextMsg(msg);
							break;
						case 'voice':
							this.addVoiceMsg(msg);
							break;
						case 'img':
							this.addImgMsg(msg);
							break;
						case 'redEnvelope':
							this.addRedEnvelopeMsg(msg);
							break;
					}
					//非自己的消息震动
					if (msg.msg.userinfo.uid != this.myuid) {
						console.log('振动');
						uni.vibrateLong();
					}
				}
				this.$nextTick(function() {
					// 滚动到底
					this.scrollToView = 'msg' + msg.msg.id
				});
			},

			//触发滑动到顶部(加载历史信息记录)
			loadHistory(e) {
				if (this.isHistoryLoading) {
					return;
				}
				this.isHistoryLoading = true; //参数作为进入请求标识，防止重复请求
				this.scrollAnimation = false; //关闭滑动动画
				let Viewid = this.chatRoomData[0].msg.idServer; //记住第一个信息ID
				//本地模拟请求历史记录效果
				setTimeout(() => {


					//这段代码很重要，不然每次加载历史数据都会跳到顶部
					this.$nextTick(function() {
						this.scrollToView = 'msg' + Viewid; //跳转上次的第一行信息位置
						this.$nextTick(function() {
							this.scrollAnimation = true; //恢复滚动动画
						});

					});
					this.isHistoryLoading = false;

				}, 1000)
			},
			// 加载初始页面消息
			getMsgList() {
				// 消息列表
				let list = [{
						type: "system",
						msg: {
							id: 0,
							type: "text",
							content: {
								text: "欢迎进入HM-chat聊天室"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 1,
							type: "text",
							time: "12:56",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								text: "为什么温度会相差那么大？"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 2,
							type: "text",
							time: "12:57",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								text: "这个是有偏差的，两个温度相差十几二十度是很正常的，如果相差五十度，那即是质量问题了。"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 3,
							type: "voice",
							time: "12:59",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								url: "/static/voice/1.mp3",
								length: "00:06"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 4,
							type: "voice",
							time: "13:05",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								url: "/static/voice/2.mp3",
								length: "00:06"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 5,
							type: "img",
							time: "13:05",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								url: "/static/img/p10.jpg",
								w: 200,
								h: 200
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 6,
							type: "img",
							time: "12:59",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								url: "/static/img/q.jpg",
								w: 1920,
								h: 1080
							}
						}
					},
					{
						type: "system",
						msg: {
							id: 7,
							type: "text",
							content: {
								text: "欢迎进入HM-chat聊天室"
							}
						}
					},

					{
						type: "system",
						msg: {
							id: 9,
							type: "redEnvelope",
							content: {
								text: "售后客服008领取了你的红包"
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 10,
							type: "redEnvelope",
							time: "12:56",
							userinfo: {
								uid: 0,
								username: "大黑哥",
								face: "/static/img/face.jpg"
							},
							content: {
								blessing: "恭喜发财，大吉大利，万事如意",
								rid: 0,
								isReceived: false
							}
						}
					},
					{
						type: "user",
						msg: {
							id: 11,
							type: "redEnvelope",
							time: "12:56",
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: {
								blessing: "恭喜发财",
								rid: 1,
								isReceived: false
							}
						}
					},
				]
				// 获取消息中的图片,并处理显示尺寸
				for (let i = 0; i < list.length; i++) {
					if (list[i].type == 'user' && list[i].msg.type == "img") {
						list[i].msg.content = this.setPicSize(list[i].msg.content);
						this.msgImgList.push(list[i].msg.content.url);
					}
				}
				this.msgList = list;
				// 滚动到底部
				this.$nextTick(function() {
					//进入页面滚动到底部
					this.scrollTop = 9999;
					console.log(111222333444)
					this.$nextTick(function() {
						this.scrollAnimation = true;
					});

				});
			},
			//处理图片尺寸，如果不处理宽高，新进入页面加载图片时候会闪
			setPicSize(content) {
				// 让图片最长边等于设置的最大长度，短边等比例缩小，图片控件真实改变，区别于aspectFit方式。
				let maxW = uni.upx2px(350); //350是定义消息图片最大宽度
				let maxH = uni.upx2px(350); //350是定义消息图片最大高度
				if (content.w > maxW || content.h > maxH) {
					let scale = content.w / content.h;
					content.w = scale > 1 ? maxW : maxH * scale;
					content.h = scale > 1 ? maxW / scale : maxH;
				}
				return content;
			},
			
			goBack() {
				uni.navigateBack()
			},

			//更多功能(点击+弹出) 
			showMore() {
				this.isVoice = false;
				this.hideEmoji = true;
				if (this.hideMore) {
					this.hideMore = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},
			// 打开抽屉
			openDrawer() {
				this.popupLayerClass = 'showLayer';
			},
			// 隐藏抽屉
			hideDrawer() {
				this.popupLayerClass = '';
				setTimeout(() => {
					this.hideMore = true;
					this.hideEmoji = true;
				}, 150);
			},
			// 选择图片发送
			chooseImage() {
				this.getImage('album');
			},
			//拍照发送
			camera() {
				this.getImage('camera');
			},
			//发红包
			handRedEnvelopes() {
				this.navigateTo({
					url: 'HM-hand/HM-hand'
				});
				this.hideDrawer();
			},
			//选照片 or 拍照
			getImage(type) {
				this.hideDrawer();
				uni.chooseImage({
					sourceType: [type],
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					success: (res) => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							uni.getImageInfo({
								src: res.tempFilePaths[i],
								success: (image) => {
									console.log(image.width);
									console.log(image.height);
									let msg = {
										url: res.tempFilePaths[i],
										w: image.width,
										h: image.height
									};
									// this.sendMsg(msg, 'img');

									this.sendImageMsg(res.tempFilePaths[i])
								}
							});
						}
					}
				});
			},
			// 选择表情
			chooseEmoji() {
				this.hideMore = true;
				if (this.hideEmoji) {
					this.hideEmoji = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},
			//添加表情
			addEmoji(em) {
				this.textMsg += em.alt;
			},

			//获取焦点，如果不是选表情ing,则关闭抽屉
			textareaFocus() {
				if (this.popupLayerClass == 'showLayer' && this.hideMore == false) {
					this.hideDrawer();
				}
			},

			//替换表情符号为图片
			replaceEmoji(str) {
				console.log(this.emojiList.length, 'this.emojiList.lengththis.emojiList.lengththis.emojiList.length', str
					.replace(/\[([^(\]|\[)]*)\]/g));
				let replacedStr = str.replace(/\[([^(\]|\[)]*)\]/g, (item, index) => {
					console.log("item: " + item);
					for (let i = 0; i < this.emojiList.length; i++) {
						let row = this.emojiList[i];
						for (let j = 0; j < row.length; j++) {
							let EM = row[j];
							if (EM.alt == item) {
								//在线表情路径，图文混排必须使用网络路径，请上传一份表情到你的服务器后再替换此路径 
								//比如你上传服务器后，你的100.gif路径为https://www.xxx.com/emoji/100.gif 则替换onlinePath填写为https://www.xxx.com/emoji/
								let onlinePath = 'https://s2.ax1x.com/2019/04/12/'
								let imgstr = '<img src="' + onlinePath + this.onlineEmoji[EM.url] + '">';
								return imgstr;
							}
						}
					}
				});
				return replacedStr.indexOf('<img src=img') > -1 ?
					'<div style="display: flex;align-items: center;word-wrap:break-word;">' + replacedStr + '</div>' :
					replacedStr;
			},

			// 发送消息
			sendMsg(content, type) {
				//实际应用中，此处应该提交长连接，模板仅做本地处理。
				var nowDate = new Date();
				let lastid = this.msgList[this.msgList.length - 1].msg.id;
				lastid++;
				let msg = {
					type: 'user',
					msg: {
						id: lastid,
						time: nowDate.getHours() + ":" + nowDate.getMinutes(),
						type: type,
						userinfo: {
							uid: 0,
							username: "大黑哥",
							face: "/static/img/face.jpg"
						},
						content: content
					}
				}
				// 发送消息
				this.screenMsg(msg);
				// 定时器模拟对方回复,三秒
				setTimeout(() => {
					lastid = this.msgList[this.msgList.length - 1].msg.id;
					lastid++;
					msg = {
						type: 'user',
						msg: {
							id: lastid,
							time: nowDate.getHours() + ":" + nowDate.getMinutes(),
							type: type,
							userinfo: {
								uid: 1,
								username: "售后客服008",
								face: "/static/img/im/face/face_2.jpg"
							},
							content: content
						}
					}
					// 本地模拟发送消息
					this.screenMsg(msg);
				}, 3000)
			},

			// 添加文字消息到列表
			addTextMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加语音消息到列表
			addVoiceMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加图片消息到列表
			addImgMsg(msg) {
				msg.msg.content = this.setPicSize(msg.msg.content);
				this.msgImgList.push(msg.msg.content.url);
				this.msgList.push(msg);
			},
			addRedEnvelopeMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加系统文字消息到列表
			addSystemTextMsg(msg) {
				this.msgList.push(msg);
			},
			// 添加系统红包消息到列表
			addSystemRedEnvelopeMsg(msg) {
				this.msgList.push(msg);
			},
			// 打开红包
			openRedEnvelope(msg, index) {
				let rid = msg.content.rid;
				uni.showLoading({
					title: '加载中...'
				});
				console.log("index: " + index);
				//模拟请求服务器效果
				setTimeout(() => {
					//加载数据
					if (rid == 0) {
						this.redenvelopeData = {
							rid: 0, //红包ID
							from: "大黑哥",
							face: "/static/img/im/face/face.jpg",
							blessing: "恭喜发财，大吉大利",
							money: "已领完"
						}
					} else {
						this.redenvelopeData = {
							rid: 1, //红包ID
							from: "售后客服008",
							face: "/static/img/im/face/face_2.jpg",
							blessing: "恭喜发财",
							money: "0.01"
						}
						if (!msg.content.isReceived) {
							// {type:"system",msg:{id:8,type:"redEnvelope",content:{text:"你领取了售后客服008的红包"}}},
							this.sendSystemMsg({
								text: "你领取了" + (msg.userinfo.uid == this.myuid ? "自己" : msg.userinfo
									.username) + "的红包"
							}, 'redEnvelope');
							console.log("this.msgList[index]: " + JSON.stringify(this.msgList[index]));
							this.msgList[index].msg.content.isReceived = true;
						}
					}
					uni.hideLoading();
					this.windowsState = 'show';

				}, 200)

			},
			// 关闭红包弹窗
			closeRedEnvelope() {
				this.windowsState = 'hide';
				setTimeout(() => {
					this.windowsState = '';
				}, 200)
			},
			sendSystemMsg(content, type) {
				let lastid = this.msgList[this.msgList.length - 1].msg.id;
				lastid++;
				let row = {
					type: "system",
					msg: {
						id: lastid,
						type: type,
						content: content
					}
				};
				this.screenMsg(row)
			},
			//领取详情
			toDetails(rid) {
				this.navigateTo({
					url: 'HM-details/HM-details?rid=' + rid
				})
			},
			// 预览图片
			showPic(msg) {
				console.log(this.msgImgList)
				uni.previewImage({
					indicator: "none",
					current: msg.content.url,
					urls: [msg.content.url]
				});
			},
			// 播放语音
			playVoice(msg) {
				this.playMsgid = msg.id;
				this.AUDIO.src = msg.content.url;
				this.$nextTick(function() {
					this.AUDIO.play();
				});
			},
			// 录音开始
			voiceBegin(e) {
				console.log(123, e.touches.length);
				if (e.touches.length > 1) {
					return;
				}
				this.initPoint.Y = e.touches[0].clientY;
				this.initPoint.identifier = e.touches[0].identifier;
				this.RECORDER.start({
					format: "mp3"
				}); //录音开始,
			},
			//录音开始UI效果
			recordBegin(e) {
				this.recording = true;
				this.voiceTis = '松开 结束';
				this.recordLength = 0;
				this.recordTimer = setInterval(() => {
					this.recordLength++;
				}, 1000)
			},
			// 录音被打断
			voiceCancel() {
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				this.willStop = true; //不发送录音
				this.RECORDER.stop(); //录音结束
			},
			// 录音中(判断是否触发上滑取消发送)
			voiceIng(e) {
				if (!this.recording) {
					return;
				}
				let touche = e.touches[0];
				//上滑一个导航栏的高度触发上滑取消发送
				if (this.initPoint.Y - touche.clientY >= uni.upx2px(100)) {
					this.willStop = true;
					this.recordTis = '松开手指 取消发送'
				} else {
					this.willStop = false;
					this.recordTis = '手指上滑 取消发送'
				}
			},
			// 结束录音
			voiceEnd(e) {
				if (!this.recording) {
					return;
				}
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				this.RECORDER.stop(); //录音结束
			},
			//录音结束(回调文件)
			recordEnd(e) {
				clearInterval(this.recordTimer);
				if (!this.willStop) {
					console.log("e: " + JSON.stringify(e));
					let msg = {
						length: 0,
						url: e.tempFilePath
					}
					let min = parseInt(this.recordLength / 60);
					let sec = this.recordLength % 60;
					min = min < 10 ? '0' + min : min;
					sec = sec < 10 ? '0' + sec : sec;
					msg.length = min + ':' + sec;
					this.sendAudioMsg(msg)
					// this.$store.commit('SET_onMsg', msg)
					// this.sendMsg(msg, 'voice');

				} else {
					console.log('取消发送录音');
				}
				this.willStop = false;
			},
			// 切换语音/文字输入
			switchVoice() {
				this.hideDrawer();
				this.isVoice = this.isVoice ? false : true;
			},
			discard() {
				return;
			},
			goNav(url) {
				this.navigateTo({
					url
				})
			},
			// At其他人
			async chooseAt() {
				console.log('ATT')
				if (this.teamMembers.length == 0) {
					await this.getTeamMembers()
				}
				this.chooseAtAccounts = []
				this.showChooseMember = true
				
			},
			checkMember(member) {
				const index = this.chooseAtAccounts.indexOf(member.account)
				if (index > -1) {
					this.chooseAtAccounts.splice(index, 1)
				} else {
					this.chooseAtAccounts.push(member.account)
				}
			},
			checkAll() {
				this.chooseAtAccounts = this.teamMembers.map(item => item.account)
				this.showChooseMember = false
				this.textMsg += `@所有人`
			},
			chooseMemberCancel() {
				this.showChooseMember = false
			},
			chooseMemberConfirm() {
				if (!this.chooseAtAccounts.length) {
					uni.showToast({
						title: "请选择要@的群成员",
						icon: 'none'
					})
					return
				}
				let atMessage = ''
				this.chooseAtAccounts.forEach(account => {
					const nick = this.teamMembers.find(member => member.account === account)?.nick
					if (nick) {
						atMessage += `@${nick} `
					}
				})
				this.showChooseMember = false
				this.textMsg += atMessage
				
			},
			scrollToAtMsg() {
				this.scrollToView = 'msg' + this.atMessageID
				this.$nextTick(() => {
					this.showAtMe = false
				})
			},
			goActivity() {
				if (!this.activityId) {
					uni.showToast({
						title: "活动ID为空",
						icon: 'none'
					})
					return
				}
				uni.navigateTo({
					url: `/pages/HM-chat/teamSetting?uuid=${this.activityId}`
				})
			}
		}
	}
</script>
<style lang="scss">
	@import "@/static/HM-chat/css/style.scss";
</style>
<style lang="scss" scoped>
	.noSend {
		font-weight: 500;
		font-size: 24rpx;
		color: #8C8C8C;
		text-align: center;
		margin-top: 60rpx;
	
		view:last-child {
			background-image: linear-gradient(to right, #4BC6ED 10%, 40%, #BC93F2);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}
	
	.tz {
		background-image: linear-gradient(to right, #4BC6ED 10%, 70%, #BC93F2);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
	
	.status {
		position: absolute;
		right: 0rpx;
		bottom: -40rpx;
		text-align: right;
	}
	
	.msgtime {
		font-size: 24rpx;
		color: #ddd;
		text-align: center;
	}
	
	.btnItem {
		justify-content: space-between;
		padding: 0 84rpx;
		margin-bottom: 50rpx;
		// margin-bottom: 68rpx;
	}
	
	::v-deep .custom-location {
		.custom-location-content {
			width: 408rpx;
			display: flex;
			align-items: center;
	
			.mapimg {
				width: 124rpx;
				height: 124rpx;
				background-image: url(@/static/map/mapicon1.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				position: relative;
				margin-top: 20rpx;
	
				.mapavatar {
					width: 40rpx;
					height: 40rpx;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-80%, -80%);
					z-index: 22;
					border-radius: 50%;
				}
	
	
			}
	
			.info {
				height: 124rpx;
				margin-left: 10rpx;
	
				.name {
					font-size: 32rpx;
					margin-top: 10rpx;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}
	
				.location {
					width: 240rpx;
					font-size: 24rpx;
					margin-top: 10rpx;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}
			}
	
	
		}
	}
	
	::v-deep .custom {
		width: 90vw;
		display: flex;
		flex-direction: column;
	
		.cus-avatar {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 24rpx;
	
			.cus-l {
				display: flex;
				align-items: center;
				font-size: 24rpx;
	
				image {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
	
	
		}
	
		.cus-content {
			max-width: 100%;
	
			.cus-content-imgs {
				width: 430rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
	
				image {
					border-radius: 20rpx;
					margin: 20rpx 0;
				}
			}
	
		}
	
		.cus-location {
			width: 100%;
			display: flex;
			align-items: center;
			font-size: 24rpx;
	
			image {
				width: 20rpx;
				height: 20rpx;
				margin-right: 10rpx;
			}
		}
	}
	
	.right {
		position: relative;
	
		.is_read {
			margin: 0 0 0 20rpx;
			font-size: 24rpx;
		}
	}
	.chooose-bg {
		backdrop-filter: blur(30px);
		background: rgba(0, 0, 0, 0.8);
		height: 100%;
		padding: 32rpx;
		.btn_wrapper {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.btn {
				color: rgb(255, 255, 255);
				font-family: HarmonyOS Sans;
				font-size: 28rpx;
				padding: 12rpx;
			}
		}
		.member-item {
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 16rpx 16rpx;
			.checkbox {
				box-sizing: border-box;
				height: 28rpx;
				width: 28rpx;
				border-radius: 4rpx;
				background: rgb(255, 255, 255);
			}
			.checked {
				border: 2rpx white solid;
				background: rgb(71, 153, 185)
			}
			.avatar {
				width: 100rpx;
				height: 100rpx;
				box-sizing: border-box;
				border: 2rpx solid rgb(255, 255, 255);
				margin: 0 32rpx;
			}
			.member-name {
				color: rgb(255, 255, 255);
				font-family: HarmonyOS Sans;
				font-size: 32rpx;
				font-weight: 400;
			}
			
		}
	}
	.content {
		width: 100%;
	}
	.more {
	  width: 30rpx;
	  height: 30rpx;
	  margin-right: 20rpx;
	  padding: 20rpx;
	}
	.at_wrapper {
		position: fixed;
		top: 260rpx;
		right: 32rpx;
		background-color: white;
		border-radius: 18rpx;
		font-size: 28rpx;
		padding: 12rpx 20rpx;
		font-weight: 400;
		color: rgb(188, 147, 242);
		display: flex;
		align-items: center;
		.up {
			width: 28rpx;
			height: 28rpx;
			margin-right: 8rpx;
		}
	}
	
</style>