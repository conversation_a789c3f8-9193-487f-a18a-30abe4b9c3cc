<template>
	<view>
		<scroll-view :scroll-with-animation="true" scroll-y="true" style="height: 100vh;" class="scroll-view_back"
			@scrolltolower="scrollBnt">
			<view style="border: 1rpx solid #333;"></view>
			<!-- <block v-for="(item,index) in itemArr" :key="index"> -->
			<view class="item_group" v-for="(item,index) in arr" style="justify-content: space-between;" :key="index">
				<view class="t_display">
					<image style="width: 108rpx;height: 108rpx;border-radius: 50%;" :src="item.user.avatar"
						mode="aspectFill">
					</image>
					<view class="info">
						<view class="t_display">
							<view class="name">
								{{item.user.nickname}}
							</view>
							<image style="width: 32rpx;height: 32rpx;" src="../../static/images/roleme.png" mode=""
								v-if="false">
							</image>
						</view>
						<view class="note t_display">
							<image src="../../static/images/manLogo.png" class="img32" v-if="item.user.ext.sex=='1'"
								mode="" />
							<image src="../../static/images/womanLogo.png" class="img32" v-else mode="" />
							<view class="" style="margin-left: 10rpx;">
								{{item.user.ext.age}}岁 | {{item.user.home_town_format}}
							</view>
						</view>
					</view>
				</view>
				<view class="t_display" v-if="item.status == 2">
					<view class="no">
						<view class="themeColor" @click="ack(item.request_id,3,index)">
							拒绝
						</view>
					</view>
					<view class="yes" @click="ack(item.request_id,1,index)">
						通过
					</view>
				</view>
				<view class=" declined" v-if="item.status==3">
					已拒绝
				</view>
				<view class=" declined" v-if="item.status==1">
					已同意
				</view>
			</view>
		</scroll-view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				arr: []
			}
		},
		onLoad() {
			this.getData()
		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			ack(requestId, type, index) {
				this.$http.post('/api/user/friend/ack', {
					requestId,
					type,
					"msg": ""
				}).then(res => {
					this.arr.splice(index, 1)
				})
			},
			getData() {
				this.$http.get('/api/user/friend/record').then(res => {
					this.arr = res.message.list
				})
			},
			scrollBnt() {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.item_group {
		display: flex;
		padding: 24rpx 32rpx;
		align-items: center;

		.declined {
			width: 126rpx;
			height: 56rpx;
			border-radius: 16rpx;
			opacity: 1;
			font-size: 22rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: #767676;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 2rpx solid #767676;
		}

		.yes {
			width: 126rpx;
			height: 56rpx;
			text-align: center;
			line-height: 56rpx;
			border-radius: 16rpx;
			font-size: 22rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: #FFFFFF;
			background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
			margin-left: 16rpx;
		}

		.no {
			font-size: 22rpx;
			font-weight: 400;
			text-align: center;
			width: 126rpx;
			height: 56rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid transparent;
			border-radius: 16rpx;
			background-clip: padding-box, border-box;
			background-origin: padding-box, border-box;
			background-image: linear-gradient(to right, #333, #333), linear-gradient(0deg, rgb(174, 111, 255) 0%, rgb(46, 179, 255) 100%);
		}

		.outer_circle {
			position: relative;
			margin: 50px;
			width: 100px;
			height: 100px;
			border-radius: 50%;
			background: #ffffff;
		}

		.inner_circle {
			background-image: linear-gradient(to bottom, rgb(123, 93, 255) 0%,
					rgb(56, 225, 255) 100%);
			content: '';
			position: absolute;
			top: -20px;
			bottom: -20px;
			right: -20px;
			left: -20px;
			z-index: -1;
			border-radius: inherit;
		}

		.right {
			width: 12rpx;
			height: 38rpx;
		}

		.info {
			margin-left: 32rpx;

			.note {
				font-size: 28rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.64);
			}

			.name {
				font-size: 34rpx;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #FFFFFF;
			}
		}


		.text {
			margin-left: 32rpx;
			font-size: 34rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
</style>