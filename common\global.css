.t_display {
	display: flex;
	align-items: center;
}

.t_zt {
	background-image: linear-gradient(to right, #4BC6ED 10%, 15%, #BC93F2 50%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.t_font_grey {
	color: rgba(255, 255, 255, 0.82);
	font-size: 26rpx;
}

.t_radius {
	border-radius: 50%;
}

.img52 {
	width: 52rpx;
	height: 52rpx;
}

.img222 {
	width: 222rpx;
	height: 222rpx;
}

.img88 {
	width: 88rpx;
	height: 88rpx;
}

.t-align-between {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.img686 {
	width: 686rpx;
	height: 686rpx;
}

.img92 {
	width: 92rpx;
	height: 92rpx;
}

.img409 {
	width: 409rpx;
	height: 409rpx;
}

.img323 {
	width: 323rpx;
	height: 323rpx;
}

.img76 {
	width: 76rpx;
	height: 76rpx;
}

.t_betweent {
	display: flex;
	justify-content: space-between;
}

.t_betweent_center {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.t_center {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.img18 {
	width: 18rpx;
	height: 18rpx;
}

.img36 {
	width: 36rpx;
	height: 36rpx;
}

.img140 {
	width: 140rpx;
	height: 140rpx;
}

.img32 {
	width: 32rpx;
	height: 32rpx;
}

.img24 {
	width: 24rpx;
	height: 24rpx;
}

.img335 {
	width: 335rpx;
	height: 335rpx;
}

.img102 {
	width: 102rpx;
	height: 102rpx;
}

.img42 {
	width: 42rpx;
	height: 42rpx;
}

.img80 {
	width: 80rpx;
	height: 80rpx;
}

.img74 {
	width: 74rpx;
	height: 74rpx;
}

.img124 {
	width: 124rpx;
	height: 124rpx;
}

.img108 {
	width: 108rpx;
	height: 108rpx;
}

.themeColor {
	background: linear-gradient(to right, #4BC6ED, #BC93F2);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.grey {
	width: 126rpx;
	height: 56rpx;
	text-align: center;
	line-height: 56rpx;
	color: #767676;
	border-radius: 16rpx;
	border: 2rpx solid #767676;
}