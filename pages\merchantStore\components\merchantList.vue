<template>
	<view class="merchant-list-container">
		<view class="merchant-list-title">
			<view class="title-container">
				<view class="shop">
					<view class="shop-title">商户名称</view>
					<view class="shop-localtions">
						<image class="localtions-icon" src="@/static/images/address-icon.png" mode="widthFix">
						</image>
						12.2km
					</view>
				</view>
				<view class="customer">
					<view class="customer-title">
						<view class="customer-title-item">
							<view class="customer-title-item-bg">
								<view class="customer-title-item-word-bg">
									<image class="customer-title-item-icon" src="@/static/images/email-icon.png" mode="">
									</image>
									发消息
								</view>
							</view>
						</view>
						<view class="customer-title-item">
							<view class="customer-title-item-bg">
								<view class="customer-title-item-word-bg">
									<image class="customer-title-item-icon" src="@/static/images/phone-icon.png" mode="">
									</image>
									客服
								</view>
							</view>
						</view>
					</view>
					<view class="customer-total">参与人数 <view class="customer-total-number">17.9w</view></view>
				</view>
			</view>
			<!-- 店铺公告 -->
			<view class="shop-notice" :style="{ height: isShopOpen ? 'auto' : '80px' }">
				<view class="shop-notice-item">
					<view class="shop-notice-title">店铺公告</view>
					<view class="shop-content">商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商户简介这里是一段文字商</view>
				</view>
				<view class="shop-notice-item">
					<view class="shop-notice-title">客服电话</view>
					<view class="shop-content">12345674567</view>
				</view>
				<view class="shop-notice-item">
					<view class="shop-notice-title">商户地址</view>
					<view class="shop-content">TOBeDeterminedTOBeDetermnedTOBeDeterminedTOBeDeterminedTOBeDetermned</view>
				</view>
				<view class="shop-open-container" @click="isShopOpen = !isShopOpen">
					<view v-if="!isShopOpen" class="shop-button">
						展开
						<image class="arrow-icon" src="@/static/images/down-icon.png" mode="">
						</image>
					</view>
					<view v-else class="shop-button">
						收起
						<image class="arrow-icon" src="@/static/images/up-icon.png" mode="">
						</image>
					</view>
				</view>
			</view>
		</view>
		<!-- 活动列表 -->
		<view class="merchant-activity">
			<view class="merchant-activity-title-container">
				<view class="merchant-activity-title" @click="changeTitle(index)" :class="{ 'merchant-activity-title-active': index === activityTitleIndex }" v-for="(item,index) in activityTitle" :key="index" >
					<image class="activity-title-icon" src="@/static/images/pinglun.png" mode=""></image>
					{{item.name}}
				</view>
			</view>
			<!-- 活动列表 -->
			<view v-if="activityTitleIndex==0" class="merchant-activity-list">
				<view class="item" @click="goDetails()">
					<view class="item-cover">
						<image class="activeimage" src="@/static/images/title-image.png" mode="">
						</image>
						<view class="activeimagecover" v-if="state == 3">
							<image class="activeimageend" src="@/static/images/activeEnd.png" mode="">
							</image>
						</view>
					</view>
					<!-- <image class="activeimage" :src="item.cover" mode=""></image> -->
					<view class="item-info">
						<view class="item-title">
							端午节赛龙舟端午节赛龙舟
						</view>
						<view class="item-total">
							<view>参与人数60</view>
							<button v-if="state==1" class="item-button item-button-unjoin" type="default">
								<view>参加</view>
							</button>
							<button v-else-if="state==2" class="item-button item-button-join" type="default">
								<view>已参加</view>
							</button>
							<button v-else class="item-button item-button-end" type="default">
								<view>活动结束</view>
							</button>
						</view>
						<view class="item-time">
							<image class="time-clock" src="@/static/images/vip/clock.png" mode=""></image>
							<view class="time">截止至2024.10.01</view>
							<!-- <view class="time">截止至{{ item.end_time.split(" ")[0] }}</view> -->
						</view>
					</view>
				</view>
				<view class="item">
						<view class="item-cover">
							<image class="activeimage" src="@/static/images/title-image.png" mode="">
							</image>
							<view class="activeimagecover" v-if="state == 3">
								<image class="activeimageend" src="@/static/images/activeEnd.png" mode="">
								</image>
							</view>
						</view>
						<!-- <image class="activeimage" :src="item.cover" mode=""></image> -->
						<view class="item-info">
							<view class="item-title">
								端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟端午节赛龙舟
							</view>
							<view class="item-total">
								<view>参与人数60</view>
								<button v-if="state==1" class="item-button item-button-unjoin" type="default">
									<view>参加</view>
								</button>
								<button v-else-if="state==2" class="item-button item-button-join" type="default">
									<view>已参加</view>
								</button>
								<button v-else class="item-button item-button-end" type="default">
									<view>活动结束</view>
								</button>
							</view>
							<view class="item-time">
								<image class="time-clock" src="@/static/images/vip/clock.png" mode=""></image>
								<view class="time">截止至2024.10.01</view>
								<!-- <view class="time">截止至{{ item.end_time.split(" ")[0] }}</view> -->
							</view>
						</view>
					</view>
				
			</view>
			<!-- 展品列表 -->
			<view v-else-if="activityTitleIndex==1" class="exhibit-list">
				<view v-for="(item,index) in 10" :key="index" class="exhibit-list-item">
					<image class="exhibit-list-item-cover" src="@/static/images/title-image.png" mode="widthFix">
					</image>
					<view class="exhibit-list-item-title">今日特卖克里斯汀联动百事可乐百事可...</view>
					<view class="exhibit-list-item-price">￥19.99</view>
					<view class="exhibit-list-item-total">
						<view class="exhibit-list-item-total-like exhibit-list-item-total-item">
							<image class="exhibit-list-item-total-icon" src="@/static/images/like-icon.png" mode="widthFix"></image>
							1.2k
						</view>
						<view class="exhibit-list-item-total-comment exhibit-list-item-total-item">
							<image class="exhibit-list-item-total-icon" src="@/static/images/comment-icon.png" mode="widthFix"></image>
							1.2k
						</view>
					</view>
				</view>
				<view class="exhibit-list-tips">一百米以内可进行点赞或评论</view>
			</view>
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				state:3,
				isShopOpen:false,//是否展开
				activityTitle:[
					{
						name:"活动列表",
						icon:"pinglun.png"
					},
					{
						name:"展品列表",
						icon:"topTop.png"
					}
				],
				activityTitleIndex:0,
				shopInfo:{},
				activeList:[]
			}
		},
		onShow() {
			console.log("嗲接口")
			this.getShopInfo();
		},
		onLoad(){
			console.log("嗲接口")
			this.getShopInfo();
		},
		methods: {
			goDetails() {
				uni.navigateTo({
					url: '/pages/merchantStoreDetails/merchantStoreDetails'
				});
			},
			getImageUrl(iconName) {
				return require('@/static/images/' + iconName);
			},
			// 切换title
			changeTitle(index){
				this.activityTitleIndex = index;
			},
			// 获取商户信息
			getShopInfo() {
				this.$http.get('/api/user/business/get').then((res) => {
					this.shopInfo = res.message;
					console.log(this.shopInfo)
					this.getActiveList();
				});
			},
			// 获取活动列表
			getActiveList() {
				this.$http.get('/api/user/business/activity/list', { business_id: this.shopInfo.id,page:1,size:10 }).then((res) => {
					console.log(res);
					this.activeList = res.message.list;
					
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.merchant-list-container{
		padding:20px 0;
		box-sizing: border-box;
		.title-hr{
			width: 100%;
			height: 1px;
			background-color: #787878;
			border: 0;
		}
		.merchant-list-title{
			width:100%;
			padding:0 20px;
			box-sizing: border-box;
			margin-bottom: 20px;
			
			.title-container{
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				// margin-bottom:20px;
				.shop{
					.shop-title{
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 22px;
						font-weight: 700;
						line-height: 26px;
						margin-bottom:8px;
					}
					.shop-localtions{
						width: 48px;
						// height: 14px;
						padding:2px 4px;
						box-sizing: border-box;
						border-radius: 1px;
						background: rgba(143, 163, 201, 0.23);
						display: flex;
						align-items: center;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 9px;
						font-weight: 400;
						line-height: 11px;
						.localtions-icon{
							width:10px;
							height: 10px;
						}
					}
				}
				.customer{
					.customer-title{
						display: flex;
						align-items: center;
						justify-content: flex-end;
						margin-bottom:20px;
						.customer-title-item{
							width:64px;
							padding:1px;
							box-sizing: border-box;
							border-radius: 60px;
							background: linear-gradient(90.00deg, rgb(83, 194, 238),rgb(207, 178, 250) 100%);
							margin-left:10px;
							.customer-title-item-bg{
								width:100%;
								height: 100%;
								border-radius: 60px;
								background-color: #23232D;
								.customer-title-item-word-bg{
									height: 24px;
									padding:0px 8px;
									box-sizing: border-box;
									border-radius: 60px;
									display: flex;
									align-items: center;
									justify-content: center;
									background: linear-gradient(90.00deg, rgb(83, 194, 238),rgb(207, 178, 250));
									-webkit-background-clip:text;
									-webkit-text-fill-color:transparent;
									background-clip:text;
									text-fill-color:transparent;
									font-family: HarmonyOS Sans;
									font-size: 10px;
									font-weight: 400;
									line-height: 12px;
									.customer-title-item-icon{
										width:14px;
										height: 14px;
										margin-right: 2px;
									}
								}
							}
						}
					}
					.customer-total{
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 10px;
						font-weight: 400;
						line-height: 12px;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						.customer-total-number{
							color: rgb(255, 255, 255);
							font-family: HarmonyOS Sans;
							font-size: 20px;
							font-weight: 700;
							line-height: 23px;
							margin-left:4px;
						}
					}
				}
			}
			// 店铺公告
			.shop-notice{
				 height: 80px;
				 overflow-y: hidden;
				 position: relative;
				.shop-notice-item{
					padding:15px 0;
					box-sizing: border-box;
					border-bottom: 1px solid #787878;
					.shop-notice-title{
						 color: rgb(255, 255, 255);
						 font-family: HarmonyOS Sans;
						 font-size: 14px;
						 font-weight: 500;
						 line-height: 16px;
						margin-bottom: 8px;
					}
					.shop-content{
						color: rgb(206, 206, 206);
						font-family: 思源黑体;
						font-size: 14px;
						font-weight: 400;
						line-height: 20px;
						 word-break: break-all;
					}
				}
				.shop-notice-item:nth-child(3){
					border-bottom:0;
					margin-bottom:28px;
				}
				.shop-open-container{
					position: absolute;
					left:0;
					right:0;
					bottom:0;
					width:100%;
					height: 20px;
					background-image: linear-gradient(to bottom, rgba(35, 35, 45, 0.1), #23232D);
					display: flex;
					align-items: center;
					justify-content: flex-end;
					.shop-button{
						background-color: #23232D;
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 500;
						line-height: 16px;
						padding-left:20px;
						box-sizing: border-box;
						display: flex;
						align-items: center;
						.arrow-icon{
							width: 16px;
							height: 16px;
						}
					}
				}
				
				
			}
		}
		// 活动列表
		.merchant-activity{
			width:100%;
			.merchant-activity-title-container{
				width:100%;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;	
				.merchant-activity-title{
					display: flex;
					align-items: center;
					justify-content: center;
					padding:10px 0;
					box-sizing: border-box;
					width:53%;
					background-repeat: no-repeat;
					background-size:cover;
					position: absolute;
					.activity-title-icon{
						width:20px;
						height:20px;
						margin-right:6px;
					}
				}
				.merchant-activity-title.merchant-activity-title-active{
					z-index: 100;
				}
				.merchant-activity-title:nth-of-type(1).merchant-activity-title-active{
					background-image: url("../../../static/images/shop-title-bg-left-active.png");
					left:0;
				}
				.merchant-activity-title:nth-of-type(2).merchant-activity-title-active{
					background-image: url("../../../static/images/shop-title-bg-right-active.png");
					right:0;
				}
				.merchant-activity-title:nth-of-type(1){
					left:0;
					background-image: url("../../../static/images/shop-title-bg-left.png");
				}
				.merchant-activity-title:nth-of-type(2){
					right:0;
					background-image: url("../../../static/images/shop-title-bg-right.png");
				}
			}
			// 商品列表
			.merchant-activity-list{
				width:100%;
				padding:20px;
				box-sizing: border-box;
				.item {
						width: 100%;
						display: flex;
						justify-content: space-evenly;
						align-items: flex-start;
						border-radius: 10px;
						background: #383A44;
						margin-bottom: 20px;
						padding:10px;
						box-sizing: border-box;
						.item-cover{
							position: relative;
							margin-right:10px;
							border-radius: 6px;
							.activeimage {
								width: 122px;
								height: 122px;
								border-radius: 6px;
							}
							.activeimagecover{
								position: absolute;
								top:0;
								left:0;
								right:0;
								bottom:0;
								width: 122px;
								height: 122px;
								border-radius: 9px;
								background: rgba(0, 0, 0, 0.7);
								display: flex;
								align-items: center;
								justify-content: center;
								.activeimageend{
									width: 102px;
									height: 48px;
									margin:0 auto;
								}
							}
						}
						
				
						.item-info {
							flex:1;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							padding:6px 0 10px 0;
							box-sizing: border-box;
							.item-title {
								color: rgb(255, 255, 255);
								font-family: HarmonyOS Sans;
								font-size: 18px;
								font-weight: 700;
								line-height: 22px;
								margin-bottom:10px;
								 display: -webkit-box;
								  -webkit-box-orient: vertical;
								  -webkit-line-clamp: 2;
								  overflow: hidden;
								  text-overflow: ellipsis;
							}
							.item-total{
								display: flex;
								align-items: flex-end;
								justify-content: space-between;
								color: rgb(224, 224, 224);
								font-family: HarmonyOS Sans;
								font-size: 10px;
								font-weight: 400;
								line-height: 12px;
								margin-bottom:13px;
								.item-button{
									width: 64px;
									height: 26px;
									color: rgb(255, 255, 255);
									font-family: HarmonyOS Sans;
									font-size: 12px;
									font-weight: 500;
									line-height: 14px;
									padding:0;
									margin:0;
									border-radius: 60px;
									display: flex;
									align-items: center;
									justify-content: center;
								}
								.item-button-join{
									border:1px solid #EDEDED;
									background:#383A44;
								}
								.item-button-unjoin{
									background: linear-gradient(90.00deg, rgb(83, 194, 238),rgb(207, 178, 250) 100%);
								}
								.item-button-end{
									background-color:#50515a;
								}
							}
							.item-time {
								display: flex;
								align-items: center;
								justify-content: flex-start;
								color: rgb(255, 255, 255);
								font-family: HarmonyOS Sans;
								font-size: 10px;
								font-weight: 400;
								line-height: 12px;
								letter-spacing: 0px;
								text-align: left;
								.time-clock {
									width: 12px;
									height: 12px;
									margin-right: 3px;
								}
							}
						}
					}
				
			}
			// 展品列表
			.exhibit-list{
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				flex-wrap: wrap;
				.exhibit-list-item{
					width: 48%;
					border-radius: 6px;
					background: rgb(56, 58, 68);
					padding:10px;
					box-sizing: border-box;
					.exhibit-list-item-cover{
						width:100%;
						height: auto;
						margin-bottom: 12px;
					}
					.exhibit-list-item-title{
						color: rgb(255, 255, 255);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom:12px;
					}
					.exhibit-list-item-price{
						color: rgb(89, 192, 239);
						font-family: HarmonyOS Sans;
						font-size: 16px;
						font-weight: 700;
						line-height: 19px;
						letter-spacing: 0px;
						text-align: left;
						margin-bottom:12px;
					}
					.exhibit-list-item-total{
						padding:14px 12px;
						box-sizing: border-box;
						border-top:1px solid rgba(196, 196, 196, 0.12);
						display: flex;
						align-items: center;
						justify-content: space-between;
						color: rgb(197, 209, 230);
						font-family: HarmonyOS Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 16px;
						letter-spacing: 0px;
						text-align: left;
						.exhibit-list-item-total-item{
							display: flex;
							align-items: center;
							.exhibit-list-item-total-icon{
								width:15px;
								height: auto;
								margin-right:5px;
							}
						}
						
					}
				}
			}
		}
		.twoDot{
			display: -webkit-box;
			 -webkit-box-orient: vertical;
			 -webkit-line-clamp: 2;
			 overflow: hidden;
			 text-overflow: ellipsis;
		}
	}
</style>