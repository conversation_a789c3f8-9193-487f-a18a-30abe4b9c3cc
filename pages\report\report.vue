<template>
	<view class="report-container">
		<!-- 提示文字 -->
		<!-- <view class="tip-text">
			侵犯肖像、隐私、名誉、商标、专利权
		</view> -->

		<!-- 举报选项列表 -->
		<view class="report-options">
			<view v-for="(item, index) in reportOptions" :key="index" class="option-wrapper">
				<view class="option-item" @click="selectOption(item)">
					<view class="option-text">{{ item.text }}</view>
					<view class="option-circle" v-if="selectedIndex !== item.value">
					</view>
					<image v-else class="selected-icon" src="../../static/images/report/xuanze.png" mode="aspectFit">
					</image>
				</view>
				<!-- 选中项的提示信息 -->
				<view class="info-box" v-if="selectedIndex === item.value">
					<text class="info-text">{{ item.tip }}</text>
				</view>
			</view>
		</view>

		<!-- 下一步按钮 -->
		<view class="next-btn" :class="{ 'btn-active': selectedIndex !== null }" @click="nextStep">下一步</view>
	</view>
</template>

<script>
	import {
		ReportTypes
	} from '../../utils/reportEnums.js';

	export default {
		data() {
			return {
				selectedValue: "",
				uuid: '',
				type: "",
				selectedIndex: null,
				reportOptions: Object.values(ReportTypes)
			}
		},
		onLoad(option) {
			// 获取传递过来的活动ID
			if (option.params) {
				this.uuid = option.params;
				this.type = option.type
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 选择举报选项
			selectOption(item) {
				this.selectedIndex = item.value;
				this.selectedValue = item;
			},

			// 下一步
			nextStep() {
				if (this.selectedIndex === null) {
					uni.showToast({
						title: '请选择举报类型',
						icon: 'none'
					});
					return;
				}
				// 跳转到举报详情页面
				uni.navigateTo({
					url: '/pages/report/reportDetail/reportDetail?current=' + encodeURIComponent(JSON.stringify(
						this
						.selectedValue)) + '&uuid=' + this.uuid + '&type=' + this.type,
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 设置页面根元素背景色 */
	page {
		background-color: #ffffff !important;
		overflow: hidden;
	}

	/* 设置页面容器背景色 */
	.report-container {
		min-height: 100vh;
		background-color: #ffffff !important;
		padding: 0 30rpx;
		padding-bottom: 150rpx;
		/* 为底部按钮留出空间 */
		overflow: hidden;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.tip-text {
		font-size: 28rpx;
		color: #666;
		margin: 30rpx 0 50rpx;
		line-height: 1.5;
	}

	.report-options {
		.option-wrapper {
			// border-bottom: 1px solid #f5f5f5;
		}

		.option-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;

			.option-text {
				font-size: 32rpx;
				color: #333;
			}

			.option-circle {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				border: 1px solid #ddd;
			}

			.selected-icon {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.option-tip {
			font-size: 26rpx;
			color: #ff6b6b;
			padding: 0 0 20rpx 0;
		}
	}

	.info-box {
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;

		.info-text {
			font-size: 26rpx;
			color: #999;
			line-height: 1.5;
		}
	}

	.next-btn {
		position: fixed;
		bottom: 50rpx;
		left: 30rpx;
		right: 30rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: #ccc;
		color: #fff;
		font-size: 32rpx;
		border-radius: 45rpx;
		z-index: 100;

		&.btn-active {
			background-color: #ff6b6b;
		}
	}
</style>