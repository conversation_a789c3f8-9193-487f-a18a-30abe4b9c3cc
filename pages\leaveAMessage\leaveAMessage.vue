<template>
	<view>
		<u-tabs :list="list" :is-scroll="false" :current="swiperCurrent" @change="tabsChange" active-color="#fff"
			inactive-color="#999" bar-width="375" bgColor="#191C26" bar-height="2"></u-tabs>
		<view>
			<swiper class="swiper" :current="swiperCurrent" @change="swiperChange">
				<swiper-item>
					<view class="swiper-item">
						<view class="noSend" v-if="!sendArr.length">
							<image class="img222" src="../../static/images/message/wufasong.png" mode=""></image>
							<view class="sendBtn" @click="goSend">发送信件</view>
						</view>
						<scroll-view scroll-y v-else refresher-enabled :refresher-triggered="sendFlag"
							@refresherrefresh="refresherrefresh" @scrolltolower="scrolltolower"
							refresher-background="#191C26" style="height: 92vh;">
							<view class="card" v-for="item in sendArr" @click="seeSendDetail(item)">
								<view class="t_display">
									<image class="avatar img88" :src="item.user_info?item.user_info.avatar:''"></image>
									<view class="info">
										<view>{{item.user_info?item.user_info.nickname:''}}</view>
										<view>已发送 {{item.created_at}}</view>
									</view>
								</view>
								<image class="img42" src="../../static/images/message/yiduLogo.png" mode=""
									v-if="item.receive" />
								<image class="img42" src="../../static/images/message/weiduLogo.png" mode="" v-else />
							</view>
						</scroll-view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<view class="no" v-if="!messageArr.length">
							<image class="img222" src="../../static/images/message/wuxinxi.png" mode=""></image>
							<view class="">还没有信件哦~</view>
						</view>
						<scroll-view style="height: 92vh;" scroll-y v-else :refresher-triggered="msgFlag"
							@refresherrefresh="refresherrefreshS" @scrolltolower="scrolltolowerS" refresher-enabled
							refresher-background="#191C26">
							<view class="card" v-for="item in messageArr" @click="seeDetail(item)">
								<view class="t_display">
									<image class="avatar img88" :src="item.user_info?item.user_info.avatar:''"></image>
									<view class="info">
										<view>{{item.user_info?item.user_info.nickname:''}}</view>
										<view>{{item.receive>0?"已发送":"未回复"}} {{item.created_at}}</view>
									</view>
								</view>
								<image class="img42" src="../../static/images/message/yiduLogo.png" mode=""
									v-if="item.receive" />
								<image class="img42" src="../../static/images/message/weiduLogo.png" mode="" v-else />
							</view>
						</scroll-view>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	import Tabs from "../my/components/tabs.vue"
	import {
		apiMsgDetail
	} from '@/api/common.js';
	export default {
		components: {
			Tabs
		},
		data() {
			return {
				sendFlag: true,
				msgFlag: false,
				sendArr: [{
					name: 'xxx'
				}],
				messageArr: [{
					name: 'xxx'
				}, {
					name: 'xxx'
				}],
				list: [{
					name: '发送'
				}, {
					name: '查看'
				}],
				page: 1,
				pageS: 1,
				total: 0,
				totalS: 0,
				msgTotal: 0,
				msgPage: 1,
				swiperCurrent: 0, // swiper组件的current值，表示当前那个swiper-item是活动的

			}
		},
		onLoad() {
			this.getData()
			this.getDataS()
		},
		methods: {
			seeSendDetail(item) {
				apiMsgDetail({
					source: 2,
					id: item.id
				}).then(res => {
					if (res.code == 200) {
						const it = {
							...item,
							...res.message
						}
						uni.$emit('seeDetail', it)
						// 1. 获取当前页面栈实例（此时最后一个元素为当前页）
						let pages = getCurrentPages()

						// 2. 上一页面实例
						// 注意是length长度，所以要想得到上一页面的实例需要 -2
						// 若要返回上上页面的实例就 -3，以此类推
						let prevPage = pages[pages.length - 2]

						// 3. 给上一页面实例绑定getValue()方法和参数（注意是$vm）
						prevPage.$vm.setCurrent(0)
						uni.navigateBack()
					}
				})
			},
			seeDetail(item) {
				apiMsgDetail({
					source: 2,
					id: item.id
				}).then(res => {
					if (res.code == 200) {
						uni.$emit('seeDetail', {
							...item,
							...res.message
						})
						// 1. 获取当前页面栈实例（此时最后一个元素为当前页）
						let pages = getCurrentPages()

						// 2. 上一页面实例
						// 注意是length长度，所以要想得到上一页面的实例需要 -2
						// 若要返回上上页面的实例就 -3，以此类推
						let prevPage = pages[pages.length - 2]

						// 3. 给上一页面实例绑定getValue()方法和参数（注意是$vm）
						prevPage.$vm.setCurrent(0)
						uni.navigateBack()
					}
				})
			},
			goSend() {
				uni.$emit('sendMsg', true)
				uni.navigateBack()
			},
			refresherrefresh() {
				this.page = 1;
				this.getData()
				this.sendFlag = true
				setTimeout(() => {
					this.sendFlag = false
				}, 500)
			},

			refresherrefreshS() {
				this.msgPage = 1;
				this.getDataS()
				this.msgFlag = true
				setTimeout(() => {
					this.msgFlag = false
				}, 500)
			},
			scrolltolower() {
				if (this.total != 0) {
					this.page++;
					this.getData()
				}
			},
			scrolltolowerS() {
				if (this.msgTotal != 0) {
					this.msgPage++;
					this.getDataS()
				}
			},
			getData() {
				this.$http.get('/api/location-msg/list/send', {
					page: this.page,
				}).then(res => {
					this.sendArr = this.page == 1 ? [...res.message] : [...this.sendArr, ...res.message]
					this.total = res.message.length
				})
			},
			getDataS() {
				this.$http.get('/api/location-msg/list/receive', {
					page: this.msgPage,
				}).then(res => {
					this.messageArr = this.msgPage == 1 ? [...res.message] : [...this.messageArr, ...res.message]
					this.msgTotal = res.message.length
				})
			},
			swiperChange(event) {
				this.swiperCurrent = event.detail.current
			},
			tabsChange(idx) {
				this.swiperCurrent = idx
			}
		}
	}
</script>

<style lang="scss" scoped>
	.swiper {
		height: calc(100vh - 80rpx);
	}

	.swiper-item {
		display: block;
		height: 300rpx;
		text-align: center;
		padding: 0 32rpx;

		.card {
			margin-top: 32rpx;
			width: 100%;
			height: 152rpx;
			background: #2F3341;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			border: 1rpx solid #fff;
			padding: 32rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.info {
				height: 88rpx;
				text-align: left;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-left: 12rpx;
			}

			.avatar {
				border-radius: 50%;
			}
		}

		.noSend {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 148rpx;

			.sendBtn {
				line-height: 82rpx;
				font-size: 32rpx;
				font-weight: bold;
				margin-top: 42rpx;
				width: 257rpx;
				height: 82rpx;
				background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
				border-radius: 12rpx;
			}

		}

		.no {
			margin-top: 148rpx;

			view {
				font-size: 32rpx;
				margin-top: 42rpx;
				color: #C8C8C8;
			}
		}
	}
</style>