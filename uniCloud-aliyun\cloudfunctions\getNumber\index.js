'use strict';
exports.main = async (event, context) => {
	// event里包含着客户端提交的参数
	console.log("event:" + event);
	const res = await uniCloud.getPhoneNumber({
        appid: "__UNI__2DF6F7C", 
		appId: "__UNI__2DF6F7C", 
		provider: 'univerify',
		access_token: event.queryStringParameters.access_token,
		openid: event.queryStringParameters.openid
	})

	console.log(res);
	return {
		code: 200,
		message: '获取手机号成功',
		data: res
	}
};