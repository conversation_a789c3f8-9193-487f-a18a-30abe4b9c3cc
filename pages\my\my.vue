<template>
	<view class="container">
		<scroll-view scroll-y style="height: 100vh;" @scrolltolower="currentGetData">
			<image class="bg" :src="info.user_info? info.user_info.ext.cover:''" mode="aspectFill" />
			<view class="" style="height: 95rpx;" />
			<view class="head">
				<view class=""></view>
				<view style="display: flex;">
					<image class="rightIcon"
						@click="goNav(vipLevel >= 3 ? '/pages/visitor/visitor' : '/pages/visitor/noVipVisitor')"
						src="../../static/images/my/otheradd.png" mode="" style="margin-right: 40rpx;">
					</image>
					<image class="rightIcon" @click="showDrawer" src="../../static/images/my/more.png" mode=""></image>
				</view>
			</view>
			<view class="body">
				<view style="padding: 0 32rpx;transform: translateY(-495rpx);">
					<view class="ava">
						<image class="avatarS" :src="info.user_info?info.user_info.avatar+'?t='+Date.now():''"
							mode="aspectFill">
						</image>
						<image @click="goNav('/pages/editInfo/editInfo')" class="edit"
							src="../../static/images/edit.png" mode="aspectFill">
						</image>
					</view>
					<view class="name " v-if="info.user_info">
						{{info.user_info.nickname}}
						<image class="img36 logo" :src="'../../static/images/vip/vipLogo'+vipLevel+'.png'" mode="">
						</image>
					</view>
					<view class="idInfo t_display" v-if="info.user_info">
						ID:{{info.user_info.uuid}}
						<image @click="goNav('/pages/my/myScan')" class="img32" src="../../static/images/my/scan.png"
							mode="" style="margin-left: 16rpx;">
						</image>
					</view>
					<view class="introduce" v-if="info.user_info" style="margin-top: 20rpx;width: 686rpx;">
						{{info.user_info.ext.introduction}}
					</view>
					<view class="t_display" style="margin-top: 16rpx;">
						<view class="t_display" style="margin-right: 42rpx;" v-if="info.geo_info.province">
							<image class="icon" src="../../static/images/address.png" mode=""></image>
							<view class="introduceS">
								{{info.geo_info.province}}
							</view>
						</view>
						<view class="t_display" style="margin-right: 42rpx;" v-if="info.user_info.ext.birthday">
							<image class="icon" src="../../static/images/birthday.png" mode=""></image>
							<view class="introduceS">
								{{info.user_info.ext.birthday}}
							</view>
						</view>
					</view>
					<view class="t_display" style="margin-top: 20rpx;" v-if="info.user_info.home_town_format">
						<image class="icon" src="../../static/images/map.png" mode=""></image>
						<view class="introduceS" style="width: 500rpx;">
							来自于 {{info.user_info.home_town_format}}
						</view>
					</view>
					<view class="t_display" style="margin-top: 16rpx;" @click="goNav('/pages/follow_list/follow_list')">
						<view class="t_display" style="margin-right: 26rpx;">
							<view>{{info.relation.follow}}</view>
							<view class="introduceS">关注</view>
						</view>
						<view class="t_display" style="margin-right: 26rpx;">
							<view>{{info.relation.fans}}</view>
							<view class="introduceS">粉丝</view>
						</view>
						<view class="t_display">
							<view>{{info.relation.friend}}</view>
							<view class="introduceS">朋友</view>
						</view>
					</view>
					<view style="font-size: 26rpx;margin-top: 28rpx;">
						相册
					</view>
					<view class="">
						<scroll-view class="scroll-view_H" scroll-x="true">
							<image @click="upload" src="../../static/images/addBig.png" class="scroll-view-item_H"
								style="transform: translateY(10rpx);" mode="aspectFill">
							</image>
							<view class="scroll-view-item_H" style="transform: translateX(-20rpx);"
								v-for="(src,index) in imgArr" :key="index">
								<image class="img" :src="src.photoPath" mode="aspectFill" @click="preview(index)">
								</image>
								<image class="close" @click="delImg(src.id)" src="../../static/images/close.png"
									mode="aspectFill">
								</image>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
			<view class="" style="transform: translateY(-500rpx);overflow: hidden;">
				<Tabs :tabs="tabList" :current="swiperCurrent" @setCurrent="tabsChange" />
				<view v-if="!show">
					<view v-if="swiperCurrent==0">
						<view class="nullMoment" v-if="!dataArrS.length">
							<image class="img124" src="../../static/images/wudongtai.png" mode=""></image>
							<view class="tit">还没有发布作品</view>
							<view class="momentBtn" @click="goNav('/pages/send/send')">
								去发布
							</view>
						</view>
						<Post :showVipLogo="false" :list="dataArrS" @more="more" @scrolltolower="scrolltolowerS"
							:previewFlag="true" v-else @share="share" @setLike="setLikeS" style="margin-bottom: 150rpx;"
							@goAddress="goNavLoaction">
						</Post>
					</view>
					<Post @scrolltolower="scrolltolowerB" :list="dataArrB" @more="more" :previewFlag="true"
						@share="share" v-else-if="swiperCurrent==1" @setLike="setLikeB" style="margin-bottom: 150rpx;"
						@goAddress="goNavLoaction">
					</Post>
				</view>
				<Load text="加载中.." :mask="true" :show="show" ref="loading" />
			</view>
		</scroll-view>
		<uni-drawer ref="showRight" mode="right">
			<scroll-view style="height: 100%;padding: 100rpx 32rpx;" scroll-y="true">
				<Menu @goNav="closeDrawer" @popup="menuPopup"></Menu>
				<div style="height: 100px;"></div>
			</scroll-view>
		</uni-drawer>
		<MorePopup ref="morePopup" :isUp="tempInfo.up==1" @edit="edit" @first="first" @top="topActirle" @del="delMoment"
			@choose="seePopChoose"></MorePopup>
		<SeePopup ref="seePopup" @choose="seePopChoose"></SeePopup>
		<sharePopup ref="share" :post="shareItem"></sharePopup>
		<u-toast ref='notify' />
		<Ypopup ref="ypopRef" />
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirm" />
	</view>
</template>
<script>
	import {
		config
	} from "@/config.js"
	import Post from "@/components/post/post.vue"
	import MorePopup from "../my/components/morePopup.vue"
	import Menu from "../my/components/menu.vue"
	import Tabs from "../my/components/tabs.vue"
	import SeePopup from "../my/components/seeLook.vue"
	import Load from "../my/components/t_loading.vue"
	import Ypopup from "../my/components/ydPopup.vue"
	export default {
		components: {
			Ypopup,
			Menu,
			Load,
			Tabs,
			Post,
			MorePopup,
			SeePopup,
		},
		watch: {
			swiperCurrent: {
				handler() {
					this.show = true
					if (this.swiperCurrent == 0) {
						this.dataArrS = []
						this.totalS = true
						this.pageS = 1
						this.getDynamic()
					} else if (this.swiperCurrent == 1) {
						this.dataArrB = []
						this.totalB = true
						this.pageB = 1
						this.getLikeInfo()
					}
					setTimeout(() => {
						this.show = false
					}, 500)
				}
			}
		},
		data() {
			return {
				imgCurrent: 0,
				show: true,
				loading: true,
				vipLevel: "",
				skeleton: [{
					type: 'line',
					num: 3,
					gap: '20rpx'
				}],
				dataArrB: [],
				totalB: true,
				pageB: 1,
				dataArrS: [],
				totalS: true,
				pageS: 1,
				fixed: false,
				page: "/pages/my/my",
				scrollTop: 0, // 页面滚动距离
				dataArr: [],
				barStyle: {
					height: "1px",
					background: "linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)",
				},
				tabList: [{
					name: '动态'
				}, {
					name: '喜欢'
				}],
				ImgUrl: 'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/',
				info: {
					geo_info: {
						province: ''
					},
					user_info: {
						uid: 0,
						ext: {
							birthday: ""
						}
					},
					ext: {},
					relation: {}
				},
				statusBarHeight: 0,
				imgArr: [],
				photoArr: [],
				shareItem: {},
				uploadArr: [],
				index: 0,
				list: [{
					name: '动态'
				}, {
					name: '喜欢'
				}],
				tempInfo: "",
				// 因为内部的滑动机制限制，请将tabs组件和swiper组件的current用不同变量赋值
				current: 0, // tabs组件的current值，表示当前活动的tab选项
				swiperCurrent: 0, // swiper组件的current值，表示当前那个swiper-item是活动的
			}
		},
		created() {
			console.log('------created------------');
			const system = uni.getStorageSync('system')
			this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
			this.dataArrS = []
			this.dataArrB = []
			this.getUpdate()
		},
		methods: {
			goNavLoaction(item) {
				this.$emit('goAddress', item)
			},
			confirm() {
				uni.navigateTo({
					url: '/pages/vipCenter/vipCenter'
				})
			},
			edit() {
				const vipLevel = uni.getStorageSync('userInfo').vip_level
				if ([2, 3].includes(vipLevel)) {
					uni.navigateTo({
						url: "/pages/send/send?pages=4&momentId=" + this.tempInfo.moment_id
					})
				} else {
					this.imgCurrent = 1
					this.$refs.vipTips.open()
				}
				this.$refs.morePopup.close()

			},
			confirmVip() {

			},
			topActirle(flag) {
				const vipLevel = uni.getStorageSync('userInfo').vip_level
				if ([2, 3].includes(vipLevel)) {
					if (flag) {
						//取消置顶
						this.$http.post('/api/moment/cancel-up', {
							momentId: this.tempInfo.moment_id,
						}).then(res => {
							// this.$set(this.dataArrS, this.tempInfo.index, {
							// 	...this.dataArrS[this.tempInfo.index],
							// 	up: 0
							// })
							this.dataArrS = []
							this.pageS = 1
							this.getDynamic(1)
						})
					} else {
						this.$http.post('/api/moment/up', {
							momentId: this.tempInfo.moment_id,
						}).then(res => {
							this.show = true
							// this.$set(this.dataArrS, this.tempInfo.index, {
							// 	...this.dataArrS[this.tempInfo.index],
							// 	up: 1
							// })
							this.dataArrS = []
							this.pageS = 1
							console.log('-------置顶成功--------', this.pageS);
							this.getDynamicOne()
							setTimeout(() => {
								this.show = false
								this.toast('置顶成功')
							}, 300)
						})
					}

					this.$refs.morePopup.close()
				} else {
					this.imgCurrent = 6
					this.$refs.morePopup.close()
					this.$refs.vipTips.open()
				}

			},
			menuPopup(type) {
				this.$refs.showRight.close();
				switch (type) {
					case "newGuide":
						this.$refs.ypopRef.open()
						break;
					default:
						break;
				}
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			showDrawer() {
				this.$refs.showRight.open();
			},
			closeDrawer() {
				this.$refs.showRight.close();
			},
			async getUpdate() {
				console.log('-----------my getUpdate--------------', this.pageS);
				this.dataArrS = []
				this.totalS = true
				this.dataArrB = []
				this.totalB = true
				this.pageS = 1
				this.pageB = 1
				this.show = true
				await this.getData({
					pageS: this.pageS,
					pageB: this.pageB
				})

			},
			preview(current) {
				uni.previewImage({
					current,
					urls: this.photoArr,
				});
			},
			setLikeS(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					like: item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrS, item.index, {
						...this.dataArrS[item.index],
						is_like: !item.isLike,
						like: this.dataArrS[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			delMoment() {
				this.$http.post('/api/moment/del', {
					momentId: this.tempInfo.moment_id
				}).then(res => {
					this.toast('删除成功')
					this.dataArrS.splice(this.tempInfo.index, 1)
					this.$refs.morePopup.close()
				})
			},
			setLikeB(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					"like": item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrB, item.index, {
						...this.dataArrB[item.index],
						is_like: !item.isLike,
						like: this.dataArrB[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			async getData(page) {

				await this.$http.get('/api/user/info').then(async (res) => {
					this.info = res.message
					console.log('个人信息：', this.info);
					this.vipLevel = res.message.user_info.vip_level
					this.pageS = 1
					this.pageB = 1

					await this.getPhoto()
					this.getDynamic(this.pageS)
					this.getLikeInfo()
					setTimeout(() => {
						this.show = false
					}, 500)
				})
			},
			share(item) {
				this.shareItem = item
				this.$refs.share.open()
			},
			currentGetData() {
				if (this.swiperCurrent == 0) {
					this.scrolltolowerS()
				} else if (this.swiperCurrent == 1) {
					this.scrolltolowerB()
				}
			},
			scrolltolowerB() {
				if (this.totalB) {
					this.pageB += 1;
					this.getLikeInfo()
				}
			},
			scrolltolowerS() {
				if (this.totalS) {
					this.pageS += 1;
					this.getDynamic()
				}
			},
			getDynamicOne() {
				this.$http.get('/api/moment/user/list', {
					uid: this.info.user_info.uid,
					page: 1,
				}).then(res => {
					this.dataArrS = res.message

					this.totalS = !!res.message.length
				})
			},
			getDynamic() {
				this.$http.get('/api/moment/user/list', {
					uid: this.info.user_info.uid,
					page: this.pageS,
				}).then(res => {
					if (this.pageS == 1) {
						this.dataArrS = res.message
					} else {
						this.dataArrS.push(...res.message)
					}

					this.totalS = !!res.message.length
				})
			},
			getLikeInfo() {
				this.$http.get('/api/moment/user/like/list', {
					uid: this.info.user_info.uid,
					page: this.pageB,
				}).then(res => {
					if (this.pageB == 1) {
						this.dataArrB = res.message
					} else {
						this.dataArrB.push(...res.message)
					}
					this.totalB = !!res.message.length
				})
			},
			seePopChoose(role) {
				console.log('this.tempInfo', this.tempInfo);
				this.$http.post('/api/moment/update-visible', {
					momentId: this.tempInfo.moment_id,
					role
				}).then(res => {
					this.$refs.morePopup.close()
					this.toast('设置成功')
					this.$refs.seePopup.close()
					if (this.swiperCurrent == 0) {
						this.dataArrS[this.tempInfo.index].role = role
					} else if (this.swiperCurrent == 1) {
						this.dataArrB[this.tempInfo.index].role = role
					}
				})
			},
			first() {
				this.$refs.morePopup.close()
				this.$refs.seePopup.open()
			},
			more(item) {
				this.tempInfo = item
				this.$refs.morePopup.open()
			},
			setMomentLook() {
				this.$http.post('/api/user/del-photo', {
					id
				}).then(res => {
					this.getPhoto()
				})
			},
			unfixed(ids) {
				this.fixed = false
			},
			setFixed(flag) {
				this.fixed = true
			},
			// swiper-item左右移动，通知tabs的滑块跟随移动
			transition(e) {
				let dx = e.detail.current;
				this.swiperCurrent = dx
				// this.$refs.uTabs.setDx(dx);
			},
			// 由于swiper的内部机制问题，快速切换swiper不会触发dx的连续变化，需要在结束时重置状态
			// swiper滑动结束，分别设置tabs和swiper的状态
			// animationfinish(e) {
			// 	let current = e.detail.current;
			// 	this.$refs.uTabs.setFinishCurrent(current);
			// 	this.swiperCurrent = current;
			// 	this.current = current;
			// },
			// scroll-view到底部加载更多
			onreachBottom() {

			},
			// tabs通知swiper切换
			async tabsChange(index) {
				this.swiperCurrent = index;
				this.pageS = 1
				this.pageB = 1
			},
			delImg(id) {
				this.$http.post('/api/user/del-photo', {
					id
				}).then(res => {
					this.getPhoto()
				})
			},
			getPhoto() {
				this.$http.get('/api/user/get-photo', {
					uid: this.info.user_info.uid
				}).then(res => {
					this.imgArr = res.message
					this.photoArr = res.message.map(item => item.photoPath)
				})
			},
			goNav(url) {
				if (!this.vipLevel) return
				this.navigateTo({
					url
				})
			},
			upload() {
				uni.chooseImage({
					count: 6,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let arr = []
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							arr.push({
								"photo_path": img,
								"id": 0
							})
						}, ))
						this.$http.post('/api/user/add-photo', {
							photos: arr
						}).then(res => {
							this.getPhoto()
						})
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.nullMoment {
		width: 750rpx;
		height: 550rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.momentBtn {
			width: 228rpx;
			height: 70rpx;
			line-height: 70rpx;
			background: #272727;
			border-radius: 16rpx;
			text-align: center;
			margin-top: 32rpx;
		}

		.tit {
			font-size: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #A5A5A5;
			margin-top: 40rpx;
		}
	}

	.scroll-view_H {
		margin-top: 12rpx;
		white-space: nowrap;
		width: 100%;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 153rpx;
		height: 153rpx;
		padding: 10rpx;
		text-align: center;
		border-radius: 14rpx;
		// background-color: #777883;
		margin-right: 30rpx;
		position: relative;

		.img {
			width: 153rpx;
			height: 153rpx;
			border-radius: 14rpx;
		}
	}

	.close {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		right: -25rpx;
		top: -3rpx;
		// transform: translateY(-15rpx);

	}

	.icon {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}

	// .appPage {
	// 	height: 60vh;
	// 	width: 100%;
	// 	display: flex;
	// 	flex-direction: column;

	.picture {
		width: 100%;
		white-space: nowrap;
		margin-top: 12rpx;
		height: 153rpx;
	}

	.bg {
		width: 750rpx;
		height: 495rpx;
		// height: 264rpx;
		position: absolute;
	}

	/* 利用flex布局, */
	.sColumn {
		display: flex;
		flex: 1;
	}

	.body {
		display: flex;
		flex: 1;
		width: 100%;
		margin-top: 733rpx;
		background-color: #191C26;
		flex-direction: column;
		position: relative;

		// z-index: 999;

		.addImg {
			width: 154rpx;
			height: 154rpx;
			background: #777883;
			border-radius: 14rpx;
			display: inline-block;
			margin-right: 16rpx;
		}

		.introduce {
			font-size: 25rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: rgba(255, 255, 255, 0.64);
			line-height: 40rpx;
		}

		.introduceS {
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			margin-left: 10rpx;
			font-size: 25rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: rgba(255, 255, 255, 0.64);
			line-height: 40rpx;
		}

		.idInfo {
			font-size: 26rpx;
			font-family: Source Han Sans-Medium, Source Han Sans;
			font-weight: 500;
			color: #767676;
			line-height: 40rpx;
			margin-top: 8rpx;
		}

		.name {
			font-size: 48rpx;
			font-family: Source Han Sans-Bold, Source Han Sans;
			font-weight: 700;
			color: #FFFFFF;
			margin-top: 23rpx;
			display: flex;
			align-items: center;

			.logo {
				margin-left: 32rpx;
			}
		}

		.ava {
			display: flex;
			justify-content: space-between;

			align-items: flex-end;

			.avatarS {
				width: 180rpx;
				height: 180rpx;
				border-radius: 50%;
				border: 10rpx solid #191C26;
			}

			.edit {
				width: 56rpx;
				height: 56rpx;

			}
		}

	}

	.head {
		height: 100rpx;
		padding: 20rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		// transform: translateY(-60rpx);
	}

	.rightIcon {
		width: 76rpx;
		height: 76rpx;
	}

	// }

	//=================================================================

	.container {
		// height: 200vh;
		// margin-top: 150rpx;
	}

	.sticky {
		// width: 750rpx;
		// height: 120rpx;
		// margin-top: 45rpx;
	}
</style>