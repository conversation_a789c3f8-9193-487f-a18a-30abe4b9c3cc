<template>
	<view class="appPage">
		<!-- <view class="item">
			自动更新您常用的地址
			<switch checked="true" @change="" style="transform:scale(0.9)" color="#4BC6ED" />
		</view> -->
		<view class="item" v-if="!showNewPwd">
			设置密码
			<uni-icons type="forward" color="" @click="goNav('/pages/newPassword/newPassword')"></uni-icons>
		</view>
		<view class="item" v-else>
			密码修改
			<uni-icons type="forward" color="" @click="goNav('/pages/editPassword/editPassword')"></uni-icons>
		</view>
		<view class="item">
			黑名单
			<uni-icons type="forward" color="" @click="goNav('/pages/blacklist/blacklist')"></uni-icons>
		</view>
		<view class="title">
			账号
		</view>
		<view class="item">
			注销账号
			<uni-icons type="forward" color="" @click="goCancelOut"></uni-icons>
		</view>
		<view class="item" style="color: #E82E29;">
			退出登录
			<uni-icons type="forward" color="#fff" @click="showQuit"></uni-icons>
		</view>
		<uni-popup ref="alertDialog" type="dialog">
			<uni-popup-dialog type="info" :showTitle="false" cancelText="取消" confirmText="同意" content="确定注销账号"
				@confirm="dialogConfirm" @close="dialogClose"></uni-popup-dialog>
		</uni-popup>
		<uni-popup ref="quitDialog" type="dialog">
			<uni-popup-dialog type="info" :showTitle="false" cancelText="取消" confirmText="确定" content="确定退出账号"
				@confirm="toLogut" @close="dialogClose"></uni-popup-dialog>
		</uni-popup>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showNewPwd: "",
			}
		},
		onLoad() {
			const userInfo = uni.getStorageSync('userInfo')
			console.log('userInfo', userInfo);
			this.showNewPwd = userInfo.has_set_password
		},
		onShow() {
			this.$http.get('/api/user/info').then(res => {
				const {
					has_set_password
				} = res.message.user_info
				console.log('has_set_password', has_set_password);
				this.showNewPwd = has_set_password
			})
		},
		methods: {
			dialogClose() {

			},
			dialogConfirm() {
				this.$http.post('/api/user/disable').then(res => {
					this.toast('注销成功')
					setTimeout(() => {
						uni.reLaunch({
							url: "/pages/login/login"
						})
					}, 500)
				})
			},
			showQuit() {
				this.$refs.quitDialog.open()
			},
			goCancelOut() {
				this.$refs.alertDialog.open()
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			toLogut() {
				uni.clearStorageSync('RefreshToken')
				uni.clearStorageSync('token')
				uni.clearStorageSync('im')
				uni.clearStorageSync('firstRegister')
				this.$store.dispatch('SET_YXIM_DISCONNECT')
				uni.reLaunch({
					url: "/pages/login/login"
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
	.title {
		padding: 0 32rpx;
		height: 94rpx;
		font-size: 32rpx;
		line-height: 94rpx;
		background: #201F1F;
	}

	.item {
		padding: 41rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-family: Source Han Sans-Medium, Source Han Sans;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 41rpx;
	}
</style>