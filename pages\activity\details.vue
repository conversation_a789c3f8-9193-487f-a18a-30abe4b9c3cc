<template>
	<view class="appPage">
		<u-navbar :customBack="goBack" back-text="" title="发布" :background="{ backgroundColor: '#191C26' }"
			:border-bottom="false" height="60" title-color="#fff" back-icon-color="#fff">
			<view slot="content">
				<view class="head">
					<view class="title">活动详情</view>
				</view>
			</view>
			<view class="navbar-right" slot="right">
				<image class="more" src="../../static/images/report/gengduo.png" alt=""
					@click="showActionMenu = true" />
			</view>
		</u-navbar>

		<!-- 自定义操作菜单弹窗 -->
		<view class="action-menu-mask" v-if="showActionMenu" @click="showActionMenu = false"></view>
		<view class="action-menu" v-if="showActionMenu">
			<view class="action-item" v-for="(item, index) in actionMenuItems" :key="index"
				@click="handleActionItem(item)">
				<image class="action-icon" :src="item.icon" mode="aspectFit"></image>
			</view>
		</view>

		<view class="content">
			<view class="child item" style="" @click="toChildList(mainId)">
				<view class="t_display">
					<image src="../../static/active/svg.png" mode="aspectFill"
						style="width: 28rpx; height: 28rpx; margin-right: 20rpx"></image>
					{{ activeData.activity.series_title }}
				</view>
				<view class="t_display">
					<uni-icons type="right" color="rgba(255, 255, 255, 0.74)"></uni-icons>
				</view>
			</view>
			<view class="list">
				<image :src="activeData.activity.cover" mode="aspectFill"
					@click="preview(1, activeData.activity.cover, 1)" style="
            width: 244rpx;
            height: 244rpx;
            margin-right: 20rpx;
            border-radius: 12rpx;
          ">
				</image>

				<view class="active">
					<view class="title">
						{{ activeData.card.title }}
					</view>
					<view class="t-distance"> 活动ID: {{ activeData.card.uuid }} </view>
					<view class="avatar" @click="toMember(activeData.card.uuid)">
						<image v-for="(src, index) in activeData.card.member_avatar" :src="src" mode="aspectFill"
							:style="{ left: index * 20 + 'rpx' }"></image>
						<span v-if="activeData.card.people_cnt >= 5" style="
                width: 40rpx;
                height: 40rpx;
                text-align: center;
                line-height: 25rpx;
                position: absolute;
                top: 0;
                left: 80rpx;
                background-color: rgb(180, 180, 180);
                border-radius: 50%;
              ">
							...
						</span>
						<span :style="{
                marginLeft:
                  activeData.card.member_avatar.length * 20 + 40 + 'rpx',
                color: 'rgb(224, 224, 224)',
                fontSize: '18rpx',
              }">共{{ activeData.card.people_cnt }}人</span>
					</view>
				</view>
			</view>
			<!-- 需要门票 -->
			<view v-if="activeData.activity.price > 0">
				<view v-if="!self && !activeData.order_no" class="ticket_unjoin" @click="join_ticket">
					门票：¥{{ activeData.activity.price }}
				</view>
				<view v-else style="display: flex; align-items: center; margin-bottom: 40rpx">
					<view class="ticket_joined" style="flex: 1; margin-right: 32rpx" @click="joinGroup">
						进入群聊
					</view>
					<view v-if="!self" style="
              background: rgb(255, 226, 202);
              height: 94rpx;
              width: 94rpx;
              border-radius: 20rpx;
              display: flex;
              justify-content: center;
              align-items: center;
            ">
						<image style="width: 40rpx; height: 40rpx" src="../../static/active/scan_group.png"
							mode="aspectFill" @click="goOrderDetail"></image>
					</view>
				</view>
			</view>
			<view v-else>
				<view class="item join" style="font-size: 30rpx" v-if="!self && !isJoin" @click="join">
					参与
				</view>
				<!-- 		<view class="isJoin" style="font-size: 30rpx; background-color: rgb(88, 117, 128); text-align: center;"
							v-else>
							已加入
						</view> -->
				<view v-else class="joinGroup" @click="joinGroup"> 进入群聊 </view>
			</view>

			<view class="item" style="
          display: flex;
          justify-content: space-between;
          padding: 22rpx 40rpx 22rpx 40rpx;
        ">
				<view class="t_display">
					<image style="width: 24rpx; height: 24rpx; margin-right: 20rpx" src="../../static/images/watch.png"
						mode="aspectFill"></image>
					<view class="textC"> 活动时间 </view>
				</view>
				<view class="t_display" style="width: 60%; text-align: right">
					<textarea class="textC" style="width: 100%; margin-right: 10rpx" v-model="activeData.card.timeline"
						auto-height disabled>
						<!-- {{ activeData.card.timeline }} -->
					</textarea>
				</view>
			</view>
			<view class="item">
				<view class="address" style="display: flex; justify-content: space-between">
					<view class="t_display">
						<image style="width: 24rpx; height: 24rpx; margin-right: 20rpx"
							src="../../static/images/localtions.png" mode="aspectFill">
						</image>
						<view class="textC"> 活动地点 </view>
					</view>
					<view class="t_display" style="width: 60%">
						<view class="textC" style="width: 100%; margin-right: 10rpx; text-align: right">
							{{ activeData.activity.address }}
						</view>
					</view>
				</view>
				<view class="map-container" @click="toMap">
					<image :src="activeData.static_map_img" mode="aspectFill" style="height: 100%; width: 100%">
					</image>
					<!-- <map :latitude="latitude" :longitude="longitude" :scale="16" :markers="markers"
						style="width: 100%; height: 100%; border-radius: 15px;"></map> -->
					<cover-view class="leftTop"></cover-view>
				</view>
			</view>
			<view class="">
				<view class="">
					<image src="../../static/active/content.png" mode="aspectFill"
						style="width: 28rpx; height: 28rpx; margin-right: 20rpx"></image>
					活动内容
				</view>
				<view class="item" style="margin-top: 20rpx; width: 100%; word-wrap: break-word">
					{{ activeData.activity.content }}
				</view>
			</view>
			<view class="" v-if="activeData.location_img.length > 0">
				<view class="">
					<image src="../../static/active/pic.png" mode="aspectFill"
						style="width: 28rpx; height: 24rpx; margin-right: 20rpx"></image>
					地点图片
				</view>
				<!-- <view style="width: 100%; display: flex; flex-wrap: wrap;"> -->
				<view class="item" style="margin-top: 20rpx; display: flex; flex-wrap: wrap">
					<view v-for="(item, index) in activeData.location_img" :key="index" :class="
              (index + 1) % 3 === 0
                ? location_img_hide === 1
                  ? 'img-wrapper mh1-wrapper'
                  : 'img-wrapper mh2-wrapper'
                : location_img_hide === 1
                ? 'img-wrapper mh3-wrapper'
                : 'img-wrapper mh4-wrapper'
            " @click="
              () => {
                preview(index, activeData.location_img, 0);
              }
            ">
						<template v-if="location_img_hide === 1">
							<!-- 正常图片（不模糊） -->
							<image v-if="(index + 1) % 3 === 0" :src="item" mode="aspectFill" class="mh2 blur-base">
							</image>
							<image v-else :src="item" mode="aspectFill" class="mh4 blur-base"></image>

							<!-- 模糊层 -->
							<view class="blur-overlay"></view>
						</template>
						<template v-else>
							<image :src="item" mode="aspectFill" :class="
                  (index + 1) % 3 === 0 ? 'mh2' : 'mh4'
                "></image>
						</template>
					</view>
				</view>
				<!-- </view> -->
			</view>
			<view class="" v-if="activeData.activity_img.length > 0">
				<image src="../../static/active/pic.png" mode="aspectFill"
					style="width: 28rpx; height: 24rpx; margin-right: 20rpx"></image>
				活动展示图片
				<view class="item" style="margin-top: 20rpx; display: flex; flex-wrap: wrap">
					<image v-for="(item, index) in activeData.activity_img" :src="item" mode="aspectFill"
						@click="previewArr(index, activeData.activity_img)" :style="
              (index + 1) % 3 === 0
                ? 'margin-right: 0rpx;width: 170rpx; height: 170rpx; margin-bottom: 40rpx; border-radius: 24rpx;'
                : 'margin-right: 40rpx; width: 170rpx; height: 170rpx; margin-bottom: 34rpx; border-radius: 24rpx;'
            ">
					</image>
				</view>
			</view>
		</view>
		<view class="comments" style="padding: 20px">
			<view style="height: 60rpx"> 评论区 </view>
			<scroll-view @scrolltolower="scrolltolower" :refresher-threshold="150" :scroll-y="true"
				refresher-background="grey41" style="width: 100%; height: 30vh" v-if="commentsArr.length > 0">
				<view class="comments_list" v-for="(item, index) in commentsArr" style="
            display: flex;
            justify-content: space-between;
            padding-top: 10rpx;
          ">
					<image :src="item.userinfo.avatar" mode="aspectFill" style="
              width: 80rpx;
              height: 80rpx;
              margin-right: 20rpx;
              border: 1px solid white;
              border-radius: 50%;
            "></image>
					<view class="" style="width: 570rpx">
						<view style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 42rpx;
                margin-top: 30rpx;
              ">
							<span style="font-size: 26rpx; line-height: 36rpx">{{
                item.userinfo.nickname
              }}</span>
							<span style="font-size: 10px; color: rgb(169, 169, 169)">{{
                item.created_at
              }}</span>
						</view>
						<view v-if="index + 1 === commentsArr.length"
							style="padding-bottom: 20px; font-size: 26rpx; line-height: 36rpx">
							{{ item.content }}
						</view>
						<view v-else style="
                border-bottom: 2rpx solid rgba(196, 196, 196, 0.12);
                padding-bottom: 20px;
                font-size: 26rpx;
                line-height: 36rpx;
              ">
							{{ item.content }}
						</view>
					</view>
				</view>
			</scroll-view>
			<view v-else style="text-align: center; padding-bottom: 40rpx">
				<image src="../../static/active/zwpj.png" mode="aspectFill" style="
            width: 274rpx;
            height: 80rpx;
            margin-top: 140rpx;
            margin-bottom: 108rpx;
          ">
				</image>
			</view>
			<view class="send" style="
          display: flex;
          justify-content: space-between;
          margin-top: 10rpx;
          margin-bottom: 60rpx;
        ">
				<view class="input-box" style="
            width: 267px;
            height: 40px;
            border-radius: 60px;
            background: rgb(56, 58, 67);
            position: relative;
            margin-right: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
          ">
					<image :src="avatar" mode="aspectFill" style="
              width: 24px;
              height: 24px;
              border-radius: 20px;
              margin-left: 10px;
            ">
					</image>
					<input v-model="sendMsg" type="text" placeholder="请输入评论内容" @input="message" :border="false"
						style="line-height: 30px; height: 30px; width: 80%" clearable />
				</view>
				<view class="btn_send" style="
            width: 100px;
            height: 40px;
            border-radius: 60px;
            line-height: 40px;
            text-align: center;
            background: linear-gradient(
              90deg,
              rgb(79, 197, 238),
              rgb(191, 147, 250) 100%
            );
          " @click="send">发布</view>
			</view>
		</view>

		<!-- 活动卡片分享 -->
		<!-- <view class="share-btn" @click="openShare">
			<image src="../../static/images/share.png" mode="aspectFill"></image>
		</view> -->

		<!-- 活动卡片画布（用于生成分享图片） -->
		<view class="card-canvas-container" :style="{ display: 'none' }">
			<canvas canvas-id="cardCanvas" id="cardCanvas" style="width: 600rpx; height: 800rpx"></canvas>
		</view>
		<!-- 分享组件 -->
		<mark-simpleshare ref="shareComponent" type="activity"></mark-simpleshare>

		<Uqrcode ref="uqrcode" canvas-id="qrcode" size="500" :value="href" :options="{ margin: 10 }" hide>
		</Uqrcode>

		<!-- 购买门票弹窗 -->
		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showPay">
			<view style="overflow: hidden">
				<view style="
            border-radius: 20rpx 20rpx 0 0;
            background: linear-gradient(
              90deg,
              rgb(251, 245, 230),
              rgb(228, 247, 255) 100%
            );
            height: 200rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          ">
					<view>
						<text style="color: rgb(33, 33, 33); font-size: 32rpx; font-weight: 500">¥</text>
						<text
							style="color: rgb(33, 33, 33); font-size: 68rpx; font-weight: 700">{{ activeData.activity.price }}</text>
					</view>
					<view style="color: rgb(33, 33, 33); font-size: 26rpx; font-weight: 500">确认购买该门票</view>
				</view>

				<view style="padding: 0rpx 32rpx">
					<view style="height: 2rpx; background: rgb(244, 239, 246)"></view>
					<text style="
              font-size: 28rpx;
              color: rgb(102, 102, 102);
              font-weight: 400;
              display: block;
              margin-top: 24rpx;
            ">活动名称</text>
					<text style="
              font-size: 28rpx;
              color: rgb(33, 33, 33);
              font-weight: 400;
              display: block;
              margin-top: 24rpx;
            ">{{ activeData.activity.title }}</text>
					<view style="
              height: 2rpx;
              background: rgb(244, 239, 246);
              margin-top: 32rpx;
            "></view>
					<view style="
              display: flex;
              justify-content: space-between;
              padding: 48rpx 0;
            ">
						<text style="
                font-size: 28rpx;
                color: rgb(102, 102, 102);
                font-weight: 400;
              ">活动时间</text>
						<text
							style="font-size: 28rpx; color: rgb(33, 33, 33); font-weight: 400">{{ activeData.card.timeline }}</text>
					</view>
					<view style="height: 2rpx; background: rgb(244, 239, 246)"></view>
					<view style="
              display: flex;
              justify-content: space-between;
              padding: 32rpx 0;
            ">
						<text style="
                font-size: 28rpx;
                color: rgb(102, 102, 102);
                font-weight: 400;
              ">我的余额</text>
						<text
							style="font-size: 28rpx; color: rgb(33, 33, 33); font-weight: 500">￥{{ leftAmount }}</text>
					</view>
					<view v-if="activeData.activity.price > leftAmount"
						style="border: 1px solid rgb(255, 76, 76 border-radius: 4px;background: rgb(255, 243, 243); padding: 24rpx; display: flex; justify-content: space-between; align-items: center;">
						<view style="color: rgb(33, 33, 33); font-weight: 500; font-size: 28rpx">当前余额不足</view>
						<view style="
                color: rgb(100, 61, 27);
                font-size: 20rpx;
                padding: 14rpx 24rpx;
                border-radius: 24rpx;
                background: linear-gradient(
                  90deg,
                  rgb(249, 225, 202),
                  rgb(241, 185, 137) 100%
                );
              " @click="goPay">立即充值<u-icon name="arrow-right"></u-icon></view>
					</view>

					<view style="display: flex; justify-content: center">
						<text style="
                font-size: 24rpx;
                color: rgb(102, 102, 102);
                font-weight: 400;
                margin-top: 32rpx;
              "><text style="color: rgb(255, 76, 76)">*</text>门票过期未核验，将自动退款。</text>
					</view>
					<view v-if="activeData.activity.price <= leftAmount" style="
              margin-top: 24rpx;
              margin-bottom: 32rpx;
              width: 100%;
              border-radius: 16rpx;
              background: rgb(255, 76, 76);
              color: white;
              font-size: 26rpx;
              display: flex;
              justify-content: center;
              padding: 26rpx;
            " @click="payTicket">
						确认支付</view>
					<view v-else style="
              margin-top: 24rpx;
              margin-bottom: 32rpx;
              width: 100%;
              border-radius: 16rpx;
              background: rgb(216, 203, 203);
              color: white;
              font-size: 26rpx;
              display: flex;
              justify-content: center;
              padding: 26rpx;
            ">
						确认支付</view>
				</view>
			</view>
		</u-popup>
		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showFinish">
			<view>
				<view class="pop-content-exit">
					<view class="pop-title">确认结束活动</view>
				</view>
				<view class="pop-note">注：已参加此活动的人<text style="color: rgb(190, 147, 250)">仍可以看到此活动内容</text>，但<text
						style="color: rgb(190, 147, 250)">不会再推荐显示</text></view>
				<view class="pop-button">
					<view class="fail" @click="showFinish = false">取消</view>
					<view class="success" @click="finishConfirm">确认</view>
				</view>
			</view>
		</u-popup>
		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showExit">
			<view>
				<view class="pop-content-exit">
					<view class="pop-title">确认退出活动</view>
				</view>
				<view class="pop-note">注：如果为付费活动，退出活动后再次进入需要<text style="color: rgb(190, 147, 250)">重新购买门票</text></view>
				<view class="pop-button">
					<view class="fail" @click="showExit = false">取消</view>
					<view class="success" @click="exitConfirm">确认</view>
				</view>
			</view>
		</u-popup>

		<u-popup mode="center" width="90%" :closeable="true" border-radius="20" v-model="showExit2">
			<view>
				<view class="pop-content-exit">
					<view class="pop-title">确认退款</view>
				</view>
				<view class="pop-note">注：退款后钱款会<text style="color: rgb(190, 147, 250)">原额返回</text>钱包内</view>
				<view class="pop-button">
					<view class="fail" @click="showExit2 = false">取消</view>
					<view class="success" @click="exitConfirm2">确认</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import markSimpleshare from "@/components/mark-simpleshare/mark-simpleshare.vue";
	import Uqrcode from "@/components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue";
	export default {
		components: {
			Uqrcode,
			markSimpleshare,
		},
		data() {
			return {
				uuid: "",
				avatar: "",
				href: "",
				activeData: {
					activity: {
						cover: "",
						address: "",
						content: "",
					},
					card: {
						series_title: "",
						title: "",
						uuid: "",
						people_cnt: "",
						timeline: "",
						member_avatar: [],
					},
					location_img: [],
					activity_img: [],
				},
				latitude: "",
				longitude: "",
				markers: [],
				mainId: "",
				main_uuid: "",
				self: false,
				commentsArr: [],
				page: 1,
				commentsLength: true,
				sendMsg: "",
				id: "",
				type: null,
				location_img_hide: 0,
				isJoin: false,
				isWrite: false,
				showActionMenu: false,
				actionMenuItems: [],
				uuid: "",
				qrCodeImage: "",
				showPay: false,
				leftAmount: 0,
				joinLoading: false,
				showExit: false,
				showExit2: false,
				showFinish: false
			};
		},
		onLoad(opthion) {
			if (opthion.type) {
				this.type = opthion.type;
			}
			this.$http.get("/api/user/info").then((res) => {
				this.avatar = res.message.user_info.avatar;
			});

			this.getData(opthion.id);
			this.getComments(opthion.id);
			this.id = opthion.id;
		},
		mounted(opthion) {
			// this.getData(opthion.id)
		},
		methods: {
			// 打开分享面板
			openShare() {
				// 使用uqrcode组件获取base64图像
				// if (this.$refs.uqrcode) {
				//   this.$refs.uqrcode.toBase64({
				//     success: (res) => {
				// 将base64赋值给cardData.qrCodeImage
				// this.qrCodeImage = res.base64;

				this.$http
					.get("/api/user/qrcode-generate", {
						content: this.href,
					})
					.then((res) => {
						this.qrCodeImage = 'data:image/*;base64,' + res.message
						const {
							title,
							uuid,
							people_cnt
						} = this.activeData.card;
						// 准备卡片数据
						const cardData = {
							title: title,
							cardId: uuid,
							memberCount: people_cnt,
						};
						// 调用分享组件
						this.$refs.shareComponent.show({
							...cardData,
							qrCodeImage: this.qrCodeImage,
						});

						// 监听保存卡片图片事件
						uni.$on("saveCardImage", (cardId) => {
							if (cardId === this.activeData.card.uuid) {
								this.saveCardAsImage();
							}
						});
					})

				// },
				//   fail: (err) => {
				//     console.error("二维码生成失败:", err);
				//     uni.showToast({
				//       icon: "none",
				//       title: "二维码生成失败",
				//     });
				//   },
				// });
				// }
			},
			goBack() {
				if (this.type) {
					let current = getCurrentPages();
					current.map((item) => {
						console.log(item.$getAppWebview());
					});
					// console.log(current.length);
					uni.navigateBack({
						delta: current.length,
					});

					// uni.redirectTo({
					// 	url: "/pages/index/index?current=3"
					// })
				} else {
					uni.navigateBack();
				}
				// uni.navigateTo({
				// 	url: "/pages/events/events"
				// }).catch((err) => {
				// 	console.error('SwitchTab failed', err);
				// })
				// console.log(123);
			},
			toMember(e) {
				uni.navigateTo({
					url: "/pages/activity/activeMembers?uuid=" + e,
				});
			},
			toChildList(e) {
				uni.navigateTo({
					url: "/pages/activity/childList?id=" + e,
				});
			},
			previewArr(current, urls) {
				uni.previewImage({
					current,
					urls,
				});
			},
			preview(current, urls, cover) {
				let that = this;
				if (cover === 1) {
					uni.previewImage({
						current,
						urls: [urls],
					});
				} else {
					if (that.location_img_hide === 0) {
						uni.previewImage({
							current,
							urls,
						});
					} else {
						uni.showToast({
							title: `到达指定地点后才能查看`,
							icon: "none",
						});
					}
				}
			},
			toMap() {
				uni.navigateTo({
					url: "/pages/activity/map?uuid=" + this.id,
				});
			},
			getData(uuid) {
				let that = this;
				this.$http
					.get("/activity/detail", {
						uuid,
					})
					.then((res) => {
						console.log(res);

						that.activeData = res.message;
						that.activeData.card.timeline = that.activeData.card.timeline.replace(
							/\~/,
							"\~\r\n"
						);
						this.href =
							"https://static.lluuxiu.com/v1/download.html?type=activity&uuid=" +
							res.message.activity.uuid;
						that.mainId = res.message.activity.main_uuid;
						that.main_uuid = res.message.activity.uuid;

						that.uuid = res.message.activity.uuid;
						that.location_img_hide = res.message.activity.location_img_hide;
						let coordinate = res.message.activity.coordinate.split(",");
						let userInfo = uni.getStorageSync("userInfo");
						if (userInfo.uid === res.message.activity.uid) {
							that.self = true;
						}
						if (
							res.message.hasOwnProperty("is_member") &&
							res.message.is_member
						) {
							that.isJoin = true;
						} else {
							that.isJoin = false;
						}
						// 购买未核验
						if (!that.isJoin && res.message.order_no) {
							that.isWrite = true;
						} else {
							that.isWrite = false;
						}

						//自己创建的活动
						if (that.self) {
							this.actionMenuItems = [{
									icon: "../../static/images/report/fenxiang.png",
									text: "分享",
								},
								{
									icon: "../../static/images/report/jieshuhuodong.png",
									text: "结束活动",
								},
							];
						} else if (!that.self && !that.isJoin) {
							if (that.isWrite) {
								this.actionMenuItems = [{
										icon: "../../static/images/report/fenxiang.png",
										text: "分享",
									},
									{
										icon: "../../static/images/report/jubao.png",
										text: "举报",
									},
									{
										icon: "../../static/images/report/tuikuan.png",
										text: "退款",
									},
								];
							} else {
								this.actionMenuItems = [{
										icon: "../../static/images/report/fenxiang.png",
										text: "分享",
									},
									{
										icon: "../../static/images/report/jubao.png",
										text: "举报",
									},
									{
										icon: "../../static/images/report/buganxingqu.png",
										text: "不感兴趣",
									},
								];
							}

						} else if (!that.self && that.isJoin) {
							this.actionMenuItems = [{
									icon: "../../static/images/report/fenxiang.png",
									text: "分享",
								},
								{
									icon: "../../static/images/report/jubao.png",
									text: "举报",
								},
								{
									icon: "../../static/images/report/tuichuhuodong.png",
									text: "退出活动",
								},
							];
						}

						// if (that.self) {
						// 	that.actionMenuItems = [{
						// 		name: '结束活动',
						// 		icon: 'compose'
						// 	}, {
						// 		name: '删除',
						// 		icon: 'trash'
						// 	}]
						// }
						that.latitude = coordinate[1];
						that.longitude = coordinate[0];
						let iconPath = "../../static/active/<EMAIL>";
						that.markers = [{
							iconPath, // 标记点图标路径
							id: 0, // 标记点ID
							latitude: this.latitude, // 标记点纬度
							longitude: this.longitude, // 标记点经度
							width: 100, // 图标的宽度
							height: 100, // 图标的高度
							zIndex: 1, // 层级顺序
						}, ];
						console.log(that.uuid);
					});
			},
			getComments(uuid) {
				this.$http
					.get("/activity/comment-list", {
						uuid,
						page: this.page,
					})
					.then((res) => {
						this.commentsLength = res.message.length;
						if (this.page == 1) {
							this.commentsArr = res.message;
						} else {
							this.commentsArr.push(...res.message);
						}
					});
			},
			scrolltolower(s) {
				if (this.commentsLength) {
					this.page++;
					this.getComments(this.main_uuid);
				}
			},
			message(e) {
				this.sendMsg = e.detail.value;
			},
			send() {
				this.$http
					.post("/activity/comment", {
						uuid: this.main_uuid,
						content: this.sendMsg,
					})
					.then((res) => {
						uni.showToast({
							title: `评论成功`,
							icon: "none",
						});
						this.sendMsg = "";
						this.page = 1;
						this.getComments(this.main_uuid);
					});
			},
			join() {
				// 防止重复点击
				if (this.joinLoading === true) {
					uni.showToast({
						title: `正在加入请勿重复点击`,
						icon: "none",
					});
					return;
				}
				this.joinLoading = true;
				setTimeout(() => {
					this.joinLoading = false;
				}, 1000);
				this.$http
					.post("/activity/join", {
						uuid: this.uuid,
					})
					.then((res) => {
						uni.showToast({
							title: `加入成功`,
							icon: "none",
						});
						this.showPay = false;
						this.getData(this.id);
					})
					.catch((e) => {
						console.log(e);
					});
			},

			payTicket() {
				// 防止重复点击
				if (this.joinLoading === true) {
					uni.showToast({
						title: `正在支付请勿重复点击`,
						icon: "none",
					});
					return;
				}
				this.joinLoading = true;
				setTimeout(() => {
					this.joinLoading = false;
				}, 1000);
				this.$http
					.post("/activity/buy-ticket", {
						uuid: this.uuid,
					})
					.then((res) => {
						uni.showToast({
							title: `购买成功`,
							icon: "none",
						});
						this.showPay = false;
						this.getData(this.id);
					})
					.catch((e) => {
						console.log(e);
					});
			},

			async joinGroup() {
				if (!this.activeData.im_group_id) {
					uni.showToast({
						title: "群聊ID为空",
						icon: "none",
					});
					return;
				}

				console.log("team_id", this.activeData.im_group_id);
				console.log(this.$store.state.Yxim_info.allsessions);
				this.$Yxim.team
					.getTeamInfo({
						teamId: this.activeData.im_group_id,
					})
					.then((teamInfo) => {
						uni.navigateTo({
							url: "/pages/HM-chat/HM-chatNew?userItem=" +
								encodeURIComponent(
									JSON.stringify({
										chatName: teamInfo.name,
										uid: "team-" + teamInfo.teamId,
										type: "team",
										teamId: teamInfo.teamId,
										nuck: teamInfo.name,
										acitvityId: this.uuid,
									})
								),
						});
					});
			},

			join_ticket() {
				this.showPay = true;
				// 获取钱包余额
				this.$http.get("/api/user/wallet").then((res) => {
					console.log(res);
					this.leftAmount = res.message.total_amount || 0;
				});
			},

			goPay() {
				uni.navigateTo({
					url: "/pages/purse/purse",
				});
			},

			handleActionItem(item) {
				switch (item.text) {
					case "分享":
						this.openShare();
						break;
					case "不感兴趣":
						this.$http
							.post("/activity/block", {
								uuid: this.activeData.card.uuid,
							})
							.then((res) => {
								if (res.code == 200) {
									uni.hideLoading();
									uni.showToast({
										title: "已设置为不感兴趣",
										icon: "none",
									});
								} else {
									uni.hideLoading();
									uni.showToast({
										title: "设置失败",
										icon: "none",
									});
								}
							})
							.catch((err) => {
								uni.showToast({
									title: "网络异常，请稍后再试",
									icon: "none",
								});
							});
						// 这里可以添加将活动标记为不感兴趣的逻辑
						break;
					case "举报":
						uni.navigateTo({
							url: "/pages/report/report?params=" +
								this.activeData.card.uuid +
								"&type=2",
						});
						break;
					case "退款":
						this.showExit2 = true
						break;
					case "退出活动":
						if (this.self) {
							uni.showToast({
								title: "您是活动创建者，无法退出",
								icon: "none",
							});
							return;
						}
						this.showExit = true
						break;
					case '结束活动':
						this.showFinish = true
						break;
				}
				this.showActionMenu = false;
			},

			exitConfirm() {
				// 这里可以添加退出活动的逻辑
				uni.showLoading({
					title: "退出中...",
				});
				this.$http
					.post("/activity/quit", {
						uuid: this.uuid,
					})
					.then((res) => {
						this.showExit = false
						uni.hideLoading();
						if (res.code === 200) {
							uni.showToast({
								title: "已退出活动",
								icon: "none",
							});
							this.isJoin = false;
							this.getData(this.uuid)
						} else {
							uni.showToast({
								title: res.message,
								icon: "none"
							})
						}
					})
					.catch((err) => {
						uni.hideLoading();
						uni.showToast({
							title: "网络异常，请稍后再试",
							icon: "none",
						});
					});
			},
			exitConfirm2() {
				this.showExit2 = false
				uni.showLoading({
					title: '退款中...'
				});
				this.$http.post("/activity/refund-ticket", {
					uuid: this.uuid
				}).then((res) => {
					uni.hideLoading()
					console.log(res);
					if (res.code === 200) {
						uni.showToast({
							title: "退款成功！"
						})
						this.getData(this.uuid)
					} else {
						uni.showToast({
							title: res.message,
							icon: "none"
						})
					}
				});
			},

			finishConfirm() {
				// 这里可以添加退出活动的逻辑
				uni.showLoading({
					title: "结束中...",
				});
				this.$http
					.post("/activity/close", {
						uuid: this.uuid,
					})
					.then((res) => {
						this.showFinish = false
						uni.hideLoading();
						if (res.code === 200) {
							uni.showToast({
								title: "已结束活动",
								icon: "none",
							});
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.showToast({
								title: res.message,
								icon: "none"
							})
						}
					})
					.catch((err) => {
						uni.hideLoading();
						uni.showToast({
							title: "网络异常，请稍后再试",
							icon: "none",
						});
					});
			},

			goOrderDetail() {
				const orderNo = this.activeData.order_no;
				if (orderNo) {
					uni.navigateTo({
						url: `/pages/order/detail?orderNo=${orderNo}`,
					});
				}
			},

			// 保存卡片为图片
			saveCardAsImage() {
				const that = this;

				// 获取画布上下文
				const ctx = uni.createCanvasContext("cardCanvas", this);

				// 设置画布尺寸
				const canvasWidth = 300;
				const canvasHeight = 400;

				// 绘制背景
				ctx.fillStyle = "#191C26";
				ctx.fillRect(0, 0, canvasWidth, canvasHeight);

				// 绘制标题
				ctx.fillStyle = "#FFFFFF";
				ctx.font = "bold 16px sans-serif";
				ctx.fillText("活动标题: " + that.activeData.card.title, 20, 40);

				// 绘制活动ID
				ctx.fillStyle = "#CCCCCC";
				ctx.font = "14px sans-serif";
				ctx.fillText("活动ID: " + that.activeData.card.uuid, 20, 70);

				// 绘制活动时间
				ctx.fillStyle = "#FFFFFF";
				ctx.font = "14px sans-serif";
				ctx.fillText("活动时间: " + that.activeData.card.timeline, 20, 100);

				// 绘制活动地点
				ctx.fillStyle = "#FFFFFF";
				ctx.font = "14px sans-serif";
				ctx.fillText("活动地点: " + that.activeData.activity.address, 20, 130);

				// 绘制活动内容
				ctx.fillStyle = "#CCCCCC";
				ctx.font = "12px sans-serif";

				// 处理长文本换行
				const content = that.activeData.activity.content || "";
				const maxWidth = canvasWidth - 40;
				let lastSubStrIndex = 0;
				let currentLine = 0;

				for (let i = 0; i < content.length; i++) {
					if (currentLine >= 5) {
						// 最多显示5行
						ctx.fillText("...", 20, 160 + currentLine * 20);
						break;
					}

					const str = content.substring(lastSubStrIndex, i);
					const strWidth = ctx.measureText(str).width;

					if (strWidth > maxWidth) {
						ctx.fillText(
							content.substring(lastSubStrIndex, i - 1),
							20,
							160 + currentLine * 20
						);
						currentLine++;
						lastSubStrIndex = i - 1;
					}

					if (i == content.length - 1) {
						ctx.fillText(
							content.substring(lastSubStrIndex, i + 1),
							20,
							160 + currentLine * 20
						);
					}
				}

				// 绘制底部Logo和二维码
				ctx.fillStyle = "#FFFFFF";
				ctx.font = "12px sans-serif";
				ctx.fillText("LightingBall", 20, canvasHeight - 30);
				ctx.fillText("扫码参与活动", canvasWidth - 100, canvasHeight - 30);

				// 绘制完成后，生成图片
				ctx.draw(false, () => {
					setTimeout(() => {
						uni.canvasToTempFilePath({
								canvasId: "cardCanvas",
								success: function(res) {
									// 保存到相册
									uni.saveImageToPhotosAlbum({
										filePath: res.tempFilePath,
										success: function() {
											uni.showToast({
												title: "保存成功",
												icon: "success",
											});
										},
										fail: function(err) {
											console.log(err);
											uni.showToast({
												title: "保存失败",
												icon: "none",
											});
										},
									});
								},
								fail: function(err) {
									console.log(err);
									uni.showToast({
										title: "生成图片失败",
										icon: "none",
									});
								},
							},
							this
						);
					}, 200);
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.more {
		width: 30rpx;
		height: 30rpx;
		margin-right: 20rpx;
		padding: 20rpx;
	}

	.img-wrapper {
		overflow: hidden;
		border-radius: 24rpx;
		position: relative;
		background-color: rgba(0, 0, 0, 0.1);
	}

	.mh1-wrapper {
		width: 170rpx;
		height: 170rpx;
		margin-right: 0rpx;
	}

	.mh2-wrapper {
		width: 170rpx;
		height: 170rpx;
		margin-right: 0rpx;
		margin-bottom: 34rpx;
	}

	.mh3-wrapper {
		width: 170rpx;
		height: 170rpx;
		margin-right: 40rpx;
		margin-bottom: 34rpx;
	}

	.mh4-wrapper {
		width: 170rpx;
		height: 170rpx;
		margin-right: 40rpx;
		margin-bottom: 34rpx;
	}

	/* 基础图片样式 */
	.blur-base {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	/* 模糊覆盖层 */
	.blur-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(8px);
		-webkit-backdrop-filter: blur(8px);
		z-index: 1;
	}

	.mh1 {
		width: 100%;
		height: 100%;
	}

	.mh2 {
		width: 100%;
		height: 100%;
	}

	.mh3 {
		width: 100%;
		height: 100%;
	}

	.mh4 {
		width: 100%;
		height: 100%;
	}

	.map-container {
		margin-top: 40rpx;
		display: flex;
		justify-content: center;
		border-radius: 20rpx;
		overflow: hidden;
		width: 100%;
		height: 216px;
		position: relative;

		.leftTop {
			// position: absolute;
			// top: 0;
			// left: 0;
			// width: 50px;
			// height: 50px;
			// background-color: red;
		}
	}

	.content {
		width: 100%;
		padding: 40rpx;
		background: rgb(41, 44, 51);

		.item {
			box-sizing: border-box;
			border: 1rpx solid rgb(102, 104, 115);
			border-radius: 20rpx;
			margin-bottom: 40rpx;
			padding: 32rpx 38rpx 32rpx 38rpx;
		}

		.isJoin {
			box-sizing: border-box;
			border: 1px solid rgb(102, 104, 115);
			height: 94rpx;
			border-radius: 20rpx;
			margin-bottom: 40rpx;
			line-height: 94rpx;
			align-items: center;
		}

		.joinGroup {
			border-radius: 10px;
			background: rgb(173, 155, 248);
			height: 94rpx;
			border-radius: 20rpx;
			margin-bottom: 40rpx;
			line-height: 94rpx;
			font-size: 30rpx;
			text-align: center;
			font-weight: 500;
			color: white;
		}

		.ticket_unjoin {
			border-radius: 10px;
			background: linear-gradient(90deg,
					rgb(249, 225, 202),
					rgb(241, 185, 137) 100%);
			height: 94rpx;
			border-radius: 20rpx;
			margin-bottom: 40rpx;
			line-height: 94rpx;
			font-size: 30rpx;
			text-align: center;
			font-weight: 500;
			color: rgb(100, 61, 27);
		}

		.ticket_joined {
			border-radius: 10px;
			background: linear-gradient(90deg,
					rgb(249, 225, 202),
					rgb(241, 185, 137) 100%);
			height: 94rpx;
			border-radius: 20rpx;
			line-height: 94rpx;
			font-size: 30rpx;
			text-align: center;
			font-weight: 500;
			color: rgb(100, 61, 27);
		}

		.child {
			display: flex;
			justify-content: space-between;
		}

		.join {
			width: 100%;
			height: 94rpx;
			border-radius: 10px;
			border: none;
			justify-content: center;
			font-weight: bold;
			display: flex;
			align-items: center;
			text-align: center;
			background: linear-gradient(90deg, rgb(82, 195, 238), rgb(191, 147, 250));
		}

		.list {
			width: 100%;
			height: 284rpx;
			display: flex;
			padding: 20rpx;
			box-sizing: border-box;
			border: 1px solid rgb(102, 104, 115);
			border-radius: 20rpx;
			margin-bottom: 40rpx;

			.active {
				width: 338rpx;
				height: 204rpx;
				margin-top: 20rpx;

				.title {
					font-size: 16px;
					width: 100%;
					height: 36px;
					line-height: 18px;
					margin-bottom: 32rpx;
					overflow: hidden;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					word-break: break-all;
				}

				.avatar {
					position: relative;

					image {
						position: absolute;
						border-radius: 50%;
						display: inline-block;
						top: 0;
						left: 0;
						width: 40rpx;
						height: 40rpx;
					}
				}

				.t-distance {
					font-size: 16rpx;
					display: flex;
					align-items: center;
					margin-bottom: 30rpx;
					white-space: normal;
				}
			}
		}

		.comments {
			padding: 20px;

			.comments_list {
				margin-bottom: 60rpx;
				padding-left: 40rpx;
				padding-top: 20px;
				display: flex;
				justify-content: space-between;

				image {
					width: 80rpx;
					height: 80rpx;
					margin-right: 20rpx;
					border: 1px solid white;
					border-radius: 50%;
				}
			}

			.send {
				height: 40rpx;
				border-radius: 20rpx 20rpx 0rpx 0rpx;
				background: rgb(28, 28, 28);
				display: flex;
				justify-content: space-between;
				position: absolute;
				bottom: 0;
				left: 0;
				padding: 50rpx;

				.input-box {
					padding: 20px;
					// height: 100rpx;
				}

				.btn_send {
					width: 78rpx;
					// height: 40rpx;
					border-radius: 60rpx;
					background: linear-gradient(90deg,
							rgb(79, 197, 238),
							rgb(191, 147, 250) 100%);
				}
			}
		}
	}

	.action-menu-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 99998;
	}

	.action-menu {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #f5f5f5;
		padding: 50rpx 30rpx;
		display: flex;
		justify-content: space-around;
		z-index: 99999;
		border-top-right-radius: 12px;
		border-top-left-radius: 12px;
	}

	.action-item {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 25%;
	}

	.action-icon {
		width: 140rpx;
		height: 140rpx;
	}

	.share-btn {
		position: fixed;
		right: 30rpx;
		bottom: 120rpx;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background: linear-gradient(90deg, rgb(79, 197, 238), rgb(191, 147, 250));
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
		z-index: 10;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.card-canvas-container {
		position: absolute;
		left: -2000rpx;
		top: 0;
	}

	.card-desc {
		.desc-text {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			line-height: 1.5;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
			line-clamp: 3;
			overflow: hidden;
		}
	}

	.pop-content-exit {
		border-radius: 40rpx 40rpx 0px 0px;
		background-image: url(@/static/images/order/bg_exit.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding-top: 80rpx;
		padding-bottom: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.pop-content {
		border-radius: 40rpx 40rpx 0px 0px;
		background-image: url(@/static/images/order/bg_self_verify.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding-top: 80rpx;
		padding-bottom: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.pop-img {
		width: 160rpx;
		height: 160rpx
	}

	.pop-title {
		color: rgb(33, 33, 33);
		font-family: HarmonyOS Sans;
		font-size: 40rpx;
		font-weight: 700;
		letter-spacing: 0px;
		text-align: center;
	}

	.pop-desc {
		color: rgb(69, 69, 69);
		font-family: HarmonyOS Sans;
		font-size: 32rpx;
		font-weight: 500;
		letter-spacing: 0px;
		text-align: center;
		margin-top: 64rpx;
	}

	.pop-button {
		display: flex;
		align-items: center;
		padding: 40rpx 40rpx 64rpx 40rpx;
	}

	.fail {
		color: rgb(120, 120, 120);
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		border: rgb(120, 120, 120) solid 2rpx;
		flex: 1;
		padding: 24rpx 0;
	}

	.success {
		color: rgb(255, 255, 255);
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		background: rgb(255, 75, 58);
		flex: 1;
		padding: 24rpx 0;
		margin-left: 32rpx;
	}

	.verify {
		font-family: HarmonyOS Sans;
		font-size: 28rpx;
		font-weight: 400;
		letter-spacing: 0px;
		text-align: center;
		border-radius: 18rpx;
		background: rgb(255, 75, 58);
		flex: 1;
		padding: 24rpx 0;
		margin-left: 32rpx;
		background: rgb(39, 203, 255);
	}

	.pop-note {
		margin: 0 40rpx;
		margin-top: 16rpx;
		border-radius: 20rpx;
		background: linear-gradient(90.00deg, rgb(238, 247, 251), rgb(248, 249, 253) 100%);
		padding: 24rpx 32rpx;
		color: rgb(102, 102, 102);
		font-size: 28rpx;
	}
</style>