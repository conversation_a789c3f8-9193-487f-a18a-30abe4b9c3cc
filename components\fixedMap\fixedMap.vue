<template>
  <view class="fixed-map-container">
    <view id="fixedMapRender" class="map-view" :style="{ width: width, height: height }" :center="center"
      :change:center="fixedMapRender.receiveCenter" :markers="markers" :change:markers="fixedMapRender.receiveMarkers"
      :userAvatar="userAvatar" :change:userAvatar="fixedMapRender.receiveUserAvatar" :bgImage="bgImage"
      :change:bgImage="fixedMapRender.receiveBgImage">
    </view>
  </view>
</template>

<script module="fixedMapRender" lang="renderjs">
	let myMap;
	const bgImage = 'https://static-1317942045.cos.ap-shanghai.myqcloud.com/black.png'
	export default {
		data() {
			return {
				mapInitialized: false,
				currentUserAvatar: null,
				bgImage: 'https://static-1317942045.cos.ap-shanghai.myqcloud.com/black.png'
			}
		},
		mounted() {
			this.initMap();
		},
		methods: {
			initMap() {
				if (typeof window.AMap === 'function') {
					this.createMap();
				} else {
					// 设置高德地图安全密钥
					window._AMapSecurityConfig = {
						securityJsCode: '********************************',
					}

					// 动态加载高德地图1.4.15 API (更稳定)
					const script = document.createElement('script');
					script.src = "https://webapi.amap.com/maps?v=1.4.15&key=26ab172d25bd6002eb28192f569071a3";
					script.onload = this.createMap.bind(this);
					document.head.appendChild(script);
				}
			},

			createMap() {
				try {
					// 创建地图实例，使用高德地图1.4.15配置
					myMap = new AMap.Map('fixedMapRender', {
						// 地图视图配置
						viewMode: "3D", // 3D视图模式
						pitch: 50, // 地图俯仰角度，有效范围 0-83 度
						rotation: 0, // 地图顺时针旋转角度

						// 地图中心和缩放
						center: this.center || [116.4074, 39.9042], // 地图中心点
						zoom: 15, // 地图缩放级别
						zooms: [3, 20], // 地图缩放范围

						// 地图样式
						mapStyle: "amap://styles/48edbfe517aa7c40d462ffd134bb5289", // 使用自定义地图样式

						// 交互控制
						draggable: true, // 启用拖拽
						zoomEnable: true, // 启用缩放
						rotateEnable: true, // 启用旋转
						pitchEnable: true, // 启用倾斜
						scrollWheel: true, // 启用滚轮缩放
						doubleClickZoom: true, // 启用双击缩放
						keyboardEnable: true, // 启用键盘操作

						// 地图功能
						resizeEnable: true, // 启用地图自适应
						animateEnable: true // 启用动画
					});

					// 地图加载完成事件
					myMap.on("complete", () => {
						console.log('固定地图加载完成');
						this.mapInitialized = true;

						// 确保地图中心位置
						if (this.center) {
							myMap.setCenter(this.center);
						}

						// 添加默认标记
						this.addDefaultMarker();
					});

				} catch (error) {
					console.error('地图初始化失败:', error);
				}
			},

			addDefaultMarker() {
				if (!myMap) return;

				try {
					let marker;
					console.log("this.currentUserAvatar", this.currentUserAvatar);

					if (this.currentUserAvatar) {
						// 创建用户头像标记
						marker = new AMap.Marker({
							position: this.center || [116.4074, 39.9042],
							content: this.createAvatarMarkerContent(this.currentUserAvatar, '当前位置'),
							anchor: 'center'
						});
					} else {
						// 创建默认图标标记
						const icon = new AMap.Icon({
							image: "https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/location.png",
							size: new AMap.Size(30, 30),
							imageSize: new AMap.Size(30, 30)
						});

						marker = new AMap.Marker({
							position: this.center || [116.4074, 39.9042],
							icon: icon,
							title: '当前位置'
						});
					}

					// 添加标记到地图
					marker.setMap(myMap);

				} catch (error) {
					console.error('添加标记失败:', error);
				}
			},

			// 接收中心点变化
			receiveCenter(newValue, oldValue, ownerVm, vm) {
				if (myMap && this.mapInitialized && newValue) {
					try {
						// 使用动画方式设置地图中心
						myMap.setCenter(newValue, true);
						// 清除旧标记并添加新标记
						myMap.clearMap();
						this.addDefaultMarker();
					} catch (error) {
						console.error('设置地图中心失败:', error);
					}
				}
			},

			// 创建头像标记的HTML内容
			createAvatarMarkerContent(avatarUrl, title) {
				const bgImage = 'https://static-1317942045.cos.ap-shanghai.myqcloud.com/black.png';

				return `
        <div style="
          width: 50px;
          height: 50px;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <!-- 背景图片使用img标签 -->
          ${bgImage ? `<img src="${bgImage}"
                           style="
                             position: absolute;
                             width: 100%;
                             height: 100%;
                             object-fit: contain;
                             z-index: 1;
                           "
                           alt="背景图片" />` : ''}

          <!-- 用户头像 -->
          <div style="
            width: 35px;
            height: 34px;
            border-radius:6px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            top:-4px;
          ">
            <img src="${avatarUrl}"
                 style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;"
                 onerror="this.src='https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/location.png'"
                 alt="${title || '用户头像'}" />
          </div>
        </div>
      `;
			},

			// 接收标记变化
			receiveMarkers(newValue, oldValue, ownerVm, vm) {
				if (myMap && this.mapInitialized && newValue) {
					try {
						// 清除现有标记
						myMap.clearMap();

						// 添加新标记
						const markers = [];
						newValue.forEach(markerData => {
							// 判断是否为头像标记
							const isAvatar = markerData.isAvatar || markerData.type === 'avatar';
							const avatarUrl = markerData.avatar || markerData.iconPath;

							let marker;

							if (isAvatar && avatarUrl) {
								// 创建自定义HTML标记用于头像
								marker = new AMap.Marker({
									position: [markerData.longitude, markerData.latitude],
									content: this.createAvatarMarkerContent(avatarUrl, markerData.title),
									anchor: 'center'
								});
							} else {
								// 创建普通图标标记
								const icon = new AMap.Icon({
									image: markerData.iconPath ||
										"https://lb-1317942045.cos.ap-shanghai.myqcloud.com/config/location.png",
									size: new AMap.Size(markerData.width || 30, markerData.height || 30),
									imageSize: new AMap.Size(markerData.width || 30, markerData.height ||
										30)
								});

								marker = new AMap.Marker({
									position: [markerData.longitude, markerData.latitude],
									icon: icon,
									title: markerData.title || '位置标记'
								});
							}

							markers.push(marker);
						});

						// 批量添加标记到地图
						markers.forEach(marker => {
							marker.setMap(myMap);
						});

					} catch (error) {
						console.error('设置标记失败:', error);
					}
				}
			},

			// 接收用户头像变化
			receiveUserAvatar(newValue, oldValue, ownerVm, vm) {
				console.log('接收到用户头像:', newValue);
				this.currentUserAvatar = newValue;

				// 如果地图已初始化，重新添加标记
				if (myMap && this.mapInitialized) {
					myMap.clearMap();
					this.addDefaultMarker();
				}
			},

			// 接收背景图片变化
			receiveBgImage(newValue, oldValue, ownerVm, vm) {
				console.log('接收到背景图片:', newValue);
				this.bgImage = newValue;

				// 如果地图已初始化，重新添加标记
				if (myMap && this.mapInitialized) {
					myMap.clearMap();
					this.addDefaultMarker();
				}
			}
		}
	}
</script>

<script>
	export default {
		name: 'FixedMap',
		props: {
			width: {
				type: String,
				default: '100%'
			},
			height: {
				type: String,
				default: '300rpx'
			},
			latitude: {
				type: Number,
				default: 39.9042
			},
			longitude: {
				type: Number,
				default: 116.4074
			},
			markers: {
				type: Array,
				default: () => []
			},
			userAvatar: {
				type: String,
				default: ''
			},
			bgImage: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				center: null
			}
		},
		mounted() {
			// 设置地图中心点
			this.center = [this.longitude, this.latitude];
			console.log('Vue组件mounted, userAvatar:', this.userAvatar);
		},
		watch: {
			latitude(newVal) {
				this.updateCenter();
			},
			longitude(newVal) {
				this.updateCenter();
			},
			userAvatar(newVal) {
				console.log('Vue组件中userAvatar变化:', newVal);
				// 这里会自动触发 :change:userAvatar="fixedMapRender.receiveUserAvatar"
			},
			bgImage(newVal) {
				console.log('Vue组件中bgImage变化:', newVal);
				// 这里会自动触发 :change:bgImage="fixedMapRender.receiveBgImage"
			}
		},
		methods: {
			updateCenter() {
				this.center = [this.longitude, this.latitude];
			}
		}
	}
</script>

<style scoped>
.fixed-map-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 10rpx;
  position: relative;
}

.map-view {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 确保地图容器不会移动 */
.fixed-map-container,
.map-view {
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
}
</style>