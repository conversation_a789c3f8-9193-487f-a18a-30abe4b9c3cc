<template>
	<view class="appPage">
		<view class="t_display" v-show="userInfo">
			<image class="img52" :src="userInfo.avatar" mode="" style="border-radius: 50%;"> </image>
			<view style="margin: 0 18rpx;font-size: 32rpx;" @click="goNav('/pages/vipCenter/vipCenter')">
				{{userInfo.nickname}}
			</view>
			<image class="img24" src="../../../static/map/righticon.png" @click="goNav('/pages/vipCenter/vipCenter')"
				mode=""></image>
		</view>
		<view class="vipLogo" v-show="userInfo">
			<image class="logo" :src="imgSrc " mode="widthFix" />
			<view class="formDate">{{userInfo.vip_format}}</view>
		</view>

		<view class="card1">
			<view class="item title" @click="scan">
				个人
			</view>
			<view class="line"></view>
			<view class="item t_display" @click="scan">
				<image class="img42" src="@/static/images/my/menu1.png" mode=""></image>
				<view class="">
					扫一扫
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/my/myScan')">
				<image class="img42" src="@/static/images/my/ewm.png" mode=""></image>
				<view class="">
					我的二维码
				</view>
			</view>
			<!-- <view class="item t_display">
				<image class="img42"  src="@/static/images/my/menu3.png" mode=""></image>
				<view class="">
					实名认证
				</view>
			</view>
			<view class="line"></view> -->
		</view>
		<view class="card1" style="margin-top: 32rpx;">
			<view class="item title" @click="scan">
				功能
			</view>
			<view class="line"></view>
			<view class="item t_display" @click="goNav('/pages/chostMode/chostMode')">
				<image class="img42" src="@/static/images/my/ylms.png" mode=""></image>
				<view class="">
					幽灵模式
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/seeRadius/seeRadius')">
				<image class="img42" src="@/static/images/my/menu7.png" mode=""></image>
				<view class="">
					可见范围
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/commonAddress/commonAddress')">
				<image class="img42" src="@/static/images/my/menu6.png" mode=""></image>
				<view class="">
					常用地址
				</view>
			</view>
		</view>
		<view class="card1" style="margin-top: 32rpx;">
			<view class="item title" @click="scan">
				其他
			</view>
			<view class="line"></view>
			<view class="item t_display" @click="goNav('/pages/purse/purse')">
				<image class="img42" src="@/static/images/my/menu2.png" mode="">
				</image>
				<view class="">
					钱包
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/vipCenter/vipCenter')">
				<image class="img42" src="@/static/images/my/menu4.png" mode=""></image>
				<view class="">
					会员中心
				</view>
			</view>
			<view class="item t_display" @click="goNav('/pages/order/order')">
				<image class="img42" src="@/static/images/my/order.png" mode=""></image>
				<view class="">
					订单
				</view>
			</view>
			<!-- <view class="item t_display" @click="goBusiness('/pages/business/business')"> -->
			<view class="item t_display" @click="goBusiness()">
				<image class="img42" src="@/static/images/my/menu5.png" mode=""></image>
				<view class="">
					商家
				</view>
			</view>
		</view>
		<view class="card1" style="margin-top: 32rpx;">
			<view class="item t_display" @click="goNav('/pages/myInfo/myInfo')">
				<image class="img42" src="@/static/images/my/menu8.png" mode="">
				</image>
				<view class="">
					设置
				</view>
			</view>
		</view>
		<view class="card1" style="margin-top: 32rpx;">
			<view class="item t_display" @click="goNewGuide">
				<image class="img42" src="@/static/images/my/xinrenzhiyin.png" mode="">
				</image>
				<view class="">
					新人指引
				</view>
			</view>
		</view>
		<!-- <view class="card1" style="margin-top: 32rpx;">
			<view class="item t_display" @click="goNav('/pages/adolescent/adolescent')">
				<image class="img42" src="@/static/images/my/qingshaonian.png" mode="">
				</image>
				<view class="">
					青少年模式
				</view>
			</view>
		</view> -->
		<view class="img140"></view>
		<u-toast ref='notify' />
		<Ypopup ref="ypopRef" />
	</view>
</template>

<script>
	import Ypopup from "./ydPopup.vue"
	export default {
		components: {
			Ypopup
		},
		data() {
			return {
				userInfo: "",
				imgSrc: "",
			}
		},
		async created() {
			await this.$http.get('/api/user/info').then(async (res) => {
				this.userInfo = res.message.user_info
				uni.setStorageSync('userInfo', res.message.user_info)
			})
			this.imgSrc = require('../../../static/images/vip/gerenlogo' + this.userInfo.vip_level + '.png')
		},
		methods: {
			goNewGuide() {
				this.$emit('popup', 'newGuide')
			},
			async scan() {
				//关闭抽屉
				this.$emit('goNav')
				// 只允许通过相机扫码
				const code = await this.$common.scan()
				if (code) {
					uni.showLoading({
						title: "商家核验中..."
					})
					// 商家端核验
					this.$http.post('/api/pay/business-check-ticket').then(res => {
						uni.hideLoading()
						console.log(res)
						if (res.code === 200) {
							uni.showToast({
								title: "核验成功"
							})

						} else {
							uni.showToast({
								title: res,
								icon: "none"
							})
						}
					})
				}
			},
			async goBusiness() {
				await this.$http.get('/api/user/info').then(async (res) => {
					//关闭抽屉
					console.log(res)
					if (res.code == 200) {
						var data = res.message;
						console.log(data.user_info.is_business)
						// 1申请中 2正常 3申请驳回 0未申请
						if (data.user_info.is_business * 1 == 1 || data.user_info.is_business * 1 == 2) {
							console.log("商户1")
							// 跳转到商户页面
							this.$emit('goNav')
							uni.navigateTo({
								url: '/pages/shop/shop'
							});
						} else {
							console.log("商户2")
							// 跳转到认证页面
							this.$emit('goNav')
							uni.navigateTo({
								url: '/pages/shop/merchantAuthentication?type=first'
							});
						}
					}

				})

			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNav(url) {
				//关闭抽屉
				this.$emit('goNav')
				this.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		color: #000;
	}

	.vipLogo {
		position: relative;

		.logo {
			width: 371rpx;
			margin: 16rpx 0;
		}

		.formDate {
			position: absolute;
			bottom: 30rpx;
			font-size: 22rpx;
			left: 16rpx;
			color: #fff;
		}
	}


	.card1 {
		width: 371rpx;
		background: #F7F7F7;
		box-shadow: 0rpx 4rpx 6rpx 0rpx rgba(0, 0, 0, 0.12);
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		opacity: 1;

		.title {
			font-size: 24rpx;
			color: rgba(56, 56, 56, 0.72);
			line-height: 35rpx;
			text-align: left;
		}

		.item {
			font-size: 30rpx;
			padding: 33rpx;

			view {
				margin-left: 24rpx;
				color: #383838;
			}
		}

		.line {
			height: 1rpx;
			background: #E5E5E5;
		}
	}



	.vipSize {
		width: 77rpx;
		height: 55rpx;
		position: absolute;
		z-index: 1;
		top: 140rpx;
		left: 32rpx;
	}

	.membershipStatus {
		font-size: 26rpx;
		font-weight: 500;
		height: 55rpx;
		position: absolute;
		z-index: 1;
		top: 206rpx;
		left: 32rpx;
	}

	.leftStatus {
		width: 106rpx;
		height: 43rpx;
		line-height: 43rpx;
		background: #404D92;
		position: absolute;
		top: 60rpx;
		left: 1rpx;
		border-radius: 26rpx 0 26rpx 0;
		z-index: 1;
		text-align: center;
		font-weight: 500;
		font-size: 24rpx;
		color: #CDC4D9;
	}
</style>