<template>
	<view class="pageWrapper">
		<view class="row" @click="goActivityDetail">
			<view class="left">
				<image src="/static/images/activity_flag.png" class="flag" mode="aspectFill"></image>
				<text class="title">查看活动</text>
			</view>
			<uni-icons type="forward" color=""></uni-icons>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activityId: ''
			}
		},
		onLoad(options) {
			this.activityId = options.uuid
		},
		methods: {
			goActivityDetail() {
				uni.navigateTo({
					url: `/pages/activity/details?id=${this.activityId}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.pageWrapper {
		padding: 32rpx;
	}
.row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-radius: 18rpx;
	background: rgb(56, 58, 67);
	.left {
		display: flex;
		align-items: center;
		column-gap: 16rpx;
		.flag {
			width: 30rpx;
			height: 30rpx;
		}
		.title {
			color: rgb(255, 255, 255);
			font-size: 32rpx;
			font-weight: 400;
		}
	}
}
</style>
