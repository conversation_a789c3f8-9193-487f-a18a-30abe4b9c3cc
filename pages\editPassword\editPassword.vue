<template>
	<view class="appPage">
		<view class="inputBg">
			<u-input v-model="old_password" type="password" placeholder="请输入初密码" :border="false" clearable />
		</view>
		<view class="inputBg">
			<u-input v-model="password" type="password" placeholder="请输入新密码" :border="false" clearable />
		</view>
		<view class="inputBg">
			<u-input v-model="rulePwd" type="password" placeholder="请再次输入新密码" :border="false" clearable />
		</view>
		<!-- <view class="txt" @click="goNav('/pages/findPwd/findPwd')">
			忘记密码
		</view> -->
		<view class="login" @click="submit">确定
		</view>
		<u-toast ref='notify' />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				password: "",
				old_password: "",
				rulePwd: "",
			}
		},
		computed: {

		},
		methods: {
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			submit() {
				if (this.password !== this.rulePwd) return this.toast('两次密码输入不一致')
				this.$http.post('/api/user/reset-password', {
					password: this.password,
					old_password: this.old_password
				}).then(res => {
					uni.navigateBack()
				})
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appPage {
		padding: 32rpx;

		.txt {
			font-size: 26rpx;
			background: linear-gradient(to right, #4BC6ED, #BC93F2);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			display: flex;
			justify-content: flex-end;
		}

		.login {
			height: 94rpx;
			line-height: 94rpx;
			margin-top: 100rpx;
			border-radius: 14rpx;
			font-size: 32rpx;
			text-align: center;
			background: linear-gradient(#4BC6ED, #BC93F2);
		}

		.inputBg {
			color: #fff;
			background: #201F1F;
			height: 97rpx;
			// line-height: 97rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			padding: 0 24rpx;
			margin-bottom: 42rpx;
		}

		.tips {
			margin-top: 32rpx;
			font-size: 22rpx;
			font-family: Source Han Sans-Regular, Source Han Sans;
			font-weight: 400;
			color: #848484;
			line-height: 32rpx;
		}
	}
</style>